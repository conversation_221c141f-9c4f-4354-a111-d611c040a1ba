// include/algorithm/path_planner/ipath_planner.h
#pragma once

#include "core/types.h"
#include "utils/logging.h"
#include "uav/idynamic_model.h"
#include "mission/task_strategies.h"
#include "planning/planning_types.h"
#include <vector>
#include <memory>
#include <string>
#include <map>
#include "nlohmann/json.hpp"

namespace NSDrones {
	namespace NSUav { class Uav; }
	namespace NSMission { struct PathConstraintStrategy; }
	namespace NSParams { class ParamValues; }
}

namespace NSDrones {
	namespace NSAlgorithm {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSPlanning;

		/**
		 * @class IPathPlanner
		 * @brief 重构后的路径规划算法接口
		 *
		 * 设计原则：
		 * - 外部接口使用WGS84坐标系
		 * - 内部计算使用ECEF坐标系
		 * - 使用统一的数据结构
		 */
		class IPathPlanner {
		public:
			/** @brief 默认构造函数 */
			explicit IPathPlanner();

			/** @brief 虚析构函数 */
			virtual ~IPathPlanner() = default;

			// --- 禁止拷贝和移动 ---
			IPathPlanner(const IPathPlanner&) = delete;
			IPathPlanner& operator=(const IPathPlanner&) = delete;
			IPathPlanner(IPathPlanner&&) = delete;
			IPathPlanner& operator=(IPathPlanner&&) = delete;

		public:
			/**
			 * @brief 查找路径（使用统一数据结构）
			 * @param request 路径规划请求（包含WGS84坐标）
			 * @return 路径规划结果（包含WGS84坐标）
			 */
			virtual PathPlanningResult findPath(const PathPlanningRequest& request) = 0;

			/**
			 * @brief 检查路径可行性
			 * @param waypoints WGS84路径点
			 * @return 可行性检查结果
			 */
			virtual bool isPathFeasible(const std::vector<WGS84Point>& waypoints) const = 0;

		protected:
			// 内部计算可以使用ECEF坐标
			virtual std::vector<EcefPoint> findPathECEF(const EcefPoint& start, const EcefPoint& goal) = 0;
		};

		using IPathPlannerPtr = std::shared_ptr<IPathPlanner>;

	} // namespace NSAlgorithm
} // namespace NSDrones

using namespace NSDrones::NSAlgorithm;