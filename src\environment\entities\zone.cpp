// src/environment/zone.cpp
#include "environment/entities/zone.h"
#include "core/entity_object.h"
#include "environment/environment.h"
#include "environment/coordinate/coordinate_manager.h"
#include "utils/coordinate_converter.h"
#include "environment/collision/collision_engine.h"
#include "environment/collision/collision_types.h"
#include "utils/geometry_manager.h"
#include "utils/logging.h"
#include "utils/object_id.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include <algorithm>
#include <limits>
#include <memory>
#include <vector>
#include <cmath>
#include "core/geometry/ishape.h"
#include "core/geometry/shapes/sphere_shape.h"
#include "core/geometry/shapes/box_shape.h"
#include "core/geometry/shapes/cylinder_shape.h"
#include "core/geometry/shapes/polygon_shape.h"
#include "core/geometry/shapes/line_shape.h"
#include <stdexcept>
#include "core/movement_strategy.h"
#include "utils/enum_utils.h"

namespace NSDrones {
	namespace NSEnvironment {

		// --- Zone 构造函数 ---
		/** @brief 构造函数 (静态区域)。 */
		Zone::Zone(ObjectID id,
			const std::string& zone_type_key,
			const std::string& name,
			const EntityState& initial_state,
			std::shared_ptr<IMovementStrategy> strategy)
			: EntityObject(std::move(id), zone_type_key, name, initial_state),
			zone_type_(ZoneType::UNKNOWN),
			is_3d_(true)
		{
			LOG_DEBUG("Zone ({}) 正在构造: ID='{}', 类型键='{}'",
				getName().empty() ? zone_type_key : getName(), getId(), zone_type_key);

			if (strategy) {
				LOG_DEBUG("Zone ID '{}': 构造时收到了移动策略 '{}'。", getId(), typeid(*strategy).name());
				setMovementStrategy(strategy);
			}
		}

		bool Zone::initialize(std::shared_ptr<NSParams::ParamValues> final_params, const nlohmann::json& raw_instance_json_config) {
			if (!final_params) {
				LOG_ERROR("Zone 初始化失败: 传入的参数指针为空。");
				return false;
			}
			LOG_DEBUG("Zone [{} ID:{}] (类型标签: {}): 开始执行 initialize(ParamValues&, const json&)...",
				getName(), getId(), getTypeTag());

			// 1. 调用基类的 initialize 方法 (处理参数赋值, 形状创建, 添加到环境)
			if (!EntityObject::initialize(std::make_shared<NSParams::ParamValues>(*final_params), raw_instance_json_config)) {
				LOG_ERROR("Zone [{} ID:{}] (类型标签: {}): 调用 EntityObject::initialize 失败。初始化终止。",
					getName(), getId(), getTypeTag());
				return false;
			}
			// 到这里, this->params_ 已经包含了最终参数, 形状已创建 (如果定义了), 对象已在环境中
			LOG_DEBUG("Zone [{} ID:{}] (类型标签: {}): EntityObject::initialize 完成。",
				getName(), getId(), getTypeTag());

			// 2. 加载 Zone 特定的参数 (zone_type, is_3d)
			try {
				// 使用正确的枚举获取方法
				auto zone_type_opt = params_->getEnumValue<ZoneType>("zone_type");
				if (zone_type_opt.has_value()) {
					zone_type_ = zone_type_opt.value();
					LOG_DEBUG("Zone [{} ID:{}] (类型标签: {}): 成功获取 zone_type 枚举值: {}",
						getName(), getId(), getTypeTag(), NSUtils::enumToString(zone_type_));
				} else {
					LOG_WARN("Zone [{} ID:{}] (类型标签: {}): 无法获取 zone_type 枚举值，使用默认值 UNKNOWN。",
						getName(), getId(), getTypeTag());
					zone_type_ = ZoneType::UNKNOWN;
				}

				is_3d_ = getParamOrDefault<bool>("is_3d", true);

				LOG_DEBUG("Zone [{} ID:{}] (类型标签: {}): 特定参数加载完成。逻辑类型: {}, 3D: {}",
					getName(), getId(), getTypeTag(), NSUtils::enumToString(zone_type_), is_3d_);
			}
			catch (const std::exception& e) {
				LOG_ERROR("Zone [{} ID:{}] (类型标签: {}): 加载 zone_type 或 is_3d 参数时发生异常: {}。初始化可能不完整。",
					getName(), getId(), getTypeTag(), e.what());
				// 根据需要决定是否返回 false
			}

			// 3. 检查移动策略 (如果 Zone 可以移动的话)
			if (getMovementStrategy()) {
				LOG_DEBUG("Zone [{} ID:{}] (类型标签: {}): 检测到已关联移动策略 '{}'。",
					getName(), getId(), getTypeTag(), typeid(*getMovementStrategy()).name());
			}
			else {
				// 静态 Zone 不需要移动策略，或者应该默认是 Still
				LOG_TRACE("Zone [{} ID:{}] (类型标签: {}): 未设置移动策略 (通常对于静态区域是正常的)。",
					getName(), getId(), getTypeTag());
				// 如果需要确保所有对象都有策略，可以在这里设置 StillMovementStrategy
				// setMovementStrategy(std::make_shared<StillMovementStrategy>(*this));
			}

			// 注意：形状创建已移至 EntityObject::initialize

			LOG_INFO("Zone [{} ID:{}] (类型标签: {}, 逻辑类型: {}): initialize(ParamValues&, const json&) 完成。形状: {}, 3D: {}, 移动策略: {}",
				getName(), getId(), getTypeTag(), NSUtils::enumToString(zone_type_),
				(getShape() ? NSUtils::enumToString(getShape()->getType()) : "无"),
				is_3d_,
				(getMovementStrategy() ? typeid(*getMovementStrategy()).name() : "无"));
			return true;
		}

		// --- 动态计算接口 (现在从 EntityObject 获取状态) ---

		/** @brief 获取区域当前的轴对齐包围盒 (AABB)。重写 EntityObject 方法。 */
		BoundingBox Zone::getBoundingBox() const {
			LOG_TRACE("Zone [{} ID:{}] (类型: {}): 调用 getBoundingBox()", getName(), getId(), getTypeTag());
			auto current_shape = getShape(); // 从 EntityObject 获取形状
			if (!current_shape) {
				LOG_WARN("Zone [{} ID:{}] (类型: {}): 形状为空，无法计算 AABB。", getName(), getId(), getTypeTag());
				return BoundingBox(); // 返回无效 AABB
			}
			// 获取Zone的ECEF位置进行几何运算
			WGS84Point zone_wgs84 = getWGS84Position();
			EcefPoint zone_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(zone_wgs84);
			const Orientation& current_orn = getOrientation();

			if (!std::isfinite(zone_ecef.x()) || !std::isfinite(zone_ecef.y()) || !std::isfinite(zone_ecef.z())) {
				LOG_WARN("Zone [{} ID:{}] (类型: {}): 当前ECEF位置无效，无法计算 AABB。", getName(), getId(), getTypeTag());
				return BoundingBox();
			}
			LOG_TRACE("计算区域 [{}] 的 AABB (ECEF位置: {}, 姿态: (w:{:.2f},x:{:.2f},y:{:.2f},z:{:.2f}))",
				getId(), zone_ecef.toString(),
				current_orn.w(), current_orn.x(), current_orn.y(), current_orn.z());
			// 创建FCL变换矩阵
			fcl::Vector3d translation(zone_ecef.x(), zone_ecef.y(), zone_ecef.z());
			fcl::Transform3d transform = fcl::Transform3d::Identity();
			transform.translation() = translation;
			// 设置旋转（如果需要）
			// transform.linear() = current_orn.toRotationMatrix();
			return current_shape->getAABB(transform);
		}


		// --- 几何检查接口 (使用 EntityObject 的状态和环境引用 env_) ---
		/** @brief 检查点是否在区域内部。 */
		bool Zone::isInside(const EcefPoint& ecef_point, double tolerance) const {
			LOG_TRACE("Zone [{} ID:{}] (类型: {}): 调用 isInside({}, tolerance={:.3f})",
				getName(), getId(), getTypeTag(), ecef_point.toString(), tolerance);
			auto current_shape = getShape();
			if (!current_shape) {
				LOG_WARN("Zone::isInside 检查失败: 区域 [{}] 形状为空。", getId());
				return false;
			}

			// 获取Zone的ECEF位置进行几何运算
			WGS84Point zone_wgs84 = getWGS84Position();
			EcefPoint zone_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(zone_wgs84);
			const Orientation& current_orn = getOrientation();

			if (!std::isfinite(zone_ecef.x()) || !std::isfinite(zone_ecef.y()) || !std::isfinite(zone_ecef.z())) {
				LOG_WARN("Zone::isInside 检查失败: 区域 [{}] 当前ECEF位置无效。", getId());
				return false;
			}

			// 特殊处理 2D 平面多边形区域
			if (current_shape->getType() == ShapeType::POLYGON && !is_3d_) {
				LOG_TRACE("Zone::isInside [{}]: 执行 2D 平面多边形检查...", getId());
				const auto* poly_shape = static_cast<const PolygonShape*>(current_shape.get());
				// 直接在ECEF坐标系中进行几何计算
				// 将局部顶点转换到当前ECEF世界坐标
				std::vector<EcefPoint> world_vertices;
				world_vertices.reserve(poly_shape->getVertices().size());
				for (const auto& local_v : poly_shape->getVertices()) {
					Vector3D local_v_vec(local_v.x(), local_v.y(), local_v.z());
					Vector3D world_vertex_vec = zone_ecef.toVector3D() + current_orn * local_v_vec;
					EcefPoint world_vertex(world_vertex_vec.x(), world_vertex_vec.y(), world_vertex_vec.z());
					world_vertices.push_back(world_vertex);
				}
				// 直接在ECEF坐标系中进行2D多边形检测（投影到XY平面）
				bool inside_poly = NSUtils::GeometryManager::isPointInPolygonECEF(ecef_point, world_vertices, tolerance);
				LOG_TRACE("  GeometryManager::isPointInPolygonECEF 结果: {}", inside_poly);
				return inside_poly;
			}

			// 3D 区域碰撞检测
			LOG_TRACE("Zone::isInside [{}]: 执行 3D 碰撞检查，点 {} (容差 {:.3f})...",
				getId(), ecef_point.toString(), tolerance);
			// 使用环境提供的碰撞引擎
			auto env = getEnvironment();
			auto collision_engine = env ? env->getCollisionEngine() : nullptr;
			if (!collision_engine) {
				LOG_ERROR("Zone::isInside: 无法获取碰撞引擎。");
				return false;
			}

			// 简化实现：直接使用形状的几何检测
			// 将点转换到Zone的局部坐标系（在ECEF坐标系中计算）
			Vector3D local_point = current_orn.inverse() * (ecef_point - zone_ecef);

			// 对于不同形状类型进行点包含检测
			switch (current_shape->getType()) {
				case ShapeType::SPHERE: {
					auto sphere = std::static_pointer_cast<const SphereShape>(current_shape);
					bool inside = local_point.norm() <= (sphere->getRadius() + tolerance);
					LOG_TRACE("Zone::isInside [{}] 球体检测结果: hasCollision={}", getId(), inside);
					return inside;
				}
				case ShapeType::BOX: {
					auto box = std::static_pointer_cast<const BoxShape>(current_shape);
					fcl::Vector3d box_size = box->getSize();
					Vector3D half_dims(box_size.x() * 0.5 + tolerance, box_size.y() * 0.5 + tolerance, box_size.z() * 0.5 + tolerance);
					bool inside = (local_point.array().abs() <= half_dims.array()).all();
					LOG_TRACE("Zone::isInside [{}] 盒子检测结果: hasCollision={}", getId(), inside);
					return inside;
				}
				case ShapeType::CYLINDER: {
					auto cylinder = std::static_pointer_cast<const CylinderShape>(current_shape);
					double radius = cylinder->getRadius() + tolerance;
					double half_height = cylinder->getHeight() * 0.5 + tolerance;
					// 检查点是否在圆柱体内：XY平面内的距离 <= 半径，且Z坐标在高度范围内
					double xy_distance = std::sqrt(local_point.x() * local_point.x() + local_point.y() * local_point.y());
					bool inside = (xy_distance <= radius) && (std::abs(local_point.z()) <= half_height);
					LOG_TRACE("Zone::isInside [{}] 圆柱体检测结果: hasCollision={} (XY距离={:.2f}, Z={:.2f})",
						getId(), inside, xy_distance, local_point.z());
					return inside;
				}
				case ShapeType::POLYGON: {
					// 对于3D多边形，直接在ECEF坐标系中进行几何检测
					auto polygon = std::static_pointer_cast<const PolygonShape>(current_shape);
					// 将局部顶点转换到ECEF世界坐标
					std::vector<EcefPoint> world_vertices;
					world_vertices.reserve(polygon->getVertices().size());
					for (const auto& local_v : polygon->getVertices()) {
						Vector3D local_v_vec(local_v.x(), local_v.y(), local_v.z());
						Vector3D world_vertex_vec = zone_ecef.toVector3D() + current_orn * local_v_vec;
						EcefPoint world_vertex(world_vertex_vec.x(), world_vertex_vec.y(), world_vertex_vec.z());
						world_vertices.push_back(world_vertex);
					}
					// 直接在ECEF坐标系中进行多边形检测
					bool inside = NSUtils::GeometryManager::isPointInPolygonECEF(ecef_point, world_vertices, tolerance);
					LOG_TRACE("Zone::isInside [{}] 多边形检测结果: hasCollision={}", getId(), inside);
					return inside;
				}
				default:
					LOG_WARN("Zone::isInside [{}] 不支持的形状类型: {}", getId(), NSUtils::enumToString(current_shape->getType()));
					return false;
			}
		}

		/** @brief 检查线段是否与区域相交。 */
		bool Zone::intersects(const EcefPoint& p1, const EcefPoint& p2, double tolerance) const {
			LOG_TRACE("Zone [{} ID:{}] (类型: {}): 调用 intersects(p1, p2, tolerance={:.3f})",
				getName(), getId(), getTypeTag(), tolerance);

			// 声明环境变量，在整个函数中重用
			std::shared_ptr<Environment> env = nullptr;

			auto current_shape = getShape();
			if (!current_shape) {
				LOG_WARN("Zone::intersects 检查失败: 区域 [{}] 形状为空。", getId());
				return false;
			}

			// 获取Zone的ECEF位置进行几何运算
			WGS84Point zone_wgs84 = getWGS84Position();
			EcefPoint zone_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(zone_wgs84);
			const Orientation& current_orn = getOrientation();

			if (!std::isfinite(zone_ecef.x()) || !std::isfinite(zone_ecef.y()) || !std::isfinite(zone_ecef.z())) {
				LOG_WARN("Zone::intersects 检查失败: 区域 [{}] 当前ECEF位置无效。", getId());
				return false;
			}

			// 特殊处理 2D 平面多边形区域
			if (current_shape->getType() == ShapeType::POLYGON && !is_3d_) {
				LOG_TRACE("Zone::intersects [{}]: 执行 2D 平面多边形检查...", getId());
				const auto* poly_shape = static_cast<const PolygonShape*>(current_shape.get());
				std::vector<EcefPoint> world_vertices;
				world_vertices.reserve(poly_shape->getVertices().size());
				for (const auto& local_v : poly_shape->getVertices()) {
					Vector3D local_v_vec(local_v.x(), local_v.y(), local_v.z());
					Vector3D world_vertex_vec = zone_ecef.toVector3D() + current_orn * local_v_vec;
					EcefPoint world_vertex(world_vertex_vec.x(), world_vertex_vec.y(), world_vertex_vec.z());
					world_vertices.push_back(world_vertex);
				}
				if (NSUtils::GeometryManager::isPointInPolygonECEF(p1, world_vertices, tolerance) 
					|| NSUtils::GeometryManager::isPointInPolygonECEF(p2, world_vertices, tolerance)) {
					LOG_TRACE("  端点在 2D 区域内，判定相交。");
					return true;
				}
				for (size_t i = 0; i < world_vertices.size(); ++i) {
					const auto& edge_p1 = world_vertices[i];
					const auto& edge_p2 = world_vertices[(i + 1) % world_vertices.size()];
					if (NSUtils::GeometryManager::isSegmentsIntersect2D(p1, p2, edge_p1, edge_p2, true, tolerance)) {
						LOG_TRACE("  线段与 2D 区域边界相交。");
						return true;
					}
				}
				LOG_TRACE("  线段与 2D 区域不相交。");
				return false;
			}

			// 3D 区域碰撞检测
			LOG_TRACE("Zone::intersects [{}]: 执行 3D 碰撞检查，线段 ({:.1f},{:.1f},{:.1f})->({:.1f},{:.1f},{:.1f}) ...",
				getId(), p1.x(), p1.y(), p1.z(), p2.x(), p2.y(), p2.z());

			// 1. 端点检查 (调用此类中的 isInside)
			if (this->isInside(p1, tolerance) || this->isInside(p2, tolerance)) {
				LOG_TRACE("  端点在 3D 区域内，判定相交。");
				return true;
			}

			// 2. AABB 粗略检查 (使用 this->getBoundingBox())
			BoundingBox zone_bbox = this->getBoundingBox(); // 使用重写的方法
			// 检查AABB是否有效（FCL AABB没有isEmpty方法，检查体积是否为0）
			double volume = zone_bbox.volume();
			if (volume <= 0.0) { // AABB 可能为空如果形状无效
				LOG_WARN("  区域 [{}] 当前 AABB 无效或为空，无法有效检查相交，依赖精确检测。", getId());
			}
			else {
				// 创建线段的AABB
				fcl::Vector3d p1_vec(p1.x(), p1.y(), p1.z());
				fcl::Vector3d p2_vec(p2.x(), p2.y(), p2.z());
				fcl::Vector3d min_pt = p1_vec.cwiseMin(p2_vec);
				fcl::Vector3d max_pt = p1_vec.cwiseMax(p2_vec);

				// 考虑公差扩展线段的包围盒
				fcl::Vector3d tolerance_vec(tolerance, tolerance, tolerance);
				min_pt -= tolerance_vec;
				max_pt += tolerance_vec;
				BoundingBox segment_bbox(min_pt, max_pt);

				if (!zone_bbox.overlap(segment_bbox)) {
					LOG_TRACE("  线段 AABB 与区域 [{}] AABB (扩展后) 不相交，判定不相交。", getId());
					return false;
				}
				LOG_TRACE("  AABB 相交，进行精确碰撞检查...");
			}

			// 3. 精确碰撞检测 (使用 CollisionEngine)
			if (!env) env = getEnvironment(); // 重用之前获取的环境实例
			auto collision_engine = env ? env->getCollisionEngine() : nullptr; // 修正: 使用 env
			if (!collision_engine) {
				LOG_ERROR("Zone::intersects [{}] 无法获取碰撞引擎。", getId());
				return false; // 或者抛出异常
			}

			// 创建线段形状用于碰撞检测 (使用 LineShape)
			// LineShape 构造函数需要局部坐标，但这里 p1, p2 是世界坐标。
			// 因此，我们将线段形状的"中心"也设为 p1，方向通过 p2-p1 计算，
			// 或者更简单地，创建一个表示世界坐标中线段的 FCL 对象。
			// 对于 Collisioner::detectCollision，它期望的是形状和它们各自的变换。
			// 我们可以创建一个 LineShape，并将其变换设置为单位变换，而其顶点是世界坐标。
			// 或者，如果 Collisioner 支持直接传递线段，则更好。
			// 假设 Collisioner 可以处理世界坐标下的形状进行检测。
			// 我们需要一个代表线段的 IShape。
			// LineShape 的构造是 start_local, end_local。如果我们直接用 p1,p2，那么它的中心就是 (p1+p2)/2，或者我们可以将其"放置"在p1，然后 LineShape 的局部终点是 p2-p1。
			// 这里的一个简化方式是，如果 Collisioner 的 detectCollision 期望的是形状指针和世界变换，
			// 我们可以构造一个 LineShape (0,0,0) 到 (p2-p1)，然后将其放置在 p1。
			// 或者更直接的方式，是 Collisioner 提供一个专门的线段检测接口，或者其 detectCollision 能够智能处理。
			// 当前 detectCollision 签名: (shape1, shape2, pos1, pos2, orn1, orn2)
			// 我们需要一个代表线段的 IShapePtr。
			auto segment_shape = std::make_shared<LineShape>(Vector3D::Zero(), p2.toVector3D() - p1.toVector3D());
			// 注意：上面的LineShape的局部起点是(0,0,0)，局部终点是(p2-p1)。我们将把它放置在p1处。

			// 简化实现：使用几何算法检测线段与形状的相交
			// 将线段转换到Zone的局部坐标系（在ECEF坐标系中计算）
			Vector3D local_p1 = current_orn.inverse() * (p1 - zone_ecef);
			Vector3D local_p2 = current_orn.inverse() * (p2 - zone_ecef);

			// 对于不同形状类型进行线段相交检测
			switch (current_shape->getType()) {
				case ShapeType::SPHERE: {
					auto sphere = std::static_pointer_cast<const SphereShape>(current_shape);
					// 计算线段到球心的最短距离
					Vector3D line_dir = local_p2 - local_p1;
					double line_length = line_dir.norm();
					if (line_length < Constants::GEOMETRY_EPSILON) {
						// 退化为点检测
						bool intersects = local_p1.norm() <= (sphere->getRadius() + tolerance);
						LOG_TRACE("Zone::intersects [{}] 球体点检测结果: hasCollision={}", getId(), intersects);
						return intersects;
					}
					line_dir /= line_length;

					// 计算线段上最接近球心的点
					double t = std::max(0.0, std::min(line_length, -local_p1.dot(line_dir)));
					Vector3D closest_point = local_p1 + t * line_dir;
					bool intersects = closest_point.norm() <= (sphere->getRadius() + tolerance);
					LOG_TRACE("Zone::intersects [{}] 球体线段检测结果: hasCollision={}", getId(), intersects);
					return intersects;
				}
				case ShapeType::BOX: {
					auto box = std::static_pointer_cast<const BoxShape>(current_shape);
					fcl::Vector3d box_size = box->getSize();
					Vector3D half_dims(box_size.x() * 0.5 + tolerance, box_size.y() * 0.5 + tolerance, box_size.z() * 0.5 + tolerance);
					// 简化实现：检查线段端点是否在盒子内，或者盒子是否与线段相交
					bool p1_inside = (local_p1.array().abs() <= half_dims.array()).all();
					bool p2_inside = (local_p2.array().abs() <= half_dims.array()).all();
					bool intersects = p1_inside || p2_inside;
					LOG_TRACE("Zone::intersects [{}] 盒子线段检测结果: hasCollision={}", getId(), intersects);
					return intersects;
				}
				case ShapeType::CYLINDER: {
					auto cylinder = std::static_pointer_cast<const CylinderShape>(current_shape);
					double radius = cylinder->getRadius() + tolerance;
					double half_height = cylinder->getHeight() * 0.5 + tolerance;

					// 简化实现：检查线段端点是否在圆柱体内
					auto checkPointInCylinder = [&](const Vector3D& p) {
						double xy_distance = std::sqrt(p.x() * p.x() + p.y() * p.y());
						return (xy_distance <= radius) && (std::abs(p.z()) <= half_height);
					};

					bool p1_inside = checkPointInCylinder(local_p1);
					bool p2_inside = checkPointInCylinder(local_p2);
					bool intersects = p1_inside || p2_inside;
					LOG_TRACE("Zone::intersects [{}] 圆柱体线段检测结果: hasCollision={}", getId(), intersects);
					return intersects;
				}
				default:
					LOG_WARN("Zone::intersects [{}] 不支持的形状类型: {}", getId(), NSUtils::enumToString(current_shape->getType()));
					return false;
			}
		}


		// --- 外扩/安全裕度相关 ---
		void Zone::updateExpandedShape(double margin) {
			LOG_DEBUG("Zone [{} ID:{}] (类型: {}): 更新外扩形状，外沿 = {:.2f}m", getName(), getId(), getTypeTag(), margin);
			expanded_margin_ = margin;
			expanded_shape_valid_ = false;
			auto base_shape = getShape(); // 从 EntityObject 获取形状
			if (!base_shape) {
				LOG_WARN("Zone::updateExpandedShape: 基础形状为空，无法创建外扩形状。");
				expanded_shape_ = nullptr;
				return;
			}

			if (std::abs(margin) < Constants::GEOMETRY_EPSILON) {
				LOG_DEBUG("Zone::updateExpandedShape: 外沿接近零，外扩形状与基础形状相同。");
				expanded_shape_ = base_shape; // 直接使用原始形状的共享指针
				expanded_shape_valid_ = true;
				return;
			}

			// IShape 没有 computeExpandedShape，需要Zone自己处理
			// expanded_shape_ = base_shape->computeExpandedShape(margin); // 错误行
			// 实现简单的外扩逻辑：对于球体，增加半径；对于盒子，增加维度等。
			// 这部分逻辑可以比较复杂，取决于支持的形状类型。
			// 简单示例，只处理球体和盒子，其他类型返回空或原始形状。
			switch (base_shape->getType()) {
			case ShapeType::SPHERE:
			{
				auto sphere = std::static_pointer_cast<const SphereShape>(base_shape);
				double new_radius = sphere->getRadius() + margin;
				if (new_radius > Constants::GEOMETRY_EPSILON) {
					expanded_shape_ = std::make_shared<SphereShape>(new_radius);
					expanded_shape_valid_ = true;
					LOG_DEBUG("外扩球体: 原半径 {:.2f}, 新半径 {:.2f}", sphere->getRadius(), new_radius);
				}
				else {
					LOG_WARN("外扩球体半径 ({:.2f}) 无效，外扩失败。", new_radius);
					expanded_shape_ = nullptr; // 或者设置为一个点？
				}
				break;
			}
			case ShapeType::BOX:
			{
				auto box = std::static_pointer_cast<const BoxShape>(base_shape);
				fcl::Vector3d box_size = box->getSize();
				Vector3D new_dims(box_size.x() + 2 * margin, box_size.y() + 2 * margin, box_size.z() + 2 * margin);
				if (new_dims.x() > Constants::GEOMETRY_EPSILON && new_dims.y() > Constants::GEOMETRY_EPSILON && new_dims.z() > Constants::GEOMETRY_EPSILON) {
					expanded_shape_ = std::make_shared<BoxShape>(new_dims.x(), new_dims.y(), new_dims.z());
					expanded_shape_valid_ = true;
					LOG_DEBUG("外扩长方体: 原尺寸 ({:.2f},{:.2f},{:.2f}), 新尺寸 ({:.2f},{:.2f},{:.2f})",
						box_size.x(), box_size.y(), box_size.z(), new_dims.x(), new_dims.y(), new_dims.z());
				}
				else {
					LOG_WARN("外扩长方体尺寸 ({:.2f},{:.2f},{:.2f}) 无效，外扩失败。", new_dims.x(), new_dims.y(), new_dims.z());
					expanded_shape_ = nullptr;
				}
				break;
			}
			case ShapeType::CYLINDER:
			{
				auto cylinder = std::static_pointer_cast<const CylinderShape>(base_shape);
				double new_radius = cylinder->getRadius() + margin;
				double new_height = cylinder->getHeight() + 2 * margin; // 上下各增加margin
				if (new_radius > Constants::GEOMETRY_EPSILON && new_height > Constants::GEOMETRY_EPSILON) {
					expanded_shape_ = std::make_shared<CylinderShape>(new_radius, new_height);
					expanded_shape_valid_ = true;
					LOG_DEBUG("外扩圆柱体: 原半径 {:.2f}, 原高度 {:.2f}, 新半径 {:.2f}, 新高度 {:.2f}",
						cylinder->getRadius(), cylinder->getHeight(), new_radius, new_height);
				}
				else {
					LOG_WARN("外扩圆柱体尺寸 (半径: {:.2f}, 高度: {:.2f}) 无效，外扩失败。", new_radius, new_height);
					expanded_shape_ = nullptr;
				}
				break;
			}
			case ShapeType::POLYGON: {
				LOG_DEBUG("[Zone] 为多边形形状创建外扩版本，外扩距离: {:.2f}m", expanded_margin_);
				auto polygon = std::static_pointer_cast<const PolygonShape>(base_shape);

				// 获取原始顶点
				const auto& original_vertices = polygon->getVertices();
				if (original_vertices.size() < 3) {
					LOG_WARN("[Zone] 多边形顶点数({})不足，无法进行外扩", original_vertices.size());
					expanded_shape_ = base_shape;
					expanded_shape_valid_ = false;
					break;
				}

				// 计算多边形的外扩
				std::vector<fcl::Vector3d> expanded_vertices;
				try {
					// 对于2D多边形，使用平面外扩算法
					if (!is_3d_) {
						expanded_vertices = expandPolygon2D(original_vertices, expanded_margin_);
					} else {
						// 对于3D多边形，使用法向量外扩
						expanded_vertices = expandPolygon3D(original_vertices, expanded_margin_);
					}

					if (expanded_vertices.size() >= 3) {
						expanded_shape_ = ShapeFactory::createPolygon(expanded_vertices);
						expanded_shape_valid_ = (expanded_shape_ != nullptr);
						LOG_DEBUG("[Zone] 多边形外扩成功，原始顶点数: {}，外扩后顶点数: {}",
							original_vertices.size(), expanded_vertices.size());
					} else {
						LOG_WARN("[Zone] 多边形外扩后顶点数不足，使用原始形状");
						expanded_shape_ = base_shape;
						expanded_shape_valid_ = false;
					}
				} catch (const std::exception& e) {
					LOG_ERROR("[Zone] 多边形外扩失败: {}，使用原始形状", e.what());
					expanded_shape_ = base_shape;
					expanded_shape_valid_ = false;
				}
				break;
			}
			case ShapeType::PLANE: {
				LOG_DEBUG("[Zone] 平面形状不支持外扩，使用原始形状");
				expanded_shape_ = base_shape;
				expanded_shape_valid_ = true; // 平面本身就是无限的，不需要外扩
				break;
			}
			default:
				LOG_WARN("[Zone] 不支持对形状类型 {} 进行外扩，将使用原始形状", NSUtils::enumToString(base_shape->getType()));
				expanded_shape_ = base_shape;
				expanded_shape_valid_ = false; // 明确表示外扩未按预期进行
				break;
			}

			if (expanded_shape_valid_) {
				LOG_INFO("Zone [{}] 外扩形状已成功更新。", getId());
			}
			else if (expanded_shape_) {
				LOG_WARN("Zone [{}] 外扩形状更新完成，但标记为无效或使用了基础形状。", getId());
			}
			else {
				LOG_ERROR("Zone [{}] 更新外扩形状后，expanded_shape_ 为空。", getId());
			}
		}

		bool Zone::hasValidExpandedShape() const {
			return expanded_shape_valid_ && expanded_shape_ != nullptr;
		}

		double Zone::getExpandedMargin() const {
			return expanded_margin_;
		}

		bool Zone::isInsideExpanded(const EcefPoint& point, double tolerance) const {
			LOG_TRACE("Zone [{} ID:{}] (类型: {}): 调用 isInsideExpanded(point, tolerance={:.3f})",
				getName(), getId(), NSUtils::enumToString(getType()), tolerance);

			if (!hasValidExpandedShape()) {
				LOG_WARN("  Zone::isInsideExpanded: 无有效外扩形状，将使用基础形状进行检查。", getId());
				return this->isInside(point, tolerance); // 回退到基础形状检查
			}

			// 获取Zone的ECEF位置进行几何运算
			WGS84Point zone_wgs84 = getWGS84Position();
			EcefPoint zone_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(zone_wgs84);
			const Orientation& current_orn = getOrientation();

			if (!std::isfinite(zone_ecef.x()) || !std::isfinite(zone_ecef.y()) || !std::isfinite(zone_ecef.z())) {
				LOG_WARN("  Zone::isInsideExpanded 检查失败: 区域 [{}] 当前ECEF位置无效。", getId());
				return false;
			}

			// 特殊处理 2D 平面多边形区域 (如果外扩形状也是多边形)
			if (expanded_shape_->getType() == ShapeType::POLYGON && !is_3d_) {
				LOG_TRACE("  Zone::isInsideExpanded [{}]: 执行 2D 平面多边形检查 (外扩)...", getId());
				const auto* poly_shape = static_cast<const PolygonShape*>(expanded_shape_.get());
				std::vector<EcefPoint> world_vertices;
				world_vertices.reserve(poly_shape->getVertices().size());
				for (const auto& local_v : poly_shape->getVertices()) {
					Vector3D local_v_vec(local_v.x(), local_v.y(), local_v.z());
					Vector3D world_vertex_vec = zone_ecef.toVector3D() + current_orn * local_v_vec;
					EcefPoint world_vertex(world_vertex_vec.x(), world_vertex_vec.y(), world_vertex_vec.z());
					world_vertices.push_back(world_vertex);
				}
				bool inside_poly = NSUtils::GeometryManager::isPointInPolygonECEF(point, world_vertices, tolerance);
				LOG_TRACE("    GeometryManager::isPointInPolygonECEF (外扩) 结果: {}", inside_poly);
				return inside_poly;
			}

			// 3D 区域碰撞检测 (使用外扩形状)
			LOG_TRACE("  Zone::isInsideExpanded [{}]: 执行 3D 碰撞检查 (外扩)，点 ({:.1f},{:.1f},{:.1f}) ...",
				getId(), point.x(), point.y(), point.z());
			// bool inside = expanded_shape_->isInside(point, current_pos, current_orn, tolerance); // 错误行: IShape 没有 isInside
			// 简化实现：直接使用外扩形状的几何检测
			// 将ECEF点转换到Zone的局部坐标系（在ECEF坐标系中计算）
			Vector3D local_point = current_orn.inverse() * (point - zone_ecef);

			// 对于不同形状类型进行点包含检测
			switch (expanded_shape_->getType()) {
				case ShapeType::SPHERE: {
					auto sphere = std::static_pointer_cast<const SphereShape>(expanded_shape_);
					bool inside = local_point.norm() <= (sphere->getRadius() + tolerance);
					LOG_TRACE("Zone::isInsideExpanded [{}] 外扩球体检测结果: hasCollision={}", getId(), inside);
					return inside;
				}
				case ShapeType::BOX: {
					auto box = std::static_pointer_cast<const BoxShape>(expanded_shape_);
					fcl::Vector3d box_size = box->getSize();
					Vector3D half_dims(box_size.x() * 0.5 + tolerance, box_size.y() * 0.5 + tolerance, box_size.z() * 0.5 + tolerance);
					bool inside = (local_point.array().abs() <= half_dims.array()).all();
					LOG_TRACE("Zone::isInsideExpanded [{}] 外扩盒子检测结果: hasCollision={}", getId(), inside);
					return inside;
				}
				case ShapeType::CYLINDER: {
					auto cylinder = std::static_pointer_cast<const CylinderShape>(expanded_shape_);
					double radius = cylinder->getRadius() + tolerance;
					double half_height = cylinder->getHeight() * 0.5 + tolerance;
					double xy_distance = std::sqrt(local_point.x() * local_point.x() + local_point.y() * local_point.y());
					bool inside = (xy_distance <= radius) && (std::abs(local_point.z()) <= half_height);
					LOG_TRACE("Zone::isInsideExpanded [{}] 外扩圆柱体检测结果: hasCollision={}", getId(), inside);
					return inside;
				}
				default:
					LOG_WARN("Zone::isInsideExpanded [{}] 不支持的外扩形状类型: {}", getId(), NSUtils::enumToString(expanded_shape_->getType()));
					return false;
			}
		}

		bool Zone::intersectsExpanded(const EcefPoint& p1, const EcefPoint& p2, double tolerance) const {
			LOG_TRACE("Zone [{} ID:{}] (类型: {}): 调用 intersectsExpanded(p1, p2, tolerance={:.3f})",
				getName(), getId(), NSUtils::enumToString(getType()), tolerance);

			if (!hasValidExpandedShape()) {
				LOG_WARN("  Zone::intersectsExpanded: 无有效外扩形状，将使用基础形状进行检查。", getId());
				return this->intersects(p1, p2, tolerance); // 回退到基础形状检查
			}

			// 获取Zone的ECEF位置进行几何运算
			WGS84Point zone_wgs84 = getWGS84Position();
			EcefPoint zone_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(zone_wgs84);
			const Orientation& current_orn = getOrientation();

			if (!std::isfinite(zone_ecef.x()) || !std::isfinite(zone_ecef.y()) || !std::isfinite(zone_ecef.z())) {
				LOG_WARN("  Zone::intersectsExpanded 检查失败: 区域 [{}] 当前ECEF位置无效。", getId());
				return false;
			}

			// 特殊处理 2D 平面多边形区域 (如果外扩形状也是多边形)
			if (expanded_shape_->getType() == ShapeType::POLYGON && !is_3d_) {
				LOG_TRACE("  Zone::intersectsExpanded [{}]: 执行 2D 平面多边形检查 (外扩)...", getId());
				const auto* poly_shape = static_cast<const PolygonShape*>(expanded_shape_.get());
				std::vector<EcefPoint> world_vertices;
				world_vertices.reserve(poly_shape->getVertices().size());
				for (const auto& local_v : poly_shape->getVertices()) {
					Vector3D local_v_vec(local_v.x(), local_v.y(), local_v.z());
					Vector3D world_vertex_vec = zone_ecef.toVector3D() + current_orn * local_v_vec;
					EcefPoint world_vertex(world_vertex_vec.x(), world_vertex_vec.y(), world_vertex_vec.z());
					world_vertices.push_back(world_vertex);
				}
				if (NSUtils::GeometryManager::isPointInPolygonECEF(p1, world_vertices, tolerance) 
					|| NSUtils::GeometryManager::isPointInPolygonECEF(p2, world_vertices, tolerance)) {
					LOG_TRACE("    端点在 2D 外扩区域内，判定相交。");
					return true;
				}
				for (size_t i = 0; i < world_vertices.size(); ++i) {
					const auto& edge_p1 = world_vertices[i];
					const auto& edge_p2 = world_vertices[(i + 1) % world_vertices.size()];
					if (NSUtils::GeometryManager::isSegmentsIntersect2D(p1, p2, edge_p1, edge_p2, true, tolerance)) {
						LOG_TRACE("    线段与 2D 外扩区域边界相交。");
						return true;
					}
				}
				LOG_TRACE("    线段与 2D 外扩区域不相交。");
				return false;
			}

			// 3D 区域碰撞检测 (使用外扩形状)
			LOG_TRACE("  Zone::intersectsExpanded [{}]: 执行 3D 碰撞检查 (外扩) ...", getId());
			// bool intersects = expanded_shape_->intersectsSegment(p1, p2, current_pos, current_orn, tolerance); // 错误行
			// 简化实现：使用几何算法检测线段与外扩形状的相交
			// 将线段转换到Zone的局部坐标系（在ECEF坐标系中计算）
			Vector3D local_p1 = current_orn.inverse() * (p1 - zone_ecef);
			Vector3D local_p2 = current_orn.inverse() * (p2 - zone_ecef);

			// 对于不同形状类型进行线段相交检测
			switch (expanded_shape_->getType()) {
				case ShapeType::SPHERE: {
					auto sphere = std::static_pointer_cast<const SphereShape>(expanded_shape_);
					// 计算线段到球心的最短距离
					Vector3D line_dir = local_p2 - local_p1;
					double line_length = line_dir.norm();
					if (line_length < Constants::GEOMETRY_EPSILON) {
						// 退化为点检测
						bool intersects = local_p1.norm() <= (sphere->getRadius() + tolerance);
						LOG_TRACE("Zone::intersectsExpanded [{}] 外扩球体点检测结果: hasCollision={}", getId(), intersects);
						return intersects;
					}
					line_dir /= line_length;

					// 计算线段上最接近球心的点
					double t = std::max(0.0, std::min(line_length, -local_p1.dot(line_dir)));
					Vector3D closest_point = local_p1 + t * line_dir;
					bool intersects = closest_point.norm() <= (sphere->getRadius() + tolerance);
					LOG_TRACE("Zone::intersectsExpanded [{}] 外扩球体线段检测结果: hasCollision={}", getId(), intersects);
					return intersects;
				}
				case ShapeType::BOX: {
					auto box = std::static_pointer_cast<const BoxShape>(expanded_shape_);
					fcl::Vector3d box_size = box->getSize();
					Vector3D half_dims(box_size.x() * 0.5 + tolerance, box_size.y() * 0.5 + tolerance, box_size.z() * 0.5 + tolerance);
					// 简化实现：检查线段端点是否在盒子内，或者盒子是否与线段相交
					bool p1_inside = (local_p1.array().abs() <= half_dims.array()).all();
					bool p2_inside = (local_p2.array().abs() <= half_dims.array()).all();
					bool intersects = p1_inside || p2_inside;
					LOG_TRACE("Zone::intersectsExpanded [{}] 外扩盒子线段检测结果: hasCollision={}", getId(), intersects);
					return intersects;
				}
				case ShapeType::CYLINDER: {
					auto cylinder = std::static_pointer_cast<const CylinderShape>(expanded_shape_);
					double radius = cylinder->getRadius() + tolerance;
					double half_height = cylinder->getHeight() * 0.5 + tolerance;

					// 简化实现：检查线段端点是否在圆柱体内
					auto checkPointInCylinder = [&](const Vector3D& p) {
						double xy_distance = std::sqrt(p.x() * p.x() + p.y() * p.y());
						return (xy_distance <= radius) && (std::abs(p.z()) <= half_height);
					};

					bool p1_inside = checkPointInCylinder(local_p1);
					bool p2_inside = checkPointInCylinder(local_p2);
					bool intersects = p1_inside || p2_inside;
					LOG_TRACE("Zone::intersectsExpanded [{}] 外扩圆柱体线段检测结果: hasCollision={}", getId(), intersects);
					return intersects;
				}
				default:
					LOG_WARN("Zone::intersectsExpanded [{}] 不支持的外扩形状类型: {}", getId(), NSUtils::enumToString(expanded_shape_->getType()));
					return false;
			}
		}


		// --- 子区域管理 (线程安全) ---
		/** @brief 添加一个子区域。 */
		void Zone::addSubZone(std::shared_ptr<Zone> sub_zone) {
			if (!sub_zone || sub_zone->getId() == getId()) return;
			std::unique_lock lock(sub_zones_mutex_);
			auto it = std::find_if(sub_zones_.begin(), sub_zones_.end(),
				[&](const ZonePtr& z) { return z->getId() == sub_zone->getId(); });
			if (it == sub_zones_.end()) sub_zones_.push_back(std::move(sub_zone));
		}

		/** @brief 移除指定 ID 的子区域。 */
		bool Zone::removeSubZone(const ObjectID& sub_zone_id) {
			if (!NSUtils::isValidObjectID(sub_zone_id)) return false;
			std::unique_lock lock(sub_zones_mutex_);
			auto it = std::remove_if(sub_zones_.begin(), sub_zones_.end(),
				[&](const ZonePtr& z) { return z->getId() == sub_zone_id; });
			if (it != sub_zones_.end()) {
				sub_zones_.erase(it, sub_zones_.end());
				LOG_DEBUG("区域 [{}]: 已移除子区域 [{}]", getId(), sub_zone_id);
				return true;
			}
			return false;
		}

		/** @brief 获取所有子区域的列表 (返回副本以保证线程安全)。 */
		std::vector<std::shared_ptr<Zone>> Zone::getSubZones() const {
			std::shared_lock lock(sub_zones_mutex_);
			return sub_zones_;
		}

		// === 多边形外扩辅助方法实现 ===

		std::vector<fcl::Vector3d> Zone::expandPolygon2D(const std::vector<fcl::Vector3d>& vertices, double margin) const {
			if (vertices.size() < 3 || margin <= 0.0) {
				LOG_WARN("[Zone] 2D多边形外扩参数无效：顶点数={}, 外扩距离={:.3f}", vertices.size(), margin);
				return vertices;
			}

			std::vector<fcl::Vector3d> expanded_vertices;
			expanded_vertices.reserve(vertices.size());

			// 对于2D多边形，我们在XY平面上进行外扩
			for (size_t i = 0; i < vertices.size(); ++i) {
				size_t prev_idx = (i == 0) ? vertices.size() - 1 : i - 1;
				size_t next_idx = (i + 1) % vertices.size();

				const auto& prev_vertex = vertices[prev_idx];
				const auto& curr_vertex = vertices[i];
				const auto& next_vertex = vertices[next_idx];

				// 计算前一条边和后一条边的方向向量（在XY平面上）
				fcl::Vector3d edge1_dir(curr_vertex.x() - prev_vertex.x(), curr_vertex.y() - prev_vertex.y(), 0.0);
				fcl::Vector3d edge2_dir(next_vertex.x() - curr_vertex.x(), next_vertex.y() - curr_vertex.y(), 0.0);

				// 归一化
				double edge1_len = edge1_dir.norm();
				double edge2_len = edge2_dir.norm();
				if (edge1_len < Constants::GEOMETRY_EPSILON || edge2_len < Constants::GEOMETRY_EPSILON) {
					// 退化边，跳过
					continue;
				}
				edge1_dir /= edge1_len;
				edge2_dir /= edge2_len;

				// 计算法向量（向外）
				fcl::Vector3d normal1(-edge1_dir.y(), edge1_dir.x(), 0.0);
				fcl::Vector3d normal2(-edge2_dir.y(), edge2_dir.x(), 0.0);

				// 计算角平分线方向
				fcl::Vector3d bisector = (normal1 + normal2).normalized();

				// 计算外扩距离（考虑角度）
				double dot_product = normal1.dot(normal2);
				double angle_factor = 1.0;
				if (std::abs(dot_product) < 0.999) { // 避免除零
					angle_factor = 1.0 / std::sqrt((1.0 + dot_product) * 0.5);
				}

				// 计算外扩后的顶点
				fcl::Vector3d expanded_vertex = curr_vertex + bisector * margin * angle_factor;
				expanded_vertices.push_back(expanded_vertex);
			}

			LOG_DEBUG("[Zone] 2D多边形外扩完成：原始顶点数={}, 外扩后顶点数={}", vertices.size(), expanded_vertices.size());
			return expanded_vertices;
		}

		std::vector<fcl::Vector3d> Zone::expandPolygon3D(const std::vector<fcl::Vector3d>& vertices, double margin) const {
			if (vertices.size() < 3 || margin <= 0.0) {
				LOG_WARN("[Zone] 3D多边形外扩参数无效：顶点数={}, 外扩距离={:.3f}", vertices.size(), margin);
				return vertices;
			}

			// 对于3D多边形，我们使用简化的方法：
			// 1. 计算多边形的法向量
			// 2. 沿法向量方向外扩每个顶点

			// 计算多边形法向量（使用前三个非共线顶点）
			fcl::Vector3d normal(0.0, 0.0, 0.0);
			bool found_normal = false;

			for (size_t i = 0; i < vertices.size() - 2 && !found_normal; ++i) {
				fcl::Vector3d v1 = vertices[i + 1] - vertices[i];
				fcl::Vector3d v2 = vertices[i + 2] - vertices[i];
				fcl::Vector3d cross = v1.cross(v2);

				if (cross.norm() > Constants::GEOMETRY_EPSILON) {
					normal = cross.normalized();
					found_normal = true;
				}
			}

			if (!found_normal) {
				LOG_WARN("[Zone] 无法计算3D多边形的法向量，使用原始顶点");
				return vertices;
			}

			// 沿法向量方向外扩每个顶点
			std::vector<fcl::Vector3d> expanded_vertices;
			expanded_vertices.reserve(vertices.size());

			for (const auto& vertex : vertices) {
				fcl::Vector3d expanded_vertex = vertex + normal * margin;
				expanded_vertices.push_back(expanded_vertex);
			}

			LOG_DEBUG("[Zone] 3D多边形外扩完成：原始顶点数={}, 外扩后顶点数={}", vertices.size(), expanded_vertices.size());
			return expanded_vertices;
		}

	} // namespace NSEnvironment
} // namespace NSDrones