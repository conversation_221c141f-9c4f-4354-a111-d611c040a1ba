﻿  trajectory_optimizer.cpp
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(953): error C3867: “NSDrones::NSCore::EcefPoint::x”: 非标准语法；请使用 "&" 来创建指向成员的指针
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(953,16): error C2672: “isfinite”: 未找到匹配的重载函数
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\ucrt\corecrt_math.h(405,32): message : 可能是“bool isfinite(_Ty) noexcept”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(953): error C3867: “NSDrones::NSCore::EcefPoint::y”: 非标准语法；请使用 "&" 来创建指向成员的指针
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(953,46): error C2672: “isfinite”: 未找到匹配的重载函数
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\ucrt\corecrt_math.h(405,32): message : 可能是“bool isfinite(_Ty) noexcept”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(953): error C3867: “NSDrones::NSCore::EcefPoint::z”: 非标准语法；请使用 "&" 来创建指向成员的指针
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(953,76): error C2672: “isfinite”: 未找到匹配的重载函数
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\ucrt\corecrt_math.h(405,32): message : 可能是“bool isfinite(_Ty) noexcept”
  config.cpp
E:\source\dronesplanning\include\planning/task_planners/followpath_taskplanner.h(41,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\include\planning/task_planners/loiterpoint_taskplanner.h(120,36): error C2535: “std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>> NSDrones::NSPlanning::LoiterPointTaskPlanner::generateLoiterWaypoints(const NSDrones::NSCore::WGS84Point &,double,int,bool) const”: 已经定义或声明成员函数
E:\source\dronesplanning\include\planning/task_planners/loiterpoint_taskplanner.h(106,36): message : 参见“NSDrones::NSPlanning::LoiterPointTaskPlanner::generateLoiterWaypoints”的声明
E:\source\dronesplanning\include\planning/task_planners/loiterpoint_taskplanner.h(64,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\include\planning/task_planners/scanarea_taskplanner.h(55,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\include\planning/task_planners/surveymultipoints_taskplanner.h(57,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\include\planning/task_planners/surveysphere_taskplanner.h(38,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\include\planning/task_planners/surveycylinder_taskplanner.h(38,12): error C2065: “task_type”: 未声明的标识符
  zone.cpp
E:\source\dronesplanning\src\environment\entities\zone.cpp(533,25): error C2653: “ShapeFactory”: 不是类或命名空间名称
E:\source\dronesplanning\src\environment\entities\zone.cpp(533,39): error C3861: “createPolygon”: 找不到标识符
  environment.cpp
  main.cpp
E:\source\dronesplanning\include\planning/task_planners/followpath_taskplanner.h(41,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\include\planning/task_planners/loiterpoint_taskplanner.h(120,36): error C2535: “std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>> NSDrones::NSPlanning::LoiterPointTaskPlanner::generateLoiterWaypoints(const NSDrones::NSCore::WGS84Point &,double,int,bool) const”: 已经定义或声明成员函数
E:\source\dronesplanning\include\planning/task_planners/loiterpoint_taskplanner.h(106,36): message : 参见“NSDrones::NSPlanning::LoiterPointTaskPlanner::generateLoiterWaypoints”的声明
E:\source\dronesplanning\include\planning/task_planners/loiterpoint_taskplanner.h(64,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\include\planning/task_planners/scanarea_taskplanner.h(55,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\include\planning/task_planners/surveymultipoints_taskplanner.h(57,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\include\planning/task_planners/surveysphere_taskplanner.h(38,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\include\planning/task_planners/surveycylinder_taskplanner.h(38,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\src\main.cpp(893,24): error C2039: "wasSuccessful": 不是 "NSDrones::NSPlanning::PlanningResult" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(583,10): message : 参见“NSDrones::NSPlanning::PlanningResult”的声明
E:\source\dronesplanning\src\main.cpp(895,46): error C2039: "getAllRoutes": 不是 "NSDrones::NSPlanning::PlanningResult" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(583,10): message : 参见“NSDrones::NSPlanning::PlanningResult”的声明
E:\source\dronesplanning\src\main.cpp(895,28): error C2530: “all_routes”: 必须初始化引用
E:\source\dronesplanning\src\main.cpp(896,5): error C3536: “all_routes”: 初始化之前无法使用
E:\source\dronesplanning\src\main.cpp(897,35): error C2672: “begin”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(609,23): message : 可能是“const _Ty *std::begin(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\main.cpp(897,35): message : “const _Ty *std::begin(const std::valarray<_Ty> &)”: 无法从“int”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(604,17): message : 或    “_Ty *std::begin(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\main.cpp(897,35): message : “_Ty *std::begin(std::valarray<_Ty> &)”: 无法从“int”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1589,27): message : 或    “_Ty *std::begin(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\main.cpp(897,35): message : “_Ty *std::begin(_Ty (&)[_Size]) noexcept”: 无法从“int”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1571,30): message : 或    “unknown-type std::begin(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\main.cpp(897,35): message : 未能使函数模板“unknown-type std::begin(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\main.cpp(897,35): message : 用下列模板参数:
E:\source\dronesplanning\src\main.cpp(897,35): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1565,30): message : 或    “unknown-type std::begin(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\main.cpp(897,35): message : 未能使函数模板“unknown-type std::begin(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\main.cpp(897,35): message : 用下列模板参数:
E:\source\dronesplanning\src\main.cpp(897,35): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(55,35): message : 或    “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\main.cpp(897,35): message : “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”: 无法从“int”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\main.cpp(897,35): error C2672: “end”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(619,23): message : 可能是“const _Ty *std::end(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\main.cpp(897,35): message : “const _Ty *std::end(const std::valarray<_Ty> &)”: 无法从“int”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(614,17): message : 或    “_Ty *std::end(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\main.cpp(897,35): message : “_Ty *std::end(std::valarray<_Ty> &)”: 无法从“int”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1594,27): message : 或    “_Ty *std::end(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\main.cpp(897,35): message : “_Ty *std::end(_Ty (&)[_Size]) noexcept”: 无法从“int”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1583,30): message : 或    “unknown-type std::end(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\main.cpp(897,35): message : 未能使函数模板“unknown-type std::end(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\main.cpp(897,35): message : 用下列模板参数:
E:\source\dronesplanning\src\main.cpp(897,35): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1577,30): message : 或    “unknown-type std::end(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\main.cpp(897,35): message : 未能使函数模板“unknown-type std::end(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\main.cpp(897,35): message : 用下列模板参数:
E:\source\dronesplanning\src\main.cpp(897,35): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(60,35): message : 或    “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\main.cpp(897,35): message : “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”: 无法从“int”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\main.cpp(897,47): error C3536: “<begin>$L3”: 初始化之前无法使用
E:\source\dronesplanning\src\main.cpp(897,47): error C3536: “<end>$L3”: 初始化之前无法使用
E:\source\dronesplanning\src\main.cpp(897,45): error C2100: 非法的间接寻址
E:\source\dronesplanning\src\main.cpp(898,30): error C2530: “route_id”: 必须初始化引用
E:\source\dronesplanning\src\main.cpp(899,24): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\src\main.cpp(899,24): error C2143: 语法错误: 缺少“;”(在“&”的前面)
E:\source\dronesplanning\src\main.cpp(899,26): error C2065: “route”: 未声明的标识符
E:\source\dronesplanning\src\main.cpp(901,6): error C2065: “route”: 未声明的标识符
E:\source\dronesplanning\src\main.cpp(902,6): error C2065: “route”: 未声明的标识符
E:\source\dronesplanning\src\main.cpp(903,6): error C2065: “route”: 未声明的标识符
E:\source\dronesplanning\src\main.cpp(904,6): error C2065: “route”: 未声明的标识符
E:\source\dronesplanning\src\main.cpp(906,31): error C2065: “route”: 未声明的标识符
E:\source\dronesplanning\src\main.cpp(906,6): error C2530: “point”: 必须初始化引用
E:\source\dronesplanning\src\main.cpp(906,6): error C3531: “point”: 类型包含“auto”的符号必须具有初始值设定项
E:\source\dronesplanning\src\main.cpp(906,29): error C2143: 语法错误: 缺少“;”(在“:”的前面)
E:\source\dronesplanning\src\main.cpp(906,51): error C2143: 语法错误: 缺少“;”(在“)”的前面)
E:\source\dronesplanning\src\main.cpp(920,43): error C2039: "getWarnings": 不是 "NSDrones::NSPlanning::PlanningResult" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(583,10): message : 参见“NSDrones::NSPlanning::PlanningResult”的声明
E:\source\dronesplanning\src\main.cpp(920,25): error C2530: “warnings”: 必须初始化引用
E:\source\dronesplanning\src\main.cpp(921): error C3536: “warnings”: 初始化之前无法使用
E:\source\dronesplanning\src\main.cpp(923,32): error C2672: “begin”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(609,23): message : 可能是“const _Ty *std::begin(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\main.cpp(923,32): message : “const _Ty *std::begin(const std::valarray<_Ty> &)”: 无法从“int”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(604,17): message : 或    “_Ty *std::begin(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\main.cpp(923,32): message : “_Ty *std::begin(std::valarray<_Ty> &)”: 无法从“int”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1589,27): message : 或    “_Ty *std::begin(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\main.cpp(923,32): message : “_Ty *std::begin(_Ty (&)[_Size]) noexcept”: 无法从“int”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1571,30): message : 或    “unknown-type std::begin(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\main.cpp(923,32): message : 未能使函数模板“unknown-type std::begin(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\main.cpp(923,32): message : 用下列模板参数:
E:\source\dronesplanning\src\main.cpp(923,32): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1565,30): message : 或    “unknown-type std::begin(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\main.cpp(923,32): message : 未能使函数模板“unknown-type std::begin(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\main.cpp(923,32): message : 用下列模板参数:
E:\source\dronesplanning\src\main.cpp(923,32): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(55,35): message : 或    “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\main.cpp(923,32): message : “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”: 无法从“int”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\main.cpp(923,32): error C2672: “end”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(619,23): message : 可能是“const _Ty *std::end(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\main.cpp(923,32): message : “const _Ty *std::end(const std::valarray<_Ty> &)”: 无法从“int”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(614,17): message : 或    “_Ty *std::end(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\main.cpp(923,32): message : “_Ty *std::end(std::valarray<_Ty> &)”: 无法从“int”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1594,27): message : 或    “_Ty *std::end(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\main.cpp(923,32): message : “_Ty *std::end(_Ty (&)[_Size]) noexcept”: 无法从“int”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1583,30): message : 或    “unknown-type std::end(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\main.cpp(923,32): message : 未能使函数模板“unknown-type std::end(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\main.cpp(923,32): message : 用下列模板参数:
E:\source\dronesplanning\src\main.cpp(923,32): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1577,30): message : 或    “unknown-type std::end(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\main.cpp(923,32): message : 未能使函数模板“unknown-type std::end(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\main.cpp(923,32): message : 用下列模板参数:
E:\source\dronesplanning\src\main.cpp(923,32): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(60,35): message : 或    “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\main.cpp(923,32): message : “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”: 无法从“int”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\main.cpp(923,42): error C3536: “<begin>$L4”: 初始化之前无法使用
E:\source\dronesplanning\src\main.cpp(923,42): error C3536: “<end>$L4”: 初始化之前无法使用
E:\source\dronesplanning\src\main.cpp(923,40): error C2100: 非法的间接寻址
  control_point.cpp
  itask_planner.cpp
E:\source\dronesplanning\src\planning\itask_planner.cpp(74,22): error C2039: "validateTaskBasics": 不是 "NSDrones::NSPlanning::ITaskPlanner" 的成员
E:\source\dronesplanning\include\planning/itask_planner.h(42,9): message : 参见“NSDrones::NSPlanning::ITaskPlanner”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(77,44): error C2270: “validateTaskBasics”: 非成员函数上不允许修饰符
E:\source\dronesplanning\src\planning\itask_planner.cpp(82,12): error C2039: "setStatus": 不是 "NSDrones::NSPlanning::PlanningResult" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(583,10): message : 参见“NSDrones::NSPlanning::PlanningResult”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(90,12): error C2039: "setStatus": 不是 "NSDrones::NSPlanning::PlanningResult" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(583,10): message : 参见“NSDrones::NSPlanning::PlanningResult”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(96,23): error C3861: “getEnvironment”: 找不到标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(97,24): error C3861: “getPathPlanner”: 找不到标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(100,12): error C2039: "setStatus": 不是 "NSDrones::NSPlanning::PlanningResult" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(583,10): message : 参见“NSDrones::NSPlanning::PlanningResult”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(109,22): error C2039: "validateUavState": 不是 "NSDrones::NSPlanning::ITaskPlanner" 的成员
E:\source\dronesplanning\include\planning/itask_planner.h(42,9): message : 参见“NSDrones::NSPlanning::ITaskPlanner”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(112,42): error C2270: “validateUavState”: 非成员函数上不允许修饰符
E:\source\dronesplanning\src\planning\itask_planner.cpp(124,12): error C2039: "addWarning": 不是 "NSDrones::NSPlanning::PlanningResult" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(583,10): message : 参见“NSDrones::NSPlanning::PlanningResult”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(135,12): error C2039: "addWarning": 不是 "NSDrones::NSPlanning::PlanningResult" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(583,10): message : 参见“NSDrones::NSPlanning::PlanningResult”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(146,48): error C2511: “std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> NSDrones::NSPlanning::ITaskPlanner::planPathToTarget(const NSDrones::NSUav::UavState &,const NSDrones::NSCore::WGS84Point &,const NSDrones::NSUav::UavPtr &,const NSDrones::NSMission::Task &,NSDrones::NSPlanning::PlanningResult &) const”:“NSDrones::NSPlanning::ITaskPlanner”中没有找到重载的成员函数
E:\source\dronesplanning\include\planning/itask_planner.h(42,9): message : 参见“NSDrones::NSPlanning::ITaskPlanner”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(155,24): error C2352: “NSDrones::NSPlanning::ITaskPlanner::getPathPlanner”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\src\planning\itask_planner.cpp(45,33): message : 参见“NSDrones::NSPlanning::ITaskPlanner::getPathPlanner”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(156,21): error C3536: “path_planner”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(184,12): error C2039: "addWarning": 不是 "NSDrones::NSPlanning::PlanningResult" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(583,10): message : 参见“NSDrones::NSPlanning::PlanningResult”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(198,52): error C2039: "getAbsolutePosition": 不是 "NSDrones::NSPlanning::ITaskPlanner" 的成员
E:\source\dronesplanning\include\planning/itask_planner.h(42,9): message : 参见“NSDrones::NSPlanning::ITaskPlanner”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(201,3): error C2270: “getAbsolutePosition”: 非成员函数上不允许修饰符
E:\source\dronesplanning\src\planning\itask_planner.cpp(202,23): error C3861: “getEnvironment”: 找不到标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(220): error C3536: “adjusted_height”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(221,28): error C2100: 非法的间接寻址
E:\source\dronesplanning\src\planning\itask_planner.cpp(240,35): error C2039: "generateLinearTrajectorySegment": 不是 "NSDrones::NSPlanning::ITaskPlanner" 的成员
E:\source\dronesplanning\include\planning/itask_planner.h(42,9): message : 参见“NSDrones::NSPlanning::ITaskPlanner”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(246,3): error C2270: “generateLinearTrajectorySegment”: 非成员函数上不允许修饰符
E:\source\dronesplanning\src\planning\itask_planner.cpp(305,22): error C2039: "checkTrajectorySegmentWarnings": 不是 "NSDrones::NSPlanning::ITaskPlanner" 的成员
E:\source\dronesplanning\include\planning/itask_planner.h(42,9): message : 参见“NSDrones::NSPlanning::ITaskPlanner”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(309,3): error C2270: “checkTrajectorySegmentWarnings”: 非成员函数上不允许修饰符
E:\source\dronesplanning\src\planning\itask_planner.cpp(310,23): error C3861: “getEnvironment”: 找不到标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(315,22): error C2530: “zones”: 必须初始化引用
E:\source\dronesplanning\src\planning\itask_planner.cpp(316): error C3536: “zones”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(426,16): error C2039: "addWarning": 不是 "NSDrones::NSPlanning::PlanningResult" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(583,10): message : 参见“NSDrones::NSPlanning::PlanningResult”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(545,7): error C2679: 二元“=”: 没有找到接受“_Ty”类型的右操作数的运算符(或没有可接受的转换)
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryPoint
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(1566,26): message : 可能是“std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>> &std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>>::operator =(std::initializer_list<_Ty>)”
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryPoint
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(1545,26): message : 或    “std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>> &std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>>::operator =(const std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>> &)”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(798,26): message : 或    “std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>> &std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>>::operator =(std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>> &&) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(545,7): message : 尝试匹配参数列表“(NSDrones::NSPlanning::TrajectorySegment, _Ty)”时
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryPoint
          ]
E:\source\dronesplanning\src\planning\itask_planner.cpp(558,26): error C2039: "push_back": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(578,22): error C2039: "isZoneConstraintSatisfied": 不是 "NSDrones::NSPlanning::ITaskPlanner" 的成员
E:\source\dronesplanning\include\planning/itask_planner.h(42,9): message : 参见“NSDrones::NSPlanning::ITaskPlanner”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(579,3): error C2270: “isZoneConstraintSatisfied”: 非成员函数上不允许修饰符
E:\source\dronesplanning\src\planning\itask_planner.cpp(642,22): error C2039: "checkSafetyConstraints": 不是 "NSDrones::NSPlanning::ITaskPlanner" 的成员
E:\source\dronesplanning\include\planning/itask_planner.h(42,9): message : 参见“NSDrones::NSPlanning::ITaskPlanner”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(643,3): error C2270: “checkSafetyConstraints”: 非成员函数上不允许修饰符
E:\source\dronesplanning\src\planning\itask_planner.cpp(644,23): error C3861: “getEnvironment”: 找不到标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(653,26): error C2530: “all_zones”: 必须初始化引用
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,43): error C3536: “all_zones”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): error C2672: “begin”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(609,23): message : 可能是“const _Ty *std::begin(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : “const _Ty *std::begin(const std::valarray<_Ty> &)”: 无法从“int”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(604,17): message : 或    “_Ty *std::begin(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : “_Ty *std::begin(std::valarray<_Ty> &)”: 无法从“int”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1589,27): message : 或    “_Ty *std::begin(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : “_Ty *std::begin(_Ty (&)[_Size]) noexcept”: 无法从“int”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1571,30): message : 或    “unknown-type std::begin(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : 未能使函数模板“unknown-type std::begin(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1565,30): message : 或    “unknown-type std::begin(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : 未能使函数模板“unknown-type std::begin(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(55,35): message : 或    “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”: 无法从“int”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): error C2672: “end”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(619,23): message : 可能是“const _Ty *std::end(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : “const _Ty *std::end(const std::valarray<_Ty> &)”: 无法从“int”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(614,17): message : 或    “_Ty *std::end(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : “_Ty *std::end(std::valarray<_Ty> &)”: 无法从“int”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1594,27): message : 或    “_Ty *std::end(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : “_Ty *std::end(_Ty (&)[_Size]) noexcept”: 无法从“int”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1583,30): message : 或    “unknown-type std::end(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : 未能使函数模板“unknown-type std::end(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1577,30): message : 或    “unknown-type std::end(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : 未能使函数模板“unknown-type std::end(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(60,35): message : 或    “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,32): message : “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”: 无法从“int”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,43): error C3536: “<begin>$L0”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,43): error C3536: “<end>$L0”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(654,41): error C2100: 非法的间接寻址
E:\source\dronesplanning\src\planning\itask_planner.cpp(688,22): error C2039: "isPathSegmentSafe": 不是 "NSDrones::NSPlanning::ITaskPlanner" 的成员
E:\source\dronesplanning\include\planning/itask_planner.h(42,9): message : 参见“NSDrones::NSPlanning::ITaskPlanner”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(689,3): error C2270: “isPathSegmentSafe”: 非成员函数上不允许修饰符
E:\source\dronesplanning\src\planning\itask_planner.cpp(693,23): error C3861: “getEnvironment”: 找不到标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(697,12): error C2039: "addWarning": 不是 "NSDrones::NSPlanning::PlanningResult" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(583,10): message : 参见“NSDrones::NSPlanning::PlanningResult”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(739,26): error C2530: “all_zones”: 必须初始化引用
E:\source\dronesplanning\src\planning\itask_planner.cpp(740): error C3536: “all_zones”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): error C2672: “begin”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(609,23): message : 可能是“const _Ty *std::begin(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : “const _Ty *std::begin(const std::valarray<_Ty> &)”: 无法从“int”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(604,17): message : 或    “_Ty *std::begin(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : “_Ty *std::begin(std::valarray<_Ty> &)”: 无法从“int”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1589,27): message : 或    “_Ty *std::begin(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : “_Ty *std::begin(_Ty (&)[_Size]) noexcept”: 无法从“int”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1571,30): message : 或    “unknown-type std::begin(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : 未能使函数模板“unknown-type std::begin(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1565,30): message : 或    “unknown-type std::begin(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : 未能使函数模板“unknown-type std::begin(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(55,35): message : 或    “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”: 无法从“int”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): error C2672: “end”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(619,23): message : 可能是“const _Ty *std::end(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : “const _Ty *std::end(const std::valarray<_Ty> &)”: 无法从“int”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(614,17): message : 或    “_Ty *std::end(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : “_Ty *std::end(std::valarray<_Ty> &)”: 无法从“int”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1594,27): message : 或    “_Ty *std::end(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : “_Ty *std::end(_Ty (&)[_Size]) noexcept”: 无法从“int”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1583,30): message : 或    “unknown-type std::end(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : 未能使函数模板“unknown-type std::end(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1577,30): message : 或    “unknown-type std::end(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : 未能使函数模板“unknown-type std::end(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(60,35): message : 或    “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,33): message : “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”: 无法从“int”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,44): error C3536: “<begin>$L0”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,44): error C3536: “<end>$L0”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(745,42): error C2100: 非法的间接寻址
E:\source\dronesplanning\src\planning\itask_planner.cpp(752,8): error C2672: “NSDrones::NSUtils::enumToString”: 未找到匹配的重载函数
E:\source\dronesplanning\include\utils/enum_utils.h(21,22): message : 可能是“std::string NSDrones::NSUtils::enumToString(EnumType)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(757,49): error C2672: “NSDrones::NSUtils::enumToString”: 未找到匹配的重载函数
E:\source\dronesplanning\include\utils/enum_utils.h(21,22): message : 可能是“std::string NSDrones::NSUtils::enumToString(EnumType)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(758,15): error C2039: "addWarning": 不是 "NSDrones::NSPlanning::PlanningResult" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(583,10): message : 参见“NSDrones::NSPlanning::PlanningResult”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(772,22): error C2039: "smoothAndTimeParameterizeECEF": 不是 "NSDrones::NSPlanning::ITaskPlanner" 的成员
E:\source\dronesplanning\include\planning/itask_planner.h(42,9): message : 参见“NSDrones::NSPlanning::ITaskPlanner”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(782,33): error C2039: "addWarning": 不是 "NSDrones::NSPlanning::PlanningResult" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(583,10): message : 参见“NSDrones::NSPlanning::PlanningResult”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(788,33): error C2039: "addWarning": 不是 "NSDrones::NSPlanning::PlanningResult" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(583,10): message : 参见“NSDrones::NSPlanning::PlanningResult”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(836,24): error C2039: "getCurrentState": 不是 "NSDrones::NSUav::Uav" 的成员
E:\source\dronesplanning\include\uav/uav.h(51,9): message : 参见“NSDrones::NSUav::Uav”的声明
  mission_planner.cpp
E:\source\dronesplanning\src\planning\mission_planner.cpp(17,10): fatal  error C1083: 无法打开包括文件: “spdlog/fmt/format.h”: No such file or directory
  planning_types.cpp
E:\source\dronesplanning\src\planning\planning_types.cpp(230,44): error C2039: "calculateDirection": 不是 "NSDrones::NSUtils::GeometryManager" 的成员
E:\source\dronesplanning\include\utils/geometry_manager.h(32,9): message : 参见“NSDrones::NSUtils::GeometryManager”的声明
E:\source\dronesplanning\src\planning\planning_types.cpp(230,62): error C3861: “calculateDirection”: 找不到标识符
  followpath_taskplanner.cpp
E:\source\dronesplanning\include\planning/task_planners/followpath_taskplanner.h(41,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(175,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(207,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(248,6): error C2039: "zone_id": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(253,29): error C2653: “AvoidanceConstraintType”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(253,54): error C2065: “ALTITUDE_SEPARATION”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(254,7): error C2039: "min_altitude": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(254,7): error C2039: "max_altitude": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(257,34): error C2653: “AvoidanceConstraintType”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(257,59): error C2065: “SPATIAL_AVOIDANCE”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(258,7): error C2039: "avoidance_radius": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
  loiterpoint_taskplanner.cpp
E:\source\dronesplanning\include\planning/task_planners/loiterpoint_taskplanner.h(120,36): error C2535: “std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>> NSDrones::NSPlanning::LoiterPointTaskPlanner::generateLoiterWaypoints(const NSDrones::NSCore::WGS84Point &,double,int,bool) const”: 已经定义或声明成员函数
E:\source\dronesplanning\include\planning/task_planners/loiterpoint_taskplanner.h(106,36): message : 参见“NSDrones::NSPlanning::LoiterPointTaskPlanner::generateLoiterWaypoints”的声明
E:\source\dronesplanning\include\planning/task_planners/loiterpoint_taskplanner.h(64,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(173,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(220,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(261,6): error C2039: "zone_id": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(266,29): error C2653: “AvoidanceConstraintType”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(266,54): error C2065: “ALTITUDE_SEPARATION”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(267,7): error C2039: "min_altitude": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(267,7): error C2039: "max_altitude": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(270,34): error C2653: “AvoidanceConstraintType”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(270,59): error C2065: “SPATIAL_AVOIDANCE”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(271,7): error C2039: "avoidance_radius": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
  scanarea_taskplanner.cpp
E:\source\dronesplanning\include\planning/task_planners/scanarea_taskplanner.h(55,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(65,49): error C2084: 函数“NSDrones::NSPlanning::SingleTaskPlanningResult NSDrones::NSPlanning::ScanAreaTaskPlanner::planSingleTask(const NSDrones::NSPlanning::SingleTaskPlanningRequest &)”已有主体
E:\source\dronesplanning\include\planning/task_planners/scanarea_taskplanner.h(54,29): message : 参见“planSingleTask”的前一个定义
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(209,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(241,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
  surveycylinder_taskplanner.cpp
E:\source\dronesplanning\include\planning/task_planners/surveycylinder_taskplanner.h(38,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(259,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(291,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
  surveymultipoints_taskplanner.cpp
E:\source\dronesplanning\include\planning/task_planners/surveymultipoints_taskplanner.h(57,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(166,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(198,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
  surveysphere_taskplanner.cpp
E:\source\dronesplanning\include\planning/task_planners/surveysphere_taskplanner.h(38,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(263,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(290,23): error C2676: 二进制“[”:“NSDrones::NSPlanning::Trajectory”不定义该运算符或到预定义运算符可接收的类型的转换
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(307,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
  fixedwing_dynamics.cpp
  uav_config.cpp
E:\source\dronesplanning\include\planning/task_planners/followpath_taskplanner.h(41,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\include\planning/task_planners/loiterpoint_taskplanner.h(120,36): error C2535: “std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>> NSDrones::NSPlanning::LoiterPointTaskPlanner::generateLoiterWaypoints(const NSDrones::NSCore::WGS84Point &,double,int,bool) const”: 已经定义或声明成员函数
E:\source\dronesplanning\include\planning/task_planners/loiterpoint_taskplanner.h(106,36): message : 参见“NSDrones::NSPlanning::LoiterPointTaskPlanner::generateLoiterWaypoints”的声明
E:\source\dronesplanning\include\planning/task_planners/loiterpoint_taskplanner.h(64,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\include\planning/task_planners/scanarea_taskplanner.h(55,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\include\planning/task_planners/surveymultipoints_taskplanner.h(57,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\include\planning/task_planners/surveysphere_taskplanner.h(38,12): error C2065: “task_type”: 未声明的标识符
E:\source\dronesplanning\include\planning/task_planners/surveycylinder_taskplanner.h(38,12): error C2065: “task_type”: 未声明的标识符
  enum_utils.cpp
E:\source\dronesplanning\src\utils\enum_utils.cpp(87,39): error C2039: "PlanningStrategyType": 不是 "NSDrones::NSPlanning" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(31,12): message : 参见“NSDrones::NSPlanning”的声明
E:\source\dronesplanning\src\utils\enum_utils.cpp(87,59): error C2065: “PlanningStrategyType”: 未声明的标识符
E:\source\dronesplanning\src\utils\enum_utils.cpp(87,119): error C2039: "PlanningStrategyType": 不是 "NSDrones::NSPlanning" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(31,12): message : 参见“NSDrones::NSPlanning”的声明
E:\source\dronesplanning\src\utils\enum_utils.cpp(87,119): error C3083: “PlanningStrategyType”:“::”左侧的符号必须是一种类型
E:\source\dronesplanning\src\utils\enum_utils.cpp(87,141): error C2039: "DEFAULT": 不是 "NSDrones::NSPlanning" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(31,12): message : 参见“NSDrones::NSPlanning”的声明
E:\source\dronesplanning\src\utils\enum_utils.cpp(87,148): error C2065: “DEFAULT”: 未声明的标识符
E:\source\dronesplanning\src\utils\enum_utils.cpp(88,39): error C2039: "PlannerType": 不是 "NSDrones::NSPlanning" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(31,12): message : 参见“NSDrones::NSPlanning”的声明
E:\source\dronesplanning\src\utils\enum_utils.cpp(88,50): error C2065: “PlannerType”: 未声明的标识符
E:\source\dronesplanning\src\utils\enum_utils.cpp(88,111): error C2039: "PlannerType": 不是 "NSDrones::NSPlanning" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(31,12): message : 参见“NSDrones::NSPlanning”的声明
E:\source\dronesplanning\src\utils\enum_utils.cpp(88,111): error C3083: “PlannerType”:“::”左侧的符号必须是一种类型
E:\source\dronesplanning\src\utils\enum_utils.cpp(88,124): error C2039: "UNKNOWN": 不是 "NSDrones::NSPlanning" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(31,12): message : 参见“NSDrones::NSPlanning”的声明
E:\source\dronesplanning\src\utils\enum_utils.cpp(88,131): error C2065: “UNKNOWN”: 未声明的标识符
  orientation_utils.cpp
  正在生成代码...
