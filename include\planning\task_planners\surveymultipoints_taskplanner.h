// include/planning/task_planners/task_planner_surveymultipoints.h
#pragma once

#include "planning/itask_planner.h"
#include "environment/environment_fwd.h"
#include "mission/task.h"
#include "mission/task_params.h"

namespace NSDrones {
	namespace NSPlanning {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class SurveyMultiPointsTaskPlanner
		 * @brief 多点巡检任务规划器
		 *
		 * 负责规划SURVEY_MULTIPOINTS类型任务，支持：
		 * - 多个巡检点的顺序访问
		 * - 路径优化和时间控制
		 * - 可配置的巡检参数
		 * - 多无人机协同巡检
		 */
		class SurveyMultiPointsTaskPlanner : public ITaskPlanner {
		public:
			// === 生命周期管理 ===

			/**
			 * @brief 构造函数
			 */
			SurveyMultiPointsTaskPlanner();

			/**
			 * @brief 初始化多点巡检任务规划器
			 * @param params 配置参数对象
			 * @param raw_config 原始JSON配置
			 * @return 初始化成功返回true
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> params,
						   const nlohmann::json& raw_config) override;

			// === 核心接口实现 ===

			/**
			 * @brief 规划单机多点巡检任务（重构后的接口）
			 * @param request 单任务规划请求
			 * @return 单任务规划结果
			 */
			SingleTaskPlanningResult planSingleTask(const SingleTaskPlanningRequest& request) override;

			/**
			 * @brief 检查是否支持指定的子任务类型
			 * @param sub_target 子任务目标
			 * @return 是否支持
			 */
			bool isSubTaskSupported(const SubTaskTarget& sub_target) const override{
				return sub_target.task_type == NSMission::TaskType::SURVEY_MULTIPOINTS;
			}

		private:
			// === 配置参数 ===
			double default_survey_speed_ = 8.0;  ///< 默认巡检速度(米/秒)

			// === 私有辅助方法 ===

			/**
			 * @brief 规划多点巡检轨迹的核心实现
			 * @param request 单任务规划请求
			 * @param params 巡检任务参数
			 * @param start_state 无人机起始状态
			 * @return 单任务规划结果
			 */
			SingleTaskPlanningResult planSurveyTrajectory(
				const SingleTaskPlanningRequest& request,
				const NSMission::SurveyMultiPointsTaskParams& params,
				const NSUav::UavState& start_state);

			/**
			 * @brief 应用避障约束到轨迹
			 * @param trajectory 要修改的轨迹
			 * @param constraints 避障约束列表
			 * @param result 规划结果（用于记录警告）
			 * @return 应用成功返回true
			 */
			bool applyAvoidanceConstraints(
				Trajectory& trajectory,
				const std::vector<AvoidanceConstraint>& constraints,
				SingleTaskPlanningResult& result);
		};

	} // namespace NSPlanning
} // namespace NSDrones