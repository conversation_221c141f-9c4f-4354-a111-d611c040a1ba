// src/uav/idynamic_model.cpp
#include "uav/idynamic_model.h"
#include "uav/uav.h" 
#include "utils/logging.h"   
#include <cmath>              
#include <limits>             
#include <algorithm>          
#include <sstream>           

namespace NSDrones {
	namespace NSUav {

		// --- 构造函数 ---
		/**
		 * @brief IDynamicModel 构造函数。
		 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
		 */
		IDynamicModel::IDynamicModel(const Uav& owner)
			: owner_(owner) // 初始化 owner 引用
		{
			LOG_TRACE("IDynamicModel 基类已构造，所有者 ID: {}", owner_.getId());
		}

		// --- 默认约束实现 (基于 owner_ 获取参数) ---
		/** @brief 获取最大水平飞行速度 (m/s)。查找 "dynamics.base.maxHVel"。 */
		double IDynamicModel::getMaxHorizontalSpeed(const UavState& state) const {
			// 从 owner_ 获取参数，如果找不到则使用默认值 0.0
			// 键名示例: "dynamics.base.maxHVel"
			double speed = owner_.getParamOrDefault<double>("dynamics.base.maxHVel", 0.0);
			LOG_TRACE("获取最大水平速度 (基类实现, 所有者 ID: {}): {:.2f} m/s", owner_.getId(), speed);
			return std::max(0.0, speed); // 确保非负
		}

		/** @brief 获取最大爬升速度 (m/s, 正值向上)。查找 "dynamics.base.maxVVelUp"。 */
		double IDynamicModel::getMaxClimbSpeed(const UavState& state) const {
			double speed = owner_.getParamOrDefault<double>("dynamics.base.maxVVelUp", 0.0);
			LOG_TRACE("获取最大爬升速度 (基类实现, 所有者 ID: {}): {:.2f} m/s", owner_.getId(), speed);
			return std::max(0.0, speed); // 确保非负
		}

		/** @brief 获取最大下降速度 (m/s, 正值向下)。查找 "dynamics.base.maxVVelDown"。 */
		double IDynamicModel::getMaxDescendSpeed(const UavState& state) const {
			double speed = owner_.getParamOrDefault<double>("dynamics.base.maxVVelDown", 0.0);
			LOG_TRACE("获取最大下降速度 (基类实现, 所有者 ID: {}): {:.2f} m/s", owner_.getId(), speed);
			return std::max(0.0, speed); // 确保非负 (下降速度也表示为正值)
		}

		/** @brief 获取最大水平加速度 (m/s^2)。查找 "dynamics.base.maxHAcc"。 */
		double IDynamicModel::getMaxHorizontalAcceleration(const UavState& state) const {
			double acc = owner_.getParamOrDefault<double>("dynamics.base.maxHAcc", 0.0);
			LOG_TRACE("获取最大水平加速度 (基类实现, 所有者 ID: {}): {:.2f} m/s^2", owner_.getId(), acc);
			return std::max(0.0, acc); // 确保非负
		}

		/** @brief 获取最大垂直加速度 (m/s^2, 向上)。查找 "dynamics.base.maxVAcc"。 */
		double IDynamicModel::getMaxVerticalAcceleration(const UavState& state) const {
			double acc = owner_.getParamOrDefault<double>("dynamics.base.maxVAcc", 0.0);
			LOG_TRACE("获取最大垂直加速度 (基类实现, 所有者 ID: {}): {:.2f} m/s^2", owner_.getId(), acc);
			return std::max(0.0, acc); // 确保非负
		}

		/** @brief 获取最大水平减速度 (m/s^2, 绝对值)。查找 "dynamics.base.maxHDecel"，若无则用 HAcc。 */
		double IDynamicModel::getMaxHorizontalDeceleration(const UavState& state) const {
			// 默认等于最大加速度（如果 maxHDecel 参数不存在）
			double default_decel = getMaxHorizontalAcceleration(state); // 先获取 HAcc
			double decel = owner_.getParamOrDefault<double>("dynamics.base.maxHDecel", default_decel);
			LOG_TRACE("获取最大水平减速度 (基类实现, 所有者 ID: {}): {:.2f} m/s^2", owner_.getId(), decel);
			return std::max(0.0, decel); // 确保非负
		}

		/** @brief 获取最大垂直减速度 (m/s^2, 绝对值)。查找 "dynamics.base.maxVDecel"，若无则用 VAcc。 */
		double IDynamicModel::getMaxVerticalDeceleration(const UavState& state) const {
			// 默认等于最大加速度（如果 maxVDecel 参数不存在）
			double default_decel = getMaxVerticalAcceleration(state); // 先获取 VAcc
			double decel = owner_.getParamOrDefault<double>("dynamics.base.maxVDecel", default_decel);
			LOG_TRACE("获取最大垂直减速度 (基类实现, 所有者 ID: {}): {:.2f} m/s^2", owner_.getId(), decel);
			return std::max(0.0, decel); // 确保非负
		}

		/** @brief 获取最大运行高度 (米)。查找 "dynamics.base.maxAlt"。 */
		double IDynamicModel::getMaxAltitude(const UavState& state) const {
			// 如果参数不存在，返回无穷大表示无限制
			double alt = owner_.getParamOrDefault<double>("dynamics.base.maxAlt", Constants::INF);
			LOG_TRACE("获取最大高度 (基类实现, 所有者 ID: {}): {:.1f} m", owner_.getId(), alt);
			return alt;
		}

		/** @brief 获取最小运行速度 (m/s)。查找 "dynamics.base.minOpSpeed"。 */
		double IDynamicModel::getMinOperationalSpeed(const UavState& state) const {
			// 默认 0 (多旋翼)，固定翼等需要重写或通过参数指定
			double speed = owner_.getParamOrDefault<double>("dynamics.base.minOpSpeed", 0.0);
			LOG_TRACE("获取最小运行速度 (基类实现, 所有者 ID: {}): {:.2f} m/s", owner_.getId(), speed);
			return std::max(0.0, speed); // 确保非负
		}

		// --- 辅助估算方法实现 ---
		/**
		 * @brief 估算速度变化所需时间。
		 */
		Time IDynamicModel::estimateSpeedChangeTime(const UavState& state, double currentSpeed, double targetSpeed) const {
			double speedDiff = std::abs(targetSpeed - currentSpeed); // 计算速度差绝对值
			if (speedDiff < Constants::VELOCITY_EPSILON) {
				LOG_TRACE("估算速度变化时间 (所有者 ID: {})：速度差 ({:.4f}) 过小，返回 0。", owner_.getId(), speedDiff);
				return 0.0; // 速度差很小，认为不需要时间
			}

			double acceleration = 0.0; // 使用的加速度（或减速度）
			if (targetSpeed > currentSpeed) { // 如果是加速过程
				acceleration = getMaxHorizontalAcceleration(state); // 使用最大水平加速度
				LOG_TRACE("  估算加速时间：使用最大水平加速度 {:.2f} m/s^2", acceleration);
			}
			else { // 如果是减速过程
				acceleration = getMaxHorizontalDeceleration(state); // 使用最大水平减速度
				LOG_TRACE("  估算减速时间：使用最大水平减速度 {:.2f} m/s^2", acceleration);
			}

			// 检查加速度是否有效 (大于零且有限)
			if (acceleration <= Constants::EPSILON || !std::isfinite(acceleration)) {
				LOG_WARN("估算速度变化时间 (所有者 ID: {})：加速度/减速度限制无效 ({:.4f})，无法估算时间，返回无穷大。", owner_.getId(), acceleration);
				return Constants::INF; // 加速度无效，无法估算时间，返回无穷大
			}

			Time estimated_time = speedDiff / acceleration; // 时间 = 速度差 / 加速度
			LOG_TRACE("估算速度变化时间 (所有者 ID: {})：速度差 {:.2f} m/s, 加/减速度 {:.2f} m/s^2, 估算时间 {:.4f} s", owner_.getId(), speedDiff, acceleration, estimated_time);
			return estimated_time;
		}

		/**
		 * @brief 估算转弯所需时间。
		 */
		Time IDynamicModel::estimateTurnTime(const UavState& state, const Vector3D& currentVelocity, const Vector3D& nextVelocity) const {
			double currentSpeed = currentVelocity.norm(); // 当前速度大小
			double nextSpeed = nextVelocity.norm();       // 目标速度大小

			// 如果任一速度接近零，无法计算有效角度或转弯率，认为无需转弯时间
			if (currentSpeed < Constants::VELOCITY_EPSILON || nextSpeed < Constants::VELOCITY_EPSILON) {
				LOG_TRACE("估算转弯时间 (所有者 ID: {})：起始或目标速度接近零，返回 0。", owner_.getId());
				return 0.0;
			}

			// 计算速度向量之间的夹角 (弧度)
			double dotProduct = currentVelocity.dot(nextVelocity);
			// 计算点积与模长乘积的比值，并限制在 [-1, 1] 范围内防止 acos 域错误
			// 注意检查分母是否接近零 (已通过上面速度检查保证)
			double cosTheta = std::clamp(dotProduct / (currentSpeed * nextSpeed), -1.0, 1.0);
			double angle = std::acos(cosTheta); // 计算夹角 [0, PI]

			// 如果角度变化很小，认为不需要转弯时间
			if (std::abs(angle) < Constants::ANGLE_EPSILON) {
				LOG_TRACE("估算转弯时间 (所有者 ID: {})：角度变化 ({:.4f} rad) 过小，返回 0。", owner_.getId(), angle);
				return 0.0;
			}

			double turnRate = getMaxTurnRate(state); // 获取当前状态下的最大转弯率 (rad/s)
			LOG_TRACE("估算转弯时间 (所有者 ID: {})：角度变化 {:.4f} rad, 最大转弯率 {:.4f} rad/s", owner_.getId(), angle, turnRate);

			// 检查最大转弯率是否有效
			if (turnRate <= Constants::EPSILON || !std::isfinite(turnRate)) {
				// 无穷大转弯率 (如多旋翼) 表示瞬时转弯
				if (std::isinf(turnRate) && turnRate > 0) {
					LOG_TRACE("  最大转弯率为无穷大，返回 0。");
					return 0.0;
				}
				else { // 无效或非正转弯率
					LOG_WARN("  最大转弯率无效 ({:.4f})，无法估算转弯时间，返回无穷大。", turnRate);
					return Constants::INF;
				}
			}

			Time estimated_time = std::abs(angle) / turnRate; // 时间 = 角度 / 角速度
			LOG_DEBUG("  估算转弯时间: {:.4f} s", estimated_time);
			return estimated_time;
		}

		// --- 静态辅助函数实现  ---
		/**
		 * @brief 静态辅助函数：检查速度向量是否在限制内。
		 */
		/*static*/ bool IDynamicModel::checkSpeedLimit(const Vector3D& velocity, double maxHVel, double maxVVelUp, double maxVVelDown, double minOpSpeed) {
			double hSpeed = velocity.head<2>().norm(); // 计算水平速度大小
			double vSpeed = velocity.z();              // 获取垂直速度 (正为上，负为下)

			// 检查水平速度上限 (使用容差)
			if (hSpeed > maxHVel + Constants::VELOCITY_EPSILON) {
				LOG_TRACE("速度检查失败: 水平速度 {:.2f} > 最大限制 {:.2f}", hSpeed, maxHVel);
				return false;
			}
			// 检查最小运行速度 (仅当水平速度不为零时才有意义)
			// minOpSpeed 为 0 时，此检查总是通过
			if (minOpSpeed > Constants::VELOCITY_EPSILON && hSpeed > Constants::VELOCITY_EPSILON && hSpeed < minOpSpeed - Constants::VELOCITY_EPSILON) {
				LOG_TRACE("速度检查失败: 水平速度 {:.2f} < 最小运行速度 {:.2f}", hSpeed, minOpSpeed);
				return false;
			}
			// 检查垂直上升速度上限
			if (vSpeed > maxVVelUp + Constants::VELOCITY_EPSILON) {
				LOG_TRACE("速度检查失败: 上升速度 {:.2f} > 最大限制 {:.2f}", vSpeed, maxVVelUp);
				return false;
			}
			// 检查垂直下降速度上限 (注意 maxVVelDown 是正值，表示下降速率)
			if (-vSpeed > maxVVelDown + Constants::VELOCITY_EPSILON) { // -vSpeed 是下降速率
				LOG_TRACE("速度检查失败: 下降速度 {:.2f} > 最大限制 {:.2f}", -vSpeed, maxVVelDown);
				return false;
			}
			// 所有检查通过
			LOG_TRACE("速度检查通过: HVel={:.2f}, VVel={:.2f} (限制: maxH={:.2f}, minOp={:.2f}, maxUp={:.2f}, maxDown={:.2f})",
				hSpeed, vSpeed, maxHVel, minOpSpeed, maxVVelUp, maxVVelDown);
			return true;
		}

		/**
		 * @brief 静态辅助函数：检查位置高度是否超限。
		 */
		/*static*/ bool IDynamicModel::checkAltitudeLimit(const WGS84Point& position, double maxAlt) {
			// 检查高度是否小于等于最大高度 (加容差)
			bool ok = position.altitude <= maxAlt + Constants::GEOMETRY_EPSILON;
			if (!ok) {
				LOG_TRACE("高度检查失败: 高度 {:.2f} > 最大限制 {:.2f}", position.altitude, maxAlt);
			}
			return ok;
		}

		/**
		 * @brief 静态辅助函数：检查转弯约束。
		 */
		/*static*/ bool IDynamicModel::checkTurnLimit(const Vector3D& v_current, const Vector3D& v_next, Time dt, double maxTurnRate, double minTurnRadius) {
			// 只考虑水平速度进行转弯检查
			Vector2D v_current_h = v_current.head<2>();
			Vector2D v_next_h = v_next.head<2>();
			double speed_current_h = v_current_h.norm();
			double speed_next_h = v_next_h.norm();

			// 速度接近零时，认为转弯约束满足（或不适用）
			if (speed_current_h < Constants::VELOCITY_EPSILON && speed_next_h < Constants::VELOCITY_EPSILON) {
				LOG_TRACE("转弯约束检查：速度接近零，跳过检查。");
				return true;
			}
			// 如果其中一个速度接近零，则主要是加速/减速，转弯约束可能不适用或难以计算
			// 简化处理：如果一个速度为零，认为约束满足（允许从静止开始转弯或转弯停止）
			if (speed_current_h < Constants::VELOCITY_EPSILON || speed_next_h < Constants::VELOCITY_EPSILON) {
				LOG_TRACE("转弯约束检查：起始或结束水平速度接近零，跳过检查。");
				return true;
			}

			// 计算水平方向变化角度 (使用 acos)
			double dot_h = v_current_h.dot(v_next_h);
			// 防止除零和 acos 输入超出范围
			double cosTheta_h = std::clamp(dot_h / (speed_current_h * speed_next_h), -1.0, 1.0);
			double angle_change_h = std::acos(cosTheta_h); // 水平角度变化量 [0, PI]

			// 如果角度变化很小，认为约束满足
			if (angle_change_h < Constants::ANGLE_EPSILON) {
				LOG_TRACE("转弯约束检查：水平角度变化 ({:.4f} rad) 过小，跳过检查。", angle_change_h);
				return true;
			}

			// --- 检查最大转弯率 ---
			// 检查 dt 是否有效
			if (dt <= Constants::TIME_EPSILON) {
				LOG_TRACE("转弯约束检查失败：时间步长过小 ({:.4f}) 且需要转弯 ({:.4f} rad)。", dt, angle_change_h);
				return false; // 需要转弯但时间步长无效
			}
			// 检查 maxTurnRate 是否有效 (有限且为正数)
			if (std::isfinite(maxTurnRate) && maxTurnRate > Constants::ANGLE_EPSILON) { // 使用容差比较
				double required_turn_rate = angle_change_h / dt; // 计算所需的平均转弯率
				// 检查所需转弯率是否超过最大限制 (加容差)
				if (required_turn_rate > maxTurnRate + Constants::ANGLE_EPSILON) {
					LOG_TRACE("转弯率检查失败: 需要 {:.3f} rad/s, 限制 {:.3f} rad/s (角度变化: {:.3f} rad, 时间: {:.3f} s)",
						required_turn_rate, maxTurnRate, angle_change_h, dt);
					return false; // 超过最大转弯率
				}
				LOG_TRACE("  转弯率检查通过 (需要 {:.3f} <= {:.3f})", required_turn_rate, maxTurnRate);
			}
			else if (std::isinf(maxTurnRate) && maxTurnRate > 0) { // 如果是正无穷大
				LOG_TRACE("  最大转弯率为无穷大，转弯率检查自动通过。");
				// 无限转弯率，始终满足
			}
			else { // 如果限制无效 (非正或 NaN)
				// 如果 maxTurnRate 无效但需要转弯，则失败
				LOG_WARN("转弯率检查失败：最大转弯率限制无效 ({:.4f})，但需要转弯 ({:.4f} rad)。", maxTurnRate, angle_change_h);
				return false;
			}

			// --- 检查最小转弯半径 ---
			// 仅当最小半径有效时检查 (大于零)
			if (minTurnRadius > Constants::GEOMETRY_EPSILON) {
				// 使用平均水平速度估算
				double avg_speed_h = (speed_current_h + speed_next_h) / 2.0;
				// 使用公式 R ≈ V / omega 估算所需半径
				// omega ≈ angle_change_h / dt (所需的平均角速度)
				// 避免除以零角度
				if (angle_change_h < Constants::ANGLE_EPSILON) {
					// 理论上这里不会到达，因为前面已经判断过角度变化
					LOG_TRACE("  最小半径检查：角度变化过小，跳过。");
				}
				else {
					double estimated_radius = avg_speed_h * dt / angle_change_h; // 估算转弯半径
					// 检查估算半径是否小于最小限制 (减容差)
					if (estimated_radius < minTurnRadius - Constants::GEOMETRY_EPSILON) {
						LOG_TRACE("转弯半径检查失败: 估算半径 {:.3f} m < 最小限制 {:.3f} m (平均速度: {:.2f} m/s, 角度: {:.3f} rad, 时间: {:.3f} s)",
							estimated_radius, minTurnRadius, avg_speed_h, angle_change_h, dt);
						return false; // 估算半径小于最小半径
					}
					LOG_TRACE("  最小半径检查通过 (估算 {:.3f} >= {:.3f})", estimated_radius, minTurnRadius);
				}
			}
			else {
				LOG_TRACE("  最小转弯半径限制为 0 或无效，跳过半径检查。");
			}

			// 所有转弯约束检查通过
			LOG_TRACE("转弯约束检查通过。");
			return true;
		}

		// --- 速度约束校验方法实现 ---
		/**
		 * @brief 综合速度校验函数 - 主要用于路径规划中的水平飞行速度校验
		 */
		bool IDynamicModel::validateAndAdjustSpeed(const UavState& state,
			double desired_speed,
			double& adjusted_speed,
			std::string& warning_message) const {

			// 获取速度限制
			double max_horizontal_speed = getMaxHorizontalSpeed(state);
			double min_operational_speed = getMinOperationalSpeed(state);

			// 初始化调整后的速度
			adjusted_speed = desired_speed;
			warning_message.clear();

			// 检查是否为负速度
			if (desired_speed < 0.0) {
				adjusted_speed = 0.0;
				warning_message = "期望速度为负值 (" + std::to_string(desired_speed) +
					" m/s)，已调整为0";
				LOG_WARN("IDynamicModel (UAV {}): {}", owner_.getId(), warning_message);
			}

			// 检查是否超过最大水平速度
			if (adjusted_speed > max_horizontal_speed + Constants::VELOCITY_EPSILON) {
				double original_speed = adjusted_speed;
				adjusted_speed = max_horizontal_speed;
				std::string max_speed_warning = "期望速度 " + std::to_string(original_speed) +
					" m/s 超过最大水平速度 " + std::to_string(max_horizontal_speed) +
					" m/s，已调整为最大水平速度";
				if (!warning_message.empty()) {
					warning_message += "; " + max_speed_warning;
				} else {
					warning_message = max_speed_warning;
				}
				LOG_WARN("IDynamicModel (UAV {}): {}", owner_.getId(), max_speed_warning);
			}

			// 检查是否低于最小运行速度（仅当速度不为零时）
			if (adjusted_speed > Constants::VELOCITY_EPSILON &&
				adjusted_speed < min_operational_speed - Constants::VELOCITY_EPSILON) {
				double original_speed = adjusted_speed;
				adjusted_speed = min_operational_speed;
				std::string min_speed_warning = "调整后速度 " + std::to_string(original_speed) +
					" m/s 低于最小运行速度 " + std::to_string(min_operational_speed) +
					" m/s，已调整为最小运行速度";
				if (!warning_message.empty()) {
					warning_message += "; " + min_speed_warning;
				} else {
					warning_message = min_speed_warning;
				}
				LOG_WARN("IDynamicModel (UAV {}): {}", owner_.getId(), min_speed_warning);
			}

			LOG_DEBUG("IDynamicModel (UAV {}): 速度校验完成 - 期望: {:.2f}, 调整后: {:.2f}",
				owner_.getId(), desired_speed, adjusted_speed);

			return true; // 总是返回true，因为我们总是能调整到有效值
		}

		/**
		 * @brief 三维速度向量校验函数 - 校验并调整三维速度向量
		 */
		bool IDynamicModel::validateAndAdjustVelocity(const UavState& state,
			const Vector3D& desired_velocity,
			Vector3D& adjusted_velocity,
			std::string& warning_message) const {

			adjusted_velocity = desired_velocity;
			warning_message.clear();

			// 获取各方向速度限制
			double max_horizontal_speed = getMaxHorizontalSpeed(state);
			double min_operational_speed = getMinOperationalSpeed(state);
			double max_climb_speed = getMaxClimbSpeed(state);
			double max_descend_speed = getMaxDescendSpeed(state);

			// 校验水平速度分量
			double horizontal_speed = desired_velocity.head<2>().norm();
			double adjusted_horizontal_speed = horizontal_speed;

			// 检查水平速度限制
			if (horizontal_speed > max_horizontal_speed + Constants::VELOCITY_EPSILON) {
				adjusted_horizontal_speed = max_horizontal_speed;
				warning_message += "水平速度 " + std::to_string(horizontal_speed) +
					" m/s 超过最大限制 " + std::to_string(max_horizontal_speed) + " m/s";
			}

			if (adjusted_horizontal_speed > Constants::VELOCITY_EPSILON &&
				adjusted_horizontal_speed < min_operational_speed - Constants::VELOCITY_EPSILON) {
				adjusted_horizontal_speed = min_operational_speed;
				if (!warning_message.empty()) warning_message += "; ";
				warning_message += "水平速度低于最小运行速度 " + std::to_string(min_operational_speed) + " m/s";
			}

			// 按比例调整水平分量
			if (std::abs(adjusted_horizontal_speed - horizontal_speed) > Constants::VELOCITY_EPSILON) {
				if (horizontal_speed > Constants::VELOCITY_EPSILON) {
					double scale_factor = adjusted_horizontal_speed / horizontal_speed;
					adjusted_velocity.x() *= scale_factor;
					adjusted_velocity.y() *= scale_factor;
				} else if (adjusted_horizontal_speed > Constants::VELOCITY_EPSILON) {
					// 原始水平速度为0，但需要最小运行速度，设置默认方向
					adjusted_velocity.x() = adjusted_horizontal_speed;
					adjusted_velocity.y() = 0.0;
				}
			}

			// 校验垂直速度分量
			double vertical_speed = desired_velocity.z();

			if (vertical_speed > max_climb_speed + Constants::VELOCITY_EPSILON) {
				adjusted_velocity.z() = max_climb_speed;
				if (!warning_message.empty()) warning_message += "; ";
				warning_message += "上升速度 " + std::to_string(vertical_speed) +
					" m/s 超过最大限制 " + std::to_string(max_climb_speed) + " m/s";
			}

			if (vertical_speed < -(max_descend_speed + Constants::VELOCITY_EPSILON)) {
				adjusted_velocity.z() = -max_descend_speed;
				if (!warning_message.empty()) warning_message += "; ";
				warning_message += "下降速度 " + std::to_string(-vertical_speed) +
					" m/s 超过最大限制 " + std::to_string(max_descend_speed) + " m/s";
			}

			LOG_DEBUG("IDynamicModel (UAV {}): 三维速度校验完成 - 期望: [{:.2f}, {:.2f}, {:.2f}], 调整后: [{:.2f}, {:.2f}, {:.2f}]",
				owner_.getId(),
				desired_velocity.x(), desired_velocity.y(), desired_velocity.z(),
				adjusted_velocity.x(), adjusted_velocity.y(), adjusted_velocity.z());

			return true; // 总是返回true，因为我们总是能调整到有效值
		}

	} // namespace NSUav
} // namespace NSDrones