#pragma once

#include "core/geometry/ishape.h"
#include <fcl/geometry/shape/convex.h>
#include <vector>

namespace NSDrones {
namespace NSCore {

    /**
     * @brief 多边形形状
     * 
     * 表示一个平面多边形，在FCL中用凸包实现。
     * 主要用于区域边界、投影轮廓、截面等。
     */
    class PolygonShape : public IShape {
    private:
        std::vector<fcl::Vector3d> vertices_;  // 顶点列表
        fcl::Vector3d normal_;                 // 多边形所在平面的法向量
        
        mutable std::shared_ptr<fcl::Convexd> fcl_convex_;  // FCL凸包对象（延迟创建）
        mutable bool geometry_dirty_;  // 几何是否需要更新

    public:
        /**
         * @brief 构造函数
         * @param vertices 顶点列表（至少3个点）
         */
        explicit PolygonShape(const std::vector<fcl::Vector3d>& vertices);

        /**
         * @brief 默认构造函数（单位三角形）
         */
        PolygonShape();

        /**
         * @brief 拷贝构造函数
         */
        PolygonShape(const PolygonShape& other);

        /**
         * @brief 赋值操作符
         */
        PolygonShape& operator=(const PolygonShape& other);

        // IShape接口实现
        ShapeType getType() const override { return ShapeType::POLYGON; }
        std::shared_ptr<fcl::CollisionGeometryd> getFCLGeometry() const override;
        fcl::AABBd getAABB(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const override;
        double getVolume() const override { return 0.0; }  // 多边形的体积为0
        double getSurfaceArea() const override;
        fcl::Vector3d getCentroid() const override;
        fcl::Matrix3d getInertiaMatrix(double mass) const override;
        std::unique_ptr<IShape> clone() const override;
        nlohmann::json serialize() const override;
        bool deserialize(const nlohmann::json& json) override;
        std::string toString() const override;
        bool containsPoint(const fcl::Vector3d& point) const override;
        double distanceToPoint(const fcl::Vector3d& point) const override;
        double getCharacteristicSize() const override;
        int getDimension() const override { return 2; }  // 多边形是2维

        // 多边形特有方法
        /**
         * @brief 获取顶点列表
         * @return 顶点列表的常量引用
         */
        const std::vector<fcl::Vector3d>& getVertices() const { return vertices_; }

        /**
         * @brief 设置顶点列表
         * @param vertices 新的顶点列表
         */
        void setVertices(const std::vector<fcl::Vector3d>& vertices);

        /**
         * @brief 添加顶点
         * @param vertex 新顶点
         */
        void addVertex(const fcl::Vector3d& vertex);

        /**
         * @brief 获取顶点数量
         * @return 顶点数量
         */
        size_t getVertexCount() const { return vertices_.size(); }

        /**
         * @brief 获取多边形所在平面的法向量
         * @return 法向量
         */
        const fcl::Vector3d& getNormal() const { return normal_; }

        /**
         * @brief 计算多边形面积
         * @return 面积
         */
        double getArea() const;

        /**
         * @brief 计算多边形周长
         * @return 周长
         */
        double getPerimeter() const;

        /**
         * @brief 检查多边形是否为凸多边形
         * @return 如果是凸多边形返回true
         */
        bool isConvex() const;

        /**
         * @brief 检查点是否在多边形内部（2D投影）
         * @param point 查询点
         * @return 如果在内部返回true
         */
        bool containsPoint2D(const fcl::Vector3d& point) const;

        /**
         * @brief 创建单位三角形
         * @return 多边形形状实例
         */
        static std::unique_ptr<PolygonShape> createTriangle();

        /**
         * @brief 创建单位正方形
         * @return 多边形形状实例
         */
        static std::unique_ptr<PolygonShape> createSquare();

        /**
         * @brief 创建正多边形
         * @param sides 边数
         * @param radius 外接圆半径
         * @return 多边形形状实例
         */
        static std::unique_ptr<PolygonShape> createRegularPolygon(int sides, double radius);

        /**
         * @brief 从矩形创建多边形
         * @param width 宽度
         * @param height 高度
         * @return 多边形形状实例
         */
        static std::unique_ptr<PolygonShape> createRectangle(double width, double height);

    private:
        void ensureFCLObject() const;
        void validateVertices() const;
        void computeNormal();
        void markGeometryDirty();
    };

} // namespace NSCore
} // namespace NSDrones
