// tests/control_point_performance_test.cpp
#include "mission/control_point.h"
#include "utils/logging.h"
#include <chrono>
#include <vector>
#include <random>

using namespace NSDrones::NSMission;
using namespace std::chrono;

/**
 * @brief 性能测试：控制点基本操作
 */
void testControlPointBasicOperations() {
    LOG_INFO("开始控制点基本操作性能测试");

    // 创建复杂的控制点
    WGS84Point position{116.4074, 39.9042, 100.0};
    ControlPoint cp(position, ControlPointType::WAYPOINT_MUST_PASS, 120.0,
                   AltitudeType::ABOVE_GROUND_LEVEL, "性能测试点");

    // 添加多个载荷动作
    for (int i = 0; i < 10; ++i) {
        PayloadActionCommand cmd("TestAction" + std::to_string(i),
                               {{"param1", i}, {"param2", i * 2.0}});
        cp.addActionCommand(cmd);
    }

    cp.setLoiterTime(15.0)
      .setRequiredHeading(90.0, 5.0)
      .setSpeedLimits(5.0, 15.0)
      .setDescription("复杂的测试控制点，包含多种约束和载荷动作");

    const int iterations = 1000; // 减少迭代次数，因为没有缓存

    // 测试描述生成性能
    auto start = high_resolution_clock::now();
    for (int i = 0; i < iterations; ++i) {
        std::string desc = cp.getDetailedDescription();
        (void)desc; // 避免编译器优化
    }
    auto end = high_resolution_clock::now();
    auto desc_duration = duration_cast<microseconds>(end - start);

    LOG_INFO("描述生成 {} 次调用耗时: {}μs (平均: {:.2f}μs/次)",
             iterations, desc_duration.count(),
             static_cast<double>(desc_duration.count()) / iterations);

    // 测试停留时间计算性能
    start = high_resolution_clock::now();
    for (int i = 0; i < iterations; ++i) {
        double dwell_time = cp.getEstimatedDwellTime();
        (void)dwell_time; // 避免编译器优化
    }
    end = high_resolution_clock::now();
    auto dwell_duration = duration_cast<microseconds>(end - start);

    LOG_INFO("停留时间计算 {} 次调用耗时: {}μs (平均: {:.2f}μs/次)",
             iterations, dwell_duration.count(),
             static_cast<double>(dwell_duration.count()) / iterations);

    // 测试验证性能
    start = high_resolution_clock::now();
    for (int i = 0; i < iterations; ++i) {
        bool valid = cp.isValid();
        (void)valid; // 避免编译器优化
    }
    end = high_resolution_clock::now();
    auto validation_duration = duration_cast<microseconds>(end - start);

    LOG_INFO("有效性验证 {} 次调用耗时: {}μs (平均: {:.2f}μs/次)",
             iterations, validation_duration.count(),
             static_cast<double>(validation_duration.count()) / iterations);
}

/**
 * @brief 性能测试：控制点列表操作
 */
void testControlPointListPerformance() {
    LOG_INFO("开始控制点列表性能测试");
    
    const int point_count = 1000;
    ControlPointList points;
    points.reserve(point_count);
    
    // 生成随机控制点
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> lat_dist(39.0, 40.0);
    std::uniform_real_distribution<> lon_dist(116.0, 117.0);
    std::uniform_real_distribution<> alt_dist(50.0, 200.0);
    
    auto start = high_resolution_clock::now();
    for (int i = 0; i < point_count; ++i) {
        WGS84Point pos{lon_dist(gen), lat_dist(gen), alt_dist(gen)};
        points.emplace_back(pos, ControlPointType::WAYPOINT_MUST_PASS, 
                           pos.altitude, AltitudeType::ABSOLUTE_ALTITUDE, 
                           "Point_" + std::to_string(i));
    }
    auto end = high_resolution_clock::now();
    auto creation_duration = duration_cast<microseconds>(end - start);
    
    LOG_INFO("创建 {} 个控制点耗时: {}μs (平均: {:.2f}μs/点)", 
             point_count, creation_duration.count(), 
             static_cast<double>(creation_duration.count()) / point_count);
    
    // 测试路径长度计算
    start = high_resolution_clock::now();
    double path_length = calculatePathLength(points);
    end = high_resolution_clock::now();
    auto path_duration = duration_cast<microseconds>(end - start);
    
    LOG_INFO("计算 {} 点路径长度耗时: {}μs, 长度: {:.2f}m", 
             point_count, path_duration.count(), path_length);
    
    // 测试验证性能
    start = high_resolution_clock::now();
    bool is_valid = validateControlPointList(points, true);
    end = high_resolution_clock::now();
    auto validation_duration = duration_cast<microseconds>(end - start);
    
    LOG_INFO("详细验证 {} 个控制点耗时: {}μs, 结果: {}", 
             point_count, validation_duration.count(), is_valid ? "有效" : "无效");
}

/**
 * @brief 性能测试：到达条件检查
 */
void testArrivalConditionPerformance() {
    LOG_INFO("开始到达条件检查性能测试");
    
    WGS84Point target_pos{116.4074, 39.9042, 100.0};
    ControlPoint cp(target_pos, ControlPointType::WAYPOINT_MUST_PASS, 100.0);
    cp.setPositionTolerance(2.0)
      .setAltitudeTolerance(3.0)
      .setRequiredHeading(90.0, 5.0);
    
    // 生成测试位置
    std::vector<WGS84Point> test_positions;
    std::vector<double> test_headings;
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> pos_offset(-0.001, 0.001); // 约±100米
    std::uniform_real_distribution<> alt_offset(-5.0, 5.0);
    std::uniform_real_distribution<> heading_dist(0.0, 360.0);
    
    const int test_count = 10000;
    for (int i = 0; i < test_count; ++i) {
        WGS84Point test_pos{
            target_pos.longitude + pos_offset(gen),
            target_pos.latitude + pos_offset(gen),
            target_pos.altitude + alt_offset(gen)
        };
        test_positions.push_back(test_pos);
        test_headings.push_back(heading_dist(gen));
    }
    
    // 测试到达条件检查性能
    auto start = high_resolution_clock::now();
    int arrival_count = 0;
    for (int i = 0; i < test_count; ++i) {
        if (cp.isArrivalConditionMet(test_positions[i], test_headings[i])) {
            arrival_count++;
        }
    }
    auto end = high_resolution_clock::now();
    auto check_duration = duration_cast<microseconds>(end - start);
    
    LOG_INFO("到达条件检查 {} 次耗时: {}μs (平均: {:.2f}μs/次), 满足条件: {} 次", 
             test_count, check_duration.count(), 
             static_cast<double>(check_duration.count()) / test_count,
             arrival_count);
}

/**
 * @brief 主测试函数
 */
int main() {
    // 初始化日志系统
    NSUtils::LoggingManager::getInstance().setLogLevel(NSUtils::LogLevel::INFO);
    
    LOG_INFO("=== 控制点系统性能测试开始 ===");
    
    try {
        testControlPointCaching();
        testControlPointListPerformance();
        testArrivalConditionPerformance();
        
        LOG_INFO("=== 控制点系统性能测试完成 ===");
        return 0;
    }
    catch (const std::exception& e) {
        LOG_ERROR("性能测试失败: {}", e.what());
        return 1;
    }
}
