{"parameters": [{"key": "instance_name_override", "type": "string", "default": "", "description": "对象的实例名称，如果提供，将覆盖从ID推断的名称。主要用于显示和日志。", "required": false}, {"key": "initial_position_ned", "type": "vector3d", "default": [0.0, 0.0, 0.0], "description": "对象在NED坐标系下的初始位置 [北, 东, 下] (米)。", "required": false}, {"key": "initial_position_wgs84", "type": "wgs84point", "default": [118.8, 32.1, 10.0], "description": "对象的WGS84坐标初始位置 [经度, 纬度, 高度] (度, 度, 米)。优先级高于initial_position_ned。", "required": false}, {"key": "initial_orientation_ypr_deg", "type": "vector3d", "default": [0.0, 0.0, 0.0], "description": "对象的初始姿态 [偏航角, 俯仰角, 横滚角] (度)。", "required": false}, {"key": "shape_type", "type": "string", "enum_type": "NSDrones::NSCore::ShapeType", "default": "BOX", "description": "对象的几何形状类型。实际合法值由 enum_type 定义的C++枚举确定。", "required": false}, {"key": "shape_offset_ned", "type": "vector3d", "default": [0.0, 0.0, 0.0], "description": "形状相对于对象原点 (initial_position_wgs84 或 initial_position_ned) 的偏移量 [北, 东, 下] (米)。", "required": false}, {"key": "shape_orientation_ypr_deg", "type": "vector3d", "default": [0.0, 0.0, 0.0], "description": "形状相对于对象姿态的额外旋转 [偏航角, 俯仰角, 横滚角] (度)。", "required": false}, {"key": "shape_box_size_xyz", "type": "vector3d", "default": [1.0, 1.0, 1.0], "description": "对于 'BOX' 形状: 尺寸 [X, Y, Z] (米)。", "required": false, "condition": {"dependent_param_key": "shape_type", "required_value_type": "string", "required_value": "BOX"}}, {"key": "shape_cylinder_radius_m", "type": "double", "default": 1.0, "description": "对于 'CYLINDER' 形状: 半径 (米)。", "required": false, "condition": {"dependent_param_key": "shape_type", "required_value_type": "string", "required_value": "CYLINDER"}}, {"key": "shape_cylinder_height_m", "type": "double", "default": 1.0, "description": "对于 'CYLINDER' 形状: 高度 (米)。", "required": false, "condition": {"dependent_param_key": "shape_type", "required_value_type": "string", "required_value": "CYLINDER"}}, {"key": "shape_sphere_radius_m", "type": "double", "default": 1.0, "description": "对于 'SPHERE' 形状: 半径 (米)。", "required": false, "condition": {"dependent_param_key": "shape_type", "required_value_type": "string", "required_value": "SPHERE"}}, {"key": "shape_polygon_vertices_ned", "type": "double[][]", "default": [[0.0, 0.0, 0.0], [1.0, 0.0, 0.0], [1.0, 1.0, 0.0]], "description": "对于 'POLYGON' 形状: 多边形顶点列表 [北, 东, 下] (米)。顶点应按顺序定义，通常为逆时针。", "required": false, "condition": {"dependent_param_key": "shape_type", "required_value_type": "string", "required_value": "POLYGON"}}, {"key": "shape_polygon_min_alt_msl", "type": "double", "default": 0.0, "description": "对于 'POLYGON' 形状: 多边形区域的最小绝对高度 (MSL, 米)。", "required": false, "condition": {"dependent_param_key": "shape_type", "required_value_type": "string", "required_value": "POLYGON"}}, {"key": "shape_polygon_max_alt_msl", "type": "double", "default": 100.0, "description": "对于 'POLYGON' 形状: 多边形区域的最大绝对高度 (MSL, 米)。", "required": false, "condition": {"dependent_param_key": "shape_type", "required_value_type": "string", "required_value": "POLYGON"}}, {"key": "movement_strategy_type", "type": "string", "default": "STATIC", "enum_type": "NSDrones::NSCore::MovementStrategyType", "description": "对象的移动策略类型 (例如 'static', 'circular_path', 'scripted_path')。", "required": false}]}