// include/environment/environment_fwd.h
#pragma once

#include <memory> // 引入智能指针支持

namespace NSDrones {	// 无人机项目命名空间
	namespace NSCore { class EntityObject; }
	namespace NSUtils { using ObjectID = std::string; }
	namespace NSEnvironment {  // 环境模块命名空间

		// 碰撞检测器的前向声明及智能指针别名
		using ObjectMap = std::unordered_map<NSUtils::ObjectID, std::shared_ptr<NSCore::EntityObject>>;
		template<typename ObjectMap> class CollisionEngine;

		// 区域 (例如禁飞区、任务区) 的前向声明及智能指针别名
		class Zone;
		using ZonePtr = std::shared_ptr<Zone>;
		using ConstZonePtr = std::shared_ptr<const Zone>;

		// 障碍物的前向声明及智能指针别名
		class Obstacle;
		using ObstaclePtr = std::shared_ptr<Obstacle>;
		using ConstObstaclePtr = std::shared_ptr<const Obstacle>;

		// 环境类的前向声明及智能指针别名
		class Environment;
		using EnvironmentPtr = std::shared_ptr<Environment>;
		using ConstEnvironmentPtr = std::shared_ptr<const Environment>;

	} // namespace NSEnvironment
} // namespace NSDrones

using namespace NSDrones::NSEnvironment;