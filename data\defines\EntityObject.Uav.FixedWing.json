{"description": "Parameters specific to Fixed-Wing UAVs, inheriting from EntityObject.Uav.", "parameters": [{"key": "aero.wing_span_m", "type": "double", "description": "翼展 (m)", "default": 1.5, "constraints": {"type": "numeric", "min": 0.5, "max": 10.0}}, {"key": "aero.wing_area_m2", "type": "double", "description": "机翼面积 (m^2)", "default": 0.5, "constraints": {"type": "numeric", "min": 0.05, "max": 5.0}}, {"key": "aero.min_airspeed_mps", "type": "double", "description": "最小安全空速 (失速速度附近) (m/s)", "default": 10.0, "constraints": {"type": "numeric", "min": 5.0, "max": 30.0}}, {"key": "aero.Cl_alpha", "type": "double", "description": "升力系数随迎角变化率 (每弧度)", "default": 4.5, "constraints": {"type": "numeric", "min": 2.0, "max": 7.0}}, {"key": "aero.induced_drag_coeff", "type": "double", "description": "诱导阻力相关系数 (通常与 Oswald 效率因子 e 和展弦比 AR 有关)", "default": 0.04, "constraints": {"type": "numeric", "min": 0.01, "max": 0.1}}, {"key": "propulsion.type", "type": "string", "enum_type": "NSDrones::NSCore::PropulsionType", "description": "固定翼推进系统类型。实际合法值由 enum_type 定义的C++枚举确定。", "default": "PROPELLER", "required": false}, {"key": "propulsion.max_thrust_N", "type": "double", "description": "最大推力 (N)", "default": 20.0, "constraints": {"type": "numeric", "min": 1.0, "max": 200.0}}, {"key": "energy.consumption_cruise_power_coeff", "type": "double", "description": "巡航功耗系数 (与速度、推力等相关的模型系数)", "default": 0.01, "constraints": {"type": "numeric"}}, {"key": "aero.aspect_ratio", "type": "double", "description": "展弦比", "default": 8.0, "constraints": {"type": "numeric", "min": 2.0, "max": 20.0}}, {"key": "aero.drag_coeff_zero_lift", "type": "double", "description": "零升力阻力系数 (CD0)", "default": 0.02, "constraints": {"type": "numeric", "min": 0.005, "max": 0.1}}, {"key": "aero.stall_angle_deg", "type": "double", "description": "失速迎角 (度)", "default": 15.0, "constraints": {"type": "numeric", "min": 5.0, "max": 25.0}}]}