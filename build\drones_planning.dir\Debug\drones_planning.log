﻿  task_allocator.cpp
  energy_evaluator.cpp
E:\source\dronesplanning\include\planning/planning_types.h(372,20): error C2079: “NSDrones::NSPlanning::SubTaskAssignment::start_state”使用未定义的 struct“NSDrones::NSUav::UavState”
E:\source\dronesplanning\include\planning/planning_types.h(495,17): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/planning_types.h(495,17): error C2143: 语法错误: 缺少“;”(在“*”的前面)
E:\source\dronesplanning\include\planning/planning_types.h(495,36): error C2238: 意外的标记位于“;”之前
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(51,19): error C3646: “evaluate”: 未知重写说明符
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(51,27): error C2059: 语法错误:“(”
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(55,20): error C2238: 意外的标记位于“;”之前
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(58,22): error C2065: “TrajectoryCost”: 未声明的标识符
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(58,9): error C2923: "std::map": "TrajectoryCost" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(58,22): message : 参见“TrajectoryCost”的声明
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(58,9): error C2976: “std::map'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\map(73,11): message : 参见“std::map”的声明
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(60,19): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(60,19): error C2143: 语法错误: 缺少“,”(在“&”的前面)
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(62,20): error C2955: “std::map”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\map(73,11): message : 参见“std::map”的声明
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(58,38): error C3668: “NSDrones::NSAlgorithm::EnergyEvaluator::evaluate”: 包含重写说明符“override”的方法没有重写任何基类方法
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(116,35): error C2143: 语法错误: 缺少“;”(在“NSDrones::NSAlgorithm::EnergyEvaluator::evaluate”的前面)
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(116,43): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(120,3): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(116,35): error C2511: “int NSDrones::NSAlgorithm::EnergyEvaluator::evaluate(NSDrones::NSUav::ConstUavPtr,const NSDrones::NSPlanning::Trajectory &,const NSDrones::NSMission::Mission *) const”:“NSDrones::NSAlgorithm::EnergyEvaluator”中没有找到重载的成员函数
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(25,9): message : 参见“NSDrones::NSAlgorithm::EnergyEvaluator”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(122,19): error C2146: 语法错误: 缺少“;”(在标识符“cost_result”的前面)
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(122,19): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(123,4): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(127,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(128,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(129,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(129,5): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(130,12): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(134,4): error C2352: “NSDrones::NSCore::BaseObject::getName”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(89,23): message : 参见“NSDrones::NSCore::BaseObject::getName”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(134,4): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(145,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(146,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(147,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(147,5): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(148,12): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(158,1): error C2671: “NSDrones::NSAlgorithm::EnergyEvaluator::evaluate”: 静态成员函数没有“this”指针
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(161,19): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(301,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(163,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(165,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(167,6): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(171,6): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(172,6): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(172,6): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(174,12): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(187,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(194,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(195,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(195,5): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(196,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(197,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(199,12): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(204,45): error C2676: 二进制“[”:“const NSDrones::NSPlanning::Trajectory”不定义该运算符或到预定义运算符可接收的类型的转换
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(204,25): error C2530: “segment”: 必须初始化引用
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(205,33): error C3536: “segment”: 初始化之前无法使用
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(205,24): error C2530: “states”: 必须初始化引用
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(208): error C3536: “states”: 初始化之前无法使用
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(209,6): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(213,5): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(220,40): error C2109: 下标要求数组或指针类型
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(220,30): error C2530: “sole_state”: 必须初始化引用
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(222): error C3536: “sole_state”: 初始化之前无法使用
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(223,8): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(230,56): error C2664: “double NSDrones::NSUav::IEnergyModel::computeEnergyConsumption(const NSDrones::NSUav::UavState &,NSDrones::NSCore::Time) const”: 无法将参数 1 从“int”转换为“const NSDrones::NSUav::UavState &”
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(230,83): message : 原因如下: 无法从“int”转换为“const NSDrones::NSUav::UavState”
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(230,83): message : 无构造函数可以接受源类型，或构造函数重载决策不明确
E:\source\dronesplanning\include\uav/ienergy_model.h(60,19): message : 参见“NSDrones::NSUav::IEnergyModel::computeEnergyConsumption”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(230,56): message : 尝试匹配参数列表“(int, double)”时
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(234,7): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(239,8): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(244,8): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(245,8): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(245,8): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(247,8): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(249,8): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(250,25): error C2664: “NSDrones::NSCore::Time NSDrones::NSUav::IEnergyModel::estimateEnduranceTime(const NSDrones::NSUav::UavState &,double) const”: 无法将参数 1 从“int”转换为“const NSDrones::NSUav::UavState &”
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(250,49): message : 原因如下: 无法从“int”转换为“const NSDrones::NSUav::UavState”
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(250,49): message : 无构造函数可以接受源类型，或构造函数重载决策不明确
E:\source\dronesplanning\include\uav/ienergy_model.h(68,17): message : 参见“NSDrones::NSUav::IEnergyModel::estimateEnduranceTime”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(250,25): message : 尝试匹配参数列表“(int, double)”时
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(251,15): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(253,7): error C2679: 二元“=”: 没有找到接受“int”类型的右操作数的运算符(或没有可接受的转换)
E:\source\dronesplanning\include\uav/uav_types.h(68,3): message : 可能是“NSDrones::NSUav::UavState &NSDrones::NSUav::UavState::operator =(NSDrones::NSUav::UavState &&)”
E:\source\dronesplanning\include\uav/uav_types.h(68,3): message : 或    “NSDrones::NSUav::UavState &NSDrones::NSUav::UavState::operator =(const NSDrones::NSUav::UavState &)”
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(253,7): message : 尝试匹配参数列表“(NSDrones::NSUav::UavState, int)”时
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(257,7): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(260,35): error C2109: 下标要求数组或指针类型
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(268,49): error C2109: 下标要求数组或指针类型
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(268,31): error C2530: “start_state”: 必须初始化引用
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(269,51): error C2109: 下标要求数组或指针类型
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(269,29): error C2530: “end_state”: 必须初始化引用
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(272,26): error C3536: “end_state”: 初始化之前无法使用
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(272,51): error C3536: “start_state”: 初始化之前无法使用
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(275,8): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(282,8): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(290,56): error C2664: “double NSDrones::NSUav::IEnergyModel::computeEnergyConsumption(const NSDrones::NSUav::UavState &,NSDrones::NSCore::Time) const”: 无法将参数 1 从“int”转换为“const NSDrones::NSUav::UavState &”
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(290,83): message : 原因如下: 无法从“int”转换为“const NSDrones::NSUav::UavState”
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(290,83): message : 无构造函数可以接受源类型，或构造函数重载决策不明确
E:\source\dronesplanning\include\uav/ienergy_model.h(60,19): message : 参见“NSDrones::NSUav::IEnergyModel::computeEnergyConsumption”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(290,56): message : 尝试匹配参数列表“(int, NSDrones::NSCore::Time)”时
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(294,7): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(299,8): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(304,8): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(305,8): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(305,8): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(308,8): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(310,8): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(310,68): error C2664: “NSDrones::NSCore::Time NSDrones::NSUav::IEnergyModel::estimateEnduranceTime(const NSDrones::NSUav::UavState &,double) const”: 无法将参数 1 从“int”转换为“const NSDrones::NSUav::UavState &”
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(310,92): message : 原因如下: 无法从“int”转换为“const NSDrones::NSUav::UavState”
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(310,92): message : 无构造函数可以接受源类型，或构造函数重载决策不明确
E:\source\dronesplanning\include\uav/ienergy_model.h(68,17): message : 参见“NSDrones::NSUav::IEnergyModel::estimateEnduranceTime”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(310,68): message : 尝试匹配参数列表“(int, double)”时
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(311,15): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(319,7): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(327,5): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(333,4): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(335,4): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(337,4): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(337,4): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(337,4): error C2352: “NSDrones::NSCore::BaseObject::getName”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(89,23): message : 参见“NSDrones::NSCore::BaseObject::getName”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(342,11): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(342,22): fatal  error C1003: 错误计数超过 100；正在停止编译
  ipath_planner.cpp
E:\source\dronesplanning\include\planning/planning_types.h(495,17): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/planning_types.h(495,17): error C2143: 语法错误: 缺少“;”(在“*”的前面)
E:\source\dronesplanning\include\planning/planning_types.h(495,36): error C2238: 意外的标记位于“;”之前
E:\source\dronesplanning\src\algorithm\path_planner\ipath_planner.cpp(13,28): error C2511: “bool NSDrones::NSAlgorithm::IPathPlanner::isPathFeasible(const std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> &) const”:“NSDrones::NSAlgorithm::IPathPlanner”中没有找到重载的成员函数
E:\source\dronesplanning\include\environment/environment.h(66,9): message : 参见“NSDrones::NSAlgorithm::IPathPlanner”的声明
  rrtstar_planner.cpp
E:\source\dronesplanning\include\planning/planning_types.h(495,17): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/planning_types.h(495,17): error C2143: 语法错误: 缺少“;”(在“*”的前面)
E:\source\dronesplanning\include\planning/planning_types.h(495,36): error C2238: 意外的标记位于“;”之前
  trajectory_optimizer.cpp
