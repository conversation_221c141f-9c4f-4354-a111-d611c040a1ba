// src/planning/task_planners/task_planner_surveymultipoints.cpp
#include "planning/task_planners/surveymultipoints_taskplanner.h"
#include "planning/planning_types.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/task_strategies.h"
#include "mission/control_point.h"
#include "uav/uav.h"
#include "environment/environment.h"
#include "utils/coordinate_converter.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"
#include "environment/environment.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include <vector>
#include <map>
#include <string>
#include <algorithm>
#include <utility>
#include <numeric>

namespace NSDrones {
	namespace NSPlanning {

		SurveyMultiPointsTaskPlanner::SurveyMultiPointsTaskPlanner()
			: ITaskPlanner() {}

		bool SurveyMultiPointsTaskPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params,
													  const nlohmann::json& raw_config) {
			LOG_DEBUG("多点巡检任务规划器: 开始初始化");

			if (params) {
				// 加载巡检相关参数
				default_survey_speed_ = params->getValueOrDefault<double>("survey.speed", 8.0);

				LOG_INFO("多点巡检任务规划器: 参数加载完成 - 默认速度:{:.1f}m/s", default_survey_speed_);
			} else {
				LOG_WARN("多点巡检任务规划器: 参数对象为空，使用默认值");
			}

			LOG_DEBUG("多点巡检任务规划器: 初始化完成");
			return true;
		}

		PlanningResult SurveyMultiPointsTaskPlanner::planTask(
			const NSMission::Task& task,
			const std::vector<NSUav::UavPtr>& assigned_uavs,
			const std::map<NSUtils::ObjectID, NSUav::UavState>& start_states) {

			LOG_INFO("多点巡检任务规划器: 开始规划任务[{}]", task.getId());
			PlanningResult result;
			result.setStatus(true);

			// 1. 基本验证
			if (!validateTaskBasics(task, NSMission::TaskType::SURVEY_MULTIPOINTS, assigned_uavs, result)) {
				return result;
			}

			// 2. 获取和验证巡检参数
			auto params_ptr = task.getTaskParameters<NSMission::SurveyMultiPointsTaskParams>();
			if (!params_ptr) {
				result.setStatus(false, "无法获取多点巡检任务参数");
				LOG_ERROR("多点巡检任务规划器: 任务[{}]参数获取失败", task.getId());
				return result;
			}

			if (!validateSurveyParams(*params_ptr, task.getId(), result)) {
				return result;
			}

			// 转换巡检点为WGS84坐标
			std::vector<NSCore::WGS84Point> survey_points;
			survey_points.reserve(params_ptr->survey_points.size());
			for (const auto& cp : params_ptr->survey_points) {
				survey_points.push_back(cp.position);
			}

			LOG_INFO("多点巡检任务规划器: 任务[{}]包含{}个巡检点", task.getId(), survey_points.size());

			// 3. 为每个无人机规划路径
			bool overall_success = true;
			for (const auto& uav : assigned_uavs) {
				if (!validateUavState(uav, start_states, task.getId(), result)) {
					overall_success = false;
					continue;
				}

				const NSUtils::ObjectID& uav_id = uav->getId();
				const NSUav::UavState& start_state = start_states.at(uav_id);

				LOG_INFO("多点巡检任务规划器: 为无人机[{}]规划巡检路径", uav_id);

				if (!planSurveyPathForUav(uav, survey_points, start_state, task, result)) {
					overall_success = false;
					continue;
				}
			}

			// 4. 设置最终状态
			if (!overall_success && result.wasSuccessful()) {
				result.setStatus(false, "部分无人机巡检路径规划失败");
			}

			LOG_INFO("多点巡检任务规划器: 任务[{}]规划完成，状态:{}, 生成{}条航线",
					task.getId(), result.wasSuccessful() ? "成功" : "失败", result.getAllRoutes().size());
			return result;
		}

		// === 私有辅助方法实现 ===

		bool SurveyMultiPointsTaskPlanner::validateSurveyParams(const NSMission::SurveyMultiPointsTaskParams& params,
																const NSUtils::ObjectID& task_id,
																PlanningResult& result) const {
			// 验证巡检点数量
			if (params.survey_points.empty()) {
				std::string error_msg = "巡检点列表为空";
				result.setStatus(false, error_msg);
				LOG_ERROR("多点巡检任务规划器: 任务[{}] {}", task_id, error_msg);
				return false;
			}

			// 验证每个巡检点的有效性
			for (size_t i = 0; i < params.survey_points.size(); ++i) {
				const auto& cp = params.survey_points[i];
				if (!cp.position.isValid()) {
					std::string error_msg = "巡检点#" + std::to_string(i + 1) + "位置无效";
					result.setStatus(false, error_msg);
					LOG_ERROR("多点巡检任务规划器: 任务[{}] {}", task_id, error_msg);
					return false;
				}
			}

			LOG_DEBUG("多点巡检任务规划器: 任务[{}]参数验证通过 - 巡检点数量:{}",
					 task_id, params.survey_points.size());
			return true;
		}

		bool SurveyMultiPointsTaskPlanner::planSurveyPathForUav(const NSUav::UavPtr& uav,
																const std::vector<NSCore::WGS84Point>& survey_points,
																const NSUav::UavState& start_state,
																const NSMission::Task& task,
																PlanningResult& result) {
			const NSUtils::ObjectID& uav_id = uav->getId();

			RouteSegment total_route_segment;
			NSUav::UavState current_state = start_state;

			// 依次访问每个巡检点
			for (size_t i = 0; i < survey_points.size(); ++i) {
				const NSCore::WGS84Point& target_point = survey_points[i];

				LOG_DEBUG("多点巡检任务规划器: 无人机[{}]规划到巡检点#{}: {}",
						 uav_id, i + 1, target_point.toString());

				// 规划到当前巡检点的路径
				std::vector<NSCore::EcefPoint> path_to_point = planPathToTarget(current_state, target_point, uav, task, result);
				if (path_to_point.empty()) {
					LOG_ERROR("多点巡检任务规划器: 无人机[{}]无法规划到巡检点#{}的路径", uav_id, i + 1);
					return false;
				}

				// 平滑和时间参数化
				RouteSegment segment;
				double desired_speed = task.getDesiredSpeed(default_survey_speed_);

				if (!smoothAndTimeParameterizeECEF(path_to_point, uav, current_state, desired_speed,
												  segment, &result, task.getStrategies())) {
					LOG_ERROR("多点巡检任务规划器: 无人机[{}]到巡检点#{}的路径平滑失败", uav_id, i + 1);
					return false;
				}

				// 合并路径段
				if (!total_route_segment.empty() && !segment.empty()) {
					// 检查重合点并处理时间连续性
					if ((total_route_segment.back().position - segment.front().position).norm() < NSCore::Constants::GEOMETRY_EPSILON) {
						segment.erase(segment.begin()); // 移除重合点
					}

					// 确保时间连续性
					if (!segment.empty() && segment.front().time_stamp <= total_route_segment.back().time_stamp) {
						NSCore::Time time_offset = total_route_segment.back().time_stamp - segment.front().time_stamp + NSCore::Constants::TIME_EPSILON;
						for (auto& rp : segment) {
							rp.time_stamp += time_offset;
						}
					}
				}

				// 添加到总路径
				total_route_segment.insert(total_route_segment.end(), segment.begin(), segment.end());

				// 更新当前状态
				if (!total_route_segment.empty()) {
					current_state = NSUav::stateFromRoutePt(total_route_segment.back());
				}

				LOG_DEBUG("多点巡检任务规划器: 无人机[{}]到巡检点#{}的路径规划完成，生成{}个航点",
						 uav_id, i + 1, segment.size());
			}

			// 添加到结果
			if (total_route_segment.empty()) {
				LOG_WARN("多点巡检任务规划器: 无人机[{}]生成的航段为空", uav_id);
				return false;
			}

			PlannedRoute route(uav_id);
			route.addWaypoints(total_route_segment);
			checkSegmentWarnings(route, uav_id, result, task.getId());
			result.addRoute(std::move(route));

			LOG_INFO("多点巡检任务规划器: 无人机[{}]巡检路径规划成功，访问{}个点，生成{}个航点",
					uav_id, survey_points.size(), total_route_segment.size());
			return true;
		}

	} // namespace NSPlanning
} // namespace NSDrones