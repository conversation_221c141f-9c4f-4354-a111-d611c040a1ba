// src/params/param_defs.cpp
#include "params/param_defs.h"
#include "utils/logging.h"
#include <cmath>      
#include <algorithm>  

namespace NSDrones {
	namespace NSParams {

		// --- 加载参数实现 ---

		/** @brief 加载 UAVPhysicalProperties 参数。*/
		bool UAVPhysicalProperties::loadFromParamValues(const ParamValues& params) {
			LOG_DEBUG("加载无人机物理属性参数...");
			bool success = true; // 假设成功
			// 使用 getValueOrDefault 从 ParamValues 获取值，如果键不存在则保留现有成员值
			// 使用 "physical." 作为键前缀
			size_length = params.getValueOrDefault<double>("physical.size_length", size_length);
			size_width = params.getValueOrDefault<double>("physical.size_width", size_width);
			size_height = params.getValueOrDefault<double>("physical.size_height", size_height);
			empty_weight = params.getValueOrDefault<double>("physical.empty_weight", empty_weight);
			max_payload_weight = params.getValueOrDefault<double>("physical.max_payload_weight", max_payload_weight);
			// 添加验证，例如重量/尺寸必须为正，载荷不能为负
			if (size_length <= 0 || size_width <= 0 || size_height <= 0) {
				LOG_ERROR("加载物理参数失败：尺寸参数必须为正。");
				success = false;
			}
			if (empty_weight <= 0) {
				LOG_ERROR("加载物理参数失败：空重必须为正。");
				success = false;
			}
			if (max_payload_weight < 0) {
				LOG_ERROR("加载物理参数失败：最大载荷不能为负。");
				success = false;
			}
			LOG_DEBUG("物理属性参数加载{}完成。", success ? "" : "部分或完全");
			return success; // 返回加载是否完全成功且有效
		}

		/** @brief 加载 IDynamicParams 基类参数。*/
		bool IDynamicParams::loadFromParamValues(const ParamValues& params) {
			LOG_DEBUG("加载基础动力学参数 (前缀: dynamics.base.)...");
			bool success = true; // 假设成功
			// 使用 "dynamics.base." 作为键前缀
			maxHVel = params.getValueOrDefault<double>("dynamics.base.maxHVel", maxHVel);
			maxVVelUp = params.getValueOrDefault<double>("dynamics.base.maxVVelUp", maxVVelUp);
			maxVVelDown = params.getValueOrDefault<double>("dynamics.base.maxVVelDown", maxVVelDown);
			maxHAcc = params.getValueOrDefault<double>("dynamics.base.maxHAcc", maxHAcc);
			maxVAcc = params.getValueOrDefault<double>("dynamics.base.maxVAcc", maxVAcc);
			maxAlt = params.getValueOrDefault<double>("dynamics.base.maxAlt", maxAlt);
			maxHDecel = params.getValueOrDefault<double>("dynamics.base.maxHDecel", maxHAcc); // 默认等于 HAcc
			maxVDecel = params.getValueOrDefault<double>("dynamics.base.maxVDecel", maxVAcc); // 默认等于 VAcc
			minOpSpeed = params.getValueOrDefault<double>("dynamics.base.minOpSpeed", minOpSpeed);
			maxYawRate_dps = params.getValueOrDefault<double>("dynamics.base.max_yaw_rate_dps", maxYawRate_dps); // 新增加载

			// 添加简单的值验证
			if (maxVVelDown < 0) { // 下降速度通常表示为正值
				LOG_WARN("基础动力学参数: maxVVelDown ({:.2f}) 应为正值，已取绝对值。", maxVVelDown);
				maxVVelDown = std::abs(maxVVelDown);
				// 注意：这里不标记为失败，仅修正并警告
			}
			if (maxHVel <= 0 || maxVVelUp <= 0 || maxHAcc <= 0 || maxVAcc <= 0 || minOpSpeed < 0) {
				LOG_ERROR("基础动力学参数包含无效的非正或负速度/加速度限制。");
				success = false; // 标记为失败
			}
			if (maxYawRate_dps <= 0) { // 新增验证
				LOG_ERROR("基础动力学参数: maxYawRate_dps ({:.2f}) 必须为正。", maxYawRate_dps);
				success = false;
			}
			LOG_DEBUG("基础动力学参数加载{}完成。", success ? "" : "部分或完全");
			return success; // 返回加载是否完全成功且有效
		}

		/** @brief 加载 MultirotorDynamicsParams 参数。*/
		bool MultirotorDynamicsParams::loadFromParamValues(const ParamValues& params) {
			LOG_DEBUG("加载多旋翼特定动力学参数 (前缀: dynamics.mr.)...");
			// 1. 调用基类加载基础参数 (使用 dynamics.base. 前缀)
			bool base_success = IDynamicParams::loadFromParamValues(params);
			// 2. 加载多旋翼特定参数 (如果未来有的话)
			bool specific_success = true; // 当前无特定参数

			// 3. 使用多旋翼特定键覆盖基类加载的值（如果存在）
			maxHVel = params.getValueOrDefault<double>("dynamics.mr.maxHVel", maxHVel);
			maxVVelUp = params.getValueOrDefault<double>("dynamics.mr.maxVVelUp", maxVVelUp);
			maxVVelDown = params.getValueOrDefault<double>("dynamics.mr.maxVVelDown", maxVVelDown);
			maxHAcc = params.getValueOrDefault<double>("dynamics.mr.maxHAcc", maxHAcc);
			maxVAcc = params.getValueOrDefault<double>("dynamics.mr.maxVAcc", maxVAcc);
			maxAlt = params.getValueOrDefault<double>("dynamics.mr.maxAlt", maxAlt);
			maxHDecel = params.getValueOrDefault<double>("dynamics.mr.maxHDecel", maxHAcc); // 默认等于 HAcc
			maxVDecel = params.getValueOrDefault<double>("dynamics.mr.maxVDecel", maxVAcc); // 默认等于 VAcc
			minOpSpeed = params.getValueOrDefault<double>("dynamics.mr.minOpSpeed", 0.0); // 强制为 0

			// 重新验证加载后的值
			if (maxVVelDown < 0) {
				LOG_WARN("多旋翼动力学参数: maxVVelDown ({:.2f}) 应为正值，已取绝对值。", maxVVelDown);
				maxVVelDown = std::abs(maxVVelDown);
			}
			if (maxHVel <= 0 || maxVVelUp <= 0 || maxHAcc <= 0 || maxVAcc <= 0) {
				LOG_ERROR("多旋翼动力学参数包含无效的非正速度或加速度。");
				specific_success = false;
			}

			LOG_DEBUG("多旋翼动力学参数加载完成。总体成功: {}", base_success && specific_success);
			return base_success && specific_success; // 返回整体加载结果
		}

		/** @brief 加载 FixedWingDynamicsParams 参数。*/
		bool FixedWingDynamicsParams::loadFromParamValues(const ParamValues& params) {
			LOG_DEBUG("加载固定翼特定动力学参数 (前缀: dynamics.fw. 或 aero.)..."); // 更新日志以反映可能的 aero 前缀
			// 1. 调用基类加载基础参数
			bool base_success = IDynamicParams::loadFromParamValues(params);
			// 2. 加载固定翼特定参数
			bool specific_success = true;
			maxClimbRate = params.getValueOrDefault<double>("dynamics.fw.maxClimbRate", maxClimbRate);
			maxSinkRate = params.getValueOrDefault<double>("dynamics.fw.maxSinkRate", maxSinkRate);
			maxBankAngle = params.getValueOrDefault<double>("dynamics.fw.maxBankAngle", maxBankAngle);
			gravity = params.getValueOrDefault<double>("dynamics.fw.gravity", gravity);
			// 3. 使用固定翼特定键覆盖基类加载的值
			maxHVel = params.getValueOrDefault<double>("dynamics.fw.maxHVel", maxHVel);
			maxHAcc = params.getValueOrDefault<double>("dynamics.fw.maxHAcc", maxHAcc);
			maxVAcc = params.getValueOrDefault<double>("dynamics.fw.maxVAcc", maxVAcc); // 垂直加/减速能力
			maxAlt = params.getValueOrDefault<double>("dynamics.fw.maxAlt", maxAlt);
			maxHDecel = params.getValueOrDefault<double>("dynamics.fw.maxDecel", maxHDecel); // 如果 JSON 中没有定义，则使用基类的值 (通常等于 maxHAcc)
			minOpSpeed = params.getValueOrDefault<double>("aero.min_airspeed_mps", minOpSpeed); // 修正: 使用 JSON 中定义的键 "aero.min_airspeed_mps"

			// 4. 同步基类的垂直速度（确保与爬升/下降率一致）
			maxVVelUp = maxClimbRate;
			maxVVelDown = maxSinkRate;
			maxVDecel = maxVAcc; // 垂直减速度等于加速度 (如果需要特定垂直减速度，需添加参数)

			// 验证加载后的值
			if (maxClimbRate <= 0 || maxSinkRate <= 0 || maxBankAngle <= 0 || minOpSpeed <= 0 || gravity <= 0) {
				LOG_ERROR("固定翼动力学参数包含无效的非正参数值。");
				specific_success = false;
			}
			if (maxVVelDown < 0) { // 下降率应为正
				LOG_WARN("固定翼动力学参数: maxSinkRate ({:.2f}) 应为正值，已取绝对值。", maxSinkRate);
				maxVVelDown = std::abs(maxSinkRate);
			}
			if (maxHVel <= 0 || maxHAcc <= 0 || maxVAcc <= 0) {
				LOG_ERROR("固定翼动力学参数包含无效的非正速度或加速度。");
				specific_success = false;
			}

			LOG_DEBUG("固定翼动力学参数加载完成。总体成功: {}", base_success && specific_success);
			return base_success && specific_success;
		}

		/** @brief 加载 VtolDynamicsParams 顶层参数。*/
		bool VtolDynamicsParams::loadFromParamValues(const ParamValues& params) {
			LOG_DEBUG("加载VTOL顶层动力学参数 (前缀: dynamics.vtol.)...");
			// 1. 调用基类加载基础参数 (使用 dynamics.base. 前缀)
			bool base_success = IDynamicParams::loadFromParamValues(params);
			// 2. 加载 VTOL 顶层特定参数 (使用 dynamics.vtol. 前缀)
			bool specific_success = true;
			transitionSpeed = params.getValueOrDefault<double>("dynamics.vtol.transitionSpeed", transitionSpeed);
			if (transitionSpeed <= 0) {
				LOG_ERROR("VTOL动力学参数: transitionSpeed 必须为正。");
				specific_success = false;
			}
			// 3. 使用 VTOL 特定的键覆盖基类加载的组合值 (如果提供了 vtol. 前缀的参数)
			maxHVel = params.getValueOrDefault<double>("dynamics.vtol.maxHVel", maxHVel);
			maxVVelUp = params.getValueOrDefault<double>("dynamics.vtol.maxVVelUp", maxVVelUp);
			maxVVelDown = params.getValueOrDefault<double>("dynamics.vtol.maxVVelDown", maxVVelDown);
			maxHAcc = params.getValueOrDefault<double>("dynamics.vtol.maxHAcc", maxHAcc);
			maxVAcc = params.getValueOrDefault<double>("dynamics.vtol.maxVAcc", maxVAcc);
			maxAlt = params.getValueOrDefault<double>("dynamics.vtol.maxAlt", maxAlt);
			maxHDecel = params.getValueOrDefault<double>("dynamics.vtol.maxHDecel", maxHAcc); // 默认等于 HAcc
			maxVDecel = params.getValueOrDefault<double>("dynamics.vtol.maxVDecel", maxVAcc); // 默认等于 VAcc
			minOpSpeed = params.getValueOrDefault<double>("dynamics.vtol.minOpSpeed", 0.0); // 强制为 0

			// 重新验证加载后的值
			if (maxVVelDown < 0) {
				LOG_WARN("VTOL动力学参数: maxVVelDown ({:.2f}) 应为正值，已取绝对值。", maxVVelDown);
				maxVVelDown = std::abs(maxVVelDown);
			}
			if (maxHVel <= 0 || maxVVelUp <= 0 || maxHAcc <= 0 || maxVAcc <= 0) {
				LOG_ERROR("VTOL 动力学参数包含无效的非正速度或加速度。");
				specific_success = false;
			}

			LOG_DEBUG("VTOL顶层动力学参数加载完成。总体成功: {}", base_success && specific_success);
			// 注意：此方法不加载 hover_params 和 fw_params，这两个应由对应的模型自行加载
			return base_success && specific_success;
		}


		/** @brief 加载 IEnergyParams 基类参数。*/
		bool IEnergyParams::loadFromParamValues(const ParamValues& params) {
			LOG_DEBUG("加载基础能量参数 (前缀: energy.base.)...");
			bool success = true; // 假设成功
			// 使用 "energy.base." 作为键前缀
			max_energy_capacity = params.getValueOrDefault<double>("energy.base.max_capacity", max_energy_capacity);
			min_safe_energy_fraction = params.getValueOrDefault<double>("energy.base.min_safe_fraction", min_safe_energy_fraction);
			charging_efficiency = params.getValueOrDefault<double>("energy.base.charging_efficiency", charging_efficiency);
			discharging_efficiency = params.getValueOrDefault<double>("energy.base.discharging_efficiency", discharging_efficiency);
			baseline_power = params.getValueOrDefault<double>("energy.base.baseline_power", baseline_power);

			// 添加范围检查
			if (min_safe_energy_fraction < 0.0 || min_safe_energy_fraction > 1.0) {
				LOG_WARN("基础能量参数: 'min_safe_fraction' ({:.3f}) 超出有效范围 [0, 1]。已限制。", min_safe_energy_fraction);
				min_safe_energy_fraction = std::clamp(min_safe_energy_fraction, 0.0, 1.0);
			}
			if (charging_efficiency <= 0.0 || charging_efficiency > 1.0) { // 效率不能为0或负
				LOG_WARN("基础能量参数: 'charging_efficiency' ({:.3f}) 超出有效范围 (0, 1]。已限制。", charging_efficiency);
				charging_efficiency = std::clamp(charging_efficiency, Constants::EPSILON, 1.0); // 限制在 (0, 1]
			}
			if (discharging_efficiency <= 0.0 || discharging_efficiency > 1.0) {
				LOG_WARN("基础能量参数: 'discharging_efficiency' ({:.3f}) 超出有效范围 (0, 1]。已限制。", discharging_efficiency);
				discharging_efficiency = std::clamp(discharging_efficiency, Constants::EPSILON, 1.0);
			}
			if (max_energy_capacity <= 0) {
				LOG_ERROR("基础能量参数: 'max_capacity' ({:.2f}) 必须为正。", max_energy_capacity);
				success = false; // 标记为失败
			}
			if (baseline_power < 0) { 
				LOG_ERROR("基础能量参数: 'baseline_power' ({:.2f}) 不能为负。", baseline_power);
				success = false;
			}
			LOG_DEBUG("基础能量参数加载{}完成。", success ? "" : "部分或完全");
			return success; // 返回加载是否完全成功且有效
		}

		/** @brief 加载 MultirotorEnergyParams 参数。*/
		bool MultirotorEnergyParams::loadFromParamValues(const ParamValues& params) {
			LOG_DEBUG("加载多旋翼特定能量参数 (前缀: energy.mr.)...");
			// 1. 调用基类加载基础参数 (使用 energy.base. 前缀)
			bool base_success = IEnergyParams::loadFromParamValues(params);
			// 2. 加载多旋翼特定参数 (使用 energy.mr. 前缀)
			bool specific_success = true;
			hover_power = params.getValueOrDefault<double>("energy.mr.hover_power", hover_power);
			vertical_power_factor = params.getValueOrDefault<double>("energy.mr.vertical_power_factor", vertical_power_factor);
			horizontal_power_factor = params.getValueOrDefault<double>("energy.mr.horizontal_power_factor", horizontal_power_factor);
			baseline_power = params.getValueOrDefault<double>("energy.mr.baseline_power", baseline_power);
			endurance_est_max_v_speed = params.getValueOrDefault<double>("energy.mr.endurance_est_max_v_speed", endurance_est_max_v_speed);
			// 3. 使用多旋翼特定键覆盖基类加载的值 (如果存在)
			max_energy_capacity = params.getValueOrDefault<double>("energy.mr.max_capacity", max_energy_capacity);
			min_safe_energy_fraction = params.getValueOrDefault<double>("energy.mr.min_safe_fraction", min_safe_energy_fraction);
			charging_efficiency = params.getValueOrDefault<double>("energy.mr.charging_efficiency", charging_efficiency);
			discharging_efficiency = params.getValueOrDefault<double>("energy.mr.discharging_efficiency", discharging_efficiency);

			// 验证加载后的值
			if (hover_power <= 0 || vertical_power_factor < 0 || horizontal_power_factor < 0 || baseline_power < 0) {
				LOG_WARN("多旋翼能量参数包含无效的功率参数值。");
				specific_success = false;
			}
			// 重新验证基类参数
			if (min_safe_energy_fraction < 0.0 || min_safe_energy_fraction > 1.0) {
				LOG_WARN("多旋翼能量参数 'min_safe_fraction' ({:.3f}) 超出有效范围 [0, 1]。已限制。", min_safe_energy_fraction);
				min_safe_energy_fraction = std::clamp(min_safe_energy_fraction, 0.0, 1.0);
			}
			if (charging_efficiency <= 0.0 || charging_efficiency > 1.0) {
				LOG_WARN("多旋翼能量参数 'charging_efficiency' ({:.3f}) 超出有效范围 (0, 1]。已限制。", charging_efficiency);
				charging_efficiency = std::clamp(charging_efficiency, Constants::EPSILON, 1.0);
			}
			if (discharging_efficiency <= 0.0 || discharging_efficiency > 1.0) {
				LOG_WARN("多旋翼能量参数 'discharging_efficiency' ({:.3f}) 超出有效范围 (0, 1]。已限制。", discharging_efficiency);
				discharging_efficiency = std::clamp(discharging_efficiency, Constants::EPSILON, 1.0);
			}
			if (max_energy_capacity <= 0) {
				LOG_ERROR("多旋翼能量参数 'max_capacity' ({:.2f}) 必须为正。", max_energy_capacity);
				specific_success = false;
			}


			LOG_DEBUG("多旋翼能量参数加载完成。总体成功: {}", base_success && specific_success);
			return base_success && specific_success; // 必须基类和子类都成功
		}

		/** @brief 加载 FixedWingEnergyParams 参数。*/
		bool FixedWingEnergyParams::loadFromParamValues(const ParamValues& params) {
			LOG_DEBUG("加载固定翼特定能量参数 (前缀: energy.fw.)...");
			// 1. 调用基类加载基础参数
			bool base_success = IEnergyParams::loadFromParamValues(params);
			// 2. 加载固定翼特定参数
			bool specific_success = true;
			mass = params.getValueOrDefault<double>("energy.fw.mass", mass);
			wing_area = params.getValueOrDefault<double>("aero.wing_area_m2", wing_area);
			drag_coeff_zero_lift = params.getValueOrDefault<double>("aero.drag_coeff_zero_lift", drag_coeff_zero_lift);
			oswald_efficiency = params.getValueOrDefault<double>("energy.fw.oswald_efficiency", oswald_efficiency);
			prop_efficiency = params.getValueOrDefault<double>("energy.fw.prop_efficiency", prop_efficiency);
			aspect_ratio = params.getValueOrDefault<double>("aero.aspect_ratio", aspect_ratio);
			baseline_power = params.getValueOrDefault<double>("energy.fw.baseline_power", baseline_power);
			air_density = params.getValueOrDefault<double>("environment.air_density_kg_m3", air_density);
			cruise_speed_est = params.getValueOrDefault<double>("energy.fw.cruise_speed_est", cruise_speed_est);
			// 3. 使用固定翼特定键覆盖基类加载的值
			max_energy_capacity = params.getValueOrDefault<double>("energy.fw.max_capacity", max_energy_capacity);
			min_safe_energy_fraction = params.getValueOrDefault<double>("energy.fw.min_safe_fraction", min_safe_energy_fraction);
			charging_efficiency = params.getValueOrDefault<double>("energy.fw.charging_efficiency", charging_efficiency);
			discharging_efficiency = params.getValueOrDefault<double>("energy.fw.discharging_efficiency", discharging_efficiency);

			// 验证加载后的值
			if (mass <= 0 || wing_area <= 0 || aspect_ratio <= 0 || baseline_power < 0 || air_density <= 0 || gravity <= 0) {
				LOG_ERROR("固定翼能量参数包含无效的非正值 (质量/面积/展弦比/基线功率/密度/重力)。");
				specific_success = false;
			}
			if (prop_efficiency <= 0 || prop_efficiency > 1) {
				LOG_ERROR("固定翼能量参数 'prop_efficiency' ({:.3f}) 超出有效范围 (0, 1]。", prop_efficiency);
				specific_success = false;
			}
			if (oswald_efficiency <= 0 || oswald_efficiency > 1) {
				LOG_ERROR("固定翼能量参数 'oswald_efficiency' ({:.3f}) 超出有效范围 (0, 1]。", oswald_efficiency);
				specific_success = false;
			}
			// 重新验证基类参数
			if (min_safe_energy_fraction < 0.0 || min_safe_energy_fraction > 1.0) {
				LOG_WARN("固定翼能量参数 'min_safe_fraction' ({:.3f}) 超出有效范围 [0, 1]。已限制。", min_safe_energy_fraction);
				min_safe_energy_fraction = std::clamp(min_safe_energy_fraction, 0.0, 1.0);
			}
			if (charging_efficiency <= 0.0 || charging_efficiency > 1.0) {
				LOG_WARN("固定翼能量参数 'charging_efficiency' ({:.3f}) 超出有效范围 (0, 1]。已限制。", charging_efficiency);
				charging_efficiency = std::clamp(charging_efficiency, Constants::EPSILON, 1.0);
			}
			if (discharging_efficiency <= 0.0 || discharging_efficiency > 1.0) {
				LOG_WARN("固定翼能量参数 'discharging_efficiency' ({:.3f}) 超出有效范围 (0, 1]。已限制。", discharging_efficiency);
				discharging_efficiency = std::clamp(discharging_efficiency, Constants::EPSILON, 1.0);
			}
			if (max_energy_capacity <= 0) {
				LOG_ERROR("固定翼能量参数 'max_capacity' ({:.2f}) 必须为正。", max_energy_capacity);
				specific_success = false;
			}


			LOG_DEBUG("固定翼能量参数加载完成。总体成功: {}", base_success && specific_success);
			return base_success && specific_success;
		}

		/** @brief 加载 VtolEnergyParams 顶层参数。*/
		bool VtolEnergyParams::loadFromParamValues(const ParamValues& params) {
			LOG_DEBUG("加载VTOL顶层能量参数 (前缀: energy.vtol.)...");
			// 1. 调用基类加载基础参数 (使用 energy.base. 前缀)
			bool base_success = IEnergyParams::loadFromParamValues(params);
			// 2. 加载 VTOL 特有顶层参数 (使用 energy.vtol. 前缀)
			bool specific_success = true;
			transition_avg_power = params.getValueOrDefault<double>("energy.vtol.transition_avg_power", transition_avg_power);
			transition_duration_estimation = params.getValueOrDefault<double>("energy.vtol.transition_duration_estimation", transition_duration_estimation);
			if (transition_avg_power <= 0 || transition_duration_estimation <= 0) {
				LOG_WARN("VtolEnergyParams: 过渡功率或时间非正。");
				specific_success = false;
			}
			// 3. 使用 VTOL 特定的键覆盖基类加载的组合值
			max_energy_capacity = params.getValueOrDefault<double>("energy.vtol.max_capacity", max_energy_capacity);
			min_safe_energy_fraction = params.getValueOrDefault<double>("energy.vtol.min_safe_fraction", min_safe_energy_fraction);
			charging_efficiency = params.getValueOrDefault<double>("energy.vtol.charging_efficiency", charging_efficiency);
			discharging_efficiency = params.getValueOrDefault<double>("energy.vtol.discharging_efficiency", discharging_efficiency);

			// 重新验证基类参数
			if (min_safe_energy_fraction < 0.0 || min_safe_energy_fraction > 1.0) {
				LOG_WARN("VTOL能量参数 'min_safe_fraction' ({:.3f}) 超出有效范围 [0, 1]。已限制。", min_safe_energy_fraction);
				min_safe_energy_fraction = std::clamp(min_safe_energy_fraction, 0.0, 1.0);
			}
			if (charging_efficiency <= 0.0 || charging_efficiency > 1.0) {
				LOG_WARN("VTOL能量参数 'charging_efficiency' ({:.3f}) 超出有效范围 (0, 1]。已限制。", charging_efficiency);
				charging_efficiency = std::clamp(charging_efficiency, Constants::EPSILON, 1.0);
			}
			if (discharging_efficiency <= 0.0 || discharging_efficiency > 1.0) {
				LOG_WARN("VTOL能量参数 'discharging_efficiency' ({:.3f}) 超出有效范围 (0, 1]。已限制。", discharging_efficiency);
				discharging_efficiency = std::clamp(discharging_efficiency, Constants::EPSILON, 1.0);
			}
			if (max_energy_capacity <= 0) {
				LOG_ERROR("VTOL 能量参数 'max_capacity' ({:.2f}) 必须为正。", max_energy_capacity);
				specific_success = false;
			}


			LOG_DEBUG("VTOL顶层能量参数加载完成。总体成功: {}", base_success && specific_success);
			// 注意：此方法不加载 hover 和 fw 的子参数，这些由对应模型负责加载
			return base_success && specific_success;
		}


	} // namespace NSParams
} // namespace NSDrones