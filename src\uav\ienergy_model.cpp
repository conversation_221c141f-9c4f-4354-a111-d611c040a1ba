// src/uav/ienergy_model.cpp
#include "uav/ienergy_model.h"
#include "uav/uav.h" 
#include "utils/logging.h"   
#include <cmath>              
#include <limits>             
#include <algorithm>          
#include <sstream>           

namespace NSDrones {
	namespace NSUav {

		// --- 构造函数 ---
		/**
		 * @brief IDynamicModel 构造函数。
		 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
		 */
		IEnergyModel::IEnergyModel(const Uav& owner) : owner_(owner) {
			LOG_TRACE("IEnergyModel 基类已构造，所有者 ID: {}", owner_.getId());
		}

		double IEnergyModel::getMaxEnergyCapacity() const {
			// 从 owner_ 对象获取参数，如果找不到则使用默认值 0.0
			// 键名示例: "energy.base.max_capacity" 或特定类型如 "energy.mr.max_capacity"
			// 优先查找特定类型，然后回退到基类
			double capacity = owner_.getParamOrDefault<double>("energy.base.max_capacity", 0.0); // 获取基类参数

			// 验证容量是否为正
			if (capacity <= Constants::EPSILON) { // 使用 epsilon 检查是否接近零或负数
				LOG_WARN_ONCE("能量模型参数中的最大能量容量 ({:.2f}) 无效或非正 (来自参数 'energy.base.max_capacity')。所有者 ID: {}", capacity, owner_.getId());
				return 0.0; // 返回 0 表示无效
			}
			LOG_TRACE("获取最大能量容量 (所有者 ID: {})：{:.2f}", owner_.getId(), capacity);
			return capacity; // 返回获取到的有效容量
		}

		double IEnergyModel::getMinSafeEnergyLevel() const {
			double capacity = getMaxEnergyCapacity(); // 使用 getter 获取有效容量
			if (capacity <= Constants::EPSILON) {
				LOG_TRACE("获取最低安全能量：容量无效 ({:.2f})，返回 0。", capacity);
				return 0.0; // 容量无效，安全水平也为 0
			}

			// 从 owner_ 获取最低安全比例参数，如果找不到则使用默认值 0.0
			// 键名示例: "energy.base.min_safe_fraction" 或特定类型如 "energy.mr.min_safe_fraction"
			double fraction = owner_.getParamOrDefault<double>("energy.base.min_safe_fraction", 0.0);

			// 确保安全比例在 [0, 1] 范围内
			double original_fraction = fraction; // 保存原始值用于日志
			fraction = std::clamp(fraction, 0.0, 1.0);
			// 可选：如果参数值被限制，可以记录警告
			if (std::abs(fraction - original_fraction) > Constants::EPSILON) {
				LOG_WARN_ONCE("能量模型参数中的最低安全能量比例 ({:.3f}) 超出 [0,1] 范围，已限制为 {:.3f}。(所有者 ID: {})", original_fraction, fraction, owner_.getId());
			}

			double safe_level = capacity * fraction; // 计算安全能量水平
			LOG_TRACE("获取最低安全能量 (所有者 ID: {})：容量={:.2f}, 比例={:.3f}, 安全水平={:.2f}", owner_.getId(), capacity, fraction, safe_level);
			return safe_level;
		}

	} // namespace NSUav
} // namespace NSDrones