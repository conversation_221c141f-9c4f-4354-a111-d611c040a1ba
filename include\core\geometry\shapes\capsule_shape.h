#pragma once

#include "core/geometry/ishape.h"
#include <fcl/geometry/shape/capsule.h>

namespace NSDrones {
namespace NSCore {

    /**
     * @brief 胶囊体形状
     * 
     * 表示一个胶囊体（圆柱体两端加半球），以Z轴为中心轴。
     * 几何中心位于局部坐标系原点。
     * 胶囊体的轴沿Z轴方向，总长度为height + 2*radius。
     */
    class CapsuleShape : public IShape {
    private:
        double radius_;  // 胶囊体半径
        double height_;  // 圆柱部分高度（不包括两端半球）
        
        mutable std::shared_ptr<fcl::Capsuled> fcl_capsule_;  // FCL几何对象（延迟创建）

    public:
        /**
         * @brief 构造函数
         * @param radius 胶囊体半径
         * @param height 圆柱部分高度
         */
        CapsuleShape(double radius, double height);

        /**
         * @brief 默认构造函数（单位胶囊）
         */
        CapsuleShape();

        /**
         * @brief 拷贝构造函数
         */
        CapsuleShape(const CapsuleShape& other);

        /**
         * @brief 赋值操作符
         */
        CapsuleShape& operator=(const CapsuleShape& other);

        // IShape接口实现
        ShapeType getType() const override { return ShapeType::CAPSULE; }
        int getDimension() const override { return 3; }  // 3D实体
        std::shared_ptr<fcl::CollisionGeometryd> getFCLGeometry() const override;
        fcl::AABBd getAABB(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const override;
        double getVolume() const override;
        double getSurfaceArea() const override;
        fcl::Vector3d getCentroid() const override;
        fcl::Matrix3d getInertiaMatrix(double mass) const override;
        std::unique_ptr<IShape> clone() const override;
        nlohmann::json serialize() const override;
        bool deserialize(const nlohmann::json& json) override;
        std::string toString() const override;
        bool containsPoint(const fcl::Vector3d& point) const override;
        double distanceToPoint(const fcl::Vector3d& point) const override;
        double getCharacteristicSize() const override;

        // CapsuleShape特有方法
        /**
         * @brief 获取半径
         */
        double getRadius() const { return radius_; }

        /**
         * @brief 获取圆柱部分高度
         */
        double getHeight() const { return height_; }

        /**
         * @brief 获取总长度（包括两端半球）
         */
        double getTotalLength() const { return height_ + 2.0 * radius_; }

        /**
         * @brief 获取直径
         */
        double getDiameter() const { return 2.0 * radius_; }

        /**
         * @brief 设置尺寸
         * @param radius 半径
         * @param height 圆柱部分高度
         */
        void setSize(double radius, double height);

        /**
         * @brief 获取圆柱部分体积
         */
        double getCylinderVolume() const;

        /**
         * @brief 获取球形部分体积
         */
        double getSphereVolume() const;

        /**
         * @brief 获取圆柱部分表面积
         */
        double getCylinderSurfaceArea() const;

        /**
         * @brief 获取球形部分表面积
         */
        double getSphereSurfaceArea() const;

        /**
         * @brief 计算胶囊面上的点
         * @param theta 角度（0到2π）
         * @param z Z坐标（-getTotalLength()/2到+getTotalLength()/2）
         * @param transform 变换矩阵
         * @return 胶囊面上的点
         */
        fcl::Vector3d getPointOnCapsule(double theta, double z,
                                       const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 生成胶囊面上的均匀采样点
         * @param num_circumference 圆周方向点数
         * @param num_height 高度方向点数
         * @param transform 变换矩阵
         * @return 点列表
         */
        std::vector<fcl::Vector3d> generateUniformPoints(size_t num_circumference, size_t num_height,
                                                         const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 计算表面法向量（在给定点处）
         * @param point 表面上的点
         * @param transform 变换矩阵
         * @return 法向量
         */
        fcl::Vector3d getSurfaceNormal(const fcl::Vector3d& point,
                                      const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 创建单位胶囊（半径0.5，高度1）
         * @return CapsuleShape实例
         */
        static std::unique_ptr<CapsuleShape> createUnitCapsule();

        /**
         * @brief 从直径创建胶囊
         * @param diameter 直径
         * @param height 圆柱部分高度
         * @return CapsuleShape实例
         */
        static std::unique_ptr<CapsuleShape> createFromDiameter(double diameter, double height);

        /**
         * @brief 从总长度创建胶囊
         * @param radius 半径
         * @param total_length 总长度
         * @return CapsuleShape实例
         */
        static std::unique_ptr<CapsuleShape> createFromTotalLength(double radius, double total_length);

    private:
        /**
         * @brief 确保FCL对象已创建
         */
        void ensureFCLObject() const;

        /**
         * @brief 验证尺寸参数
         */
        void validateDimensions() const;
    };

} // namespace NSCore
} // namespace NSDrones
