#pragma once

#include "core/geometry/ishape.h"
#include <fcl/geometry/shape/cylinder.h>

namespace NSDrones{
namespace NSCore {

    /**
     * @brief 圆柱体形状
     * 
     * 表示一个以Z轴为中心轴的圆柱体，几何中心位于局部坐标系原点。
     * 圆柱体的轴沿Z轴方向，从-height/2到+height/2。
     */
    class CylinderShape : public IShape {
    private:
        double radius_;  // 圆柱体半径
        double height_;  // 圆柱体高度
        
        mutable std::shared_ptr<fcl::Cylinderd> fcl_cylinder_;  // FCL几何对象（延迟创建）

    public:
        /**
         * @brief 构造函数
         * @param radius 圆柱体半径
         * @param height 圆柱体高度
         */
        CylinderShape(double radius, double height);

        /**
         * @brief 默认构造函数（单位圆柱）
         */
        CylinderShape();

        /**
         * @brief 拷贝构造函数
         */
        CylinderShape(const CylinderShape& other);

        /**
         * @brief 赋值操作符
         */
        CylinderShape& operator=(const CylinderShape& other);

        // IShape接口实现
        ShapeType getType() const override { return ShapeType::CYLINDER; }
        int getDimension() const override { return 3; }  // 3D实体
        std::shared_ptr<fcl::CollisionGeometryd> getFCLGeometry() const override;
        fcl::AABBd getAABB(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const override;
        double getVolume() const override;
        double getSurfaceArea() const override;
        fcl::Vector3d getCentroid() const override;
        fcl::Matrix3d getInertiaMatrix(double mass) const override;
        std::unique_ptr<IShape> clone() const override;
        nlohmann::json serialize() const override;
        bool deserialize(const nlohmann::json& json) override;
        std::string toString() const override;
        bool containsPoint(const fcl::Vector3d& point) const override;
        double distanceToPoint(const fcl::Vector3d& point) const override;
        double getCharacteristicSize() const override;

        // CylinderShape特有方法
        /**
         * @brief 获取半径
         */
        double getRadius() const { return radius_; }

        /**
         * @brief 获取高度
         */
        double getHeight() const { return height_; }

        /**
         * @brief 获取直径
         */
        double getDiameter() const { return 2.0 * radius_; }

        /**
         * @brief 设置尺寸
         * @param radius 半径
         * @param height 高度
         */
        void setSize(double radius, double height);

        /**
         * @brief 获取底面积
         */
        double getBaseArea() const;

        /**
         * @brief 获取侧面积
         */
        double getLateralArea() const;

        /**
         * @brief 计算圆柱面上的点
         * @param theta 角度（0到2π）
         * @param z Z坐标（-height/2到+height/2）
         * @param transform 变换矩阵
         * @return 圆柱面上的点
         */
        fcl::Vector3d getPointOnCylinder(double theta, double z,
                                        const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 生成圆柱面上的均匀采样点
         * @param num_circumference 圆周方向点数
         * @param num_height 高度方向点数
         * @param include_caps 是否包含顶底面
         * @param transform 变换矩阵
         * @return 点列表
         */
        std::vector<fcl::Vector3d> generateUniformPoints(size_t num_circumference, size_t num_height, 
                                                         bool include_caps = true,
                                                         const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 计算表面法向量（在给定点处）
         * @param point 表面上的点
         * @param transform 变换矩阵
         * @return 法向量
         */
        fcl::Vector3d getSurfaceNormal(const fcl::Vector3d& point,
                                      const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 创建单位圆柱（半径1，高度2）
         * @return CylinderShape实例
         */
        static std::unique_ptr<CylinderShape> createUnitCylinder();

        /**
         * @brief 从直径创建圆柱
         * @param diameter 直径
         * @param height 高度
         * @return CylinderShape实例
         */
        static std::unique_ptr<CylinderShape> createFromDiameter(double diameter, double height);

    private:
        /**
         * @brief 确保FCL对象已创建
         */
        void ensureFCLObject() const;

        /**
         * @brief 验证尺寸参数
         */
        void validateDimensions() const;
    };

} // namespace NSCore
} // namespace NSDrones