﻿  task_allocator.cpp
  config.cpp
  main.cpp
  itask_planner.cpp
E:\source\dronesplanning\src\planning\itask_planner.cpp(475,24): error C2039: "getCurrentState": 不是 "NSDrones::NSUav::Uav" 的成员
E:\source\dronesplanning\include\uav/uav.h(51,9): message : 参见“NSDrones::NSUav::Uav”的声明
  mission_planner.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(275,56): error C2280: “NSDrones::NSMission::Task::Task(const NSDrones::NSMission::Task &)”: 尝试引用已删除的函数
E:\source\dronesplanning\include\mission/task.h(55,4): message : 参见“NSDrones::NSMission::Task::Task”的声明
E:\source\dronesplanning\include\mission/task.h(55,4): message : “NSDrones::NSMission::Task::Task(const NSDrones::NSMission::Task &)”: 已隐式删除函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1983): message : 查看对正在编译的函数 模板 实例化“void std::_Construct_in_place<_Ty,const NSDrones::NSMission::Task&>(_Ty &,const NSDrones::NSMission::Task &) noexcept(false)”的引用
          with
          [
              _Ty=NSDrones::NSMission::Task
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(2663): message : 查看对正在编译的函数 模板 实例化“std::_Ref_count_obj2<_Ty>::_Ref_count_obj2<const NSDrones::NSMission::Task&>(const NSDrones::NSMission::Task &)”的引用
          with
          [
              _Ty=NSDrones::NSMission::Task
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(2664,20): message : 查看对正在编译的函数 模板 实例化“std::_Ref_count_obj2<_Ty>::_Ref_count_obj2<const NSDrones::NSMission::Task&>(const NSDrones::NSMission::Task &)”的引用
          with
          [
              _Ty=NSDrones::NSMission::Task
          ]
E:\source\dronesplanning\src\planning\mission_planner.cpp(1227,63): message : 查看对正在编译的函数 模板 实例化“std::shared_ptr<NSDrones::NSMission::Task> std::make_shared<NSDrones::NSMission::Task,const NSDrones::NSMission::Task&>(const NSDrones::NSMission::Task &)”的引用
  planning_types.cpp
  followpath_taskplanner.cpp
  loiterpoint_taskplanner.cpp
  scanarea_taskplanner.cpp
  surveycylinder_taskplanner.cpp
  surveymultipoints_taskplanner.cpp
  surveysphere_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(269,23): error C2676: 二进制“[”:“NSDrones::NSPlanning::Trajectory”不定义该运算符或到预定义运算符可接收的类型的转换
  fixedwing_dynamics.cpp
  vtol_dynamics.cpp
  uav_config.cpp
  orientation_utils.cpp
  正在生成代码...
