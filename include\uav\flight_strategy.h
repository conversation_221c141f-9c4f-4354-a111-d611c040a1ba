#pragma once

#include "core/movement_strategy.h"
#include "core/types.h" 
#include "uav/uav_fwd.h" 

// Forward declare Environment if not fully included via other headers
namespace NSDrones { namespace NSEnvironment { class Environment; } }

namespace NSDrones {
namespace NSUav {

// Forward declare UAV if uav_fwd.h doesn't cover it or to be explicit
class Uav; 

/**
 * @class FlightStrategy
 * @brief A movement strategy specifically for UAVs, using their internal dynamic models.
 *        This strategy is intended to be called via EntityObject::autoStateUpdate().
 */
class FlightStrategy : public IMovementStrategy {
public:
    explicit FlightStrategy(const EntityObject& owner);
    ~FlightStrategy() override = default;

    // 禁止拷贝和移动
    FlightStrategy(const FlightStrategy&) = delete;
    FlightStrategy& operator=(const FlightStrategy&) = delete;
    FlightStrategy(FlightStrategy&&) = delete;
    FlightStrategy& operator=(FlightStrategy&&) = delete;

    /**
     * @brief 初始化飞行策略。
     * @param params JSON 参数对象 (当前未使用，但为接口保留)。
     * @param owner 拥有此策略的 EntityObject，期望是 UAV 类型。
     * @return 初始化成功返回 true。
     */
    bool initialize(const nlohmann::json& params, EntityObject& owner) override;

    /**
     * @brief 更新UAV的状态。
     *        此方法由 EntityObject::autoStateUpdate 调用。
     *        它将使用UAV的动力学模型和时间步长 dt 来演化状态。
     * @param object 正在更新状态的 EntityObject (必须是 UAV)。
     * @param dt 时间步长 (秒)。
     * @note 环境信息通过单例模式获取，无需直接传入。
     */
    void updateState(EntityObject& object, Time dt) override;

    /**
     * @brief 预测UAV在未来 dt 时间后的WGS84位置。
     * @param object EntityObject (必须是 UAV)。
     * @param dt 时间步长 (秒)。
     * @return 预测的WGS84位置。
     * @note 环境信息通过单例模式获取，无需直接传入。
     */
    WGS84Point predictWGS84Position(const EntityObject& object, Time dt) const override;

private:
    Uav* owner_uav_ = nullptr; ///< 指向拥有此策略的UAV对象的指针 (在initialize中设置)
    double default_cruise_speed_ = 5.0; // 增加声明并设置默认值
    double target_altitude_agl_ = 50.0; // 增加声明并设置默认值
    // 此处可以添加策略特定的参数，例如目标速度、导航模式等，
    // 这些参数可以在 initialize 时从 JSON 加载。
};

} // namespace NSUav
} // namespace NSDrones 