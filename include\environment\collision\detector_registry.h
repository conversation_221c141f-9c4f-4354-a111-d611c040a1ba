// include/environment/collision/detector_registry.h
#pragma once

#include "environment/collision/collision_types.h"
#include "core/geometry/ishape.h"
#include "utils/enum_utils.h"
#include "utils/logging.h"
#include <fcl/fcl.h>
#include <array>
#include <functional>
#include <memory>
#include <typeindex>

namespace NSDrones {
	namespace NSEnvironment {

		/**
		 * @class DetectorRegistry
		 * @brief 碰撞检测器注册表
		 *
		 * 基于FCL库的统一碰撞检测管理器，提供高性能的几何碰撞检测服务。
		 * 支持多种几何形状间的碰撞检测、距离计算和接触点分析。
		 *
		 * 特性：
		 * - 基于FCL库的高性能碰撞检测
		 * - 统一的检测接口，支持所有几何形状组合
		 * - 详细的碰撞信息提取（接触点、法线、穿透深度）
		 * - 可配置的检测选项和性能优化
		 */
		class DetectorRegistry {
		public:
			/**
			 * @brief 检测器函数类型定义
			 *
			 * 标准的碰撞检测函数签名，接受两个FCL碰撞对象和检测选项，
			 * 返回详细的碰撞检测结果。
			 */
			using DetectorFunction = std::function<CollisionResult(
				const fcl::CollisionObjectd*,
				const fcl::CollisionObjectd*,
				const CollisionOptions&)>;

			/**
			 * @brief 构造函数
			 * 初始化基于FCL的碰撞检测器注册表
			 */
			DetectorRegistry() {
				LOG_INFO("碰撞检测器注册表: 初始化完成，使用FCL库进行高性能碰撞检测");
			}

			/**
			 * @brief 获取碰撞检测器
			 * @param shapeType1 第一个形状类型
			 * @param shapeType2 第二个形状类型
			 * @return FCL通用检测器函数
			 *
			 * 当前版本使用统一的FCL检测器处理所有形状组合，
			 * 未来可根据性能需求添加特定形状对的优化检测器。
			 */
			DetectorFunction findDetector(ShapeType shapeType1, ShapeType shapeType2) const {
				LOG_TRACE("碰撞检测器注册表: 为 {} vs {} 获取检测器",
					NSUtils::enumToString(shapeType1), NSUtils::enumToString(shapeType2));

				// 返回统一的FCL检测器
				return [this](
					const fcl::CollisionObjectd* obj1,
					const fcl::CollisionObjectd* obj2,
					const CollisionOptions& options) -> CollisionResult {
						return performFCLDetection(obj1, obj2, options);
				};

			}

			/**
			 * @brief 执行碰撞检测
			 * @param obj1 第一个FCL碰撞对象
			 * @param obj2 第二个FCL碰撞对象
			 * @param shapeType1 第一个形状类型（用于日志记录）
			 * @param shapeType2 第二个形状类型（用于日志记录）
			 * @param options 碰撞检测选项
			 * @return 详细的碰撞检测结果
			 */
			CollisionResult detectCollision(
				const fcl::CollisionObjectd* obj1,
				const fcl::CollisionObjectd* obj2,
				ShapeType shapeType1,
				ShapeType shapeType2,
				const CollisionOptions& options
			) const {
				if (!obj1 || !obj2) {
					LOG_ERROR("碰撞检测器注册表: 输入的碰撞对象为空指针");
					return CollisionResult{};
				}

				LOG_TRACE("碰撞检测器注册表: 执行 {} vs {} 碰撞检测",
					NSUtils::enumToString(shapeType1), NSUtils::enumToString(shapeType2));

				auto detector = findDetector(shapeType1, shapeType2);
				return detector(obj1, obj2, options);
			}

		private:
			/**
			 * @brief 执行FCL碰撞检测
			 * @param obj1 第一个FCL碰撞对象
			 * @param obj2 第二个FCL碰撞对象
			 * @param options 碰撞检测选项
			 * @return 碰撞检测结果
			 */
			CollisionResult performFCLDetection(
				const fcl::CollisionObjectd* obj1,
				const fcl::CollisionObjectd* obj2,
				const CollisionOptions& options
			) const {
				CollisionResult result;
				result.collisionSource = "FCL通用检测器";

				try {
					// 执行碰撞检测
					if (performCollisionCheck(obj1, obj2, options, result)) {
						LOG_TRACE("FCL检测器: 检测到碰撞，穿透深度: {:.4f}米", result.penetrationDepth);
					}

					// 执行距离计算（如果需要）
					if (options.enableDistance) {
						performDistanceCheck(obj1, obj2, options, result);
						LOG_TRACE("FCL检测器: 最小距离: {:.4f}米", result.distance);
					}
				}
				catch (const std::exception& e) {
					LOG_ERROR("FCL检测器: 检测过程中发生异常: {}", e.what());
					result = CollisionResult{};  // 返回空结果
				}

				return result;
			}

			/**
			 * @brief 执行FCL碰撞检查
			 * @param obj1 第一个FCL碰撞对象
			 * @param obj2 第二个FCL碰撞对象
			 * @param options 碰撞检测选项
			 * @param result 碰撞结果（输出参数）
			 * @return 如果检测到碰撞返回true
			 */
			bool performCollisionCheck(
				const fcl::CollisionObjectd* obj1,
				const fcl::CollisionObjectd* obj2,
				const CollisionOptions& options,
				CollisionResult& result
			) const {
				fcl::CollisionRequestd collision_request(options.maxContacts, options.enableContact);
				fcl::CollisionResultd fcl_collision_result;

				fcl::collide(obj1, obj2, collision_request, fcl_collision_result);

				if (fcl_collision_result.isCollision()) {
					result.hasCollision = true;
					extractContactInformation(fcl_collision_result, options, result);
					return true;
				}

				return false;
			}

			/**
			 * @brief 执行FCL距离计算
			 * @param obj1 第一个FCL碰撞对象
			 * @param obj2 第二个FCL碰撞对象
			 * @param options 碰撞检测选项
			 * @param result 碰撞结果（输出参数）
			 */
			void performDistanceCheck(
				const fcl::CollisionObjectd* obj1,
				const fcl::CollisionObjectd* obj2,
				const CollisionOptions& options,
				CollisionResult& result
			) const {
				fcl::DistanceRequestd distance_request(options.enableNearestPoints);
				fcl::DistanceResultd fcl_distance_result;

				// 配置距离计算选项
				distance_request.gjk_solver_type = fcl::GJKSolverType::GST_LIBCCD;
				distance_request.enable_signed_distance = true;

				fcl::distance(obj1, obj2, distance_request, fcl_distance_result);
				result.distance = fcl_distance_result.min_distance;

				if (options.enableNearestPoints && result.distance < std::numeric_limits<double>::infinity()) {
					result.setNearestPoints(
						EcefPoint(fcl_distance_result.nearest_points[0][0],
							fcl_distance_result.nearest_points[0][1],
							fcl_distance_result.nearest_points[0][2]),
						EcefPoint(fcl_distance_result.nearest_points[1][0],
							fcl_distance_result.nearest_points[1][1],
							fcl_distance_result.nearest_points[1][2]),
						result.distance
					);
				}
			}

			/**
			 * @brief 提取接触点信息
			 * @param fcl_result FCL碰撞结果
			 * @param options 碰撞检测选项
			 * @param result 碰撞结果（输出参数）
			 */
			void extractContactInformation(
				fcl::CollisionResultd& fcl_result,
				const CollisionOptions& options,
				CollisionResult& result
			) const {
				std::vector<fcl::Contactd> contacts;
				fcl_result.getContacts(contacts);

				result.penetrationDepth = 0.0;
				unsigned int num_contacts = std::min(static_cast<unsigned int>(contacts.size()), options.maxContacts);

				for (unsigned int i = 0; i < num_contacts; ++i) {
					const auto& contact = contacts[i];
					result.addContact(
						EcefPoint(contact.pos[0], contact.pos[1], contact.pos[2]),
						Vector3D(contact.normal[0], contact.normal[1], contact.normal[2])
					);

					// 记录最大穿透深度
					if (contact.penetration_depth > result.penetrationDepth) {
						result.penetrationDepth = contact.penetration_depth;
					}
				}

				LOG_TRACE("FCL检测器: 提取了 {} 个接触点", num_contacts);
			}
		};

	} // namespace NSEnvironment
} // namespace NSDrones
