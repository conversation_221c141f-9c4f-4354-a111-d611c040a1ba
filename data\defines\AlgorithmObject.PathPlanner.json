{"description": "Parameters for Path Planner algorithms. This defines common parameters shared by all path planner implementations.", "parameters": [{"key": "planning_timeout_ms", "name": "规划超时时间", "description": "路径规划的最大允许时间 (毫秒)。", "type": "int", "default": 10000, "constraints": {"type": "numeric", "min": 1000, "max": 60000}, "required": false}, {"key": "collision_check_resolution", "name": "碰撞检测分辨率", "description": "碰撞检测的空间分辨率 (米)。", "type": "double", "default": 0.5, "constraints": {"type": "numeric", "min": 0.1, "max": 5.0}, "required": false}, {"key": "enable_smoothing", "name": "启用路径平滑", "description": "是否对生成的路径进行平滑处理。", "type": "bool", "default": true, "required": false}]}