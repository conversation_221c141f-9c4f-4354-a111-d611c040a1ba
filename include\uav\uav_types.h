// include/uav/uav_types.h
#pragma once

#include "core/types.h"
#include <string>
#include <vector>
#include <chrono> 
#include <cmath>  

namespace NSDrones {
	namespace NSUav {
		using namespace NSDrones::NSCore;

		/**
		 * @enum FlightMode
		 * @brief 定义 UAV 的飞行模式，特别用于 VTOL。
		 */
		enum class FlightMode {
			HOVER,          // 悬停或多旋翼模式
			TRANSITION,     // 过渡模式 (VTOL)
			FIXED_WING,     // 固定翼模式
			UNKNOWN         // 未知或不适用
		};

		/**
		 * @struct UAVState
		 * @brief 表示 UAV 的完整瞬时状态，包含核心运动状态和 UAV 特定状态。
		 */
		struct UavState {
			// --- 核心运动状态 (与 EntityState 保持一致) ---
			WGS84Point position;                        // 位置 (WGS84坐标系)
			Orientation orientation = Orientation::Identity(); // 姿态 (四元数, 相对于局部坐标系)
			Vector3D velocity = Vector3D::Zero();         // 速度 (米/秒, NED坐标系)
			Vector3D angular_velocity = Vector3D::Zero(); // 角速度 (弧度/秒, 机体坐标系)
			Vector3D acceleration = Vector3D::Zero();     // 加速度 (米/秒^2, 局部坐标系 ENU)
			Vector3D angular_acceleration = Vector3D::Zero(); // 角加速度 (弧度/秒^2, 机体坐标系)

			// --- UAV 特定状态 ---
			FlightMode mode = FlightMode::UNKNOWN;      // 当前飞行模式
			double current_energy = 0.0;                // 当前剩余能量 (例如 Wh)
			// std::string current_task_id = INVALID_OBJECT_ID; // 当前执行的任务 ID (默认为无效) - 移至执行器管理
			// bool is_task_completed = false;             // 当前任务是否已完成 - 移至执行器管理
			// Time task_start_time = 0.0;                 // 当前任务开始时间 (绝对时间戳) - 移至执行器管理
			// Time task_completion_time = 0.0;            // 当前任务完成时间 (绝对时间戳, 如果已完成) - 移至执行器管理

			// --- 元数据 ---
			Time time_stamp = 0.0;                      // 状态的时间戳 (秒)
			std::string status = "IDLE";                // UAV 状态 ("IDLE", "ACTIVE", "RETURNING", "ERROR")
			std::vector<std::string> capabilities;      // 能力列表

			/** @brief 默认构造函数，初始化时间戳。 */
			UavState() {
				// 初始化时间戳为当前系统时间
				time_stamp = std::chrono::duration_cast<std::chrono::duration<double>>(
					std::chrono::system_clock::now().time_since_epoch()).count();
			}

			/**
			 * @brief 验证状态数据是否有效 (例如，检查 NaN 或 Inf)。
			 * @return 如果所有数值字段都是有限的，返回 true。
			 */
			bool isEmpty() const {
				return position.allFinite() && velocity.allFinite() && acceleration.allFinite() &&
					orientation.coeffs().allFinite() &&
					angular_velocity.allFinite() && angular_acceleration.allFinite() &&
					std::isfinite(time_stamp) && std::isfinite(current_energy);
			}
		};

		/**
		 * @enum MissionStatus
		 * @brief 定义 UAV 的任务执行状态。
		 */
		enum class MissionStatus {
			NONE,      // 无任务或未初始化
			PENDING,   // 任务已分配，等待开始
			ACTIVE,    // 任务正在执行
			PAUSED,    // 任务已暂停
			COMPLETED, // 任务成功完成
			FAILED,    // 任务执行失败
			ABORTED    // 任务被中止
		};

	} // namespace NSUav
} // namespace NSDrones