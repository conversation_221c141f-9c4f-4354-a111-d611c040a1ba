// include/planning/evaluator/energy_evaluator.h
#pragma once

#include "algorithm/evaluator/itrajectory_evaluator.h"
#include "algorithm/algorithm_object.h"
#include "uav/uav_fwd.h"
#include "mission/mission_fwd.h"
#include "uav/ienergy_model.h"
#include "planning/planning_types.h"

#include <string>
#include <memory>
#include <vector>

namespace NSDrones {
	namespace NSAlgorithm {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class EnergyEvaluator
		 * @brief 轨迹评估器的一个实现，专注于能量消耗和续航估计。
		 * 现在继承自 AlgorithmObject。
		 */
		class EnergyEvaluator : public ITrajectoryEvaluator, public NSAlgorithm::AlgorithmObject {
		public:
			/**
			 * @brief 构造函数。
			 * @param id 对象ID。
			 * @param type_tag 类型标签。
			 * @param energy_model 能量模型的共享指针 (如果不由 AlgorithmObject 管理)。
			 * @param name 对象名称。
			 * @param version 版本信息。
			 */
			EnergyEvaluator(ObjectID id,
							const std::string& type_tag,
							std::shared_ptr<NSUav::IEnergyModel> energy_model,
							const std::string& name = "EnergyEvaluator",
							const std::string& version = "1.0.0.default");

			~EnergyEvaluator() override = default;

			// 删除拷贝和移动构造/赋值，依赖基类或默认行为
			EnergyEvaluator(const EnergyEvaluator&) = delete;
			EnergyEvaluator& operator=(const EnergyEvaluator&) = delete;
			EnergyEvaluator(EnergyEvaluator&&) = delete;
			EnergyEvaluator& operator=(EnergyEvaluator&&) = delete;


			// --- ITrajectoryEvaluator 接口实现 ---
			TrajectoryEvaluationMetrics evaluate(const TrajectoryEvaluationRequest& request) const override;

			// 批量评估
			std::map<ObjectID, TrajectoryEvaluationMetrics> batchEvaluate(
				const std::vector<TrajectoryEvaluationRequest>& requests) const override;

			/**
			 * @brief 初始化能量评估器。
			 * @param params 包含配置参数的 ParamValues 对象 (当前 EnergyEvaluator 可能不使用)。
			 * @param raw_config 实例的原始JSON配置 (当前 EnergyEvaluator 可能不使用)。
			 * @return 总是返回 true，因为当前 EnergyEvaluator 没有特定配置。
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) override;

		private:
			std::shared_ptr<NSUav::IEnergyModel> energy_model_;

			// 计算保真度相关成员变量
			CalculationFidelityType calculation_fidelity_ = CalculationFidelityType::SIMPLE;

			// 简单模型参数
			double simple_model_hover_w_ = 150.0;        // 悬停功耗 (W)
			double simple_model_forward_coeff_ = 0.5;    // 前飞功耗系数
		};

	} // namespace NSAlgorithm
} // namespace NSDrones