// src/params/paramregistry.cpp
#include "params/paramregistry.h"
#include "utils/logging.h"
#include "params/param_json.h"
#include "utils/file_utils.h"
#include <algorithm>
#include <vector>
#include <fstream>
#include <sstream>
#include "params/parameters.h"
#include <set>

namespace NSDrones {
	namespace NSParams {



		// --- 单例实现 ---
		ParamRegistry& ParamRegistry::getInstance() {
			static ParamRegistry instance;
			return instance;
		}

		ParamRegistry::ParamRegistry()
			: effective_defines_cache_(cache_max_age_, cache_max_size_) {
			LOG_INFO("ParamRegistry instance created at {}.", fmt::ptr(this));
			LOG_DEBUG("ParamRegistry: ThreadSafeCache配置 - 最大存活时间: {}分钟, 最大条目数: {}",
					 cache_max_age_.count(), cache_max_size_);
		}

		ParamRegistry::ParamRegistry(std::chrono::minutes cache_max_age, size_t cache_max_size)
			: effective_defines_cache_(cache_max_age, cache_max_size),
			  cache_max_age_(cache_max_age),
			  cache_max_size_(cache_max_size) {
			LOG_INFO("ParamRegistry instance created at {} with custom cache settings.", fmt::ptr(this));
			LOG_DEBUG("ParamRegistry: ThreadSafeCache配置 - 最大存活时间: {}分钟, 最大条目数: {}",
					 cache_max_age_.count(), cache_max_size_);
		}

		void ParamRegistry::configureCacheSettings(std::chrono::minutes cache_max_age, size_t cache_max_size) {
			cache_max_age_ = cache_max_age;
			cache_max_size_ = cache_max_size;

			// 重新配置缓存（清空现有缓存并应用新设置）
			effective_defines_cache_.clear();
			// 更新缓存设置（如果 ThreadSafeCache 支持运行时更新）
			effective_defines_cache_.updateConfiguration(cache_max_age, cache_max_size);

			LOG_INFO("ParamRegistry: 缓存设置已更新 - 最大存活时间: {}分钟, 最大条目数: {}",
					cache_max_age_.count(), cache_max_size_);
		}

		ParamRegistry::~ParamRegistry() {
			LOG_INFO("ParamRegistry instance at {} is being destroyed.", fmt::ptr(this));
			// ThreadSafeCache和shared_ptr会自动管理内存
			LOG_DEBUG("ParamRegistry: ThreadSafeCache和注册表将自动清理");
		}

		// --- 私有辅助函数实现 ---
		// 获取父级 type_tag，例如 "A.B.C" -> "A.B"
		std::string ParamRegistry::getParentTypeTag(const std::string& type_tag) const {
			size_t last_dot = type_tag.rfind('.'); // 查找最后一个 '.' 分隔符
			if (last_dot != std::string::npos) { // 如果找到了
				return type_tag.substr(0, last_dot); // 返回分隔符之前的部分
			}
			return ""; // 如果没有找到分隔符，说明是顶级或者格式不正确，返回空字符串
		}



		// 新增的公共方法，用于从JSON对象解析ParamDefines
		std::optional<ParamDefines> ParamRegistry::parseJsonToParamDefines(
			const nlohmann::json& definitions_json,
			const std::string& type_tag_override) const
		{
			LOG_DEBUG("ParamRegistry::parseJsonToParamDefines - 公共接口调用，type_tag_override: '{}'", type_tag_override);

			// 直接委托给 ParamDefines::fromJson
			std::string final_type_tag;
			auto result = ParamDefines::fromJson(definitions_json, type_tag_override, &final_type_tag);

			if (result.has_value()) {
				LOG_INFO("ParamRegistry::parseJsonToParamDefines - 成功从JSON对象为类型标签 '{}' 解析了ParamDefines。", final_type_tag);
			} else {
				LOG_ERROR("ParamRegistry::parseJsonToParamDefines - 从JSON对象解析ParamDefines失败。最终尝试的类型标签: '{}'", final_type_tag);
			}
			return result;
		}

		// --- 公共接口实现 ---

		bool ParamRegistry::loadAllParamDefinesFromDir(const std::string& directory_path) {
			LOG_INFO("开始从目录 '{}' 加载所有参数定义.", directory_path);

			// 使用函数注入模式：ThreadSafeCache处理缓存失效逻辑
			return effective_defines_cache_.executeUpdateWithFullInvalidation([this, &directory_path]() {
				// 数据更新函数：执行实际的目录加载操作
				try {
					std::unique_lock<std::shared_mutex> lock(registry_mutex_);
					registered_defines_map_.clear(); // 清空现有定义
					LOG_DEBUG("从目录加载前已清除现有定义.");

				fs::path dir(directory_path);
				if (!fs::exists(dir) || !fs::is_directory(dir)) {
					LOG_ERROR("目录 '{}' 不存在或不是一个有效目录.", directory_path);
					return false;
				}

				bool all_successful = true;
				unsigned int files_processed = 0;
				unsigned int files_succeeded = 0;

				for (const auto& entry : fs::directory_iterator(dir)) {
					if (entry.is_regular_file()) {
						const auto& file_path = entry.path();
						std::string extension = file_path.extension().string();
						std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

						if (extension == ".json") {
							files_processed++;
							LOG_DEBUG("处理参数定义文件: '{}'", file_path.string());

							std::ifstream ifs(file_path);
							if (!ifs.is_open()) {
								LOG_ERROR("无法打开文件 '{}'", file_path.string());
								all_successful = false;
								continue;
							}

							nlohmann::json current_file_json;
							try {
								ifs >> current_file_json;
							} catch (const nlohmann::json::parse_error& e) {
								LOG_ERROR("解析文件 '{}' 为 JSON 时失败: {}", file_path.string(), e.what());
								ifs.close();
								all_successful = false;
								continue;
							}
							ifs.close();

							std::string type_tag_for_file = file_path.stem().string(); // 从文件名推断 type_tag
							std::string actual_type_tag_from_parse;

							// 直接调用 ParamDefines::fromJson
							auto new_defines_opt = ParamDefines::fromJson(current_file_json, type_tag_for_file, &actual_type_tag_from_parse);

							if (new_defines_opt.has_value() && !actual_type_tag_from_parse.empty()) {
								// 由于已持有锁，直接更新 map
								registered_defines_map_[actual_type_tag_from_parse] = std::make_shared<ParamDefines>(std::move(*new_defines_opt));
								LOG_INFO("已成功加载并注册类型标签 '{}' 的参数定义 (来自文件 '{}')，共 {} 条。",
										 actual_type_tag_from_parse, file_path.string(), registered_defines_map_[actual_type_tag_from_parse]->size());
								files_succeeded++;
							} else {
								LOG_ERROR("从文件 '{}' (尝试的类型标签 '{}') 解析参数定义失败。解析器返回的最终类型标签为 '{}'。",
										  file_path.string(), type_tag_for_file, actual_type_tag_from_parse);
								all_successful = false;
							}
						}
					}
				}

				LOG_INFO("从目录 '{}' 加载完成。共处理 {} 个 JSON 文件，成功加载 {} 个。整体加载状态 AllSuccessful: {}.",
						 directory_path, files_processed, files_succeeded, all_successful);
				return all_successful;
			} catch (const std::exception& e) {
				LOG_ERROR("ParamRegistry: 从目录 '{}' 加载参数定义失败: {}", directory_path, e.what());
				return false;
			}
		});
	}

		bool ParamRegistry::registerParamDefines(ParamDefines param_defines) {
			const std::string type_tag_key_copy = param_defines.getTypeTag();

			LOG_DEBUG("正在为类型标签 '{}' 注册 ParamDefines 对象 (包含 {} 个定义)。可能覆盖现有定义。", type_tag_key_copy, param_defines.size());

			if (type_tag_key_copy.empty()) {
				LOG_ERROR("无法注册 type_tag 为空的 ParamDefines 对象。");
				return false;
			}

			// 使用函数注入模式：ThreadSafeCache处理缓存失效逻辑
			return effective_defines_cache_.executeUpdateWithFullInvalidation([this, &type_tag_key_copy, param_defines = std::move(param_defines)]() mutable {
				// 数据更新函数：执行实际的注册操作
				try {
					std::unique_lock<std::shared_mutex> lock(registry_mutex_);
					registered_defines_map_[type_tag_key_copy] = std::make_shared<ParamDefines>(std::move(param_defines));
					LOG_DEBUG("ParamRegistry: 成功注册类型标签 '{}' 的参数定义", type_tag_key_copy);
					return true;
				} catch (const std::exception& e) {
					LOG_ERROR("ParamRegistry: 注册类型标签 '{}' 的参数定义失败: {}", type_tag_key_copy, e.what());
					return false;
				}
			});
		}

		const ParamDefines* ParamRegistry::getParamDefines(const std::string& type_tag_to_find) const {
			std::shared_lock<std::shared_mutex> lock(registry_mutex_); // 加读锁
			auto it = registered_defines_map_.find(type_tag_to_find);

			if (it != registered_defines_map_.end()) {
				LOG_TRACE("ParamRegistry: Found direct definitions for type_tag '{}' at {}", type_tag_to_find, fmt::ptr(it->second.get())); // 恢复原有日志级别
				return it->second.get();
			}
			LOG_DEBUG("ParamRegistry: No direct param defines found for type_tag '{}'", type_tag_to_find); // 恢复原有日志级别
			return nullptr;
		}

		// 获取或构建并缓存指定类型标签的完整有效参数定义集合 (考虑继承)
		std::shared_ptr<const ParamDefines> ParamRegistry::getOrBuildEffectiveParamDefines(const std::string& type_tag) const {
			if (type_tag.empty()) {
				LOG_WARN("ParamRegistry: getOrBuildEffectiveParamDefines called with empty type_tag.");
				return nullptr;
			}
			LOG_TRACE("ParamRegistry: getOrBuildEffectiveParamDefines - Entry for type_tag: {}", type_tag);

			// 使用函数注入模式：ThreadSafeCache处理所有缓存逻辑
			return effective_defines_cache_.getOrCompute(type_tag, [this, &type_tag]() -> std::shared_ptr<const ParamDefines> {
				// 数据提供函数：构建有效参数定义
				// 注意：ThreadSafeCache不持有锁调用此函数，我们需要自己保护数据源
				LOG_TRACE("ParamRegistry: Cache miss for effective defines of type_tag '{}'. Building...", type_tag);

				// 获取注册表的读锁，保护registered_defines_map_的访问
				std::shared_lock<std::shared_mutex> registry_lock(registry_mutex_);

				// 构建过程：
				// - 获取当前 type_tag 的直接定义 (如果存在)
				// - 递归获取父 type_tag 的有效定义
				// - 合并父定义和当前直接定义

				std::vector<const ParamDefines*> all_defs_in_chain;
				std::string current_type_tag = type_tag;
				std::set<std::string> visited_tags; // 防止循环继承

				while (!current_type_tag.empty() && visited_tags.find(current_type_tag) == visited_tags.end()) {
					visited_tags.insert(current_type_tag);
					LOG_TRACE("ParamRegistry: Building chain - processing current_type_tag: {}", current_type_tag);

					// 在registry_lock保护下安全访问注册表
					auto it = registered_defines_map_.find(current_type_tag);
					if (it != registered_defines_map_.end()) {
						const ParamDefines* direct_defines = it->second.get();
						LOG_TRACE("ParamRegistry: Found direct defines for '{}' during chain build.", current_type_tag);
						all_defs_in_chain.push_back(direct_defines);
					} else {
						LOG_TRACE("ParamRegistry: No direct defines for '{}' during chain build.", current_type_tag);
					}
					current_type_tag = getParentTypeTag(current_type_tag);
				}

				if (!current_type_tag.empty() && visited_tags.count(current_type_tag)) {
					LOG_WARN("ParamRegistry: Detected cycle while resolving inheritance for type_tag '{}'. Cycle at '{}'.", type_tag, current_type_tag);
				}

				if (all_defs_in_chain.empty()) {
					LOG_DEBUG("ParamRegistry: No definitions found in the entire hierarchy for type_tag '{}'.", type_tag);
					// 返回nullptr表示此type_tag无任何定义
					return nullptr;
				}

				// 合并所有找到的 ParamDefines，父级的优先，子级的覆盖/添加
				LOG_TRACE("ParamRegistry: Merging {} definition sets for type_tag '{}'.", all_defs_in_chain.size(), type_tag);

				// 创建一个新的 ParamDefines 对象，用于存储合并结果
				auto merged_defines = std::make_shared<ParamDefines>(type_tag);

				// 从最顶层的父级开始合并 (all_defs_in_chain 的末尾是最高父级)
				for (auto it = all_defs_in_chain.rbegin(); it != all_defs_in_chain.rend(); ++it) {
					const ParamDefines* parent_defs_ptr = *it;
					if (parent_defs_ptr) { // 确保指针有效
						LOG_TRACE("ParamRegistry: Merging definitions from type_tag '{}' ({} params) into effective set for '{}'",
								  parent_defs_ptr->getTypeTag(), parent_defs_ptr->size(), type_tag);
						merged_defines->merge(*parent_defs_ptr);
					}
				}

				LOG_DEBUG("ParamRegistry: Built effective ParamDefines for type_tag '{}' with {} total keys.", type_tag, merged_defines->size());
				return merged_defines;
			}, "effective_defines");
		}

		// 修改 getEffectiveParamDefine (singular)
		const ParamDefine* ParamRegistry::getEffectiveParamDefine(const std::string& type_tag, const std::string& key) const {
			std::shared_ptr<const ParamDefines> effective_set_ptr = getOrBuildEffectiveParamDefines(type_tag);
			if (effective_set_ptr) {
				return effective_set_ptr->getEffectiveParamDefine(key); // ParamDefines::getEffectiveParamDefine 返回 const ParamDefine*
			}
			LOG_WARN("ParamRegistry: Failed to get/build effective ParamDefines for type_tag '{}' when looking for key '{}'. Returning nullptr.", type_tag, key);
			return nullptr;
		}

		// 获取一个类型标签 (包括继承) 的所有参数键
		std::vector<std::string> ParamRegistry::getEffectiveParamKeys(const std::string& type_tag) const {
			LOG_TRACE("ParamRegistry: getEffectiveParamKeys called for type_tag: {}", type_tag);
			auto effective_defines = getOrBuildEffectiveParamDefines(type_tag);
			if (effective_defines) {
				std::vector<std::string> keys = effective_defines->getKeys(); // C2248: 使用公有成员函数 getKeys()
				std::sort(keys.begin(), keys.end()); // 保持键的排序
				LOG_DEBUG("为类型标签 '{}' 的有效参数集找到 {} 个键.", type_tag, keys.size());
				return keys;
			} else {
				LOG_DEBUG("ParamRegistry: No effective param definitions found for type_tag '{}', returning empty key set.", type_tag);
				return {};
			}
		}

		// 获取所有已直接注册的 type_tag
		std::vector<std::string> ParamRegistry::getAllRegisteredTypeTags() const {
			std::shared_lock<std::shared_mutex> lock(registry_mutex_);
			std::vector<std::string> tags;
			tags.reserve(registered_defines_map_.size());
			for (const auto& pair : registered_defines_map_) {
				tags.push_back(pair.first);
			}
			std::sort(tags.begin(), tags.end()); // 排序标签名
			LOG_DEBUG("获取到 {} 个已注册的类型标签.", tags.size());
			return tags;
		}

		// 清空所有定义
		void ParamRegistry::clearAllParamDefinitions() {
			// 使用函数注入模式：ThreadSafeCache处理缓存失效逻辑
			effective_defines_cache_.executeUpdateWithFullInvalidation([this]() {
				// 数据更新函数：清空所有注册的参数定义
				try {
					std::unique_lock<std::shared_mutex> reg_lock(registry_mutex_);
					registered_defines_map_.clear();
					LOG_INFO("ParamRegistry: 已清除所有参数定义");
					return true;
				} catch (const std::exception& e) {
					LOG_ERROR("ParamRegistry: 清除参数定义失败: {}", e.what());
					return false;
				}
			});
		}

		// --- 方法实现 ---

		/**
		 * @brief 直接从 JSON 对象注册参数定义。
		 * 关键步骤：确定 type_tag -> 解析 parameters 数组 -> 创建 ParamDefines -> 存入映射表。
		 */
		bool ParamRegistry::registerParamDefinesFromJson(const nlohmann::json& definitions_json, std::string type_tag_override) {
			LOG_TRACE("从Json对象注册参数定义，类型标签为: '{}'", type_tag_override);

			// 直接调用 ParamDefines::fromJson
			std::string final_type_tag;
			auto new_defines_opt = ParamDefines::fromJson(definitions_json, type_tag_override, &final_type_tag);

			if (!new_defines_opt.has_value() || final_type_tag.empty()) {
				LOG_ERROR("未能从JSON对象成功解析参数定义： (最终 type_tag: '{}').", final_type_tag);
				return false;
			}

			// 解析成功，现在获取锁并更新注册表
			// 使用 ParamRegistry::registerParamDefines 成员函数，它会处理锁
			return registerParamDefines(std::move(*new_defines_opt));
		}

		/**
		 * @brief 从 JSON 文件加载并注册参数定义。
		 * 读取文件 -> 解析JSON -> 调用 ParamDefines::fromJson -> (如果成功) 加锁并更新注册表。
		 */
		bool ParamRegistry::registerParamDefinesFromFile(const fs::path& file_path, std::string type_tag_override) {
			//LOG_TRACE("registerParamDefinesFromFile 调用，文件: '{}', type_tag_override: '{}'", file_path.string(), type_tag_override);

			std::ifstream ifs(file_path);
			if (!ifs.is_open()) {
				LOG_ERROR("无法打开参数定义文件 '{}'", file_path.string());
				return false;
			}

			nlohmann::json definitions_json;
			try {
				ifs >> definitions_json;
				LOG_TRACE("文件 '{}' 内容已成功读取并解析为 JSON 对象。", file_path.string());
			} catch (const nlohmann::json::parse_error& e) {
				LOG_ERROR("解析文件 '{}' 内容为 JSON 时失败: {}", file_path.string(), e.what());
				return false;
			}
			ifs.close();

			std::string determined_type_tag = type_tag_override;
			if (determined_type_tag.empty()) {
				determined_type_tag = file_path.stem().string(); // 从文件名(不含扩展名)推断 type_tag
				LOG_TRACE("从文件名推断 type_tag 为 '{}'", determined_type_tag);
			}

			// 现在调用已经重构的 registerParamDefinesFromJson
			// 它内部会调用 ParamDefines::fromJson，并最终调用 registerParamDefines(ParamDefines) 来获取锁并更新
			return registerParamDefinesFromJson(definitions_json, determined_type_tag);
		}

		// 获取所有type_tag及其参数key的只读快照
		std::vector<std::pair<std::string, std::vector<std::string>>> ParamRegistry::getAllTypeTagAndKeysView() const {
			std::shared_lock<std::shared_mutex> lock(registry_mutex_);
			std::vector<std::pair<std::string, std::vector<std::string>>> result;
			for (const auto& pair : registered_defines_map_) {
				result.emplace_back(pair.first, pair.second->getKeys());
			}
			return result;
		}

		std::shared_ptr<ParamValues> ParamRegistry::createDefaultParamValues(const std::string& type_tag) const {
			LOG_TRACE("请求为类型标签 '{}' 创建默认 ParamValues 对象。", type_tag);

			std::shared_ptr<const ParamDefines> effective_defines_ptr = this->getOrBuildEffectiveParamDefines(type_tag);

			if (!effective_defines_ptr) {
				LOG_ERROR("ParamRegistry::createDefaultParamValues: 未能获取 type_tag '{}' 的有效参数定义。无法创建默认 ParamValues。", type_tag);
				return nullptr;
			}

			auto default_pv = std::make_shared<ParamValues>(type_tag);
			default_pv->loadDefaults(*this);

			LOG_INFO("ParamRegistry::createDefaultParamValues: 成功为 type_tag '{}' 创建并填充了默认 ParamValues 对象。", type_tag);
			return default_pv;
		}

	} // namespace NSParams
} // namespace NSDrones