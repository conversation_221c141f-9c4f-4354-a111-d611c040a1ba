#pragma once

#include "core/geometry/ishape.h"
#include <fcl/geometry/bvh/BVH_model.h>
#include <vector>
#include <string>

namespace NSDrones {
namespace NSCore {

    /**
     * @brief 三角网格形状
     * 
     * 表示由三角形面片构成的复杂几何体。
     * 支持从文件加载或程序生成的三角网格。
     */
    class MeshShape : public IShape {
    private:
        std::vector<fcl::Vector3d> vertices_;  // 顶点列表
        std::vector<fcl::Triangle> triangles_; // 三角形列表
        
        mutable std::shared_ptr<fcl::BVHModel<fcl::AABBd>> fcl_mesh_;  // FCL BVH模型（延迟创建）
        mutable bool geometry_dirty_;  // 几何是否需要更新

    public:
        /**
         * @brief 构造函数
         * @param vertices 顶点列表
         * @param triangles 三角形列表
         */
        MeshShape(const std::vector<fcl::Vector3d>& vertices, const std::vector<fcl::Triangle>& triangles);

        /**
         * @brief 默认构造函数（空网格）
         */
        MeshShape();

        /**
         * @brief 拷贝构造函数
         */
        MeshShape(const MeshShape& other);

        /**
         * @brief 赋值操作符
         */
        MeshShape& operator=(const MeshShape& other);

        // IShape接口实现
        ShapeType getType() const override { return ShapeType::MESH; }
        int getDimension() const override { return 3; }  // 3D实体
        std::shared_ptr<fcl::CollisionGeometryd> getFCLGeometry() const override;
        fcl::AABBd getAABB(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const override;
        double getVolume() const override;
        double getSurfaceArea() const override;
        fcl::Vector3d getCentroid() const override;
        fcl::Matrix3d getInertiaMatrix(double mass) const override;
        std::unique_ptr<IShape> clone() const override;
        nlohmann::json serialize() const override;
        bool deserialize(const nlohmann::json& json) override;
        std::string toString() const override;
        bool containsPoint(const fcl::Vector3d& point) const override;
        double distanceToPoint(const fcl::Vector3d& point) const override;
        double getCharacteristicSize() const override;

        // MeshShape特有方法
        /**
         * @brief 获取顶点数量
         */
        size_t getVertexCount() const { return vertices_.size(); }

        /**
         * @brief 获取三角形数量
         */
        size_t getTriangleCount() const { return triangles_.size(); }

        /**
         * @brief 获取顶点列表
         */
        const std::vector<fcl::Vector3d>& getVertices() const { return vertices_; }

        /**
         * @brief 获取三角形列表
         */
        const std::vector<fcl::Triangle>& getTriangles() const { return triangles_; }

        /**
         * @brief 获取指定索引的顶点
         * @param index 顶点索引
         * @return 顶点坐标
         */
        const fcl::Vector3d& getVertex(size_t index) const;

        /**
         * @brief 获取指定索引的三角形
         * @param index 三角形索引
         * @return 三角形
         */
        const fcl::Triangle& getTriangle(size_t index) const;

        /**
         * @brief 添加顶点
         * @param vertex 顶点坐标
         * @return 顶点索引
         */
        size_t addVertex(const fcl::Vector3d& vertex);

        /**
         * @brief 添加三角形
         * @param v0 第一个顶点索引
         * @param v1 第二个顶点索引
         * @param v2 第三个顶点索引
         */
        void addTriangle(size_t v0, size_t v1, size_t v2);

        /**
         * @brief 添加三角形
         * @param triangle 三角形
         */
        void addTriangle(const fcl::Triangle& triangle);

        /**
         * @brief 设置网格数据
         * @param vertices 顶点列表
         * @param triangles 三角形列表
         */
        void setMesh(const std::vector<fcl::Vector3d>& vertices, const std::vector<fcl::Triangle>& triangles);

        /**
         * @brief 清空网格
         */
        void clear();

        /**
         * @brief 检查是否为空
         */
        bool isEmpty() const { return vertices_.empty() || triangles_.empty(); }

        /**
         * @brief 计算网格法向量
         * @return 每个三角形的法向量
         */
        std::vector<fcl::Vector3d> computeNormals() const;

        /**
         * @brief 计算顶点法向量（平均相邻面法向量）
         * @return 每个顶点的法向量
         */
        std::vector<fcl::Vector3d> computeVertexNormals() const;

        /**
         * @brief 验证网格完整性
         * @return 是否有效
         */
        bool validate() const;

        /**
         * @brief 修复网格（移除重复顶点、无效三角形等）
         */
        void repair();

        /**
         * @brief 简化网格（减少三角形数量）
         * @param target_triangle_count 目标三角形数量
         */
        void simplify(size_t target_triangle_count);

        /**
         * @brief 细分网格（增加三角形数量）
         * @param iterations 细分迭代次数
         */
        void subdivide(int iterations = 1);

        /**
         * @brief 平滑网格
         * @param iterations 平滑迭代次数
         * @param factor 平滑因子
         */
        void smooth(int iterations = 1, double factor = 0.5);

        /**
         * @brief 获取边列表
         * @return 边列表（每条边用两个顶点索引表示）
         */
        std::vector<std::pair<size_t, size_t>> getEdges() const;

        /**
         * @brief 获取变换后的顶点
         * @param transform 变换矩阵
         * @return 变换后的顶点列表
         */
        std::vector<fcl::Vector3d> getTransformedVertices(const fcl::Transform3d& transform) const;

        /**
         * @brief 从文件加载网格
         * @param filename 文件路径（支持OBJ、STL等格式）
         * @return MeshShape实例
         */
        static std::unique_ptr<MeshShape> loadFromFile(const std::string& filename);

        /**
         * @brief 保存网格到文件
         * @param filename 文件路径
         * @return 是否成功
         */
        bool saveToFile(const std::string& filename) const;

        /**
         * @brief 创建立方体网格
         * @param size 立方体边长
         * @return MeshShape实例
         */
        static std::unique_ptr<MeshShape> createCube(double size);

        /**
         * @brief 创建球体网格
         * @param radius 球体半径
         * @param subdivisions 细分级别
         * @return MeshShape实例
         */
        static std::unique_ptr<MeshShape> createSphere(double radius, int subdivisions = 2);

        /**
         * @brief 创建圆柱体网格
         * @param radius 圆柱体半径
         * @param height 圆柱体高度
         * @param segments 圆周分段数
         * @return MeshShape实例
         */
        static std::unique_ptr<MeshShape> createCylinder(double radius, double height, int segments = 16);

        /**
         * @brief 创建平面网格
         * @param width 宽度
         * @param height 高度
         * @param width_segments 宽度分段数
         * @param height_segments 高度分段数
         * @return MeshShape实例
         */
        static std::unique_ptr<MeshShape> createPlane(double width, double height, int width_segments = 1, int height_segments = 1);

    private:
        /**
         * @brief 确保FCL对象已创建
         */
        void ensureFCLObject() const;

        /**
         * @brief 标记几何需要更新
         */
        void markGeometryDirty() { geometry_dirty_ = true; fcl_mesh_.reset(); }

        /**
         * @brief 验证顶点和三角形数据
         */
        void validateMesh() const;

        /**
         * @brief 验证索引有效性
         */
        void validateIndex(size_t index, size_t max_size, const std::string& type) const;

        /**
         * @brief 计算三角形面积
         * @param triangle 三角形
         * @return 面积
         */
        double computeTriangleArea(const fcl::Triangle& triangle) const;

        /**
         * @brief 计算三角形法向量
         * @param triangle 三角形
         * @return 法向量
         */
        fcl::Vector3d computeTriangleNormal(const fcl::Triangle& triangle) const;
    };

} // namespace NSCore
} // namespace NSDrones
