// src/environment/obstacle.cpp
#include "environment/entities/obstacle.h"
#include "core/entity_object.h"
#include "core/geometry/ishape.h"
#include "params/paramregistry.h"
#include "utils/logging.h"
#include "environment/environment.h"
#include "core/movement_strategy.h"

namespace NSDrones {
	namespace NSEnvironment {

		/**
		 * @brief Obstacle 构造函数。
		 */
		Obstacle::Obstacle(ObjectID id,
			const std::string& obstacle_type_key,
			const std::string& name,
			const EntityState& initial_state,
			std::shared_ptr<IMovementStrategy> strategy)
			: EntityObject(std::move(id), obstacle_type_key, name, initial_state) {

			LOG_DEBUG("Obstacle ({}) 已构造: ID='{}', 类型键='{}'",
				getName().empty() ? obstacle_type_key : getName(), getId(), obstacle_type_key);

			if (strategy) {
				LOG_DEBUG("Obstacle ID '{}': 构造时收到了移动策略 '{}'。", getId(), typeid(*strategy).name());
				setMovementStrategy(strategy); // Set the strategy passed from Config
			}
			else {
				// 检查是否是静态障碍物类型（如 Building），如果是则这是正常行为
				bool is_static_obstacle = (obstacle_type_key.find("Building") != std::string::npos) ||
										  (obstacle_type_key.find("Static") != std::string::npos);

				if (is_static_obstacle) {
					LOG_DEBUG("Obstacle ID '{}': 静态障碍物类型 '{}' 构造时未收到移动策略，将创建默认的 StillMovementStrategy。", getId(), obstacle_type_key);
				} else {
					LOG_WARN("Obstacle ID '{}': 动态障碍物类型 '{}' 构造时未收到移动策略。将默认创建一个 StillMovementStrategy。这通常表示Config中的逻辑需要检查。", getId(), obstacle_type_key);
				}

				auto default_strategy = std::make_shared<StillMovementStrategy>(*this);
				// Initialize the default strategy with empty params and this object as owner
				if (default_strategy && default_strategy->initialize(nlohmann::json::object(), *this)) {
					setMovementStrategy(default_strategy);
					LOG_DEBUG("Obstacle ID '{}': 成功创建并设置了默认的 StillMovementStrategy。", getId());
				}
				else {
					LOG_ERROR("Obstacle ID '{}': 创建或初始化默认 StillMovementStrategy 失败!", getId());
				}
			}
		}

		/**
		 * @brief 初始化障碍物。
		 *        如果构造时未提供移动策略，会尝试从参数加载。
		 */
		bool Obstacle::initialize(std::shared_ptr<NSParams::ParamValues> final_params, const nlohmann::json& raw_instance_json_config) {
			// 适配新接口，内部如需拷贝参数可用 *final_params
			if (!final_params) {
				LOG_ERROR("Obstacle 初始化失败: 传入的参数指针为空。");
				return false;
			}
			LOG_DEBUG("Obstacle [{} ID:{}] (类型标签: {}): 开始执行 initialize(ParamValues&, const json&)...", getName(), getId(), getTypeTag());

			// 1. 调用基类的 initialize 方法 (处理参数赋值, 形状创建, 添加到环境)
			if (!EntityObject::initialize(std::make_shared<NSParams::ParamValues>(*final_params), raw_instance_json_config)) {
				LOG_ERROR("Obstacle [{} ID:{}] (类型标签: {}): 调用 EntityObject::initialize 失败。初始化终止。", getName(), getId(), getTypeTag());
				return false;
			}
			LOG_DEBUG("Obstacle [{} ID:{}] (类型标签: {}): EntityObject::initialize 完成。", getName(), getId(), getTypeTag());

			// 2. Obstacle 特定初始化 (如果需要)
			// 例如，从 this->params_ 或 raw_instance_json_config 读取特定属性
			std::string material = getParamOrDefault<std::string>("building_material", "unknown");
			LOG_DEBUG("Obstacle [{} ID:{}] specific param: material = {}", getName(), getId(), material);

			// 3. 检查移动策略 (如果 Obstacle 可以移动的话)
			if (getMovementStrategy()) {
				LOG_DEBUG("Obstacle [{} ID:{}] (类型标签: {}): 检测到已关联移动策略 '{}'。", getName(), getId(), getTypeTag(), typeid(*getMovementStrategy()).name());
				// 可以添加检查，例如动态障碍物是否真的关联了动态策略
				if (getTypeTag().find("Dynamic") != std::string::npos &&
					dynamic_cast<StillMovementStrategy*>(getMovementStrategy().get())) {
					LOG_WARN("Obstacle [{} ID:{}] 标记为动态类型 ('{}') 但当前使用的是 StillMovementStrategy。请检查配置。",
						getName(), getId(), getTypeTag());
				}
			}
			else {
				LOG_TRACE("Obstacle [{} ID:{}] (类型标签: {}): 未设置移动策略 (通常对于静态障碍物是正常的)。", getName(), getId(), getTypeTag());
			}

			// 注意：形状创建已移至 EntityObject::initialize

			std::string shape_desc = getShape() ? getShape()->toString() : "无";
			std::string strategy_name = getMovementStrategy() ? typeid(*getMovementStrategy()).name() : "无";
			LOG_INFO("Obstacle [{}] ID:[{}] (类型标签: [{}]): initialize(ParamValues&, const json&) 完成。形状: [{}], 移动策略: [{}]",
				getName(), getId(), getTypeTag(), shape_desc, strategy_name);
			return true;
		}

	} // namespace NSEnvironment
} // namespace NSDrones