// src/core/base_object.cpp
#include "core/base_object.h"
#include "environment/environment.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include "utils/logging.h"
#include "utils/object_id.h"
#include "core/types.h"
#include <stdexcept>
#include <algorithm>
#include <chrono>
#include <sstream>
#include <typeinfo>
#include <set>

namespace NSDrones {
	namespace NSCore {

		// --- 辅助函数：用于从参数转换vector<double>到WGS84Point/Vector3D ---
		std::vector<WGS84Point> paramToListWGS84Point(const BaseObject* obj, const std::string& key) {
			// ParamValues 直接支持 std::vector<std::vector<double>> 对应 list_vector3d
			std::vector<std::vector<double>> list_vec_val = obj->getParamOrDefault<std::vector<std::vector<double>>>(key, {});
			std::vector<WGS84Point> points;
			for (const auto& vec_item : list_vec_val) {
				if (vec_item.size() == 3) {
					// 假设参数格式为 [longitude, latitude, altitude]
					points.emplace_back(vec_item[0], vec_item[1], vec_item[2]);
				}
				else {
					LOG_WARN("对象 [{} ID:{}] (类型标签: {}): 参数 '{}' 中的某个WGS84点项目格式不正确或大小不为3。已跳过该项目。",
						obj->getName(), obj->getId(), obj->getTypeTag(), key);
				}
			}
			return points;
		}

		// --- 内部方法实现 ---
		/** @brief 内部设置父 ID。*/
		void BaseObject::internal_setParent(const ObjectID& p_id) {
			// 仅更新内部成员变量，不触发通知或修改对方
			parent_id_ = p_id;
			LOG_TRACE("对象 [{} ID:{}]: 内部父级 ID 设置为 '{}'", name_, id_, NSUtils::isValidObjectID(parent_id_) ? parent_id_ : "None");
		}
		/** @brief 内部添加子 ID。*/
		void BaseObject::internal_addChild(const ObjectID& c_id) {
			// 仅更新内部列表，不触发通知或修改对方
			// 检查是否已存在，避免重复添加
			if (std::find(children_ids_.begin(), children_ids_.end(), c_id) == children_ids_.end()) {
				children_ids_.push_back(c_id);
				LOG_TRACE("对象 [{} ID:{}]: 内部添加子级 ID '{}'", name_, id_, c_id);
			}
			else {
				LOG_WARN("对象 [{} ID:{}]: 内部子级 ID '{}' 已存在，无需重复添加。", name_, id_, c_id);
			}
		}
		/** @brief 内部移除子 ID。*/
		void BaseObject::internal_removeChild(const ObjectID& c_id) {
			// 仅更新内部列表，不触发通知或修改对方
			// 使用 erase-remove idiom 高效移除
			auto it = std::remove(children_ids_.begin(), children_ids_.end(), c_id);
			if (it != children_ids_.end()) { // 如果找到了并移动到了末尾
				children_ids_.erase(it, children_ids_.end()); // 删除末尾元素
				LOG_TRACE("对象 [{} ID:{}]: 内部移除子级 ID '{}'", name_, id_, c_id);
			}
			else {
				LOG_WARN("对象 [{} ID:{}]: 内部未找到要移除的子级 ID '{}'。", name_, id_, c_id);
			}
		}

		/**
		 * @brief BaseObject 构造函数。
		 */
		BaseObject::BaseObject(ObjectID id, const std::string& object_config_type_key, const std::string& name)
			: id_(std::move(id)),
			type_tag_(object_config_type_key),
			name_(name.empty() ? (type_tag_ + "_" + id_) : name),
			params_(nullptr)
		{
			// 验证 ID 有效性 (现在可以在初始化列表之后安全访问 id_)
			if (!isValidObjectID(id_)) {
				// 抛出异常而不是仅记录日志，因为 ID 无效是严重问题
				std::string error_msg = "构造对象时使用了无效ID '" + id_ + "'";
				throw DroneException(error_msg, ErrorCode::InvalidArgument);
			}
			// 检查类型是否为空 (现在可以访问 type_tag_)
			if (type_tag_.empty()) {
				LOG_WARN("对象 ID '{}' 创建时类型为空，这可能导致参数加载失败。", id_);
			}

			// dynamic_data_ 在其构造函数中初始化时间戳
			LOG_TRACE("已构造: ID='{}', 配置类型键='{}', 名称='{}'.", id_, type_tag_, name_);
		}

	std::shared_ptr<Environment> BaseObject::getEnvironment() const {
		return Environment::getInstance();
	}

		/**
		 * @brief 初始化对象。
		 *        加载与对象类型标签关联的默认参数。
		 *        **重要:** 派生类重写时应调用基类的 `initialize()`。
		 *        此方法通常在对象添加到环境后被调用。
		 * @return 如果初始化成功返回 true。
		 */
		bool BaseObject::initialize(std::shared_ptr<NSParams::ParamValues> final_params,
			const nlohmann::json& raw_instance_json_config) {
			LOG_TRACE("对象 [{} ID:{}]: 开始执行 initialize(std::shared_ptr<ParamValues>, const json&) (类型标签: '{}')", name_, id_, type_tag_);

			if (!final_params) {
				LOG_ERROR("对象 [{} ID:{}]: 传递给 initialize 的 final_params 为空指针。初始化终止。", name_, id_);
				return false;
			}

			// 1. 将 Config 准备好的参数共享指针赋值给对象的内部 params_
			this->params_ = final_params;

			// 验证一下 params_ 的 type_tag_ 是否与对象的 type_tag_ 一致
			// (应该在 Config 层确保传入的 final_params 已有正确 type_tag)
			if (this->params_->getTypeTag() != this->type_tag_) {
				LOG_WARN("对象 [{} ID:{}]: 传入参数的 TypeTag ('{}') 与对象自身的 TypeTag ('{}') 不匹配。将信任并使用传入参数的TypeTag。",
					name_, id_, this->params_->getTypeTag(), this->type_tag_);
				// 通常 Config 应该已经设置好了正确的 type_tag 到 final_params
				// 如果这里的 type_tag_ 仍然重要且必须是对象构造时的那个，需要更复杂的逻辑
				// 暂时以传入的 final_params 的 type_tag 为准，或者让 final_params 自己 setTypeTag
				// this->type_tag_ = this->params_->getTypeTag(); // 例如，如果 type_tag_ 也应该从参数同步
			}
			LOG_TRACE("对象 [{} ID:{}]: 已将 Config 提供的参数 (数量: {}, TypeTag: '{}') 赋值到本地参数集 (共享)。",
				name_, id_, this->params_->size(), this->params_->getTypeTag());

			LOG_TRACE("对象 [{} ID:{}]: initialize(std::shared_ptr<ParamValues>, const json&) 已成功完成。", name_, id_);
			return true;
		}

		// --- 方法：从 JSON 应用参数 --- (新添加)
		bool BaseObject::paramsFromJson(const nlohmann::json& jsonData, const NSParams::ParamRegistry& registry) {
			LOG_TRACE("对象 [{} ID:{}]: 尝试从 JSON 应用参数...", name_, id_);
			if (!jsonData.is_object()) {
				LOG_ERROR("对象 [{} ID:{}]: paramsFromJson 失败，提供的 JSON 数据不是一个对象。", name_, id_);
				return false;
			}

			if (!params_) { // 检查共享指针本身是否为 nullptr
				LOG_ERROR("对象 [{} ID:{}]: params_ 共享指针未初始化，无法从 JSON 应用参数。请确保 initialize 已被调用且成功设置了 params_。", name_, id_);
				return false;
			}

			params_->clear(); // 解引用后调用
			if (params_->valuesFromJson(jsonData, registry)) { // 解引用后调用
				LOG_TRACE("对象 [{} ID:{}]: 已成功从 JSON 应用参数。本地参数数量: {}", name_, id_, params_->size()); // 解引用后调用
				return true;
			}
			else {
				LOG_ERROR("对象 [{} ID:{}]: 从 JSON 应用参数失败 (setParamsFromJson 返回 false)。可能是校验失败。", name_, id_);
				return false;
			}
		}

		// --- 关系管理 ---
		/** @brief 设置父对象。*/
		void BaseObject::setParent(const ObjectID& new_parent_id) {
			// 检查是否尝试设置自身为父级
			if (new_parent_id == id_ && NSUtils::isValidObjectID(id_)) {
				LOG_WARN("对象 [{} ID:{}]: 尝试设置自身为父级，操作被忽略。", name_, id_);
				return;
			}
			// 检查父级是否真的改变了
			if (parent_id_ == new_parent_id) {
				LOG_TRACE("对象 [{} ID:{}]: 新父级 ID '{}' 与当前父级相同，无需操作。", name_, id_, NSUtils::isValidObjectID(new_parent_id) ? new_parent_id : "None");
				return;
			}
			LOG_DEBUG("对象 [{} ID:{}]: 请求设置父级为 '{}' (旧父级: '{}')", name_, id_, NSUtils::isValidObjectID(new_parent_id) ? new_parent_id : "None", NSUtils::isValidObjectID(parent_id_) ? parent_id_ : "None");

			// 委托给 Environment 处理关系设置和索引更新
			// Environment::setParentRelationship 会负责调用 internal_setParent/addChild/removeChild 并更新关系索引
			auto env = getEnvironment();
			if (!env || !env->setParentRelationship(id_, new_parent_id)) {
				LOG_ERROR("对象 [{} ID:{}]: 通过环境设置父级 '{}' 失败。", name_, id_, NSUtils::isValidObjectID(new_parent_id) ? new_parent_id : "None");
			}
			else {
				// 成功信息由 setParentRelationship 记录
			}
		}


	// 显式实例化常用的模板方法
	template std::optional<double> BaseObject::getEffectiveParam<double>(const std::string& key) const;
	template std::optional<int> BaseObject::getEffectiveParam<int>(const std::string& key) const;
	template std::optional<bool> BaseObject::getEffectiveParam<bool>(const std::string& key) const;
	template std::optional<std::string> BaseObject::getEffectiveParam<std::string>(const std::string& key) const;
	template std::optional<std::vector<double>> BaseObject::getEffectiveParam<std::vector<double>>(const std::string& key) const;

	} // namespace NSCore
} // namespace NSDrones