// include/environment/attribute_index.h
#pragma once

#include "core/types.h"
#include "utils/logging.h"
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <shared_mutex>
#include <functional>

namespace NSDrones {
	namespace NSEnvironment {

		/**
		 * @class AttributeIndex
		 * @brief 对象属性索引类，管理实体对象的属性索引，支持高效的属性查询
		 *
		 * 该类使用扁平化结构存储属性键值对与对象ID的映射，提供添加、更新、删除和查询功能。
		 */
		class AttributeIndex {
		public:
			/**
			 * @brief 构造函数
			 */
			AttributeIndex() = default;

			/**
			 * @brief 析构函数
			 */
			~AttributeIndex() = default;

			/**
			 * @brief 添加或更新对象的属性
			 * @param obj_id 对象ID
			 * @param key 属性键
			 * @param value 属性值
			 */
			void addOrUpdateAttribute(const ObjectID& obj_id, const std::string& key, const std::string& value) {
				if (key.empty()) {
					LOG_WARN("属性索引: 尝试添加空键的属性，对象ID: {}", obj_id);
					return;
				}

				AttributeKey attr_key{ key, value };

				std::unique_lock lock(index_mutex_);

				// 如果对象之前有这个属性键但值不同，需要先从旧值的集合中移除
				auto obj_attrs_it = object_attributes_.find(obj_id);
				if (obj_attrs_it != object_attributes_.end()) {
					auto& attrs = obj_attrs_it->second;
					auto key_it = attrs.find(key);
					if (key_it != attrs.end() && key_it->second != value) {
						AttributeKey old_key{ key, key_it->second };
						auto& obj_set = attribute_index_[old_key];
						obj_set.erase(obj_id);

						// 如果集合为空，移除该键值对
						if (obj_set.empty()) {
							attribute_index_.erase(old_key);
						}
					}
				}

				// 更新对象的属性映射
				object_attributes_[obj_id][key] = value;

				// 更新属性索引
				attribute_index_[attr_key].insert(obj_id);

				LOG_DEBUG("属性索引: 对象 {} 添加/更新属性 '{}' = '{}'", obj_id, key, value);
			}

			/**
			 * @brief 批量添加或更新对象的属性
			 * @param obj_id 对象ID
			 * @param attributes 属性键值对映射
			 */
			void addOrUpdateAttributes(const ObjectID& obj_id,
				const std::unordered_map<std::string, std::string>& attributes) {
				for (const auto& [key, value] : attributes) {
					addOrUpdateAttribute(obj_id, key, value);
				}
			}

			/**
			 * @brief 移除对象的特定属性
			 * @param obj_id 对象ID
			 * @param key 属性键
			 * @return 如果成功移除则返回true，否则返回false
			 */
			bool removeAttribute(const ObjectID& obj_id, const std::string& key) {
				std::unique_lock lock(index_mutex_);

				// 查找对象的属性映射
				auto obj_attrs_it = object_attributes_.find(obj_id);
				if (obj_attrs_it == object_attributes_.end()) {
					return false;
				}

				auto& attrs = obj_attrs_it->second;
				auto key_it = attrs.find(key);
				if (key_it == attrs.end()) {
					return false;
				}

				// 获取旧值并从对象属性映射中移除
				std::string old_value = key_it->second;
				attrs.erase(key_it);

				// 如果对象没有任何属性了，从映射中移除
				if (attrs.empty()) {
					object_attributes_.erase(obj_attrs_it);
				}

				// 从属性索引中移除
				AttributeKey attr_key{ key, old_value };
				auto index_it = attribute_index_.find(attr_key);
				if (index_it != attribute_index_.end()) {
					index_it->second.erase(obj_id);

					// 如果集合为空，移除该键值对
					if (index_it->second.empty()) {
						attribute_index_.erase(index_it);
					}
				}

				LOG_DEBUG("属性索引: 已移除对象 {} 的属性 '{}'", obj_id, key);
				return true;
			}

			/**
			 * @brief 移除对象的所有属性
			 * @param obj_id 对象ID
			 * @return 如果成功移除则返回true，否则返回false
			 */
			bool removeAllAttributes(const ObjectID& obj_id) {
				std::unique_lock lock(index_mutex_);

				auto obj_attrs_it = object_attributes_.find(obj_id);
				if (obj_attrs_it == object_attributes_.end()) {
					return false;
				}

				// 复制属性集，因为我们会在遍历过程中修改它
				auto attrs_copy = obj_attrs_it->second;

				// 从对象属性映射中移除
				object_attributes_.erase(obj_attrs_it);

				// 从所有相关的属性索引中移除
				for (const auto& [key, value] : attrs_copy) {
					AttributeKey attr_key{ key, value };
					auto index_it = attribute_index_.find(attr_key);
					if (index_it != attribute_index_.end()) {
						index_it->second.erase(obj_id);

						// 如果集合为空，移除该键值对
						if (index_it->second.empty()) {
							attribute_index_.erase(index_it);
						}
					}
				}

				LOG_DEBUG("属性索引: 已移除对象 {} 的所有属性", obj_id);
				return true;
			}

			/**
			 * @brief 查询对象是否具有特定属性
			 * @param obj_id 对象ID
			 * @param key 属性键
			 * @return 如果对象具有该属性则返回true，否则返回false
			 */
			bool hasAttribute(const ObjectID& obj_id, const std::string& key) const {
				std::shared_lock lock(index_mutex_);

				auto obj_attrs_it = object_attributes_.find(obj_id);
				if (obj_attrs_it == object_attributes_.end()) {
					return false;
				}

				return obj_attrs_it->second.find(key) != obj_attrs_it->second.end();
			}

			/**
			 * @brief 获取对象的特定属性值
			 * @param obj_id 对象ID
			 * @param key 属性键
			 * @return 属性值的可选引用，如果不存在则为空
			 */
			std::optional<std::string> getAttributeValue(const ObjectID& obj_id, const std::string& key) const {
				std::shared_lock lock(index_mutex_);

				auto obj_attrs_it = object_attributes_.find(obj_id);
				if (obj_attrs_it == object_attributes_.end()) {
					return std::nullopt;
				}

				const auto& attrs = obj_attrs_it->second;
				auto key_it = attrs.find(key);
				if (key_it == attrs.end()) {
					return std::nullopt;
				}

				return key_it->second;
			}

			/**
			 * @brief 获取对象的所有属性
			 * @param obj_id 对象ID
			 * @return 包含所有属性键值对的映射
			 */
			std::unordered_map<std::string, std::string> getAllAttributes(const ObjectID& obj_id) const {
				std::shared_lock lock(index_mutex_);

				auto obj_attrs_it = object_attributes_.find(obj_id);
				if (obj_attrs_it == object_attributes_.end()) {
					return {};
				}

				return obj_attrs_it->second;
			}

			/**
			 * @brief 根据属性键值查找对象
			 * @param key 属性键
			 * @param value 属性值
			 * @return 包含所有匹配对象ID的向量
			 */
			std::vector<ObjectID> findByAttribute(const std::string& key, const std::string& value) const {
				std::shared_lock lock(index_mutex_);

				AttributeKey attr_key{ key, value };
				auto it = attribute_index_.find(attr_key);

				if (it == attribute_index_.end()) {
					return {};
				}

				return std::vector<ObjectID>(it->second.begin(), it->second.end());
			}

			/**
			 * @brief 根据多个属性查找对象（逻辑与关系）
			 * @param attributes 要匹配的属性键值对列表
			 * @return 包含所有匹配对象ID的向量
			 */
			std::vector<ObjectID> findByAttributes(
				const std::vector<std::pair<std::string, std::string>>& attributes) const {

				if (attributes.empty()) {
					return {};
				}

				// 先查找第一个属性
				auto result = findByAttribute(attributes[0].first, attributes[0].second);

				// 如果只有一个属性或者结果为空，直接返回
				if (attributes.size() == 1 || result.empty()) {
					return result;
				}

				// 对于剩余的每个属性，取交集
				std::unordered_set<ObjectID> result_set(result.begin(), result.end());

				for (size_t i = 1; i < attributes.size(); ++i) {
					auto next_result = findByAttribute(attributes[i].first, attributes[i].second);

					if (next_result.empty()) {
						return {};
					}

					std::unordered_set<ObjectID> next_set(next_result.begin(), next_result.end());
					std::unordered_set<ObjectID> intersection;

					for (const auto& id : result_set) {
						if (next_set.find(id) != next_set.end()) {
							intersection.insert(id);
						}
					}

					if (intersection.empty()) {
						return {};
					}

					result_set = std::move(intersection);
				}

				return std::vector<ObjectID>(result_set.begin(), result_set.end());
			}

			/**
			 * @brief 使用自定义谓词查找属性
			 * @param predicate 判断属性是否匹配的谓词函数
			 * @return 包含所有匹配对象ID的向量
			 */
			std::vector<ObjectID> findByPredicate(
				const std::function<bool(const std::unordered_map<std::string, std::string>&)>& predicate) const {

				std::vector<ObjectID> result;

				std::shared_lock lock(index_mutex_);

				for (const auto& [obj_id, attrs] : object_attributes_) {
					if (predicate(attrs)) {
						result.push_back(obj_id);
					}
				}

				return result;
			}

			/**
			 * @brief 清空所有索引
			 */
			void clear() {
				std::unique_lock lock(index_mutex_);
				attribute_index_.clear();
				object_attributes_.clear();

				LOG_INFO("属性索引: 已清空所有索引");
			}

		private:
			// 复合键结构
			struct AttributeKey {
				std::string name;
				std::string value;

				bool operator==(const AttributeKey& other) const {
					return name == other.name && value == other.value;
				}
			};

			// 哈希函数
			struct AttributeKeyHash {
				std::size_t operator()(const AttributeKey& k) const {
					return std::hash<std::string>()(k.name) ^ std::hash<std::string>()(k.value);
				}
			};

			// 属性索引: <属性键,属性值> -> 对象ID集合
			std::unordered_map<AttributeKey, std::unordered_set<ObjectID>, AttributeKeyHash> attribute_index_;

			// 对象属性映射: 对象ID -> (属性键 -> 属性值)
			std::unordered_map<ObjectID, std::unordered_map<std::string, std::string>> object_attributes_;

			// 索引互斥锁
			mutable std::shared_mutex index_mutex_;
		};

	} // namespace NSEnvironment
} // namespace NSDrones
