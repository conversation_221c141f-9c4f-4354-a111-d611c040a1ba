#include "core/geometry/shapes/box_shape.h"
#include "utils/enum_utils.h"
#include <stdexcept>
#include <sstream>
#include <cmath>
#include <algorithm>

namespace NSDrones {
namespace NSCore {

    BoxShape::BoxShape(double length, double width, double height)
        : length_(length), width_(width), height_(height), fcl_box_(nullptr) {
        validateDimensions();
    }

    BoxShape::BoxShape() : BoxShape(1.0, 1.0, 1.0) {
    }

    BoxShape::BoxShape(const BoxShape& other)
        : length_(other.length_), width_(other.width_), height_(other.height_), fcl_box_(nullptr) {
    }

    BoxShape& BoxShape::operator=(const BoxShape& other) {
        if (this != &other) {
            length_ = other.length_;
            width_ = other.width_;
            height_ = other.height_;
            fcl_box_.reset();  // 重置FCL对象，延迟重新创建
        }
        return *this;
    }

    std::shared_ptr<fcl::CollisionGeometryd> BoxShape::getFCLGeometry() const {
        ensureFCLObject();
        return fcl_box_;
    }

    fcl::AABBd BoxShape::getAABB(const fcl::Transform3d& transform) const {
        ensureFCLObject();
        fcl::AABBd aabb;
        fcl::computeBV(*fcl_box_, transform, aabb);
        return aabb;
    }

    double BoxShape::getVolume() const {
        return length_ * width_ * height_;
    }

    double BoxShape::getSurfaceArea() const {
        return 2.0 * (length_ * width_ + width_ * height_ + height_ * length_);
    }

    fcl::Vector3d BoxShape::getCentroid() const {
        return fcl::Vector3d(0.0, 0.0, 0.0);  // 几何中心在原点
    }

    fcl::Matrix3d BoxShape::getInertiaMatrix(double mass) const {
        // 长方体的惯性张量（相对于质心）
        double lx2 = length_ * length_;
        double ly2 = width_ * width_;
        double lz2 = height_ * height_;
        
        fcl::Matrix3d inertia = fcl::Matrix3d::Zero();
        inertia(0, 0) = mass / 12.0 * (ly2 + lz2);  // Ixx
        inertia(1, 1) = mass / 12.0 * (lx2 + lz2);  // Iyy
        inertia(2, 2) = mass / 12.0 * (lx2 + ly2);  // Izz
        
        return inertia;
    }

    std::unique_ptr<IShape> BoxShape::clone() const {
        return std::make_unique<BoxShape>(*this);
    }

    nlohmann::json BoxShape::serialize() const {
        nlohmann::json j;
        j["type"] = NSUtils::enumToString(getType());
        j["length"] = length_;
        j["width"] = width_;
        j["height"] = height_;
        return j;
    }

    bool BoxShape::deserialize(const nlohmann::json& json) {
        try {
            std::string type_str = json.at("type").get<std::string>();
            auto type_opt = NSUtils::stringToEnum<ShapeType>(type_str, ShapeType::UNKNOWN);
            if (type_opt != ShapeType::BOX) {
                return false;
            }

            length_ = json.at("length").get<double>();
            width_ = json.at("width").get<double>();
            height_ = json.at("height").get<double>();

            validateDimensions();
            fcl_box_.reset();  // 重置FCL对象
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    std::string BoxShape::toString() const {
        std::ostringstream oss;
        oss << "BoxShape(length=" << length_ << ", width=" << width_ << ", height=" << height_ << ")";
        return oss.str();
    }

    bool BoxShape::containsPoint(const fcl::Vector3d& point) const {
        double half_length = length_ * 0.5;
        double half_width = width_ * 0.5;
        double half_height = height_ * 0.5;
        
        return (std::abs(point.x()) <= half_length) &&
               (std::abs(point.y()) <= half_width) &&
               (std::abs(point.z()) <= half_height);
    }

    double BoxShape::distanceToPoint(const fcl::Vector3d& point) const {
        double half_length = length_ * 0.5;
        double half_width = width_ * 0.5;
        double half_height = height_ * 0.5;
        
        // 计算点到长方体表面的距离
        double dx = std::max(0.0, std::abs(point.x()) - half_length);
        double dy = std::max(0.0, std::abs(point.y()) - half_width);
        double dz = std::max(0.0, std::abs(point.z()) - half_height);
        
        // 如果点在内部，返回负距离
        if (containsPoint(point)) {
            double min_dist = std::min({half_length - std::abs(point.x()),
                                       half_width - std::abs(point.y()),
                                       half_height - std::abs(point.z())});
            return -min_dist;
        }
        
        return std::sqrt(dx * dx + dy * dy + dz * dz);
    }

    double BoxShape::getCharacteristicSize() const {
        // 使用对角线长度作为特征尺寸
        return std::sqrt(length_ * length_ + width_ * width_ + height_ * height_);
    }

    void BoxShape::setSize(double length, double width, double height) {
        length_ = length;
        width_ = width;
        height_ = height;
        validateDimensions();
        fcl_box_.reset();  // 重置FCL对象
    }

    void BoxShape::setSize(const fcl::Vector3d& size) {
        setSize(size.x(), size.y(), size.z());
    }

    std::vector<fcl::Vector3d> BoxShape::getVertices(const fcl::Transform3d& transform) const {
        double half_length = length_ * 0.5;
        double half_width = width_ * 0.5;
        double half_height = height_ * 0.5;
        
        std::vector<fcl::Vector3d> vertices = {
            fcl::Vector3d(-half_length, -half_width, -half_height),  // 0: ---
            fcl::Vector3d( half_length, -half_width, -half_height),  // 1: +--
            fcl::Vector3d(-half_length,  half_width, -half_height),  // 2: -+-
            fcl::Vector3d( half_length,  half_width, -half_height),  // 3: ++-
            fcl::Vector3d(-half_length, -half_width,  half_height),  // 4: --+
            fcl::Vector3d( half_length, -half_width,  half_height),  // 5: +-+
            fcl::Vector3d(-half_length,  half_width,  half_height),  // 6: -++
            fcl::Vector3d( half_length,  half_width,  half_height)   // 7: +++
        };
        
        // 应用变换
        for (auto& vertex : vertices) {
            vertex = transform * vertex;
        }
        
        return vertices;
    }

    std::vector<fcl::Vector3d> BoxShape::getFaceNormals(const fcl::Transform3d& transform) const {
        std::vector<fcl::Vector3d> normals = {
            fcl::Vector3d( 1.0,  0.0,  0.0),  // +X面
            fcl::Vector3d(-1.0,  0.0,  0.0),  // -X面
            fcl::Vector3d( 0.0,  1.0,  0.0),  // +Y面
            fcl::Vector3d( 0.0, -1.0,  0.0),  // -Y面
            fcl::Vector3d( 0.0,  0.0,  1.0),  // +Z面
            fcl::Vector3d( 0.0,  0.0, -1.0)   // -Z面
        };
        
        // 应用旋转变换（不包括平移）
        fcl::Matrix3d rotation = transform.rotation();
        for (auto& normal : normals) {
            normal = rotation * normal;
        }
        
        return normals;
    }

    bool BoxShape::isCube(double tolerance) const {
        double max_dim = std::max({length_, width_, height_});
        double min_dim = std::min({length_, width_, height_});
        return (max_dim - min_dim) <= tolerance;
    }

    std::unique_ptr<BoxShape> BoxShape::createUnitCube() {
        return std::make_unique<BoxShape>(1.0, 1.0, 1.0);
    }

    std::unique_ptr<BoxShape> BoxShape::createCube(double size) {
        return std::make_unique<BoxShape>(size, size, size);
    }

    void BoxShape::ensureFCLObject() const {
        if (!fcl_box_) {
            fcl_box_ = std::make_shared<fcl::Boxd>(length_, width_, height_);
        }
    }

    void BoxShape::validateDimensions() const {
        if (length_ <= 0.0 || width_ <= 0.0 || height_ <= 0.0) {
            throw std::invalid_argument("BoxShape: 所有尺寸必须为正数");
        }
        if (!std::isfinite(length_) || !std::isfinite(width_) || !std::isfinite(height_)) {
            throw std::invalid_argument("BoxShape: 尺寸必须为有限数值");
        }
    }

} // namespace NSCore
} // namespace NSDrones