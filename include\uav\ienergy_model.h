// include/uav/ienergy_model.h
#pragma once 

#include "core/types.h"       
#include "uav/uav_types.h"    
#include <memory>               

namespace NSDrones { namespace NSUav { class Uav; } }

namespace NSDrones {
	namespace NSUav {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class IEnergyModel
		 * @brief 无人机能量消耗和续航估计的抽象基类接口。
		 *
		 * 定义了能量模型的基本功能。
		 * 模型实例由 UAV 对象持有，并通过构造时传入的 UAV 引用访问参数。
		 */
		class IEnergyModel {
		public:
			/**
			 * @brief 构造函数。
			 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
			 *             用于访问参数和当前状态（如果需要）。
			 */
			explicit IEnergyModel(const Uav& owner);

			/** @brief 虚析构函数。*/
			virtual ~IEnergyModel() = default;

			// --- 禁止拷贝和移动 ---
			IEnergyModel(const IEnergyModel&) = delete;
			IEnergyModel& operator=(const IEnergyModel&) = delete;
			IEnergyModel(IEnergyModel&&) = delete;
			IEnergyModel& operator=(IEnergyModel&&) = delete;

			/**
			 * @brief 获取最大能量容量。
			 *        从 owner_ 获取 "energy.base.max_capacity" 参数。
			 * @return 最大能量容量 (单位与参数一致，例如 Wh)。如果参数无效或未找到则返回 0。
			 */
			virtual double getMaxEnergyCapacity() const;

			/**
			 * @brief 获取最低安全能量水平。
			 *        根据容量和 "energy.base.min_safe_fraction" 参数计算。
			 * @return 最低安全能量水平 (单位与容量一致)。
			 */
			virtual double getMinSafeEnergyLevel() const;

			/**
			 * @brief (纯虚函数) 计算在给定状态下，经过 dt 时间消耗的能量。
			 * @param state 无人机状态。
			 * @param dt 时间步长 (秒)。
			 * @return 消耗的能量 (单位与 max_energy_capacity 一致)。子类必须实现。
			 */
			virtual double computeEnergyConsumption(const UavState& state, Time dt) const = 0;

			/**
			 * @brief (纯虚函数) 估算在给定状态和当前能量下的剩余续航时间。
			 * @param current_state 当前无人机状态。
			 * @param current_energy 当前剩余能量。
			 * @return 估算的剩余续航时间 (秒)。子类必须实现。
			 */
			virtual Time estimateEnduranceTime(const UavState& current_state, double current_energy) const = 0;

		protected:
			/** @brief 指向拥有此模型的无人机对象 (const 引用)。*/
			const Uav& owner_; // 存储 owner 的 const 引用
		};

		// 定义能量模型接口的智能指针类型 
		using IEnergyModelPtr = std::shared_ptr<IEnergyModel>;
		using ConstIEnergyModelPtr = std::shared_ptr<const IEnergyModel>;

	} // namespace NSUav
} // namespace NSDrones