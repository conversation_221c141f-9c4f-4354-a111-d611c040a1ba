#pragma once

#include "core/geometry/ishape.h"
#include <fcl/geometry/shape/cone.h>

namespace NSDrones {
namespace NSCore {

    /**
     * @brief 圆锥体形状
     * 
     * 表示一个以Z轴为中心轴的圆锥体，几何中心位于局部坐标系原点。
     * 圆锥体的轴沿Z轴方向，从-height/2到+height/2，顶点在+Z方向。
     */
    class ConeShape : public IShape {
    private:
        double radius_;  // 圆锥体底面半径
        double height_;  // 圆锥体高度
        
        mutable std::shared_ptr<fcl::Coned> fcl_cone_;  // FCL几何对象（延迟创建）

    public:
        /**
         * @brief 构造函数
         * @param radius 圆锥体底面半径
         * @param height 圆锥体高度
         */
        ConeShape(double radius, double height);

        /**
         * @brief 默认构造函数（单位圆锥）
         */
        ConeShape();

        /**
         * @brief 拷贝构造函数
         */
        ConeShape(const ConeShape& other);

        /**
         * @brief 赋值操作符
         */
        ConeShape& operator=(const ConeShape& other);

        // IShape接口实现
        ShapeType getType() const override { return ShapeType::CONE; }
        int getDimension() const override { return 3; }  // 3D实体
        std::shared_ptr<fcl::CollisionGeometryd> getFCLGeometry() const override;
        fcl::AABBd getAABB(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const override;
        double getVolume() const override;
        double getSurfaceArea() const override;
        fcl::Vector3d getCentroid() const override;
        fcl::Matrix3d getInertiaMatrix(double mass) const override;
        std::unique_ptr<IShape> clone() const override;
        nlohmann::json serialize() const override;
        bool deserialize(const nlohmann::json& json) override;
        std::string toString() const override;
        bool containsPoint(const fcl::Vector3d& point) const override;
        double distanceToPoint(const fcl::Vector3d& point) const override;
        double getCharacteristicSize() const override;

        // ConeShape特有方法
        /**
         * @brief 获取底面半径
         */
        double getRadius() const { return radius_; }

        /**
         * @brief 获取高度
         */
        double getHeight() const { return height_; }

        /**
         * @brief 获取底面直径
         */
        double getDiameter() const { return 2.0 * radius_; }

        /**
         * @brief 设置尺寸
         * @param radius 底面半径
         * @param height 高度
         */
        void setSize(double radius, double height);

        /**
         * @brief 获取底面积
         */
        double getBaseArea() const;

        /**
         * @brief 获取侧面积
         */
        double getLateralArea() const;

        /**
         * @brief 获取母线长度
         */
        double getSlantHeight() const;

        /**
         * @brief 计算圆锥面上的点
         * @param theta 角度（0到2π）
         * @param z Z坐标（-height/2到+height/2）
         * @param transform 变换矩阵
         * @return 圆锥面上的点
         */
        fcl::Vector3d getPointOnCone(double theta, double z,
                                    const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 生成圆锥面上的均匀采样点
         * @param num_circumference 圆周方向点数
         * @param num_height 高度方向点数
         * @param include_base 是否包含底面
         * @param transform 变换矩阵
         * @return 点列表
         */
        std::vector<fcl::Vector3d> generateUniformPoints(size_t num_circumference, size_t num_height, 
                                                         bool include_base = true,
                                                         const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 计算表面法向量（在给定点处）
         * @param point 表面上的点
         * @param transform 变换矩阵
         * @return 法向量
         */
        fcl::Vector3d getSurfaceNormal(const fcl::Vector3d& point,
                                      const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 创建单位圆锥（半径1，高度2）
         * @return ConeShape实例
         */
        static std::unique_ptr<ConeShape> createUnitCone();

        /**
         * @brief 从直径创建圆锥
         * @param diameter 底面直径
         * @param height 高度
         * @return ConeShape实例
         */
        static std::unique_ptr<ConeShape> createFromDiameter(double diameter, double height);

    private:
        /**
         * @brief 确保FCL对象已创建
         */
        void ensureFCLObject() const;

        /**
         * @brief 验证尺寸参数
         */
        void validateDimensions() const;
    };

} // namespace NSCore
} // namespace NSDrones
