// include/planning/task_planners/task_planner_loiterpoint.h
#pragma once

#include "planning/itask_planner.h"
#include "environment/environment_fwd.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/control_point.h"

namespace NSDrones {
	namespace NSPlanning {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class LoiterPointTaskPlanner
		 * @brief 定点盘旋任务规划器
		 *
		 * 负责规划LOITER_POINT类型任务，支持：
		 * - 圆形盘旋路径生成
		 * - 可配置的盘旋半径和持续时间
		 * - 顺时针/逆时针盘旋方向
		 * - 多圈盘旋支持
		 *
		 * ## 主要功能
		 * - **路径优化**: 计算最优的盘旋入口点
		 * - **时间控制**: 根据持续时间计算盘旋圈数
		 * - **精确定位**: 使用GeographicLib进行精确的地理计算
		 * - **动态适应**: 根据无人机性能调整盘旋参数
		 */
		class LoiterPointTaskPlanner : public ITaskPlanner {
		public:
			// === 生命周期管理 ===

			/**
			 * @brief 构造函数
			 */
			LoiterPointTaskPlanner();

			/**
			 * @brief 初始化盘旋任务规划器
			 * @param params 配置参数对象
			 * @param raw_config 原始JSON配置
			 * @return 初始化成功返回true
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> params,
						   const nlohmann::json& raw_config) override;

			// === 核心接口实现 ===

			/**
			 * @brief 规划单机定点盘旋任务（重构后的接口）
			 * @param request 单任务规划请求
			 * @return 单任务规划结果
			 */
			SingleTaskPlanningResult planSingleTask(const SingleTaskPlanningRequest& request) override;

			/**
			 * @brief 检查是否支持指定的子任务类型
			 * @param sub_target 子任务目标
			 * @return 是否支持
			 */
			bool isSubTaskSupported(const SubTaskTarget& sub_target) const override{
				return sub_target.task_type == NSMission::TaskType::LOITER_POINT;
			}

		private:
			// === 配置参数 ===
			int default_points_per_circle_ = 36;  ///< 默认每圈航点数
			double min_loiter_radius_ = 10.0;     ///< 最小盘旋半径(米)

			// === 私有辅助方法 ===

			/**
			 * @brief 规划盘旋轨迹的核心实现
			 * @param request 单任务规划请求
			 * @param params 盘旋任务参数
			 * @param start_state 起始状态
			 * @return 单任务规划结果
			 */
			SingleTaskPlanningResult planLoiterTrajectory(
				const SingleTaskPlanningRequest& request,
				const NSMission::LoiterPointTaskParams& params,
				const NSUav::UavState& start_state);

			/**
			 * @brief 应用避障约束到轨迹
			 * @param trajectory 输入输出轨迹
			 * @param constraints 避障约束列表
			 * @param result 结果对象，用于添加警告
			 * @return 成功返回true
			 */
			bool applyAvoidanceConstraints(
				Trajectory& trajectory,
				const std::vector<AvoidanceConstraint>& constraints,
				SingleTaskPlanningResult& result);

			/**
			 * @brief 生成盘旋路径点
			 * @param center 盘旋中心点
			 * @param radius 盘旋半径
			 * @param num_points 每圈点数
			 * @param clockwise 是否顺时针
			 * @return 盘旋路径点列表
			 */
			std::vector<NSCore::WGS84Point> generateLoiterWaypoints(
				const NSCore::WGS84Point& center,
				double radius,
				int num_points,
				bool clockwise) const;


		};

	} // namespace NSPlanning
} // namespace NSDrones