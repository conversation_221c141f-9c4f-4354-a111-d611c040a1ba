{"description": "Parameters for AlgorithmObject.RRT* Path Planner.", "parameters": [{"key": "max_iterations", "type": "int", "name": "最大迭代次数", "description": "RRT* 算法的最大迭代次数。", "default": 5000, "constraints": {"type": "numeric", "min": 100, "max": 100000}}, {"key": "step_size_m", "type": "double", "name": "步长 (米)", "description": "RRT* 算法扩展时的步长，单位为米。", "default": 5.0, "constraints": {"type": "numeric", "min": 0.1, "max": 50.0}}, {"key": "goal_bias", "type": "double", "description": "目标偏置采样概率 (0 到 1)", "default": 0.1, "constraints": {"type": "numeric", "min": 0.0, "max": 1.0}}, {"key": "rewire_radius_factor", "type": "double", "description": "重连半径因子 (相对于步长)", "default": 1.5, "constraints": {"type": "numeric", "min": 1.0, "max": 5.0}}, {"key": "collision_check_resolution_m", "type": "double", "description": "路径段碰撞检测分辨率 (米)", "default": 0.5, "constraints": {"type": "numeric", "min": 0.1, "max": 5.0}}, {"key": "smoothing_iterations", "type": "int", "description": "路径平滑迭代次数 (0 表示不平滑)", "default": 10, "constraints": {"type": "numeric", "min": 0, "max": 100}}]}