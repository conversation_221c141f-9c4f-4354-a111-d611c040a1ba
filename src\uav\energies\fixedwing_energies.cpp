// src/uav/energies/fixedwing_energies.cpp
#include "uav/energies/fixedwing_energies.h"
#include "uav/uav.h"      
#include "utils/logging.h"           
#include <cmath>                     
#include <stdexcept>                
#include <algorithm>                 
#include <limits>                   

namespace NSDrones {
	namespace NSUav {

		/**
		 * @brief FixedWingEnergies 构造函数。
		 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
		 */
		FixedWingEnergies::FixedWingEnergies(const Uav& owner)
			: IEnergyModel(owner) // 调用基类构造函数，传递 owner
		{
			LOG_DEBUG("固定翼能量模型已创建，所有者: {}", owner_.getId());
		}

		/**
		 * @brief 计算固定翼能量消耗。
		 *        参数查找基于 "energy.fw.*"。
		 */
		double FixedWingEnergies::computeEnergyConsumption(const UavState& state, Time dt) const {
			// 检查时间步长是否有效
			if (dt <= Constants::TIME_EPSILON) {
				LOG_TRACE("计算固定翼能量消耗：时间步长过小 ({:.4f}s)，返回 0。", dt);
				return 0.0;
			}

			// --- 1. 从 owner_ 获取状态和参数 ---
			double speed = state.velocity.norm(); // 当前总速度 (m/s)
			LOG_TRACE("计算固定翼能量消耗 (所有者 ID: {})：速度={:.2f} m/s, dt={:.4f}s", owner_.getId(), speed, dt);

			// 从参数获取物理和气动特性 (键名示例)
			// 使用 getParamOrDefault 提供默认值，防止缺少参数导致崩溃
			double mass = owner_.getParamOrDefault<double>("energy.fw.mass", 1.5); // kg
			double g = owner_.getParamOrDefault<double>("energy.fw.gravity", Constants::GRAVITY); // m/s^2
			double wing_area = owner_.getParamOrDefault<double>("energy.fw.wing_area", 0.5); // m^2
			double rho = owner_.getParamOrDefault<double>("energy.fw.air_density", Constants::AIR_DENSITY_SEA_LEVEL_ISA); // kg/m^3
			double cd0 = owner_.getParamOrDefault<double>("energy.fw.drag_coeff_zero_lift", 0.02); // 零升阻力系数
			double ar = owner_.getParamOrDefault<double>("energy.fw.aspect_ratio", 8.0); // 展弦比
			double oswald_e = owner_.getParamOrDefault<double>("energy.fw.oswald_efficiency", 0.8); // 奥斯瓦尔德效率因子
			double prop_eff = owner_.getParamOrDefault<double>("energy.fw.prop_efficiency", 0.75); // 螺旋桨效率
			double baseline_power = owner_.getParamOrDefault<double>("energy.fw.baseline_power", 5.0); // 基线功率 W
			double discharging_eff = owner_.getParamOrDefault<double>("energy.base.discharging_efficiency", 0.95); // 放电效率
			// 获取最小运行速度 (失速速度)，应从动力学参数获取
			// 注意：这里假设能量模型可以访问动力学参数，键名可能需要调整
			double min_operational_speed = owner_.getParamOrDefault<double>("dynamics.fw.stallSpeed", 10.0); // m/s

			// 验证参数有效性
			mass = std::max(Constants::EPSILON, mass);
			wing_area = std::max(Constants::EPSILON, wing_area);
			rho = std::max(Constants::EPSILON, rho);
			ar = std::max(Constants::EPSILON, ar);
			oswald_e = std::clamp(oswald_e, Constants::EPSILON, 1.0);
			prop_eff = std::clamp(prop_eff, Constants::EPSILON, 1.0);
			baseline_power = std::max(0.0, baseline_power);
			discharging_eff = std::clamp(discharging_eff, Constants::EPSILON, 1.0);
			min_operational_speed = std::max(0.0, min_operational_speed);

			// 检查速度是否低于最小运行速度（如果正在飞行）
			if (speed > Constants::VELOCITY_EPSILON && speed < min_operational_speed) {
				LOG_TRACE("  当前速度 {:.2f} 低于最小运行速度 {:.2f}，能量计算将使用最小速度。", speed, min_operational_speed);
				speed = min_operational_speed; // 使用最小速度进行计算
			}
			else if (speed < Constants::VELOCITY_EPSILON) {
				// 如果速度接近零（例如地面静止），只消耗基线功率
				LOG_TRACE("  速度接近零 ({:.2f})，仅计算基线功率消耗。", speed);
				// 能量 (Wh) = (功率 * 时间 / 效率) / 3600
				return (baseline_power * dt / discharging_eff) / 3600.0;
			}

			// --- 2. 计算气动力 ---
			double dynamic_pressure = 0.5 * rho * speed * speed; // 计算动压 (Pa 或 N/m^2)
			// 检查动压是否有效
			if (dynamic_pressure < Constants::EPSILON) {
				// 动压过小，无法计算升阻力，回退到基线功率
				LOG_WARN("动压接近零 ({:.4e})，能量计算仅考虑基线功率 (所有者 ID: {})。", dynamic_pressure, owner_.getId());
				return (baseline_power * dt / discharging_eff) / 3600.0; // Wh
			}

			// 简化：假设升力主要用于平衡重力在垂直于速度方向的分量
			// 更准确：升力 L = m*g / cos(bank_angle) - m*a_vertical ?
			// 简化模型：假设 L ≈ m*g (忽略爬升/下降和转弯对升力系数计算的影响)
			double lift_required = mass * g; // 所需升力近似等于重力
			double lift_coeff = lift_required / (dynamic_pressure * wing_area); // 计算所需升力系数
			LOG_TRACE("  参数: m={:.2f}kg, S={:.2f}m², rho={:.3f}, Cd0={:.4f}, AR={:.1f}, e={:.2f}", mass, wing_area, rho, cd0, ar, oswald_e);
			LOG_TRACE("  计算: 动压={:.1f} Pa, 需升力≈{:.1f} N, 升力系数={:.3f}", dynamic_pressure, lift_required, lift_coeff);

			// 计算阻力系数 (Cd = Cd0 + Cdi)
			// 诱导阻力系数 Cdi = Cl^2 / (pi * AR * e)
			double drag_coeff_induced = (lift_coeff * lift_coeff) / (Constants::PI * ar * oswald_e);
			double total_drag_coeff = cd0 + drag_coeff_induced; // 总阻力系数
			LOG_TRACE("  诱导阻力系数={:.4f}, 总阻力系数={:.4f}", drag_coeff_induced, total_drag_coeff);

			// 计算阻力大小 Fd = 0.5 * rho * V^2 * S * Cd
			double drag_force = dynamic_pressure * wing_area * total_drag_coeff;
			LOG_TRACE("  计算阻力={:.2f} N", drag_force);

			// --- 3. 计算所需机械功率 ---
			// P_mech = (Thrust * V) + (Weight * climb_rate)
			// 简化：假设平飞巡航为主，推力近似等于阻力
			// 更准确：推力需要克服阻力、重力分量和提供加速度
			// Thrust ≈ Drag + m*g*sin(climb_angle) + m*acceleration_along_velocity
			// 这里简化：Thrust ≈ Drag
			double thrust_required = drag_force; // 推力近似等于阻力
			double aerodynamic_power_required = thrust_required * speed; // 克服阻力所需功率 (W)
			double climb_rate = state.velocity.z(); // 垂直速度 (m/s)
			double climb_power = mass * g * climb_rate; // 克服重力做功的功率 (爬升为正，下降为负, W)
			// 总机械功率 = 气动功率 + 爬升功率 (忽略加速功率的简化)
			double total_mechanical_power = aerodynamic_power_required + climb_power;
			LOG_TRACE("  气动功率={:.1f} W, 爬升功率={:.1f} W, 总机械功率≈{:.1f} W", aerodynamic_power_required, climb_power, total_mechanical_power);

			// --- 4. 计算电机轴功率 (考虑螺旋桨效率) ---
			double shaft_power_required = 0.0;
			// 确保螺旋桨效率在 (0, 1] 范围内
			if (prop_eff <= Constants::EPSILON) {
				LOG_WARN_ONCE("固定翼螺旋桨效率 ({:.3f}) 无效，轴功率计算可能不准确 (所有者 ID: {})。", prop_eff, owner_.getId());
				prop_eff = 0.01; // 使用小值避免除零
			}
			// 简化：假设电机不能回收能量，轴功率需求非负
			shaft_power_required = std::max(0.0, total_mechanical_power) / prop_eff; // 轴功率 = 机械功率 / 螺旋桨效率
			LOG_TRACE("  螺旋桨效率={:.3f}, 轴功率需求={:.1f} W", prop_eff, shaft_power_required);

			// --- 5. 计算总电力消耗 ---
			double total_electrical_power = shaft_power_required + baseline_power; // 总电力 = 轴功率 + 基线功率 (W)
			LOG_TRACE("  基线功率={:.1f} W, 总电力需求={:.1f} W", baseline_power, total_electrical_power);

			// --- 6. 计算能量消耗 (考虑电池放电效率) ---
			// 确保放电效率在 (0, 1] 范围内
			if (discharging_eff <= Constants::EPSILON) {
				LOG_WARN_ONCE("固定翼放电效率 ({:.3f}) 无效，能量计算可能不准确 (所有者 ID: {})。", discharging_eff, owner_.getId());
				discharging_eff = 1.0; // 使用 1.0 作为回退
			}
			double energy_consumed_joules = (total_electrical_power * dt) / discharging_eff; // 能量 (焦耳) = (电力 * 时间) / 效率

			// --- 7. 转换为 Wh ---
			double energy_consumed_wh = energy_consumed_joules / 3600.0;
			LOG_TRACE("  时间步长: {:.4f}s, 放电效率: {:.3f}, 消耗能量: {:.4f} J ({:.6f} Wh)", dt, discharging_eff, energy_consumed_joules, energy_consumed_wh);

			return energy_consumed_wh; // 返回 Wh
		}

		/**
		 * @brief 估算固定翼剩余续航时间。
		 */
		Time FixedWingEnergies::estimateEnduranceTime(
			const UavState& current_state,
			double current_energy) const
		{
			LOG_TRACE("估算固定翼剩余续航时间 (所有者 ID: {})：当前能量 {:.2f} Wh", owner_.getId(), current_energy);

			// 1. 计算可用安全能量
			double safe_energy = current_energy - getMinSafeEnergyLevel(); // getMinSafeEnergyLevel 已处理容量和比例
			LOG_TRACE("  最低安全能量: {:.2f} Wh, 可用安全能量: {:.2f} Wh", getMinSafeEnergyLevel(), safe_energy);
			if (safe_energy <= Constants::EPSILON) {
				LOG_DEBUG("  可用安全能量不足 ({:.2f} Wh)，剩余续航为 0。", safe_energy);
				return 0.0; // 可用能量不足
			}

			// 2. 估算最佳续航速度下的功耗
			//    简化：使用一个典型的巡航速度参数，或者计算理论最佳续航速度
			//    这里使用参数 "energy.fw.cruise_speed_est"
			// 从 owner_ 获取参数
			double est_speed = owner_.getParamOrDefault<double>("energy.fw.cruise_speed_est", 15.0); // 假设有巡航速度参数 (m/s)
			double min_operational_speed = owner_.getParamOrDefault<double>("dynamics.fw.stallSpeed", 10.0); // 获取失速速度
			// 确保估算速度不低于失速速度
			est_speed = std::max(est_speed, min_operational_speed);
			LOG_TRACE("  使用估算速度: {:.1f} m/s (不低于失速速度 {:.1f})", est_speed, min_operational_speed);
			if (est_speed <= Constants::VELOCITY_EPSILON) {
				LOG_WARN("估算固定翼续航时，使用的速度 ({:.2f}) 无效。", est_speed);
				return 0.0; // 速度无效，无法估算
			}

			// 获取其他所需参数 (同 computeEnergyConsumption)
			double mass = owner_.getParamOrDefault<double>("energy.fw.mass", 1.5);
			double g = owner_.getParamOrDefault<double>("energy.fw.gravity", Constants::GRAVITY);
			double wing_area = owner_.getParamOrDefault<double>("energy.fw.wing_area", 0.5);
			double rho = owner_.getParamOrDefault<double>("energy.fw.air_density", Constants::AIR_DENSITY_SEA_LEVEL_ISA);
			double cd0 = owner_.getParamOrDefault<double>("energy.fw.drag_coeff_zero_lift", 0.02);
			double ar = owner_.getParamOrDefault<double>("energy.fw.aspect_ratio", 8.0);
			double oswald_e = owner_.getParamOrDefault<double>("energy.fw.oswald_efficiency", 0.8);
			double prop_eff = owner_.getParamOrDefault<double>("energy.fw.prop_efficiency", 0.75);
			double baseline_power = owner_.getParamOrDefault<double>("energy.fw.baseline_power", 5.0);
			double discharging_eff = owner_.getParamOrDefault<double>("energy.base.discharging_efficiency", 0.95);

			// 验证参数
			mass = std::max(Constants::EPSILON, mass);
			wing_area = std::max(Constants::EPSILON, wing_area);
			rho = std::max(Constants::EPSILON, rho);
			ar = std::max(Constants::EPSILON, ar);
			oswald_e = std::clamp(oswald_e, Constants::EPSILON, 1.0);
			prop_eff = std::clamp(prop_eff, Constants::EPSILON, 1.0);
			baseline_power = std::max(0.0, baseline_power);
			discharging_eff = std::clamp(discharging_eff, Constants::EPSILON, 1.0);


			// 3. 计算在该速度下的平飞功率
			double dynamic_pressure = 0.5 * rho * est_speed * est_speed;
			if (dynamic_pressure < Constants::EPSILON) {
				LOG_WARN("估算续航时动压接近零，返回无穷大续航。");
				return Constants::INF; // 动压过小，无法飞行，理论续航无限？或者返回 0？
			}

			// 假设升力等于重力 (平飞) L = W = m*g
			double lift_coeff = (mass * g) / (dynamic_pressure * wing_area);
			// 阻力系数 Cdi = Cl^2 / (pi * AR * e)
			double drag_coeff_induced = (lift_coeff * lift_coeff) / (Constants::PI * ar * oswald_e);
			double total_drag_coeff = cd0 + drag_coeff_induced;
			// 阻力大小 Fd = 0.5 * rho * V^2 * S * Cd
			double drag_force = dynamic_pressure * wing_area * total_drag_coeff;
			// 气动功率 (假设平飞，推力=阻力) P_aero = Fd * V
			double aerodynamic_power_required = drag_force * est_speed;
			LOG_TRACE("  估算速度={:.1f}m/s, 动压={:.1f}, Cl={:.3f}, Cd={:.4f}, Fd={:.2f}N", est_speed, dynamic_pressure, lift_coeff, total_drag_coeff, drag_force);
			LOG_TRACE("  估算气动功率需求: {:.1f} W (在 {:.1f} m/s)", aerodynamic_power_required, est_speed);

			// 4. 计算轴功率和总电力
			if (prop_eff <= Constants::EPSILON) { prop_eff = 0.01; /* handle invalid eff */ }
			double shaft_power_required = std::max(0.0, aerodynamic_power_required) / prop_eff; // 轴功率
			double estimated_electrical_power = shaft_power_required + baseline_power; // 总电力
			LOG_TRACE("  估算总电力需求: {:.1f} W (轴功率 {:.1f} W + 基线 {:.1f} W)", estimated_electrical_power, shaft_power_required, baseline_power);

			// 5. 考虑效率计算有效功率消耗
			if (discharging_eff <= Constants::EPSILON) { discharging_eff = 1.0; /* handle invalid eff */ }
			double effective_power_draw = estimated_electrical_power / discharging_eff; // 电池输出功率
			LOG_TRACE("  放电效率: {:.3f}, 有效功率消耗: {:.1f} W", discharging_eff, effective_power_draw);

			// 6. 计算剩余时间
			if (effective_power_draw <= Constants::EPSILON) {
				LOG_WARN("有效功率消耗接近零 ({:.1f}W)，无法估算续航时间，返回无穷大。", effective_power_draw);
				return Constants::INF; // 功率消耗为零，理论上续航无限
			}
			// 时间 (小时) = 能量 (Wh) / 功率 (W)
			double remaining_hours = safe_energy / effective_power_draw;
			Time remaining_seconds = remaining_hours * 3600.0; // 转换为秒
			LOG_DEBUG("  估算剩余续航时间: {:.2f} 小时 ({:.1f} 秒)", remaining_hours, remaining_seconds);

			return remaining_seconds; // 返回秒
		}

	} // namespace NSUav
} // namespace NSDrones