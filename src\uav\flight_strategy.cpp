#include "uav/flight_strategy.h"
#include "uav/uav.h" 
#include "uav/idynamic_model.h"
#include "uav/uav_types.h" 
#include "utils/logging.h"
#include "utils/object_id.h" 
#include "environment/environment.h" 
#include <nlohmann/json.hpp> 

namespace NSDrones {
namespace NSUav {

FlightStrategy::FlightStrategy(const EntityObject& owner)
    : IMovementStrategy(owner) {
    LOG_DEBUG("UAVFlightStrategy: 策略实例已创建。");
    // 设置内部默认值，以防参数文件或参数未定义
    default_cruise_speed_ = 5.0; 
    target_altitude_agl_ = 50.0;
}

bool FlightStrategy::initialize(const nlohmann::json& params, EntityObject& owner) {
    LOG_DEBUG("UAVFlightStrategy: 初始化对象 '{}' 的飞行策略...", owner.getId());
    owner_uav_ = dynamic_cast<NSUav::Uav*>(&owner);
    if (!owner_uav_) {
        LOG_ERROR("UAVFlightStrategy: 初始化失败，属主对象 '{}' 不是 UAV 类型。", owner.getId());
        return false;
    }

    // 从传入的 JSON 参数中加载策略配置
    if (params.is_object() && !params.empty()) {
        LOG_INFO("UAVFlightStrategy: 对象 '{}' 收到飞行策略参数: {}。", owner.getId(), params.dump(2));
        
        // 使用 nlohmann::json::value() 方法安全地获取参数
        // 第三个参数是默认值，这里使用成员变量已经持有的（在构造函数中设置的）默认值
        default_cruise_speed_ = params.value("default_cruise_speed", default_cruise_speed_);
        target_altitude_agl_ = params.value("target_altitude_agl", target_altitude_agl_);

        LOG_INFO("UAVFlightStrategy: 对象 '{}' 的飞行策略参数已加载：default_cruise_speed = {:.2f} m/s, target_altitude_agl = {:.2f} m AGL。",
                 owner.getId(), default_cruise_speed_, target_altitude_agl_);
    } else {
        LOG_INFO("UAVFlightStrategy: 对象 '{}' 未收到有效的飞行策略 JSON 参数，或参数为空。将使用内部默认值 (速度: {:.2f} m/s, 高度: {:.2f} m AGL)。",
            owner.getId(), default_cruise_speed_, target_altitude_agl_);
    }

    LOG_INFO("UAVFlightStrategy: 对象 '{}' 的飞行策略初始化完成。", owner.getId());
    return true;
}

void FlightStrategy::updateState(EntityObject& object, Time dt) {
    if (!owner_uav_ || &object != owner_uav_) {
        LOG_ERROR("UAVFlightStrategy: updateState 调用了错误的对象或未正确初始化 (期望ID: {}, 实际ID: {})。",
                  owner_uav_ ? owner_uav_->getId() : "nullptr", object.getId());
        return;
    }

    EntityStatePtr current_object_state = owner_uav_->getState(); // 获取当前对象状态
    auto dynamic_model = owner_uav_->getDynamicsModel();

    if (!dynamic_model) {
        LOG_WARN("UAVFlightStrategy: UAV '{}' 没有有效的动力学模型，无法通过策略更新状态。", owner_uav_->getId());
        return;
    }

    LOG_TRACE("UAVFlightStrategy: 更新 UAV '{}' 状态 (dt={:.4f})。 当前位置: {}, 速度: ({:.2f}, {:.2f}, {:.2f})",
              owner_uav_->getId(), dt,
              current_object_state->position.toString(),
              current_object_state->velocity_wgs84.x(), current_object_state->velocity_wgs84.y(), current_object_state->velocity_wgs84.z());

    // --- 简化飞行逻辑：基于当前速度更新位置 ---
    // 在一个完整的实现中，这里会涉及到：
    // 1. 从导航系统获取目标点/路径/速度指令。
    // 2. 使用动力学模型计算达到该目标所需的控制输入。
    // 3. 模拟动力学模型在 dt 时间内的演化，得到新的状态 (位置, 速度, 姿态, 加速度等)。
    // 4. 更新能量消耗。

    EntityStatePtr new_object_state = current_object_state;
    // 使用ECEF坐标系进行精确的几何运算
    WGS84Point current_wgs84 = owner_uav_->getWGS84Position();
    EcefPoint current_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(current_wgs84);

    // 在ECEF坐标系中进行位置更新
    EcefPoint new_ecef(current_ecef.toVector3D() + current_object_state->velocity_wgs84 * dt);
    WGS84Point new_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(new_ecef);

    // 更新WGS84位置
    owner_uav_->setWGS84Position(new_wgs84);

    LOG_TRACE("UAVFlightStrategy: UAV '{}' 状态已通过策略更新。新WGS84位置: {}",
        owner_uav_->getId(), new_wgs84.toString());

    // 假设姿态、角速度等由更底层的控制器或动力学模型内部细节处理，
    // 或者如果此策略负责，则需要在此处更新。
    // 例如，如果速度方向改变，姿态也应该相应改变。
    // new_object_state->orientation = new_orientation_if_needed;

    new_object_state->time_stamp += dt;

    // 使用 EntityObject 的方法来更新其核心动态数据，这将触发 notifyUpdate
    owner_uav_->updateState(*new_object_state);
}

WGS84Point FlightStrategy::predictWGS84Position(const EntityObject& object, Time dt) const {
    // 获取当前WGS84位置作为回退值
    WGS84Point fallback_pos = object.getWGS84Position();

    if (!owner_uav_ || &object != owner_uav_) {
        LOG_ERROR("UAVFlightStrategy: predictWGS84Position 调用了错误的对象或未正确初始化 (期望ID: {}, 实际ID: {})。返回当前WGS84位置。",
                  owner_uav_ ? owner_uav_->getId() : "nullptr", object.getId());
        return fallback_pos;
    }

    // 使用ECEF坐标系进行精确的位置预测
    EcefPoint current_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(fallback_pos);

    // 简单的基于当前速度的预测（更复杂的预测会使用动力学模型）
    Vector3D velocity_wgs84 = owner_uav_->getVelocity();

    if (velocity_wgs84.norm() > Constants::VELOCITY_EPSILON) {
        // 在ECEF坐标系中进行位置预测（假设velocity_wgs84是ECEF坐标系中的速度）
        EcefPoint predicted_ecef(current_ecef.toVector3D() + velocity_wgs84 * dt);
        WGS84Point predicted_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(predicted_ecef);

        LOG_TRACE("UAVFlightStrategy: UAV '{}' 预测WGS84位置 (dt={:.4f}): {}",
                  owner_uav_->getId(), dt, predicted_wgs84.toString());
        return predicted_wgs84;
    } else {
        LOG_TRACE("UAVFlightStrategy: UAV '{}' 无速度信息，返回当前WGS84位置: {}",
                  owner_uav_->getId(), fallback_pos.toString());
        return fallback_pos; // 返回当前WGS84位置作为回退
    }
}

} // namespace NSUav
} // namespace NSDrones 