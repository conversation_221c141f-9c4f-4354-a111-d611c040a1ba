// src/algorithm/evaluator/energy_evaluator.cpp
#include "algorithm/evaluator/energy_evaluator.h"
#include "algorithm/algorithm_object.h" // 确保包含基类定义
#include "uav/uav.h"
#include "uav/uav_types.h"
#include "uav/ienergy_model.h"
#include "uav/idynamic_model.h" // 检查是否实际需要，如果不需要可以移除
#include "core/types.h"
#include "utils/logging.h"
#include <limits>
#include <cmath>
#include <iomanip>
#include <sstream>
#include <string>
#include "environment/environment.h" // 包含 Environment 定义
#include "params/parameters.h"   // 包含 ParamValues 定义
#include "utils/logging.h"
#include <stdexcept>

namespace {
	// 辅助函数：将 FlightMode 转换为字符串
	std::string flightModeToString(NSDrones::NSUav::FlightMode mode) {
		using namespace NSDrones::NSUav;
		switch (mode) {
		case FlightMode::UNKNOWN: return "Unknown";
		case FlightMode::HOVER: return "Hover";
		case FlightMode::FIXED_WING: return "FixedWing";
		default: return "InvalidMode";
		}
	}

} // 匿名命名空间结束

namespace NSDrones {
	namespace NSAlgorithm {

		// Constructor implementation
		EnergyEvaluator::EnergyEvaluator(ObjectID id,
                                           const std::string& type_tag,
                                           std::shared_ptr<NSUav::IEnergyModel> energy_model, // 保持 energy_model 参数
                                           const std::string& name,
                                           const std::string& version)
			: AlgorithmObject(id, type_tag, name, version), // 调用 AlgorithmObject 构造函数
			  ITrajectoryEvaluator(), // 调用 ITrajectoryEvaluator 默认构造函数
			  energy_model_(std::move(energy_model))
		{
			if (!energy_model_) {
				std::string error_msg = "EnergyEvaluator 构造失败: energy_model_ 为空。ID: " + id + ", 名称: " + getName();
				LOG_ERROR("[EnergyEvaluator] {}", error_msg);
				throw std::invalid_argument(error_msg);
			}
			LOG_INFO("[EnergyEvaluator] EnergyEvaluator 实例已创建。ID: {}, 名称: {}, 版本: {}", id, getName(), getVersion());
		}

		bool EnergyEvaluator::initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) {
			LOG_DEBUG("[EnergyEvaluator] 开始初始化 EnergyEvaluator: ID = {}, Name = {}", getId(), getName());

			// 1. 调用基类的 initialize 方法
			if (!AlgorithmObject::initialize(params, raw_config)) {
				LOG_ERROR("[EnergyEvaluator] 基类 AlgorithmObject 初始化失败。ID: {}, Name = {}", getId(), getName());
				return false;
			}

			// 2. EnergyEvaluator 特定的初始化
			if (params) {
				// 使用正确的枚举获取方法处理 calculation_fidelity 参数
				auto fidelity_opt = params->getEnumValue<CalculationFidelityType>("calculation_fidelity");
				if (!fidelity_opt.has_value()) {
					LOG_WARN("[EnergyEvaluator] 无法获取 calculation_fidelity 枚举值，使用默认值 SIMPLE。ID: {}, Name: {}", getId(), getName());
					fidelity_opt = CalculationFidelityType::SIMPLE;
				}

				calculation_fidelity_ = fidelity_opt.value();
				std::string fidelity_name = NSUtils::enumToString(calculation_fidelity_);
				LOG_INFO("[EnergyEvaluator] 配置的计算保真度级别: {}。ID: {}, Name: {}", fidelity_name, getId(), getName());

				// 根据保真度级别加载相应的参数
				switch (calculation_fidelity_) {
				case CalculationFidelityType::SIMPLE:
					// 加载简单模型参数
					simple_model_hover_w_ = params->getValueOrDefault<double>("simple_model_hover_w", 150.0);
					simple_model_forward_coeff_ = params->getValueOrDefault<double>("simple_model_forward_coeff", 0.5);
					LOG_DEBUG("[EnergyEvaluator] 简单模型参数: 悬停功耗={:.1f}W, 前飞系数={:.3f}。ID: {}",
						simple_model_hover_w_, simple_model_forward_coeff_, getId());
					break;
				case CalculationFidelityType::DETAILED:
					LOG_INFO("[EnergyEvaluator] 详细计算模式已配置，将使用无人机自身的详细能量模型。ID: {}", getId());
					break;
				case CalculationFidelityType::UNKNOWN:
				default:
					LOG_WARN("[EnergyEvaluator] 未知的计算保真度级别 '{}'，回退到 SIMPLE 模式。ID: {}", fidelity_name, getId());
					calculation_fidelity_ = CalculationFidelityType::SIMPLE;
					simple_model_hover_w_ = params->getValueOrDefault<double>("simple_model_hover_w", 150.0);
					simple_model_forward_coeff_ = params->getValueOrDefault<double>("simple_model_forward_coeff", 0.5);
					break;
				}
			} else {
				LOG_WARN("[EnergyEvaluator] 未提供参数配置，使用默认的 SIMPLE 计算保真度。ID: {}, Name: {}", getId(), getName());
				calculation_fidelity_ = CalculationFidelityType::SIMPLE;
				simple_model_hover_w_ = 150.0;
				simple_model_forward_coeff_ = 0.5;
			}

			if (!energy_model_) {
				LOG_ERROR("[EnergyEvaluator] 初始化失败: energy_model_ 未被有效设置。ID: {}, Name: {}", getId(), getName());
				return false; // 如果 energy_model_ 必须有效
			}

			LOG_INFO("[EnergyEvaluator] EnergyEvaluator 初始化成功。ID: {}, Name: {}, 计算保真度: {}, 使用的能量模型: {}",
					 getId(), getName(), NSUtils::enumToString(calculation_fidelity_),
					 energy_model_ ? typeid(*energy_model_).name() : "未指定");
			return true;
		}

		// --- ITrajectoryEvaluator 接口实现 ---
		TrajectoryEvaluationMetrics EnergyEvaluator::evaluate(const TrajectoryEvaluationRequest& request) const
		{
			// 从请求中提取参数
			const auto& trajectory = request.trajectory;
			const auto& uav_ptr = request.uav;
			const auto* mission = request.mission;

			LOG_DEBUG("[EnergyEvaluator] 开始评估轨迹。无人机 ID: {}, 轨迹点数: {}",
				uav_ptr ? uav_ptr->getId() : "N/A", trajectory.size());

			TrajectoryEvaluationMetrics result;
			result.is_feasible = true; // 先假设可行

			// --- 输入检查 ---
			if (!uav_ptr) {
				result.is_feasible = false;
				result.message = "评估轨迹失败：无效的无人机指针。";
				LOG_ERROR("[EnergyEvaluator] {} (评估器ID: {})", result.message, getId());
				return result;
			}
			const NSUav::Uav& uav = *uav_ptr; // 解引用智能指针

			LOG_DEBUG("[EnergyEvaluator] 详细评估开始 - 无人机: {}, 类型: {}, 轨迹点数: {}. 评估器: {} ({})",
					 uav.getId(), uav.getTypeTag(), trajectory.size(), getName(), getId());

			// 获取能量模型
			auto uav_energy_model = uav.getEnergyModel();
			if (!uav_energy_model) {
				result.is_feasible = false;
				result.message = "无人机 [" + uav.getId() + "] 缺少能量模型，无法评估轨迹能量。";
				LOG_ERROR("[EnergyEvaluator] {} (评估器ID: {})", result.message, getId());
				return result;
			}
			// 检查 EnergyEvaluator 自身的 energy_model_ 是否也应该在此处发挥作用。
			// 例如，如果 EnergyEvaluator 的 energy_model_ 是一个参考模型，可以用于验证 uav_energy_model。
			// 或者，如果uav没有自己的模型，则使用 this->energy_model_。
			// 目前，我们将优先使用无人机自身的模型。
			// 如果需要，可以添加日志记录 this->energy_model_ 的信息。
			LOG_DEBUG("[EnergyEvaluator] 使用无人机 [ID: {}] 自身的能量模型: {} 进行评估。评估器自身能量模型: {}",
					  uav.getId(),
					  typeid(*uav_energy_model).name(),
					  this->energy_model_ ? typeid(*this->energy_model_).name() : "未配置");

			// --- 轨迹有效性检查 & 初始状态 ---
			if (trajectory.isEmpty()) {
				LOG_DEBUG("[EnergyEvaluator] 轨迹为空，能量消耗为 0。无人机: {}", uav.getId());
				result.energy_consumption = 0.0;
				auto current_state = uav.getUavState(); // 获取当前状态用于续航估计
				result.endurance_remaining = uav_energy_model->estimateEnduranceTime(current_state, current_state.current_energy);
				if (current_state.current_energy < uav_energy_model->getMinSafeEnergyLevel()) {
					result.is_feasible = false;
					std::stringstream ss;
					ss << "无人机 [" << uav.getId() << "] 初始能量 (" << std::fixed << std::setprecision(2) << current_state.current_energy
						<< " Wh) 低于最低安全水平 (" << std::fixed << std::setprecision(2) << uav_energy_model->getMinSafeEnergyLevel() << " Wh)。空轨迹不可行。";
					result.message = ss.str();
					LOG_WARN("[EnergyEvaluator] {} (评估器ID: {})", result.message, getId());
				}
				return result;
			}

			// --- 模拟能量消耗 ---
			NSUav::UavState current_sim_state = uav.getUavState(); // 使用无人机当前状态作为模拟起点
			double start_energy = current_sim_state.current_energy;
			LOG_DEBUG("[EnergyEvaluator] 无人机 {} 模拟起点能量: {:.2f} Wh", uav.getId(), start_energy);

			double total_consumption = 0.0;
			double current_energy_sim = start_energy; // 模拟过程中的当前能量

			// 检查初始能量是否足够
			if (current_energy_sim < uav_energy_model->getMinSafeEnergyLevel()) {
				result.is_feasible = false;
				std::stringstream ss;
				ss << "无人机 [" << uav.getId() << "] 初始能量 ("
					<< std::fixed << std::setprecision(2)
					<< current_energy_sim
					<< " Wh) 低于最低安全水平 (" << std::fixed << std::setprecision(2)
					<< uav_energy_model->getMinSafeEnergyLevel() << " Wh)。轨迹不可行。";
				result.message = ss.str();
				LOG_WARN("[EnergyEvaluator] {} (评估器ID: {})", result.message, getId());
				result.energy_consumption = 0.0; // 没有飞行
				result.endurance_remaining = uav_energy_model->estimateEnduranceTime(
						current_sim_state, current_energy_sim);
				return result;
			}

			// 获取轨迹点
			const auto& trajectory_points = trajectory.getPoints();

			// 简化：直接使用能量模型评估整个轨迹
			Time total_flight_time = trajectory.getTotalTime();
			double total_distance = trajectory.getTotalLength();

			// 计算平均速度用于能量估算
			double avg_speed = (total_flight_time > Constants::TIME_EPSILON) ?
				total_distance / total_flight_time : 0.0;

			// 使用简化的能量计算
			if (calculation_fidelity_ == CalculationFidelityType::SIMPLE) {
				// 简单模型：基于距离和时间的线性估算
				double hover_time_ratio = 0.2; // 假设20%时间悬停
				double forward_time_ratio = 0.8; // 假设80%时间前飞

				total_consumption = (hover_time_ratio * total_flight_time * simple_model_hover_w_ / 3600.0) + // Wh
								   (forward_time_ratio * total_flight_time * simple_model_hover_w_ * simple_model_forward_coeff_ / 3600.0);

				LOG_DEBUG("[EnergyEvaluator] 简单模型计算 - 总时间:{:.1f}s, 总距离:{:.1f}m, 平均速度:{:.1f}m/s, 预估消耗:{:.2f}Wh",
					total_flight_time, total_distance, avg_speed, total_consumption);
			} else {
				// 详细模型：逐点计算
				for (size_t i = 0; i < trajectory_points.size() - 1; ++i) {
					const auto& start_point = trajectory_points[i];
					const auto& end_point = trajectory_points[i + 1];

					Time dt = end_point.time_stamp - start_point.time_stamp;
					if (dt <= Constants::TIME_EPSILON) continue;

					// 创建UavState用于能量计算
					NSUav::UavState state_for_energy;
					state_for_energy.position = start_point.position;
					state_for_energy.velocity = start_point.velocity_ned;
					state_for_energy.orientation = start_point.orientation;
					state_for_energy.time_stamp = start_point.time_stamp;
					state_for_energy.current_energy = current_energy_sim;
					state_for_energy.mode = uav.getCurrentFlightMode();

					double segment_consumption = uav_energy_model->computeEnergyConsumption(state_for_energy, dt);
					current_energy_sim -= segment_consumption;
					total_consumption += segment_consumption;

					// 检查能量是否足够
					if (current_energy_sim < uav_energy_model->getMinSafeEnergyLevel()) {
						result.is_feasible = false;
						result.message = fmt::format("无人机 [{}] 在轨迹点 {} 处能量不足", uav.getId(), i);
						LOG_ERROR("[EnergyEvaluator] {}", result.message);
						break;
					}
				}
			}

			// --- 设置最终结果 ---
			result.energy_consumption = total_consumption;
			result.flight_time = total_flight_time;
			result.endurance_remaining = uav_energy_model->estimateEnduranceTime(current_sim_state, current_energy_sim);

			// 最终能量检查
			if (result.is_feasible && current_energy_sim < uav_energy_model->getMinSafeEnergyLevel()) {
				result.is_feasible = false;
				result.message = fmt::format("无人机 [{}] 完成轨迹后剩余能量 ({:.2f} Wh) 低于最低安全水平 ({:.2f} Wh)",
					uav.getId(), current_energy_sim, uav_energy_model->getMinSafeEnergyLevel());
				LOG_WARN("[EnergyEvaluator] {}", result.message);
			} else if (result.is_feasible) {
				result.message = fmt::format("轨迹能量评估成功。预计消耗 {:.2f} Wh，剩余 {:.2f} Wh",
					total_consumption, current_energy_sim);
				LOG_INFO("[EnergyEvaluator] 无人机 {} 轨迹评估成功: 消耗={:.2f} Wh, 剩余={:.2f} Wh, 续航={:.1f}s",
					uav.getId(), total_consumption, current_energy_sim, result.endurance_remaining.value_or(0.0));
			}

			return result;
		}

		/**
		 * @brief 批量评估轨迹
		 */
		std::map<ObjectID, TrajectoryEvaluationMetrics> EnergyEvaluator::batchEvaluate(
			const std::vector<TrajectoryEvaluationRequest>& requests) const
		{
			std::map<ObjectID, TrajectoryEvaluationMetrics> results;

			for (const auto& request : requests) {
				TrajectoryEvaluationMetrics metrics = evaluate(request);
				results[request.request_id] = metrics;
			}

			return results;
		}
	} // namespace NSAlgorithm
} // namespace NSDrones