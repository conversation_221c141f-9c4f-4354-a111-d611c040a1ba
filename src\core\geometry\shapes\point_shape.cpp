#include "core/geometry/shapes/point_shape.h"
#include <stdexcept>
#include <cmath>

namespace NSDrones {
namespace NSCore {

    PointShape::PointShape(const fcl::Vector3d& position) 
        : position_(position), fcl_sphere_(nullptr) {
    }

    PointShape::PointShape(const PointShape& other)
        : position_(other.position_), fcl_sphere_(nullptr) {
    }

    PointShape& PointShape::operator=(const PointShape& other) {
        if (this != &other) {
            position_ = other.position_;
            fcl_sphere_.reset();  // 重置FCL对象，延迟重新创建
        }
        return *this;
    }

    std::shared_ptr<fcl::CollisionGeometryd> PointShape::getFCLGeometry() const {
        ensureFCLObject();
        return fcl_sphere_;
    }

    fcl::AABBd PointShape::getAABB(const fcl::Transform3d& transform) const {
        fcl::Vector3d transformed_pos = transform * position_;
        fcl::Vector3d min_point = transformed_pos.array() - POINT_RADIUS;
        fcl::Vector3d max_point = transformed_pos.array() + POINT_RADIUS;
        return fcl::AABBd(min_point, max_point);
    }

    fcl::Matrix3d PointShape::getInertiaMatrix(double mass) const {
        // 点的惯性张量为零矩阵
        return fcl::Matrix3d::Zero();
    }

    std::unique_ptr<IShape> PointShape::clone() const {
        return std::make_unique<PointShape>(*this);
    }

    nlohmann::json PointShape::serialize() const {
        nlohmann::json j;
        j["type"] = "POINT";
        j["position"] = {position_.x(), position_.y(), position_.z()};
        return j;
    }

    bool PointShape::deserialize(const nlohmann::json& json) {
        try {
            if (json["type"] != "POINT") {
                return false;
            }
            
            auto pos = json["position"];
            position_ = fcl::Vector3d(pos[0], pos[1], pos[2]);
            fcl_sphere_.reset();  // 重置FCL对象
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    std::string PointShape::toString() const {
        return "Point(" + std::to_string(position_.x()) + ", " + 
               std::to_string(position_.y()) + ", " + 
               std::to_string(position_.z()) + ")";
    }

    bool PointShape::containsPoint(const fcl::Vector3d& point) const {
        // 点只包含自身位置（在极小误差范围内）
        return (point - position_).norm() < POINT_RADIUS;
    }

    double PointShape::distanceToPoint(const fcl::Vector3d& point) const {
        return (point - position_).norm();
    }

    void PointShape::setPosition(const fcl::Vector3d& position) {
        position_ = position;
        fcl_sphere_.reset();  // 重置FCL对象
    }

    double PointShape::distanceTo(const PointShape& other) const {
        return (position_ - other.position_).norm();
    }

    std::unique_ptr<PointShape> PointShape::createOrigin() {
        return std::make_unique<PointShape>(fcl::Vector3d::Zero());
    }

    void PointShape::ensureFCLObject() const {
        if (!fcl_sphere_) {
            fcl_sphere_ = std::make_shared<fcl::Sphered>(POINT_RADIUS);
        }
    }

} // namespace NSCore
} // namespace NSDrones
