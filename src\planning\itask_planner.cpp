// src/planning/itask_planner.cpp
#include "planning/itask_planner.h"
#include "environment/entities/zone.h"
#include "environment/environment.h"
#include "environment/collision/collision_engine.h"
#include "mission/mission.h"
#include "mission/task.h"
#include "mission/control_point.h"
#include "mission/task_strategies.h"
#include "planning/planning_types.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "algorithm/evaluator/itrajectory_evaluator.h"
#include "uav/uav.h"
#include "uav/idynamic_model.h"
#include "utils/logging.h"
#include "utils/geometry_manager.h"
#include "utils/coordinate_converter.h"
#include "utils/object_id.h"
#include "core/types.h"
#include <utility>
#include <cmath>
#include <stdexcept>
#include <map>
#include <vector>
#include <optional>
#include <algorithm>
#include <unordered_map>

namespace NSDrones {
	namespace NSPlanning {

		// === 构造函数实现 ===

		ITaskPlanner::ITaskPlanner() {
			LOG_DEBUG("任务规划器: 基类初始化完成");
		}

		// === 环境和算法组件访问方法实现 ===

		std::shared_ptr<NSEnvironment::Environment> ITaskPlanner::getEnvironment() const {
			return NSEnvironment::Environment::getInstance();
		}

		IPathPlannerPtr ITaskPlanner::getPathPlanner() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getPathPlanner();
		}

		ITrajectoryOptimizerPtr ITaskPlanner::getTrajectoryOptimizer() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getTrajectoryOptimizer();
		}

		ITrajectoryEvaluatorPtr ITaskPlanner::getTrajectoryEvaluator() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getTrajectoryEvaluator();
		}

		// === 通用任务规划辅助方法实现 ===

		bool ITaskPlanner::validateTaskBasics(const NSMission::Task& task,
											  NSMission::TaskType expected_type,
											  const std::vector<NSUav::UavPtr>& assigned_uavs,
											  PlanningResult& result) const {
			// 验证任务类型
			if (task.getType() != expected_type) {
				std::string error_msg = "任务规划器类型不匹配: 期望" + NSUtils::enumToString(expected_type) +
									   "，实际" + NSUtils::enumToString(task.getType());
				result.setStatus(false, error_msg);
				LOG_ERROR("任务规划器: 任务[{}] {}", task.getId(), error_msg);
				return false;
			}

			// 验证无人机分配
			if (assigned_uavs.empty()) {
				std::string error_msg = "任务[" + task.getId() + "]未分配任何无人机";
				result.setStatus(false, error_msg);
				LOG_ERROR("任务规划器: {}", error_msg);
				return false;
			}

			// 验证依赖项
			auto environment = getEnvironment();
			auto path_planner = getPathPlanner();
			if (!environment || !path_planner) {
				std::string error_msg = "任务规划器缺少必要依赖项(环境/路径规划器)";
				result.setStatus(false, error_msg);
				LOG_ERROR("任务规划器: 任务[{}] {}", task.getId(), error_msg);
				return false;
			}

			LOG_DEBUG("任务规划器: 任务[{}]基本验证通过，分配{}架无人机", task.getId(), assigned_uavs.size());
			return true;
		}

		bool ITaskPlanner::validateUavState(const NSUav::UavPtr& uav,
											const std::map<NSUtils::ObjectID, NSUav::UavState>& start_states,
											const NSUtils::ObjectID& task_id,
											PlanningResult& result) const {
			if (!uav) {
				LOG_WARN("任务规划器: 任务[{}]跳过空的无人机指针", task_id);
				return false;
			}

			const NSUtils::ObjectID& uav_id = uav->getId();

			// 检查起始状态
			auto start_state_it = start_states.find(uav_id);
			if (start_state_it == start_states.end()) {
				std::string error_msg = "缺少无人机[" + uav_id + "]的起始状态";
				result.addWarning(WarningEvent(WarningType::INVALID_STATE, error_msg, 0.0,
											  NSCore::WGS84Point(NAN, NAN, NAN), uav_id,
											  NSUtils::INVALID_OBJECT_ID, task_id));
				LOG_ERROR("任务规划器: 任务[{}] {}", task_id, error_msg);
				return false;
			}

			// 检查动力学模型
			const NSUav::IDynamicModel* dynamics = uav->getDynamicsModel().get();
			if (!dynamics) {
				std::string error_msg = "无人机[" + uav_id + "]缺少动力学模型";
				result.addWarning(WarningEvent(WarningType::INVALID_STATE, error_msg, 0.0,
											  NSCore::WGS84Point(NAN, NAN, NAN), uav_id,
											  NSUtils::INVALID_OBJECT_ID, task_id));
				LOG_ERROR("任务规划器: 任务[{}] {}", task_id, error_msg);
				return false;
			}

			LOG_DEBUG("任务规划器: 无人机[{}]状态验证通过", uav_id);
			return true;
		}

		std::vector<NSCore::EcefPoint> ITaskPlanner::planPathToTarget(
			const NSUav::UavState& start_state,
			const NSCore::WGS84Point& target_wgs84,
			const NSUav::UavPtr& uav,
			const NSMission::Task& task,
			PlanningResult& result) const {

			std::vector<NSCore::EcefPoint> path;

			auto path_planner = getPathPlanner();
			if (!path_planner) {
				LOG_ERROR("任务规划器: 路径规划器未初始化");
				return path;
			}

			const NSUav::IDynamicModel* dynamics = uav->getDynamicsModel().get();
			if (!dynamics) {
				LOG_ERROR("任务规划器: 无人机[{}]缺少动力学模型", uav->getId());
				return path;
			}

			// 获取路径约束
			auto path_constraints_opt = task.getStrategyByType<NSMission::PathConstraintStrategy>();
			const NSMission::PathConstraintStrategy* path_constraints_ptr =
				path_constraints_opt ? path_constraints_opt.get() : nullptr;

			// 坐标转换
			NSCore::EcefPoint start_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(start_state.position);
			NSCore::EcefPoint target_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(target_wgs84);

			LOG_DEBUG("任务规划器: 规划路径从{}到{}", start_state.position.toString(), target_wgs84.toString());

			// 调用路径规划器
			bool path_found = path_planner->findPath(start_ecef, target_ecef, dynamics, path_constraints_ptr, path);

			if (!path_found || path.size() < 2) {
				std::string error_msg = "无法规划从" + start_state.position.toString() +
									   "到" + target_wgs84.toString() + "的路径";
				result.addWarning(WarningEvent(WarningType::PLANNING_FAILURE, error_msg,
											  start_state.time_stamp, start_state.position,
											  uav->getId(), NSUtils::INVALID_OBJECT_ID, task.getId()));
				LOG_ERROR("任务规划器: 任务[{}] {}", task.getId(), error_msg);
				path.clear();
				return path;
			}

			LOG_DEBUG("任务规划器: 成功规划路径，包含{}个点", path.size());
			return path;
		}

		// === 辅助方法实现 ===

		std::pair<NSCore::EcefPoint, bool> ITaskPlanner::getAbsolutePosition(
			const NSMission::ControlPoint& cp,
			const NSMission::Task& task) const
		{
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return { NSCore::EcefPoint(0, 0, 0), false };
			}

			// 获取控制点的WGS84位置
			NSCore::WGS84Point wgs84_position = cp.position;

			// 转换为ECEF坐标进行内部计算
			NSCore::EcefPoint absolute_position = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_position);

			// 根据任务高度策略调整高度
			NSMission::AltitudeType height_strategy = task.getDesiredHeightType();

			auto adjusted_height = environment->getAdjustedHeightAtPoint(
				wgs84_position, height_strategy, wgs84_position.altitude);

			if (adjusted_height.has_value()) {
				absolute_position.setZ(*adjusted_height);
				LOG_DEBUG("任务规划器: 控制点绝对位置计算成功 ({:.1f}, {:.1f}, {:.1f})",
					absolute_position.x(), absolute_position.y(), absolute_position.z());
			}
			else {
				// 高度调整失败的回退策略
				if (height_strategy == NSMission::AltitudeType::ABOVE_GROUND_LEVEL) {
					LOG_WARN("任务规划器: AGL高度策略失败，使用绝对高度作为回退");
				}
				else {
					LOG_WARN("任务规划器: 高度策略{}失败，使用原始高度",
						NSUtils::enumToString(height_strategy));
				}
				absolute_position.setZ(wgs84_position.altitude);
			}

			return { absolute_position, true };
		}

		TrajectorySegment ITaskPlanner::generateLinearTrajectorySegment(
			const TrajectoryPoint& start_point,
			const NSCore::WGS84Point& end_wgs84_pos,
			double speed,
			const NSUav::IDynamicModel& dynamics,
			const NSUtils::ObjectID& uav_id) const
		{
			TrajectorySegment segment;

			// 速度验证
			if (speed <= NSCore::Constants::VELOCITY_EPSILON) {
				LOG_WARN("[任务规划器] 生成线性轨迹段失败，速度({:.3f})无效", speed);
				return segment;
			}

			// 使用GeometryManager计算距离
			double distance = NSEnvironment::GeometryManager::calculateDistance(
				start_point.position, end_wgs84_pos);

			if (distance < NSCore::Constants::GEOMETRY_EPSILON) {
				LOG_WARN("[任务规划器] 起点和终点距离过近({:.3e}m)，只包含起点", distance);
				segment.push_back(start_point);
				return segment;
			}

			// 计算飞行时间和方向
			double duration = distance / speed;
			double bearing_deg = NSEnvironment::GeometryManager::calculateBearing(
				start_point.position, end_wgs84_pos);
			double bearing_rad = bearing_deg * NSCore::Constants::DEG_TO_RAD;

			// 添加起点
			segment.push_back(start_point);

			// 创建终点轨迹点
			TrajectoryPoint end_point;
			end_point.position = end_wgs84_pos;
			end_point.time_stamp = start_point.time_stamp + duration;
			end_point.velocity_ned = NSCore::Vector3D(
				std::cos(bearing_rad) * speed,
				std::sin(bearing_rad) * speed,
				0.0
			);

			// 设置姿态
			if (end_point.velocity_ned.squaredNorm() > NSCore::Constants::VELOCITY_EPSILON * NSCore::Constants::VELOCITY_EPSILON) {
				end_point.orientation = NSCore::Orientation::FromTwoVectors(
					NSCore::Vector3D::UnitX(), end_point.velocity_ned.normalized());
			}
			else {
				end_point.orientation = start_point.orientation;
			}

			// 继承载荷动作
			end_point.payload_actions = start_point.payload_actions;

			segment.push_back(end_point);

			LOG_DEBUG("[任务规划器] 成功生成线性轨迹段，无人机{}，{}个轨迹点，耗时{:.2f}s",
				uav_id, segment.size(), duration);

			return segment;
		}


		void ITaskPlanner::checkTrajectorySegmentWarnings(const TrajectorySegment& segment,
			const NSUtils::ObjectID& uav_id,
			PlanningResult& result,
			const NSUtils::ObjectID& task_id) const
		{
			auto environment = getEnvironment();
			if (!environment || segment.size() < 2) {
				return;
			}

			const auto& zones = environment->getAllZones();
			if (zones.empty()) {
				return;
			}

			for (size_t i = 0; i < segment.size() - 1; ++i) {
				const auto& p1 = segment[i].position;
				const auto& p2 = segment[i + 1].position;
				const auto t1 = segment[i].time_stamp;
				const auto t2 = segment[i + 1].time_stamp;

				// 转换为ECEF坐标进行区域检查
				NSCore::EcefPoint p1_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(p1);
				NSCore::EcefPoint p2_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(p2);

				std::vector<NSEnvironment::ConstZonePtr> zones_at_p1 = environment->getViolatedZones(p1_ecef);
				std::vector<NSEnvironment::ConstZonePtr> zones_at_p2 = environment->getViolatedZones(p2_ecef);
				std::vector<NSEnvironment::ConstZonePtr> zones_intersecting = environment->getIntersectingZones(p1_ecef, p2_ecef);

				// 收集涉及的区域
				std::unordered_map<NSUtils::ObjectID, NSEnvironment::ConstZonePtr> involved_zones;
				for (const auto& z_ptr : zones_at_p1) {
					if (z_ptr) involved_zones[z_ptr->getId()] = z_ptr;
				}
				for (const auto& z_ptr : zones_at_p2) {
					if (z_ptr) involved_zones[z_ptr->getId()] = z_ptr;
				}
				for (const auto& z_ptr : zones_intersecting) {
					if (z_ptr) involved_zones[z_ptr->getId()] = z_ptr;
				}

				// 检查每个涉及的区域
				for (const auto& pair : involved_zones) {
					const auto& zone = pair.second;
					if (!zone) continue;

					// 检查起点和终点是否在区域内
					bool was_inside = std::find_if(zones_at_p1.begin(), zones_at_p1.end(),
						[&](const NSEnvironment::ConstZonePtr& z_ptr) {
							return z_ptr && z_ptr->getId() == zone->getId();
						}) != zones_at_p1.end();

						bool is_inside = std::find_if(zones_at_p2.begin(), zones_at_p2.end(),
							[&](const NSEnvironment::ConstZonePtr& z_ptr) {
								return z_ptr && z_ptr->getId() == zone->getId();
							}) != zones_at_p2.end();

							// 生成告警事件
							WarningType wtype = WarningType::UNKNOWN;
							std::string description;
							NSCore::WGS84Point location = p1;
							NSCore::Time time = t1;
							bool generate_warning = false;

							NSCore::ZoneType zone_type = zone->getType();

							// 根据区域类型和进入/离开状态生成告警
							switch (zone_type) {
							case NSCore::ZoneType::ENTER_WARNING:
							case NSCore::ZoneType::LEAVE_WARNING:
								if (!was_inside && is_inside) {
									wtype = WarningType::ENTERED_WARNING_ZONE;
									description = "进入告警区域 " + NSUtils::toString(zone->getId());
									location = p2; time = t2; generate_warning = true;
								}
								else if (was_inside && !is_inside) {
									wtype = WarningType::LEFT_WARNING_ZONE;
									description = "离开告警区域 " + NSUtils::toString(zone->getId());
									location = p2; time = t2; generate_warning = true;
								}
								break;

							case NSCore::ZoneType::KEEPOUT:
								if (!was_inside && is_inside) {
									wtype = WarningType::ENTERED_KEEPOUT_ZONE;
									description = "进入禁飞区域 " + NSUtils::toString(zone->getId());
									location = p2; time = t2; generate_warning = true;
									LOG_WARN("任务规划器: 无人机{}进入禁飞区域{}",
										NSUtils::toString(uav_id), NSUtils::toString(zone->getId()));
								}
								else if (zone->intersects(p1_ecef, p2_ecef)) {
									wtype = WarningType::CROSS_KEEPOUT_BOUNDARY;
									description = "穿越禁飞区域 " + NSUtils::toString(zone->getId()) + " 边界";
									generate_warning = true;
									LOG_WARN("任务规划器: 无人机{}穿越禁飞区域{}边界",
										NSUtils::toString(uav_id), NSUtils::toString(zone->getId()));
								}
								break;

							case NSCore::ZoneType::THREAT:
								if (!was_inside && is_inside) {
									wtype = WarningType::ENTERED_THREAT_ZONE;
									description = "进入威胁区域 " + NSUtils::toString(zone->getId());
									location = p2; time = t2; generate_warning = true;
									LOG_WARN("任务规划器: 无人机{}进入威胁区域{}",
										NSUtils::toString(uav_id), NSUtils::toString(zone->getId()));
								}
								else if (zone->intersects(p1_ecef, p2_ecef)) {
									wtype = WarningType::CROSS_THREAT_BOUNDARY;
									description = "穿越威胁区域 " + NSUtils::toString(zone->getId()) + " 边界";
									generate_warning = true;
									LOG_WARN("任务规划器: 无人机{}穿越威胁区域{}边界",
										NSUtils::toString(uav_id), NSUtils::toString(zone->getId()));
								}
								break;

							default:
								break;
							}

							if (generate_warning && wtype != WarningType::UNKNOWN) {
								result.addWarning(WarningEvent(wtype, description, time, location, uav_id, zone->getId(), task_id));
							}
				}
			}
		}

		/**
		 * @brief 平滑几何路径并进行时间参数化。
		 * @param geometric_path 输入几何路径。
		 * @param uav 无人机指针。
		 * @param start_state 起始状态。
		 * @param desired_speed 期望速度。
		 * @param optimized_segment 输出优化后的轨迹段。
		 * @param result_ptr (可选) 用于添加告警。
		 * @param strategies (可选) 应用于此轨迹段的策略。
		 * @return 如果成功生成有效轨迹返回 true。
		 */
		bool ITaskPlanner::smoothAndTimeParameterize(const std::vector<WGS84Point>& geometric_path,
			const NSUav::UavPtr& uav,
			const NSUav::UavState& start_state,
			double desired_speed,
			Trajectory& optimized_trajectory,
			SingleTaskPlanningResult* result_ptr,
			const NSMission::ITaskStrategyMap& strategies)
		{
			LOG_DEBUG("[任务规划器] 开始为UAV{}平滑和时间参数化包含{}个点的几何路径，期望速度{:.2f}m/s",
				uav->getId(), geometric_path.size(), desired_speed);

			// === 输入验证 ===
			if (!uav) {
				LOG_ERROR("[任务规划器] 轨迹平滑和时间参数化失败：UAV指针无效");
				if (result_ptr) {
					result_ptr->warnings.push_back(WarningEvent(WarningType::CRITICAL,
						"内部错误：UAV指针无效", start_state.time_stamp));
				}
				return false;
			}

			if (geometric_path.size() < 2) {
				LOG_WARN("[任务规划器] 几何路径点数({})不足，无法处理", geometric_path.size());
				if (result_ptr) {
					result_ptr->warnings.push_back(WarningEvent(WarningType::PLANNING_ERROR,
						"几何路径点数不足", start_state.time_stamp, {}, uav->getId()));
				}
				return false;
			}

			if (desired_speed <= Constants::VELOCITY_EPSILON) {
				LOG_ERROR("[任务规划器] 期望速度({:.3f})无效", desired_speed);
				if (result_ptr) {
					result_ptr->warnings.push_back(WarningEvent(WarningType::PLANNING_ERROR,
						"期望速度无效", start_state.time_stamp, {}, uav->getId()));
				}
				return false;
			}

			const auto dynamic_model = uav->getDynamicsModel();
			if (!dynamic_model) {
				LOG_ERROR("[任务规划器] 无法获取UAV{}的动力学模型", uav->getId());
				if (result_ptr) {
					result_ptr->warnings.push_back(WarningEvent(WarningType::CRITICAL,
						"无法获取动力学模型", start_state.time_stamp, {}, uav->getId()));
				}
				return false;
			}

			try {
				// === 第1步：创建初始轨迹段 ===
				TrajectorySegment initial_segment;
				initial_segment.reserve(geometric_path.size());

				NSCore::Time current_time = start_state.time_stamp;
				NSCore::Vector3D current_velocity = start_state.velocity;

				// 生成轨迹点
				for (size_t i = 0; i < geometric_path.size(); ++i) {
					TrajectoryPoint trajectory_point;
					trajectory_point.position = geometric_path[i];
					trajectory_point.time_stamp = current_time;
					trajectory_point.velocity_ned = current_velocity;
					trajectory_point.orientation = start_state.orientation;

					initial_segment.push_back(trajectory_point);

					// 计算到下一个点的时间和速度
					if (i < geometric_path.size() - 1) {
						double distance = NSEnvironment::GeometryManager::calculateDistance(
							geometric_path[i], geometric_path[i + 1]);
						double time_increment = distance / desired_speed;
						current_time += time_increment;

						// 计算速度方向
						double bearing = NSEnvironment::GeometryManager::calculateBearing(
							geometric_path[i], geometric_path[i + 1]);
						double bearing_rad = bearing * NSCore::Constants::DEG_TO_RAD;
						current_velocity = NSCore::Vector3D(
							std::cos(bearing_rad) * desired_speed,
							std::sin(bearing_rad) * desired_speed,
							0.0
						);
					}
				}

				LOG_DEBUG("[任务规划器] 初始轨迹段生成完成，包含{}个点", initial_segment.size());

				// === 第2步：轨迹优化（如果可用） ===
				TrajectorySegment optimized_segment = initial_segment;
				auto trajectory_optimizer = getTrajectoryOptimizer();
				if (trajectory_optimizer) {
					LOG_DEBUG("[任务规划器] 尝试优化轨迹段...");

					TrajectoryOptimizationRequest opt_request;
					opt_request.initial_trajectory = { initial_segment };
					opt_request.dynamics = dynamic_model.get();
					opt_request.strategies = &strategies;

					TrajectoryOptimizationResult opt_result = trajectory_optimizer->optimize(opt_request);

					if (opt_result.success && !opt_result.optimized_trajectory.empty()) {
						optimized_segment = opt_result.optimized_trajectory[0];
						LOG_INFO("[任务规划器] 轨迹优化成功，优化后包含{}个点", optimized_segment.size());
					} else {
						LOG_WARN("[任务规划器] 轨迹优化失败：{}，使用原始轨迹", opt_result.message);
						if (result_ptr) {
							result_ptr->warnings.push_back(WarningEvent(WarningType::OPTIMIZATION_FAILED,
								"轨迹优化失败: " + opt_result.message, start_state.time_stamp, {}, uav->getId()));
						}
					}
				}

				// === 第3步：设置输出轨迹 ===
				optimized_trajectory.clear();
				optimized_trajectory.push_back(optimized_segment);

				double total_duration = optimized_segment.empty() ? 0.0 :
					optimized_segment.back().time_stamp - optimized_segment.front().time_stamp;

				LOG_INFO("[任务规划器] UAV{}轨迹平滑和时间参数化成功，生成{}个轨迹点，耗时{:.2f}s",
					uav->getId(), optimized_segment.size(), total_duration);
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("[任务规划器] 轨迹平滑和时间参数化异常: {}", e.what());
				if (result_ptr) {
					result_ptr->warnings.push_back(WarningEvent(WarningType::PLANNING_ERROR,
						"轨迹生成异常: " + std::string(e.what()), start_state.time_stamp, {}, uav->getId()));
				}
				return false;
			}
		}

		// 基于TrajectorySegment的区域约束检查实现
		bool ITaskPlanner::isZoneConstraintSatisfied(const TrajectorySegment& segment, const ConstZonePtr& zone) const
		{
			if (!zone) {
				LOG_WARN("[任务规划器] 检查区域约束时传入了无效的Zone指针");
				return true; // 无效区域无法违反，视为满足
			}
			if (segment.size() < 2) {
				LOG_TRACE("[任务规划器] 检查区域{}约束：轨迹段点数不足({})，视为满足", zone->getId(), segment.size());
				return true; // 无效轨迹段不违反约束
			}

			LOG_TRACE("[任务规划器] 检查包含{}个点的轨迹段是否满足区域{}(类型:{})的约束...",
				segment.size(), zone->getId(), NSUtils::enumToString(zone->getType()));

			ZoneType zone_type = zone->getType();
			double tolerance = Constants::GEOMETRY_EPSILON;

			if (zone_type == ZoneType::KEEPOUT || zone_type == ZoneType::THREAT) {
				LOG_TRACE("[任务规划器] 区域类型为禁飞/威胁区，检查轨迹段是否进入或穿越...");
				// 检查轨迹段的任何部分是否在区域内（考虑容差）
				for (size_t i = 0; i < segment.size(); ++i) {
					const auto& pt = segment[i].position;
					EcefPoint pt_ecef = segment[i].getEcefPosition();
					if (zone->isInside(pt_ecef, tolerance)) {
						LOG_WARN("[任务规划器] 轨迹点{}({})位于禁飞/威胁区{}(容差{:.1e}m)内部，约束违反！",
							i, pt.toString(), zone->getId(), tolerance);
						return false;
					}
					if (i < segment.size() - 1) {
						EcefPoint next_pt_ecef = segment[i + 1].getEcefPosition();
						if (zone->intersects(pt_ecef, next_pt_ecef, tolerance)) {
							LOG_WARN("[任务规划器] 轨迹段{}-{}与禁飞/威胁区{}(容差{:.1e}m)相交，约束违反！",
								i, i + 1, zone->getId(), tolerance);
							return false;
						}
					}
				}
				LOG_TRACE("[任务规划器] 轨迹段未进入或穿越禁飞/威胁区{}", zone->getId());
			}
			else if (zone_type == ZoneType::OPERATIONAL || zone_type == ZoneType::ENTER_WARNING) {
				LOG_TRACE("[任务规划器] 区域类型为作业区/进入告警区，检查轨迹段是否完全位于内部...");
				// 检查轨迹段的所有点是否都在区域内
				for (size_t i = 0; i < segment.size(); ++i) {
					const auto& pt = segment[i].position;
					EcefPoint pt_ecef = segment[i].getEcefPosition();
					// 对于作业区/围栏，通常用负容差或零容差确保在边界内
					if (!zone->isInside(pt_ecef, -tolerance)) {
						LOG_WARN("[任务规划器] 轨迹点{}({})位于作业区/进入告警区{}(容差{:.1e}m)外部，约束违反！",
							i, pt.toString(), zone->getId(), -tolerance);
						return false;
					}
				}
				LOG_TRACE("[任务规划器] 轨迹段完全位于作业区/进入告警区{}内部", zone->getId());
			}
			else {
				LOG_TRACE("  区域类型 {} 无特定约束检查逻辑，视为满足。", NSUtils::enumToString(zone_type));
				// 其他区域类型可能没有航段约束，或者需要特定逻辑
			}

			// 移除对 segment.id 的访问
			LOG_TRACE("航段满足区域 {} 的约束。", zone->getId());
			return true; // 如果没有检测到违反，则满足约束
		}

		bool ITaskPlanner::checkSafetyConstraints(const TrajectoryPoint& point, const ObjectID& uav_id, const ObjectID& task_id, PlanningResult& result, const std::vector<ZoneType>& zone_types_to_check) const
		{
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("ITaskPlanner checkSafetyConstraints(WGS84Point): Environment not set! Cannot check safety.");
				return false; // Or throw, depending on desired behavior
			}
			if (zone_types_to_check.empty()) {
				return true; // No specific zone types to check against, point is considered safe from this perspective
			}

			const auto& all_zones = environment->getAllZones(); // 修正 all_zones 的迭代问题
			for (const auto& zone_ptr : all_zones) { // 使用 auto&
				if (!zone_ptr) continue;

				ZoneType current_zone_logic_type = zone_ptr->getType();
				// 移除复杂的类型推断，直接使用 getType()
				// if (current_zone_logic_type == ZoneType::UNKNOWN) { ... } // 移除这部分逻辑

				if (current_zone_logic_type != ZoneType::UNKNOWN) { // 只检查已知类型的区域
					bool should_check_this_zone_type = false;
					for (ZoneType target_type_to_check_from_task : zone_types_to_check) {
						if (current_zone_logic_type == target_type_to_check_from_task) {
							should_check_this_zone_type = true;
							break;
						}
					}

					if (should_check_this_zone_type) {
						LOG_TRACE("ITaskPlanner checkSafetyConstraints(WGS84Point): Checking point against zone '{}' (Type: {})...", zone_ptr->getId(), NSUtils::enumToString(current_zone_logic_type));
						EcefPoint point_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(point.position);
						if (zone_ptr->isInside(point_ecef, 0.0)) { // tolerance 0.0 for exact check, GEOMETRY_EPSILON for slight tolerance
							LOG_WARN("ITaskPlanner checkSafetyConstraints(WGS84Point): Point {} is INSIDE zone '{}' (Type: {}). Constraint violated.", point.position.toString(), zone_ptr->getId(), NSUtils::enumToString(current_zone_logic_type));
							return false; // Point is inside a prohibited zone type
						}
					}
				}
			}
			// 根据策略，如果无法获取地面高程，可能也视为不安全

			LOG_INFO("ITaskPlanner：点 ({})@t={:.2f} 安全约束检查通过 (UAV:{}, Task:{})",
				point.position.toString(), point.time_stamp, toString(uav_id), toString(task_id));
			return true;
		}

		// 确保函数签名与 itask_planner.h 中的声明一致
		bool ITaskPlanner::isPathSegmentSafe(const WGS84Point& wgs84_p1, const WGS84Point& wgs84_p2, Time t1, Time t2, const ObjectID& uav_id, const ObjectID& task_id, PlanningResult& result, const std::vector<ZoneType>& zone_types_to_check) const
		{
			LOG_TRACE("ITaskPlanner 检查航段 {}@t={:.2f} -> {}@t={:.2f} 的安全约束 (UAV:{}, Task:{})",
				wgs84_p1.toString(), t1, wgs84_p2.toString(), t2, toString(uav_id), toString(task_id));

			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("  检查航段安全失败: 环境实例不存在。");
				// result.addWarning(WarningType::PLANNING_ERROR, "环境数据不可用，无法检查航段安全。", uav_id, task_id, t1, p1); // 修正 addWarning 调用
				result.addWarning(WarningEvent(WarningType::PLANNING_ERROR, "环境数据不可用，无法检查航段安全。",
					t1, wgs84_p1, uav_id, INVALID_OBJECT_ID, task_id));
				return false;
			}

			// 转换为ECEF坐标进行内部几何计算
			EcefPoint ecef_p1 = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_p1);
			EcefPoint ecef_p2 = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_p2);

			// 1. 离散化检查：沿航段采样多个点进行检查
			//    更鲁棒的检查应该使用环境的 isSegmentValid 方法，但这里我们先实现一个基础版本
			int num_samples = 5; // 可以根据航段长度和时间动态调整
			Vector3D segment_vec = ecef_p2 - ecef_p1;
			double segment_len = segment_vec.norm();
			if (segment_len < Constants::GEOMETRY_EPSILON) { // 如果是点，直接检查该点
				LOG_TRACE("  航段长度接近零，检查单点 {}@t={:.2f}", wgs84_p1.toString(), t1);
				TrajectoryPoint tp;
				tp.position = wgs84_p1;
				tp.time_stamp = t1;
				// tp.velocity_ned, tp.orientation 等可以不设置，因为 checkSafetyConstraints 主要关注位置和时间
				return checkSafetyConstraints(tp, uav_id, task_id, result, zone_types_to_check);
			}

			for (int i = 0; i <= num_samples; ++i) {
				double ratio = static_cast<double>(i) / num_samples;
				EcefPoint current_ecef = EcefPoint(ecef_p1.toVector3D() + segment_vec * ratio);
				WGS84Point current_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(current_ecef);
				Time current_t = t1 + (t2 - t1) * ratio;
				TrajectoryPoint current_tp;
				current_tp.position = current_wgs84;
				current_tp.time_stamp = current_t;

				if (!checkSafetyConstraints(current_tp, uav_id, task_id, result, zone_types_to_check)) {
					LOG_WARN("  航段在采样点 {} {}@t={:.2f} 处不安全。",
						i, current_wgs84.toString(), current_t);
					// 告警已在 checkSafetyConstraints 中添加
					return false;
				}
			}
			LOG_TRACE("  航段所有采样点均满足安全约束。");

			// 2. (更优) 直接检查整个航段与区域的相交情况
			const auto& all_zones = environment->getAllZones();
			if (all_zones.empty()) {
				LOG_TRACE("  环境中无区域，跳过航段区域相交检查。");
			}
			else {
				LOG_TRACE("  环境中有 {} 个区域，开始检查航段与禁飞区和危险区的相交...", all_zones.size());
				for (const auto& zone_ptr : all_zones) { // zone_ptr 现在是 const ZonePtr&
					if (!zone_ptr) continue;

					if (zone_ptr->getType() == ZoneType::KEEPOUT || zone_ptr->getType() == ZoneType::THREAT) {
						EcefPoint wgs84_p1_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_p1);
						EcefPoint wgs84_p2_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_p2);
						if (zone_ptr->intersects(wgs84_p1_ecef, wgs84_p2_ecef)) {
							LOG_WARN("  航段 {}->{} 与 {} 区域 [{}] 相交。",
								wgs84_p1.toString(), wgs84_p2.toString(),
								NSUtils::enumToString(zone_ptr->getType()), zone_ptr->getId());
							WarningType wt = (zone_ptr->getType() == ZoneType::KEEPOUT) ?
								WarningType::ENTERED_KEEPOUT_ZONE : WarningType::ENTERED_THREAT_ZONE;
							std::string msg = "航段与" + NSUtils::enumToString(zone_ptr->getType()) + "区域 [" + zone_ptr->getId() + "] 相交。";
							result.addWarning(WarningEvent(wt, msg, t1, wgs84_p1, uav_id, zone_ptr->getId(), task_id));

							return false; // 一旦发现不安全，立即返回
						}
					}
				}
				LOG_TRACE("  航段未与任何禁飞区或危险区相交。");
			}

			LOG_INFO("ITaskPlanner：航段 {}@t={:.2f} -> {}@t={:.2f} 安全检查通过 (UAV:{}, Task:{})",
				wgs84_p1.toString(), t1, wgs84_p2.toString(), t2, uav_id, task_id);
			return true;
		}

		bool ITaskPlanner::smoothAndTimeParameterizeECEF(const std::vector<EcefPoint>& geometric_path,
			const NSUav::UavPtr& uav,
			const NSUav::UavState& start_state,
			double desired_speed,
			TrajectorySegment& optimized_segment,
			PlanningResult* result_ptr,
			const NSMission::ITaskStrategyMap& strategies)
		{
			if (!uav) {
				LOG_ERROR("轨迹平滑和时间参数化失败：UAV 指针无效。");
				if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::CRITICAL, "内部错误：UAV 指针无效", start_state.time_stamp));
				return false;
			}

			if (geometric_path.size() < 2) {
				LOG_ERROR("轨迹平滑和时间参数化失败：几何路径点数不足 (需要至少2个点，实际{}个)。", geometric_path.size());
				if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::PLANNING_FAILURE, "几何路径点数不足", start_state.time_stamp));
				return false;
			}

			// 将ECEF路径转换为WGS84路径进行处理
			std::vector<WGS84Point> wgs84_path;
			wgs84_path.reserve(geometric_path.size());
			for (const auto& ecef_point : geometric_path) {
				wgs84_path.push_back(NSUtils::CoordinateConverter::ecefToWGS84(ecef_point));
			}

			// 生成基于时间的轨迹点
			optimized_segment.clear();
			optimized_segment.reserve(wgs84_path.size());

			Time current_time = start_state.time_stamp;

			for (size_t i = 0; i < wgs84_path.size(); ++i) {
				TrajectoryPoint tp;
				tp.position = wgs84_path[i];
				tp.time_stamp = current_time;
				tp.velocity_ned = (i < wgs84_path.size() - 1) ?
					Vector3D(desired_speed, 0, 0) : Vector3D::Zero(); // 简化速度设置
				tp.orientation = start_state.orientation; // 简化姿态设置

				optimized_segment.push_back(tp);

				// 计算到下一个点的时间
				if (i < wgs84_path.size() - 1) {
					// 使用ECEF坐标计算距离（更精确）
					double distance = (geometric_path[i + 1] - geometric_path[i]).norm();
					double time_increment = distance / desired_speed;
					current_time += time_increment;
				}
			}

			LOG_DEBUG("[任务规划器] ECEF轨迹平滑和时间参数化完成，生成{}个轨迹点", optimized_segment.size());
			return true;
		}

		bool ITaskPlanner::getUavStartState(const NSUav::UavPtr& uav, NSUav::UavState& start_state) const {
			if (!uav) {
				LOG_ERROR("任务规划器: 无法获取起始状态，UAV指针无效");
				return false;
			}

			try {
				// 获取UAV的当前状态
				start_state = uav->getCurrentState();

				// 验证状态的有效性
				if (!start_state.position.isValid()) {
					LOG_ERROR("任务规划器: UAV[{}]的位置状态无效", uav->getId());
					return false;
				}

				if (start_state.time_stamp < 0) {
					LOG_WARN("任务规划器: UAV[{}]的时间戳无效，使用当前时间", uav->getId());
					start_state.time_stamp = 0.0; // 或使用系统当前时间
				}

				LOG_DEBUG("任务规划器: 成功获取UAV[{}]起始状态 - 位置: {}, 时间: {:.3f}",
					uav->getId(), start_state.position.toString(), start_state.time_stamp);
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("任务规划器: 获取UAV[{}]起始状态异常: {}", uav->getId(), e.what());
				return false;
			}
		}

	} // namespace NSPlanning
} // namespace NSDrones