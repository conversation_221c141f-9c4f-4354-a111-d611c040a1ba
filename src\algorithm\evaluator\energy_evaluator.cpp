// src/algorithm/evaluator/energy_evaluator.cpp
#include "algorithm/evaluator/energy_evaluator.h"
#include "algorithm/algorithm_object.h" // 确保包含基类定义
#include "uav/uav.h"
#include "uav/uav_types.h"
#include "uav/ienergy_model.h"
#include "uav/idynamic_model.h" // 检查是否实际需要，如果不需要可以移除
#include "core/types.h"
#include "utils/logging.h"
#include <limits>
#include <cmath>
#include <iomanip>
#include <sstream>
#include <string>
#include "environment/environment.h" // 包含 Environment 定义
#include "params/parameters.h"   // 包含 ParamValues 定义
#include "utils/logging.h"
#include <stdexcept>

namespace {
	// 辅助函数：将 FlightMode 转换为字符串
	std::string flightModeToString(NSDrones::NSUav::FlightMode mode) {
		using namespace NSDrones::NSUav;
		switch (mode) {
		case FlightMode::UNKNOWN: return "Unknown";
		case FlightMode::HOVER: return "Hover";
		case FlightMode::FIXED_WING: return "FixedWing";
		default: return "InvalidMode";
		}
	}

} // 匿名命名空间结束

namespace NSDrones {
	namespace NSAlgorithm {

		// Constructor implementation
		EnergyEvaluator::EnergyEvaluator(ObjectID id,
                                           const std::string& type_tag,
                                           std::shared_ptr<NSUav::IEnergyModel> energy_model, // 保持 energy_model 参数
                                           const std::string& name,
                                           const std::string& version)
			: AlgorithmObject(id, type_tag, name, version), // 调用 AlgorithmObject 构造函数
			  ITrajectoryEvaluator(), // 调用 ITrajectoryEvaluator 默认构造函数
			  energy_model_(std::move(energy_model))
		{
			if (!energy_model_) {
				std::string error_msg = "EnergyEvaluator 构造失败: energy_model_ 为空。ID: " + id + ", 名称: " + getName();
				LOG_ERROR("[EnergyEvaluator] {}", error_msg);
				throw std::invalid_argument(error_msg);
			}
			LOG_INFO("[EnergyEvaluator] EnergyEvaluator 实例已创建。ID: {}, 名称: {}, 版本: {}", id, getName(), getVersion());
		}

		bool EnergyEvaluator::initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) {
			LOG_DEBUG("[EnergyEvaluator] 开始初始化 EnergyEvaluator: ID = {}, Name = {}", getId(), getName());

			// 1. 调用基类的 initialize 方法
			if (!AlgorithmObject::initialize(params, raw_config)) {
				LOG_ERROR("[EnergyEvaluator] 基类 AlgorithmObject 初始化失败。ID: {}, Name = {}", getId(), getName());
				return false;
			}

			// 2. EnergyEvaluator 特定的初始化
			if (params) {
				// 使用正确的枚举获取方法处理 calculation_fidelity 参数
				auto fidelity_opt = params->getEnumValue<CalculationFidelityType>("calculation_fidelity");
				if (!fidelity_opt.has_value()) {
					LOG_WARN("[EnergyEvaluator] 无法获取 calculation_fidelity 枚举值，使用默认值 SIMPLE。ID: {}, Name: {}", getId(), getName());
					fidelity_opt = CalculationFidelityType::SIMPLE;
				}

				calculation_fidelity_ = fidelity_opt.value();
				std::string fidelity_name = NSUtils::enumToString(calculation_fidelity_);
				LOG_INFO("[EnergyEvaluator] 配置的计算保真度级别: {}。ID: {}, Name: {}", fidelity_name, getId(), getName());

				// 根据保真度级别加载相应的参数
				switch (calculation_fidelity_) {
				case CalculationFidelityType::SIMPLE:
					// 加载简单模型参数
					simple_model_hover_w_ = params->getValueOrDefault<double>("simple_model_hover_w", 150.0);
					simple_model_forward_coeff_ = params->getValueOrDefault<double>("simple_model_forward_coeff", 0.5);
					LOG_DEBUG("[EnergyEvaluator] 简单模型参数: 悬停功耗={:.1f}W, 前飞系数={:.3f}。ID: {}",
						simple_model_hover_w_, simple_model_forward_coeff_, getId());
					break;
				case CalculationFidelityType::DETAILED:
					LOG_INFO("[EnergyEvaluator] 详细计算模式已配置，将使用无人机自身的详细能量模型。ID: {}", getId());
					break;
				case CalculationFidelityType::UNKNOWN:
				default:
					LOG_WARN("[EnergyEvaluator] 未知的计算保真度级别 '{}'，回退到 SIMPLE 模式。ID: {}", fidelity_name, getId());
					calculation_fidelity_ = CalculationFidelityType::SIMPLE;
					simple_model_hover_w_ = params->getValueOrDefault<double>("simple_model_hover_w", 150.0);
					simple_model_forward_coeff_ = params->getValueOrDefault<double>("simple_model_forward_coeff", 0.5);
					break;
				}
			} else {
				LOG_WARN("[EnergyEvaluator] 未提供参数配置，使用默认的 SIMPLE 计算保真度。ID: {}, Name: {}", getId(), getName());
				calculation_fidelity_ = CalculationFidelityType::SIMPLE;
				simple_model_hover_w_ = 150.0;
				simple_model_forward_coeff_ = 0.5;
			}

			if (!energy_model_) {
				LOG_ERROR("[EnergyEvaluator] 初始化失败: energy_model_ 未被有效设置。ID: {}, Name: {}", getId(), getName());
				return false; // 如果 energy_model_ 必须有效
			}

			LOG_INFO("[EnergyEvaluator] EnergyEvaluator 初始化成功。ID: {}, Name: {}, 计算保真度: {}, 使用的能量模型: {}",
					 getId(), getName(), NSUtils::enumToString(calculation_fidelity_),
					 energy_model_ ? typeid(*energy_model_).name() : "未指定");
			return true;
		}

		// --- ITrajectoryEvaluator 接口实现 ---
		TrajectoryCost EnergyEvaluator::evaluate(
			NSUav::ConstUavPtr uav_ptr,
			const Trajectory& trajectory,
			const NSMission::Mission* mission) const
		{
			LOG_DEBUG("[EnergyEvaluator] 开始评估轨迹。无人机 ID (来自参数): {}, 轨迹段数: {}", uav_ptr ? uav_ptr->getId() : "N/A", trajectory.size());
			TrajectoryCost cost_result;
			cost_result.is_feasible = true; // 先假设可行

			// --- 输入检查 ---
			if (!uav_ptr) {
				cost_result.is_feasible = false;
				cost_result.message = "评估轨迹失败：无效的无人机指针。";
				LOG_ERROR("[EnergyEvaluator] {} (评估器ID: {})", cost_result.message, getId());
				return cost_result;
			}
			const NSUav::Uav& uav = *uav_ptr; // 解引用智能指针

			LOG_DEBUG("[EnergyEvaluator] 详细评估开始 - 无人机: {}, 类型: {}, 轨迹段数: {}. 评估器: {} ({})",
					 uav.getId(), uav.getTypeTag(), trajectory.size(), getName(), getId());


			// 获取能量模型。注意：这里我们仍然使用无人机自带的能量模型。
			// 如果 EnergyEvaluator 自身的 energy_model_ 应该被优先使用，则需要修改此逻辑。
			// 当前假设是，EnergyEvaluator 可以评估任何无人机的轨迹，只要该无人机有能量模型。
			// 或者，EnergyEvaluator 的 energy_model_ 可以是一个通用的、用于验证或比较的模型。
			// 为了保持原始逻辑，我们继续使用无人机自身的能量模型。
			auto uav_energy_model = uav.getEnergyModel();
			if (!uav_energy_model) {
				cost_result.is_feasible = false;
				cost_result.message = "无人机 [" + uav.getId() + "] 缺少能量模型，无法评估轨迹能量。";
				LOG_ERROR("[EnergyEvaluator] {} (评估器ID: {})", cost_result.message, getId());
				return cost_result;
			}
			// 检查 EnergyEvaluator 自身的 energy_model_ 是否也应该在此处发挥作用。
			// 例如，如果 EnergyEvaluator 的 energy_model_ 是一个参考模型，可以用于验证 uav_energy_model。
			// 或者，如果uav没有自己的模型，则使用 this->energy_model_。
			// 目前，我们将优先使用无人机自身的模型。
			// 如果需要，可以添加日志记录 this->energy_model_ 的信息。
			LOG_DEBUG("[EnergyEvaluator] 使用无人机 [ID: {}] 自身的能量模型: {} 进行评估。评估器自身能量模型: {}",
					  uav.getId(),
					  typeid(*uav_energy_model).name(),
					  this->energy_model_ ? typeid(*this->energy_model_).name() : "未配置");

			// --- 轨迹有效性检查 & 初始状态 ---
			if (trajectory.empty()) {
				LOG_DEBUG("[EnergyEvaluator] 轨迹为空，能量消耗为 0。无人机: {}", uav.getId());
				cost_result.estimated_energy_consumption = 0.0;
				auto current_state = uav.getUavState(); // 获取当前状态用于续航估计
				cost_result.estimated_endurance_remaining = uav_energy_model->estimateEnduranceTime(current_state, current_state.current_energy);
				if (current_state.current_energy < uav_energy_model->getMinSafeEnergyLevel()) {
					cost_result.is_feasible = false;
					std::stringstream ss;
					ss << "无人机 [" << uav.getId() << "] 初始能量 (" << std::fixed << std::setprecision(2) << current_state.current_energy
						<< " Wh) 低于最低安全水平 (" << std::fixed << std::setprecision(2) << uav_energy_model->getMinSafeEnergyLevel() << " Wh)。空轨迹不可行。";
					cost_result.message = ss.str();
					LOG_WARN("[EnergyEvaluator] {} (评估器ID: {})", cost_result.message, getId());
				}
				return cost_result;
			}

			// --- 模拟能量消耗 ---
			NSUav::UavState current_sim_state = uav.getUavState(); // 使用无人机当前状态作为模拟起点
			double start_energy = current_sim_state.current_energy;
			LOG_DEBUG("[EnergyEvaluator] 无人机 {} 模拟起点能量: {:.2f} Wh", uav.getId(), start_energy);

			double total_consumption = 0.0;
			double current_energy_sim = start_energy; // 模拟过程中的当前能量

			// 检查初始能量是否足够
			if (current_energy_sim < uav_energy_model->getMinSafeEnergyLevel()) {
				cost_result.is_feasible = false;
				std::stringstream ss;
				ss << "无人机 [" << uav.getId() << "] 初始能量 ("
					<< std::fixed << std::setprecision(2)
					<< current_energy_sim
					<< " Wh) 低于最低安全水平 (" << std::fixed << std::setprecision(2)
					<< uav_energy_model->getMinSafeEnergyLevel() << " Wh)。轨迹不可行。";
				cost_result.message = ss.str();
				LOG_WARN("[EnergyEvaluator] {} (评估器ID: {})", cost_result.message, getId());
				cost_result.estimated_energy_consumption = 0.0; // 没有飞行
				cost_result.estimated_endurance_remaining = uav_energy_model->estimateEnduranceTime(
						current_sim_state, current_energy_sim);
				return cost_result;
			}

			// 循环遍历轨迹段
			for (size_t seg_idx = 0; seg_idx < trajectory.size(); ++seg_idx) {
				const auto& segment = trajectory[seg_idx];
				const auto& states = segment.states;
				Time segment_duration = segment.duration; // 段的总持续时间，可能用于单状态段

				if (states.empty()) {
					LOG_WARN("[EnergyEvaluator] 无人机 {} 轨迹段 {} 为空，跳过。 (评估器ID: {})", uav.getId(), seg_idx, getId());
					continue; // 跳过空段
				}

				LOG_TRACE("[EnergyEvaluator] 无人机 {} 评估段 {}: {} 个状态点. (评估器ID: {})", uav.getId(), seg_idx, states.size(), getId());

				double segment_consumption = 0.0; // 当前段的总消耗

				// --- 处理单状态段 ---
				if (states.size() == 1) {
					if (segment_duration > Constants::TIME_EPSILON) {
						const auto& sole_state = states[0];
						// 检查飞行模式
						if (sole_state.mode == NSUav::FlightMode::UNKNOWN) {
							LOG_WARN("[EnergyEvaluator] 无人机 {} 段 {} 的单状态飞行模式为 UNKNOWN，能量计算可能不准确。 (评估器ID: {})"
								, uav.getId(), seg_idx, getId());
						}
						// 假设在此状态下保持 segment_duration
						double dt = segment_duration;
						// 使用状态自带的模式，不再猜测
						// 注意：此处假设 computeEnergyConsumption 基于单个状态和持续时间进行计算，这是一个简化。
						double sub_segment_consumption = uav_energy_model->computeEnergyConsumption(sole_state, dt);
						segment_consumption += sub_segment_consumption;
						current_energy_sim -= sub_segment_consumption;

						LOG_TRACE("[EnergyEvaluator]   无人机 {} 单状态段: 模式={}, dt={:.3f}s, 消耗={:.4f} Wh, 剩余能量(模拟)={:.2f} Wh. (评估器ID: {})",
							uav.getId(), flightModeToString(sole_state.mode), dt, sub_segment_consumption, current_energy_sim, getId());

						// 检查能量
						if (current_energy_sim < uav_energy_model->getMinSafeEnergyLevel() - Constants::EPSILON) {
							cost_result.is_feasible = false;
							std::stringstream ss;
							ss << "无人机 [" << uav.getId() << "] 能量不足以完成单状态轨迹段 " << seg_idx
								<< " (预计剩余 " << std::fixed << std::setprecision(2) << current_energy_sim << " Wh, 最低安全 "
								<< std::fixed << std::setprecision(2) << uav_energy_model->getMinSafeEnergyLevel() << " Wh)。";
							cost_result.message = ss.str();
							LOG_ERROR("[EnergyEvaluator] {} (评估器ID: {})", cost_result.message, getId());
							total_consumption += segment_consumption;
							cost_result.estimated_energy_consumption = total_consumption;
							// 使用该状态和失败前的能量估计续航
							cost_result.estimated_endurance_remaining =
								uav_energy_model->estimateEnduranceTime(sole_state, current_energy_sim + sub_segment_consumption);
							return cost_result;
						}
						current_sim_state = sole_state; // 更新模拟状态
						current_sim_state.current_energy = current_energy_sim;
					}
					else {
						LOG_TRACE("[EnergyEvaluator]   无人机 {} 单状态段持续时间为零或负 ({:.3f}s)，跳过能量计算。 (评估器ID: {})"
							, uav.getId(), segment_duration, getId());
						// 即使只有一个点，也用它更新 current_sim_state，以防它是轨迹的最后一点
						current_sim_state = states[0];
						current_sim_state.current_energy = current_energy_sim; // 能量不变
					}
				}
				// --- 处理多状态段 ---
				else {
					// 迭代状态对 [i, i+1]
					for (size_t state_idx = 0; state_idx < states.size() - 1; ++state_idx) {
						const auto& start_state = states[state_idx];
						const auto& end_state = states[state_idx + 1];

						// 使用精确时间戳计算 dt
						Time dt = end_state.time_stamp - start_state.time_stamp;

						if (dt <= Constants::TIME_EPSILON) {
							LOG_TRACE("[EnergyEvaluator]     无人机 {} 跳过零时间或负时间间隔的状态 {} -> {}. (评估器ID: {})"
								, uav.getId(), state_idx, state_idx + 1, getId());
							continue;
						}

						// 检查飞行模式
						if (start_state.mode == NSUav::FlightMode::UNKNOWN) {
							LOG_WARN("[EnergyEvaluator] 无人机 {} 段 {} 状态 {}->{} 的起始飞行模式为 UNKNOWN，能量计算可能不准确。 (评估器ID: {})",
								uav.getId(), seg_idx, state_idx, state_idx + 1, getId());
						}

						// 使用起始状态 start_state 和时间间隔 dt 计算能量消耗
						// 直接使用 start_state 中定义的飞行模式
						// 注意：此处假设 computeEnergyConsumption 基于时间段的起始状态进行计算，这是一个简化。
						// 更精确的方法可能需要能量模型支持状态对或使用数值积分。
						double sub_segment_consumption = uav_energy_model->computeEnergyConsumption(start_state, dt);
						segment_consumption += sub_segment_consumption;
						current_energy_sim -= sub_segment_consumption; // 更新模拟剩余能量

						LOG_TRACE("[EnergyEvaluator]     无人机 {} 状态 {}->{}: 模式={}, dt={:.3f}s, 消耗={:.4f} Wh, 剩余能量(模拟)={:.2f} Wh. (评估器ID: {})",
							uav.getId(), state_idx, state_idx + 1, flightModeToString(start_state.mode), dt, sub_segment_consumption, current_energy_sim, getId());

						// 检查能量是否足够
						if (current_energy_sim < uav_energy_model->getMinSafeEnergyLevel() - Constants::EPSILON) {
							cost_result.is_feasible = false;
							std::stringstream ss;
							ss << "无人机 [" << uav.getId() << "] 能量不足以完成轨迹段 " << seg_idx << " 的状态 " << state_idx << " -> " << (state_idx + 1)
								<< " (预计剩余 " << std::fixed << std::setprecision(2) << current_energy_sim << " Wh, 最低安全 "
								<< std::fixed << std::setprecision(2) << uav_energy_model->getMinSafeEnergyLevel() << " Wh)。";
							cost_result.message = ss.str();
							LOG_ERROR("[EnergyEvaluator] {} (评估器ID: {})", cost_result.message, getId());
							// 记录当前总消耗和基于此点的剩余续航
							total_consumption += segment_consumption; // 加上当前段已消耗的部分
							cost_result.estimated_energy_consumption = total_consumption;
							// 使用失败前的起始状态和能量估计剩余续航
							cost_result.estimated_endurance_remaining = uav_energy_model->estimateEnduranceTime(start_state, current_energy_sim + sub_segment_consumption);
							return cost_result;
						}
					} // 结束状态对循环

					// 更新 current_sim_state 为该段的最后一个状态，为下一段做准备
					if (!states.empty()) { // 再次检查，以防万一
						current_sim_state = states.back();
						current_sim_state.current_energy = current_energy_sim; // 确保状态的能量是最新的模拟值
						LOG_TRACE("[EnergyEvaluator]   无人机 {} 段结束状态模式: {}. (评估器ID: {})",
							uav.getId(), flightModeToString(current_sim_state.mode), getId());
					}

				} // 结束多状态段处理

				// 累加整个段的消耗
				total_consumption += segment_consumption;
				LOG_TRACE("[EnergyEvaluator] 无人机 {} 段 {} 结束: 累积段消耗={:.4f} Wh, 模拟剩余能量={:.2f} Wh. (评估器ID: {})",
						uav.getId(), seg_idx, segment_consumption, current_energy_sim, getId());

			} // 结束轨迹段循环

			// --- 评估成功完成 ---
			cost_result.estimated_energy_consumption = total_consumption;
			// 使用最终模拟状态估算剩余续航
			cost_result.estimated_endurance_remaining = uav_energy_model->estimateEnduranceTime(current_sim_state, current_energy_sim);

			LOG_INFO("[EnergyEvaluator] 无人机 {} 轨迹能量评估完成: 总消耗={:.2f} Wh, 预计剩余续航={:.1f}s. 可行性: {}. (评估器ID: {}, 名称: {})",
				uav.getId(), total_consumption,
				cost_result.estimated_endurance_remaining.value_or(0.0),
				cost_result.is_feasible, getId(), getName());

			return cost_result;
		}

		// 批量评估实现 (基本版本：循环调用单轨迹评估)
		std::map<RouteID, TrajectoryCost> EnergyEvaluator::evaluate(
			NSUav::ConstUavPtr uav,
			const RouteMap& routes,
			const NSMission::Mission* mission) const
		{
			LOG_DEBUG("[EnergyEvaluator] 开始批量评估轨迹。无人机 ID (来自参数): {}, 路径数量: {}. (评估器ID: {})",
					  uav ? uav->getId() : "N/A", routes.size(), getId());
			std::map<RouteID, TrajectoryCost> results;
			for (const auto& pair : routes) {
				const RouteID& route_id = pair.first;
				const Trajectory& trajectory = pair.second;
				LOG_TRACE("[EnergyEvaluator] 批量评估: 正在评估路径 ID: {}", route_id);
				results[route_id] = evaluate(uav, trajectory, mission); // 调用单轨迹评估
			}
			LOG_INFO("[EnergyEvaluator] 批量轨迹评估完成。评估路径数: {}. (评估器ID: {}, 名称: {})",
					routes.size(), getId(), getName());
			return results;
		}

	} // namespace NSAlgorithm
} // namespace NSDrones