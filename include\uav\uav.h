// include/uav/uav.h
#pragma once

#include "core/entity_object.h"
#include "uav/uav_types.h"
#include "uav/idynamic_model.h"
#include "uav/ienergy_model.h"
#include "planning/planning_types.h"
#include <memory>
#include <mutex>
#include <optional>
#include "core/movement_strategy.h"
#include "utils/object_id.h"
#include "nlohmann/json.hpp"

namespace NSDrones {
	namespace NSParams { class ParamRegistry; }
	namespace NSUav {
		class PayloadHandler;
		class Navigator;
		using PayloadHandlerPtr = std::shared_ptr<PayloadHandler>;
		using NavigatorPtr = std::shared_ptr<Navigator>;
	}
}

namespace NSDrones {
	namespace NSUav {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSPlanning;

		/**
		 * @class Uav
		 * @brief 无人机实体类，继承自 EntityObject
		 *
		 * 该类封装了无人机的完整功能，包括：
		 * - 物理特性：类型、尺寸、重量、载荷能力等
		 * - 动力学模型：多旋翼、固定翼、垂直起降等不同飞行器的动力学特性
		 * - 能量模型：电池容量、能耗计算、续航时间等
		 * - 飞行控制：通过 IMovementStrategy 实现不同的飞行行为模式
		 * - 状态管理：位置、速度、姿态、能量等实时状态信息
		 * - 任务执行：当前任务状态、载荷处理、导航等
		 *
		 * 使用方式：
		 * 1. 通过构造函数创建无人机实例，指定类型和基本参数
		 * 2. 调用 initialize() 方法完成动力学模型、能量模型等的初始化
		 * 3. 通过 Environment 管理无人机的生命周期和状态更新
		 *
		 * @note 该类是线程安全的，支持多线程环境下的状态读写
		 */
		class Uav : public EntityObject {
		public:
			/**
			 * @brief 无人机构造函数
			 *
			 * 创建无人机实例并进行基本成员初始化。此构造函数只设置基本属性，
			 * 不创建动力学模型或能量模型，需要在构造后调用 initialize() 完成完整初始化。
			 *
			 * @param id 无人机的全局唯一标识符，用于在环境中区分不同的无人机实例
			 * @param uav_type_key 无人机类型的字符串标识（如 "UAV_Multirotor", "UAV_FixedWing"），
			 *                     将在初始化时转换为对应的 UavType 枚举值
			 * @param env 无人机所属环境的引用，用于状态同步和环境交互
			 * @param name 无人机的显示名称，如果为空则使用 ID 作为名称
			 * @param initial_state 无人机的初始状态（位置、速度、姿态等），默认为零状态
			 * @param clearance_radius 无人机的安全间隙半径（米），用于碰撞检测和路径规划，默认 0.5 米
			 * @param strategy 初始的移动策略指针，通常由 Config 系统传入 nullptr，由 UAV 内部处理
			 *
			 * @throws DroneException 当无人机类型转换失败或 ID 无效时抛出异常
			 *
			 * @note 构造完成后必须调用 initialize() 方法才能正常使用无人机功能
			 */
			Uav(ObjectID id,
				const std::string& uav_type_key,
				const std::string& name = "",
				const EntityState& initial_state = EntityState(), // 修改: 接受 EntityState
				double clearance_radius = 0.5, // 保留 clearance_radius
				std::shared_ptr<IMovementStrategy> strategy = nullptr);

			// --- 禁止拷贝和移动 ---
			Uav(const Uav&) = delete;
			Uav& operator=(const Uav&) = delete;
			Uav(Uav&&) = delete;
			Uav& operator=(Uav&&) = delete;

			/** @brief 虚析构函数 */
			virtual ~Uav() override = default;

			// --- 重载 EntityObject 虚方法 ---
			/** @brief 返回 UAV 类的固定名称标签。 */
			std::string getClassName() const override { return "Uav"; }

			// --- 状态获取方法 ---

			/**
			 * @brief 获取无人机类型
			 * @return 无人机类型枚举值（多旋翼、固定翼、垂直起降等）
			 */
			UavType getType() const;

			/**
			 * @brief 获取动力学模型实例
			 * @return 动力学模型的常量指针，如果未初始化则返回 nullptr
			 * @note 动力学模型负责计算无人机的运动特性和物理约束
			 */
			ConstIDynamicModelPtr getDynamicsModel() const { return dynamic_model_; }

			/**
			 * @brief 获取能量模型实例
			 * @return 能量模型的常量指针，如果未初始化则返回 nullptr
			 * @note 能量模型负责计算电池消耗、续航时间等能量相关信息
			 */
			ConstIEnergyModelPtr getEnergyModel() const { return energy_model_; }

			/**
			 * @brief 获取无人机当前详细状态
			 *
			 * 返回包含位置、速度、姿态、能量等完整信息的 UAVState 对象。
			 * 该方法是线程安全的，会合并基类的 EntityState 和 UAV 特定状态。
			 *
			 * @return 当前 UAVState 的副本，包含所有状态信息
			 * @note 返回的是状态副本，修改返回值不会影响无人机实际状态
			 */
			UavState getUavState() const;

			/**
			 * @brief 获取当前飞行模式
			 *
			 * 根据无人机当前的速度、高度、类型等信息判断飞行模式。
			 * 飞行模式包括：悬停、巡航、起飞、降落、紧急等。
			 *
			 * @return 当前的飞行模式枚举值
			 * @note 飞行模式会影响动力学计算和能量消耗
			 */
			FlightMode getCurrentFlightMode() const;

			/**
			 * @brief 获取当前载荷重量
			 * @return 当前载荷重量（千克），线程安全
			 */
			double getPayloadWeight() const;

			/**
			 * @brief 设置当前载荷重量
			 *
			 * 设置无人机的载荷重量，会检查是否超过最大载荷限制。
			 * 该方法是线程安全的。
			 *
			 * @param weight 载荷重量（千克），必须为非负数且不超过最大载荷
			 * @return 如果重量有效并成功设置返回 true，否则返回 false
			 */
			bool setPayloadWeight(double weight);

			// --- 能力管理方法 ---

			/**
			 * @brief 获取无人机的能力列表
			 *
			 * 返回无人机当前具备的所有能力，包括载荷、传感器、执行器等。
			 * 能力列表从状态中获取，可能包括动态能力（如当前载荷状态）。
			 *
			 * @return 能力名称的字符串向量
			 * @note 返回的是能力列表的副本，修改返回值不会影响无人机实际能力
			 */
			std::vector<std::string> getCapabilities() const;

			/**
			 * @brief 检查无人机是否具备指定能力
			 *
			 * 检查无人机是否具备执行特定任务所需的能力。
			 * 能力可以是载荷类型、传感器类型、执行器类型等。
			 *
			 * @param capability 要检查的能力名称
			 * @return 如果具备该能力返回 true，否则返回 false
			 * @note 该方法会检查当前状态中的能力列表
			 */
			bool hasCapability(const std::string& capability) const;

			// --- 状态更新 ---
			/**
			 * @brief 手动更新无人机状态 (使用详细的 UAVState)。
			 *        会同时更新基类的 EntityState 并触发索引更新。
			 * @param new_uav_state 新的 UAV 状态。
			 */
			void manualStateUpdate(const UavState& new_uav_state);

			// --- 重写基类方法 ---
			/**
			 * @brief 初始化 UAV。
			 *        此方法由工厂函数调用，负责完成 UAV 的特定设置。
			 *        假定 EntityObject::initialize 已经被调用。
			 *        负责创建动力学/能量模型实例、设置形状（如果基类没处理或需覆盖）、设置初始能量等。
			 * @param final_params 由 Config 系统准备好的参数集。
			 * @param raw_instance_json_config 实例的原始 JSON。
			 * @return 如果初始化成功返回 true。
			 */
			bool initialize(
				std::shared_ptr<NSParams::ParamValues> final_params,
				const nlohmann::json& raw_instance_json_config
			) override;

			// --- Getter/Setter ---
			PayloadHandlerPtr getPayloadHandler() const { return payload_handler_; }
			NavigatorPtr getNavigator() const { return navigator_; }
			ObjectID getCurrentTaskId() const { return current_task_id_; }
			MissionStatus getMissionStatus() const { return mission_status_; }
			// void setCurrentTaskId(const ObjectID& task_id) { current_task_id_ = task_id; }
			// void setMissionStatus(MissionStatus status) { mission_status_ = status; }

		private:
			UavType uav_type_; // 存储无人机类型 (由 uav_type_key 在构造/初始化时转换得到)
			IDynamicModelPtr dynamic_model_; // 动力学模型实例 (在 initialize 中创建)
			IEnergyModelPtr energy_model_;   // 能量模型实例 (在 initialize 中创建)

			// UAV 特定状态
			double payload_Weight_ = 0.0; // 当前载荷重量 (kg)
			double clearance_radius_;          // 清除半径，由构造函数参数初始化

			// --- 新声明的成员 ---
			PayloadHandlerPtr payload_handler_ = nullptr;
			NavigatorPtr navigator_ = nullptr;
			ObjectID current_task_id_ = NSUtils::INVALID_OBJECT_ID;
			MissionStatus mission_status_ = MissionStatus::NONE;

			mutable std::mutex state_mutex_; // 保护 UAV 特定状态访问 (例如 payload)

			/** @brief (内部) 将 UAVState 同步到底层 EntityState。 */
			void syncObjectState(const UavState& uav_state);
		};

		using UavPtr = std::shared_ptr<Uav>;
		using ConstUavPtr = std::shared_ptr<const Uav>;

		// --- 转换辅助函数 ---
		/** @brief 将 RoutePoint 转换为基础 UAVState (丢失能量、模式等信息)。 */
		UavState stateFromRoutePt(const RoutePoint& route_point);
		/** @brief 将 UAVState 转换为 RoutePoint。 */
		RoutePoint routePtFromState(const UavState& state);

	} // namespace NSUav
} // namespace NSDrones