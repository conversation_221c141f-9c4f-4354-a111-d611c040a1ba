# 任务策略继承机制使用指南

## 概述

任务策略系统采用两级继承结构，允许在Mission级别定义默认策略，在Task级别进行个性化覆盖。这种设计既保证了策略的一致性，又提供了足够的灵活性。

## 设计原理

### 两级策略结构

1. **Mission级别策略** (`Mission::strategies_`)
   - 作为所有Task的默认策略模板
   - 定义整个任务计划的通用执行策略
   - 例如：统一的高度策略、速度限制、安全约束等

2. **Task级别策略** (`Task::strategies_`)
   - 包含从Mission继承的策略 + 任务特定策略
   - 同名策略会覆盖Mission的默认策略
   - 新策略会补充到策略集合中

### 策略继承流程

```
Mission默认策略 + Task特定策略 → Task最终策略
     ↓                ↓              ↓
  {"Altitude": 100m}  {"Speed": 5m/s}  {"Altitude": 100m, "Speed": 5m/s}
  {"Speed": 3m/s}     {"Altitude": 150m}  {"Altitude": 150m, "Speed": 5m/s}
```

## 使用方法

### 推荐方式：使用 Mission::createTask()

```cpp
#include "mission/mission.h"
#include "mission/task_strategies.h"

// 1. 创建Mission并设置默认策略
auto mission = std::make_shared<Mission>("mission_001");

// 设置Mission级别的默认策略
ITaskStrategyMap default_strategies;
default_strategies["Altitude"] = std::make_shared<AltitudeStrategy>(100.0, AltitudeType::RELATIVE);
default_strategies["Speed"] = std::make_shared<SpeedStrategy>(3.0, 5.0, 1.0);
mission->setStrategies(std::move(default_strategies));

// 2. 创建任务 - 自动继承默认策略
auto task1 = mission->createTask(
    "task_001",
    TaskType::LOITER_POINT,
    createPointTarget(WGS84Point{39.9042, 116.4074, 100.0}),
    CapabilityRequirement{},
    std::make_shared<LoiterPointTaskParams>()
    // 不提供额外策略，完全使用默认策略
);

// 3. 创建任务 - 覆盖部分默认策略
ITaskStrategyMap task_specific_strategies;
task_specific_strategies["Altitude"] = std::make_shared<AltitudeStrategy>(200.0, AltitudeType::ABSOLUTE);
task_specific_strategies["PathConstraint"] = std::make_shared<PathConstraintStrategy>(10.0, 5.0);

auto task2 = mission->createTask(
    "task_002",
    TaskType::SURVEY_AREA,
    createAreaTarget(area_boundary),
    CapabilityRequirement{},
    std::make_shared<SurveyAreaTaskParams>(),
    std::move(task_specific_strategies)  // 覆盖高度策略，添加路径约束策略
);

// 最终结果：
// task1策略: {"Altitude": 100m, "Speed": 3m/s}
// task2策略: {"Altitude": 200m, "Speed": 3m/s, "PathConstraint": ...}
```

### 不推荐方式：直接使用 Task 构造函数

```cpp
// 需要手动合并策略，容易出错
ITaskStrategyMap manual_strategies = mission->getStrategies(); // 复制默认策略
manual_strategies["Altitude"] = std::make_shared<AltitudeStrategy>(200.0); // 手动覆盖

auto task = std::make_shared<Task>(
    "task_003", TaskType::LOITER_POINT, target, requirements,
    std::move(manual_strategies), params
);
```

## 策略管理最佳实践

### 1. 策略命名规范

使用一致的策略名称确保正确的覆盖行为：

```cpp
// 标准策略名称
"Altitude"        // 高度策略
"Speed"           // 速度策略  
"PathConstraint"  // 路径约束策略
"PayloadControl"  // 载荷控制策略
```

### 2. Mission级别策略设计

```cpp
// 为整个任务计划设置合理的默认值
ITaskStrategyMap mission_defaults;

// 安全高度 - 避免地面障碍物
mission_defaults["Altitude"] = std::make_shared<AltitudeStrategy>(
    120.0, AltitudeType::RELATIVE
);

// 保守速度 - 确保安全
mission_defaults["Speed"] = std::make_shared<SpeedStrategy>(
    3.0,   // desired_speed
    8.0,   // max_speed  
    1.0    // min_speed
);

mission->setStrategies(std::move(mission_defaults));
```

### 3. Task级别策略覆盖

```cpp
// 只覆盖需要特殊处理的策略
ITaskStrategyMap task_overrides;

// 高精度任务需要更低的高度
if (task_type == TaskType::DETAILED_INSPECTION) {
    task_overrides["Altitude"] = std::make_shared<AltitudeStrategy>(
        50.0, AltitudeType::RELATIVE
    );
    task_overrides["Speed"] = std::make_shared<SpeedStrategy>(
        1.5, 3.0, 0.5  // 更慢更精确
    );
}

// 紧急任务需要更高的速度
if (is_emergency_task) {
    task_overrides["Speed"] = std::make_shared<SpeedStrategy>(
        8.0, 12.0, 3.0  // 更快的速度
    );
}
```

## 调试和验证

### 检查策略继承结果

```cpp
// 创建任务后检查最终策略
auto task = mission->createTask(...);

// 获取所有策略名称
auto strategy_names = task->getStrategyNames();
LOG_INFO("任务 {} 包含策略: {}", task->getId(), 
         fmt::join(strategy_names, ", "));

// 检查特定策略
if (task->hasStrategy("Altitude")) {
    auto alt_strategy = task->getAltitudeStrategy();
    if (alt_strategy) {
        LOG_INFO("高度策略: {}m ({})", alt_strategy->value, 
                 enumToString(alt_strategy->height_type));
    }
}
```

### 常见问题排查

1. **策略没有被继承**
   - 检查是否使用了 `Mission::createTask()` 方法
   - 确认Mission的默认策略已正确设置

2. **策略覆盖不生效**
   - 检查策略名称是否完全一致（区分大小写）
   - 确认Task特定策略在正确的位置传入

3. **策略类型转换失败**
   - 检查策略指针的实际类型
   - 确认使用了正确的策略类

## 总结

通过合理使用策略继承机制，可以：

1. **提高一致性**：Mission级别统一设置默认策略
2. **增强灵活性**：Task级别可以根据需要覆盖策略  
3. **简化管理**：自动处理策略合并，减少手动错误
4. **便于维护**：集中管理默认策略，易于批量修改

建议始终使用 `Mission::createTask()` 方法创建任务，以确保策略继承的正确性。
