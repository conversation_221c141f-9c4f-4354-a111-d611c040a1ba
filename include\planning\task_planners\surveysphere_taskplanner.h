// include/planning/task_planners/task_planner_surveysphere.h
#pragma once

#include "planning/itask_planner.h"
#include "environment/environment_fwd.h"
#include "mission/task.h"
#include "mission/task_params.h"

namespace NSDrones {
	namespace NSPlanning {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class SurveySphereTaskPlanner
		 * @brief 负责规划 SURVEY_SPHERE (球面勘察) 任务。
		 */
		class SurveySphereTaskPlanner : public ITaskPlanner {
		public:
			SurveySphereTaskPlanner();

			// 新增: 初始化方法
			bool initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) override;

			/**
			 * @brief 规划单机球面勘察任务（重构后的接口）
			 * @param request 单任务规划请求
			 * @return 单任务规划结果
			 */
			SingleTaskPlanningResult planSingleTask(const SingleTaskPlanningRequest& request) override;

			/**
			 * @brief 检查是否支持指定的子任务类型
			 * @param sub_target 子任务目标
			 * @return 是否支持
			 */
			bool isSubTaskSupported(const SubTaskTarget& sub_target) const override{
				return sub_target.task_type == NSMission::TaskType::SURVEY_SPHERE;
			}

		private:
			// === 配置参数 ===
			int default_latitude_divisions_ = 18;     ///< 默认纬度分割数
			int default_longitude_divisions_ = 36;    ///< 默认经度分割数
			double default_flight_speed_ = 8.0;       ///< 默认飞行速度 (m/s)
			double min_radius_ = 1.0;                  ///< 最小半径限制 (m)
			bool enable_optimization_ = true;         ///< 是否启用轨迹优化
			std::string scan_pattern_ = "spherical";  ///< 扫描模式 ("spherical", "spiral", etc.)

			// === 私有辅助方法 ===

			/**
			 * @brief 规划球面勘察轨迹的核心实现
			 * @param request 单任务规划请求
			 * @param params 球面勘察任务参数
			 * @param start_state 无人机起始状态
			 * @return 单任务规划结果
			 */
			SingleTaskPlanningResult planSphereTrajectory(
				const SingleTaskPlanningRequest& request,
				const NSMission::SurveySphereTaskParams& params,
				const NSUav::UavState& start_state);
		};
	} // namespace NSPlanning
} // namespace NSDrones