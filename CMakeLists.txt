# --- 基本配置 ---
cmake_minimum_required(VERSION 3.16) # OMPL/GTest 等可能需要较高版本
project(drones_planning LANGUAGES CXX)

# --- 设置 C++ 标准 ---
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF) # 避免使用 GNU 扩展

# --- 添加全局编译定义 ---
add_compile_definitions(_USE_MATH_DEFINES)
# --- 构建选项 ---
option(BUILD_TESTING "Build the tests" OFF) # 默认开启测试构建

# --- 为 MSVC 添加 /utf-8 编译选项 ---
if(MSVC)
    add_compile_options(/utf-8)
    message(STATUS "Added /utf-8 compile option for MSVC")
    
    # 禁用从 size_t 转换到 unsigned int 的告警提示
    #add_compile_options(/wd4242)
    #message(STATUS "Disabled warning for size_t to unsigned int conversion (C4242)")
endif()

# --- 指定第三方库安装路径 ---
# 将你的安装根目录添加到 CMake 搜索路径
set(THIRD_PARTY_INSTALL_DIR "e:/source/third_party" CACHE PATH "已安装第三方库的根目录")
# 如果某些库安装在其他子目录，也需要添加，例如：
list(APPEND CMAKE_PREFIX_PATH "${THIRD_PARTY_INSTALL_DIR}")
#list(APPEND CMAKE_PREFIX_PATH "${THIRD_PARTY_INSTALL_DIR}/install/boost_1_87_0")
list(APPEND CMAKE_PREFIX_PATH "${THIRD_PARTY_INSTALL_DIR}/install/fcl")
list(APPEND CMAKE_PREFIX_PATH "${THIRD_PARTY_INSTALL_DIR}/install/geographiclib")
#list(APPEND CMAKE_PREFIX_PATH "${THIRD_PARTY_INSTALL_DIR}/install/ompl/include/ompl-1.7")
#list(APPEND CMAKE_PREFIX_PATH "${THIRD_PARTY_INSTALL_DIR}/install/grid_map_core") # 添加 grid_map_core 安装路径

# --- 添加必要的 CMake 模块路径 (如果需要自定义 FindXXX.cmake) ---
# list(APPEND CMAKE_MODULE_PATH ${CMAKE_CURRENT_SOURCE_DIR}/cmake)

# ============================================================
#  查找依赖库
# ============================================================
# CMake Policy for Boost (Keep as is)
cmake_policy(SET CMP0074 NEW)

# --- 必须的库 ---
# Boost (假设 find_package 能找到，可能因为它在系统路径或通过环境变量 BOOST_ROOT)
set(BOOST_DIR "${THIRD_PARTY_INSTALL_DIR}/install/boost_1_87_0/lib64-msvc-14.3/cmake/Boost-1.87.0")
find_package(Boost 1.87.0 REQUIRED PATHS ${BOOST_DIR})
message(STATUS "Found Boost: ${Boost_INCLUDE_DIRS} ; ${Boost_LIBRARIES}")

# Eigen3 配置
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
 set(EIGEN3_DIR "${THIRD_PARTY_INSTALL_DIR}/install/eigen/debug/share/eigen3/cmake") # Debug 安装路径
else()
 set(EIGEN3_DIR "${THIRD_PARTY_INSTALL_DIR}/install/eigen/release/share/eigen3/cmake") # Release 安装路径
endif()
#set(EIGEN3_DIR "${THIRD_PARTY_INSTALL_DIR}/install/boost_1_87_0/lib64-msvc-14.3/cmake/Boost-1.87.0")
find_package(Eigen3 3.3 REQUIRED PATHS ${EIGEN3_DIR})
message(STATUS "Found Eigen3: ${Eigen3_INCLUDE_DIRS}")

# OMPL 配置
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(OMPL_DIR "${THIRD_PARTY_INSTALL_DIR}/install/ompl/debug/share/ompl/cmake") # Debug 安装路径
else()
    set(OMPL_DIR "${THIRD_PARTY_INSTALL_DIR}/install/ompl/release/share/ompl/cmake") # Release 安装路径
endif()
#set(OMPL_DIR "${THIRD_PARTY_INSTALL_DIR}/install/ompl/share/ompl/cmake")
find_package(OMPL REQUIRED PATHS ${OMPL_DIR})
message(STATUS "Found OMPL include: ${OMPL_INCLUDE_DIRS}")
message(STATUS "Found OMPL libs: ${OMPL_LIBRARIES}")

# --- 根据需要启用的库 ---
# FCL (Flexible Collision Library)
find_package(FCL 0.6) # 尝试查找，指定版本（如果知道）
if(FCL_FOUND)
    message(STATUS "Found FCL: ${FCL_INCLUDE_DIRS} ; ${FCL_LIBRARIES}") # 检查变量名是否正确
else()
    message(WARNING "FCL not found in e:/source/third_party/install. Collision checking might be limited.")
endif()

# GeographicLib
find_package(GeographicLib) # 查找任何可用版本
if(GeographicLib_FOUND)
    message(STATUS "Found GeographicLib: ${GeographicLib_INCLUDE_DIRS} ; ${GeographicLib_LIBRARIES}") # 检查变量名是否正确
else()
    message(WARNING "GeographicLib not found. Coordinate conversions might be limited.")
endif()

# 查找 tinyspline (使用 CMAKE_PREFIX_PATH)
set(TINYSPLINE_DIR "${THIRD_PARTY_INSTALL_DIR}/install/tinyspline/lib64/cmake/tinysplinecxx") 
find_package(tinysplinecxx REQUIRED PATHS ${TINYSPLINE_DIR}) 
if(tinysplinecxx_FOUND)
    message(STATUS "Found tinysplinecxx: ${TINYSPLINECXX_INCLUDE_DIRS} ; ${TINYSPLINECXX_LIBRARIES}")
else()
    message(WARNING "tinysplinecxx not found.") # 简化警告信息
endif()

# 查找 grid_map_core
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(GRIDMAP_INSTALL_DIR "${THIRD_PARTY_INSTALL_DIR}/install/grid_map_core/debug") # Debug 安装路径
else()
    set(GRIDMAP_INSTALL_DIR "${THIRD_PARTY_INSTALL_DIR}/install/grid_map_core/release") # Release 安装路径
endif()
find_package(grid_map_core REQUIRED PATHS ${GRIDMAP_INSTALL_DIR})
if(grid_map_core_FOUND)
    message(STATUS "查找 grid_map_core 包完成。")
else()
    message(FATAL_ERROR "grid_map_core 包未找到。")
endif()

find_package(GDAL REQUIRED)
if(GDAL_FOUND)
    message(STATUS "查找GDAL包完成:${GDAL_INCLUDE_DIRS} ; ${GDAL_LIBRARIES}")
else()
    message(FATAL_ERROR "GDAL包未找到。")
endif()

# ============================================================
#  收集源文件
# ============================================================
# --- 定义项目源文件 ---
# 使用 GLOB_RECURSE 查找 src 目录及其子目录下的所有 .cpp 文件
file(GLOB_RECURSE SRC_FILES 
    "src/*.cpp"
    "src/**/*.cpp"
)

# --- 查找项目根目录下的 main.cpp (假设它在项目根目录) ---
set(MAIN_CPP "${CMAKE_CURRENT_SOURCE_DIR}/src/*.cpp")
# 如果 main.cpp 在 src/ 下，它应该已经被 SRC_FILES 包含了
# 确认 main.cpp 的实际位置

# --- 添加 include 目录下的所有头文件到项目中以便 IDE 查看 ---
file(GLOB_RECURSE INCLUDE_HEADERS
    "include/*.h"
    "include/*.hpp"
    "include/*.tpp"
    "include/**/*.h"
    "include/**/*.hpp"
    "include/**/*.tpp"
)

# 将源文件和头文件添加到列表中
# 确保 main.cpp 只被添加一次
# 如果 main.cpp 在 src/ 下，则 SRC_FILES 已包含它
set(PROJECT_SOURCES ${SRC_FILES} ${INCLUDE_HEADERS})

# 如果 main.cpp 不在 src/ 下，需要单独添加
# if(EXISTS ${MAIN_CPP})
#     list(APPEND PROJECT_SOURCES ${MAIN_CPP})
# else()
#     message(FATAL_ERROR "未找到入口点 main.cpp。请将其放在 src/ 目录下或调整 CMakeLists.txt。")
# endif()

# 检查 PROJECT_SOURCES 是否为空
if(NOT PROJECT_SOURCES)
    message(FATAL_ERROR "未能定义任何源文件。请检查 src/ 目录和 CMakeLists.txt 中的 PROJECT_SOURCES 设置。")
else()
    message(STATUS "项目源文件定义为: ${PROJECT_SOURCES}")
endif()

# ============================================================
#  定义主可执行目标
# ============================================================
# 将目标定义为可执行文件 (Executable) 而不是库 (Library)
add_executable(${PROJECT_NAME} ${SRC_FILES}) 
#add_executable(${PROJECT_NAME} src/main.cpp ${OTHER_SRC_FILES}) # 显式添加 main.cpp
set_target_properties(${PROJECT_NAME} PROPERTIES OUTPUT_NAME "drones_planning")

# --- 将头文件添加到目标中，以便 IDE (如 Visual Studio) 可以看到它们 ---
target_sources(${PROJECT_NAME} PRIVATE ${INCLUDE_HEADERS})

# 为主可执行文件添加包含目录
target_include_directories(${PROJECT_NAME} PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/include # 项目自身的 include
    # --- 通过 find_package 获取的包含目录变量 --- 
    ${Eigen3_INCLUDE_DIRS}
    ${Boost_INCLUDE_DIRS}
    ${OMPL_INCLUDE_DIRS}
    ${FCL_INCLUDE_DIRS}
    ${GeographicLib_INCLUDE_DIRS}
    ${TINYSPLINECXX_INCLUDE_DIRS}
    ${GDAL_INCLUDE_DIRS}
    ${grid_map_core_INCLUDE_DIRS}

    # --- 手动添加的头文件库目录 --- 
    "${THIRD_PARTY_INSTALL_DIR}/json/include"
    "${THIRD_PARTY_INSTALL_DIR}/json/single_include" 
    "${THIRD_PARTY_INSTALL_DIR}/spdlog/include"
    "${THIRD_PARTY_INSTALL_DIR}/uuid"
    "${THIRD_PARTY_INSTALL_DIR}/earcut/include"
    "${THIRD_PARTY_INSTALL_DIR}/magic_enum/include/magic_enum"
)

# 为主可执行文件链接依赖库
target_link_libraries(${PROJECT_NAME} PRIVATE
    # --- Targets from find_package --- 
    Eigen3::Eigen
    Boost::system Boost::filesystem 
    # OMPL::ompl # OMPL target 可能不可用或名称不同，使用 LIBRARIES
    # --- 库变量或 Targets (根据 find_package 结果调整) --- 
    ${OMPL_LIBRARIES} # 直接链接 OMPL 库变量
    ${FCL_LIBRARIES}         # 或 FCL::fcl
    ${GeographicLib_LIBRARIES} # 或 GeographicLib::GeographicLib
    grid_map_core::grid_map_core # grid_map_core 目标
    GDAL::GDAL
    tinysplinecxx
)

# --- 拷贝 data 目录到构建输出目录 ---
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${CMAKE_CURRENT_SOURCE_DIR}/data
    $<TARGET_FILE_DIR:${PROJECT_NAME}>/data
    COMMENT "拷贝 data 目录"
)

# ============================================================
#  构建完成消息
# ============================================================
message(STATUS "CMake configuration finished for ${PROJECT_NAME}.")