#pragma once

#include "core/geometry/ishape.h"
#include <fcl/geometry/shape/plane.h>

namespace NSDrones {
namespace NSCore {

    /**
     * @brief 平面形状
     * 
     * 表示一个无限平面，在FCL中直接使用Plane类实现。
     * 主要用于地面、墙面、禁飞区边界等。
     */
    class PlaneShape : public IShape {
    private:
        fcl::Vector3d normal_;  // 平面法向量（归一化）
        double distance_;       // 平面到原点的距离
        
        mutable std::shared_ptr<fcl::Planed> fcl_plane_;  // FCL平面对象（延迟创建）

    public:
        /**
         * @brief 构造函数
         * @param normal 平面法向量（会被自动归一化）
         * @param distance 平面到原点的距离
         */
        PlaneShape(const fcl::Vector3d& normal, double distance);

        /**
         * @brief 从三点构造平面
         * @param p1 第一个点
         * @param p2 第二个点
         * @param p3 第三个点
         */
        PlaneShape(const fcl::Vector3d& p1, const fcl::Vector3d& p2, const fcl::Vector3d& p3);

        /**
         * @brief 默认构造函数（XY平面）
         */
        PlaneShape();

        /**
         * @brief 拷贝构造函数
         */
        PlaneShape(const PlaneShape& other);

        /**
         * @brief 赋值操作符
         */
        PlaneShape& operator=(const PlaneShape& other);

        // IShape接口实现
        ShapeType getType() const override { return ShapeType::PLANE; }
        std::shared_ptr<fcl::CollisionGeometryd> getFCLGeometry() const override;
        fcl::AABBd getAABB(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const override;
        double getVolume() const override { return 0.0; }  // 平面的体积为0
        double getSurfaceArea() const override { return std::numeric_limits<double>::infinity(); }  // 无限大
        fcl::Vector3d getCentroid() const override;
        fcl::Matrix3d getInertiaMatrix(double mass) const override;
        std::unique_ptr<IShape> clone() const override;
        nlohmann::json serialize() const override;
        bool deserialize(const nlohmann::json& json) override;
        std::string toString() const override;
        bool containsPoint(const fcl::Vector3d& point) const override;
        double distanceToPoint(const fcl::Vector3d& point) const override;
        double getCharacteristicSize() const override { return std::numeric_limits<double>::infinity(); }
        int getDimension() const override { return 2; }  // 平面是2维

        // 平面特有方法
        /**
         * @brief 获取平面法向量
         * @return 归一化的法向量
         */
        const fcl::Vector3d& getNormal() const { return normal_; }

        /**
         * @brief 获取平面到原点的距离
         * @return 距离值
         */
        double getDistance() const { return distance_; }

        /**
         * @brief 设置平面参数
         * @param normal 新的法向量（会被归一化）
         * @param distance 新的距离
         */
        void setPlane(const fcl::Vector3d& normal, double distance);

        /**
         * @brief 检查点在平面的哪一侧
         * @param point 查询点
         * @return 正值表示在法向量方向，负值表示在相反方向，0表示在平面上
         */
        double signedDistanceToPoint(const fcl::Vector3d& point) const;

        /**
         * @brief 将点投影到平面上
         * @param point 要投影的点
         * @return 投影后的点
         */
        fcl::Vector3d projectPoint(const fcl::Vector3d& point) const;

        /**
         * @brief 获取平面上的一个点
         * @return 平面上距离原点最近的点
         */
        fcl::Vector3d getPointOnPlane() const;

        /**
         * @brief 创建XY平面（Z=0）
         * @return 平面形状实例
         */
        static std::unique_ptr<PlaneShape> createXYPlane();

        /**
         * @brief 创建XZ平面（Y=0）
         * @return 平面形状实例
         */
        static std::unique_ptr<PlaneShape> createXZPlane();

        /**
         * @brief 创建YZ平面（X=0）
         * @return 平面形状实例
         */
        static std::unique_ptr<PlaneShape> createYZPlane();

        /**
         * @brief 从点和法向量创建平面
         * @param point 平面上的一点
         * @param normal 法向量
         * @return 平面形状实例
         */
        static std::unique_ptr<PlaneShape> createFromPointAndNormal(const fcl::Vector3d& point, const fcl::Vector3d& normal);

    private:
        void ensureFCLObject() const;
        void validateParameters() const;
        void normalizeNormal();
    };

} // namespace NSCore
} // namespace NSDrones
