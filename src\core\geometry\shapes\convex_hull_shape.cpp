#include "core/geometry/shapes/convex_hull_shape.h"
#include "utils/enum_utils.h"
#include <stdexcept>
#include <sstream>
#include <algorithm>
#include <set>

namespace NSDrones {
namespace NSCore {

    ConvexHullShape::ConvexHullShape(const std::vector<fcl::Vector3d>& vertices)
        : vertices_(vertices), geometry_dirty_(true) {
        computeConvexHull();
        validateVertices();
    }

    ConvexHullShape::ConvexHullShape() : geometry_dirty_(true) {
    }

    ConvexHullShape::ConvexHullShape(const ConvexHullShape& other)
        : vertices_(other.vertices_), faces_(other.faces_), geometry_dirty_(true) {
    }

    ConvexHullShape& ConvexHullShape::operator=(const ConvexHullShape& other) {
        if (this != &other) {
            vertices_ = other.vertices_;
            faces_ = other.faces_;
            markGeometryDirty();
        }
        return *this;
    }

    std::shared_ptr<fcl::CollisionGeometryd> ConvexHullShape::getFCLGeometry() const {
        ensureFCLObject();
        return fcl_convex_;
    }

    fcl::AABBd ConvexHullShape::getAABB(const fcl::Transform3d& transform) const {
        if (vertices_.empty()) {
            return fcl::AABBd();
        }

        fcl::Vector3d min_pt = transform * vertices_[0];
        fcl::Vector3d max_pt = min_pt;

        for (size_t i = 1; i < vertices_.size(); ++i) {
            fcl::Vector3d transformed_pt = transform * vertices_[i];
            min_pt = min_pt.cwiseMin(transformed_pt);
            max_pt = max_pt.cwiseMax(transformed_pt);
        }

        fcl::AABBd aabb;
        aabb.min_ = min_pt;
        aabb.max_ = max_pt;
        return aabb;
    }

    double ConvexHullShape::getVolume() const {
        if (vertices_.size() < 4 || faces_.empty()) {
            return 0.0;
        }

        // 使用散度定理计算体积
        double volume = 0.0;
        for (size_t i = 0; i < faces_.size(); i += 3) {
            const fcl::Vector3d& v0 = vertices_[faces_[i]];
            const fcl::Vector3d& v1 = vertices_[faces_[i + 1]];
            const fcl::Vector3d& v2 = vertices_[faces_[i + 2]];

            // 计算三角形面积向量
            fcl::Vector3d area_vector = 0.5 * (v1 - v0).cross(v2 - v0);
            
            // 计算三角形重心
            fcl::Vector3d centroid = (v0 + v1 + v2) / 3.0;
            
            // 体积贡献
            volume += area_vector.dot(centroid);
        }

        return std::abs(volume) / 3.0;
    }

    double ConvexHullShape::getSurfaceArea() const {
        if (faces_.empty()) {
            return 0.0;
        }

        double area = 0.0;
        for (size_t i = 0; i < faces_.size(); i += 3) {
            const fcl::Vector3d& v0 = vertices_[faces_[i]];
            const fcl::Vector3d& v1 = vertices_[faces_[i + 1]];
            const fcl::Vector3d& v2 = vertices_[faces_[i + 2]];

            // 计算三角形面积
            fcl::Vector3d edge1 = v1 - v0;
            fcl::Vector3d edge2 = v2 - v0;
            area += 0.5 * edge1.cross(edge2).norm();
        }

        return area;
    }

    fcl::Vector3d ConvexHullShape::getCentroid() const {
        if (vertices_.empty()) {
            return fcl::Vector3d::Zero();
        }

        fcl::Vector3d centroid = fcl::Vector3d::Zero();
        for (const auto& vertex : vertices_) {
            centroid += vertex;
        }
        return centroid / static_cast<double>(vertices_.size());
    }

    fcl::Matrix3d ConvexHullShape::getInertiaMatrix(double mass) const {
        // 简化的惯性张量计算（假设均匀密度）
        if (vertices_.empty()) {
            return fcl::Matrix3d::Zero();
        }

        fcl::Vector3d centroid = getCentroid();
        fcl::Matrix3d inertia = fcl::Matrix3d::Zero();

        for (const auto& vertex : vertices_) {
            fcl::Vector3d r = vertex - centroid;
            double r_squared = r.squaredNorm();
            
            // 惯性张量公式
            inertia(0, 0) += r_squared - r.x() * r.x();
            inertia(1, 1) += r_squared - r.y() * r.y();
            inertia(2, 2) += r_squared - r.z() * r.z();
            inertia(0, 1) -= r.x() * r.y();
            inertia(0, 2) -= r.x() * r.z();
            inertia(1, 2) -= r.y() * r.z();
        }

        // 对称化
        inertia(1, 0) = inertia(0, 1);
        inertia(2, 0) = inertia(0, 2);
        inertia(2, 1) = inertia(1, 2);

        return (mass / vertices_.size()) * inertia;
    }

    std::unique_ptr<IShape> ConvexHullShape::clone() const {
        return std::make_unique<ConvexHullShape>(*this);
    }

    nlohmann::json ConvexHullShape::serialize() const {
        nlohmann::json j;
        j["type"] = NSUtils::enumToString(getType());
        
        nlohmann::json vertices_json = nlohmann::json::array();
        for (const auto& vertex : vertices_) {
            vertices_json.push_back({vertex.x(), vertex.y(), vertex.z()});
        }
        j["vertices"] = vertices_json;
        
        j["faces"] = faces_;
        
        return j;
    }

    bool ConvexHullShape::deserialize(const nlohmann::json& json) {
        try {
            std::string type_str = json.at("type").get<std::string>();
            auto type_opt = NSUtils::stringToEnum<ShapeType>(type_str, ShapeType::UNKNOWN);
            if (type_opt != ShapeType::CONVEX_HULL) {
                return false;
            }
            
            clear();
            
            if (json.contains("vertices") && json["vertices"].is_array()) {
                for (const auto& vertex_json : json["vertices"]) {
                    if (vertex_json.is_array() && vertex_json.size() == 3) {
                        fcl::Vector3d vertex(
                            vertex_json[0].get<double>(),
                            vertex_json[1].get<double>(),
                            vertex_json[2].get<double>()
                        );
                        vertices_.push_back(vertex);
                    }
                }
            }
            
            if (json.contains("faces") && json["faces"].is_array()) {
                faces_ = json["faces"].get<std::vector<int>>();
            } else {
                computeFaces();
            }
            
            validateVertices();
            markGeometryDirty();
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    std::string ConvexHullShape::toString() const {
        std::ostringstream oss;
        oss << "ConvexHullShape(vertices=" << vertices_.size() << ", faces=" << getFaceCount() << ")";
        return oss.str();
    }

    bool ConvexHullShape::containsPoint(const fcl::Vector3d& point) const {
        if (vertices_.size() < 4) {
            return false;
        }

        // 使用面法向量检查点是否在凸包内部
        for (size_t i = 0; i < faces_.size(); i += 3) {
            const fcl::Vector3d& v0 = vertices_[faces_[i]];
            const fcl::Vector3d& v1 = vertices_[faces_[i + 1]];
            const fcl::Vector3d& v2 = vertices_[faces_[i + 2]];

            // 计算面法向量（指向外部）
            fcl::Vector3d normal = (v1 - v0).cross(v2 - v0).normalized();
            
            // 检查点是否在面的外侧
            if (normal.dot(point - v0) > 1e-10) {
                return false;  // 点在面外侧，不在凸包内
            }
        }

        return true;
    }

    double ConvexHullShape::distanceToPoint(const fcl::Vector3d& point) const {
        if (vertices_.empty()) {
            return std::numeric_limits<double>::infinity();
        }

        if (containsPoint(point)) {
            // 点在内部，计算到最近面的距离（负值）
            double min_distance = std::numeric_limits<double>::max();
            
            for (size_t i = 0; i < faces_.size(); i += 3) {
                const fcl::Vector3d& v0 = vertices_[faces_[i]];
                const fcl::Vector3d& v1 = vertices_[faces_[i + 1]];
                const fcl::Vector3d& v2 = vertices_[faces_[i + 2]];

                fcl::Vector3d normal = (v1 - v0).cross(v2 - v0).normalized();
                double distance = std::abs(normal.dot(point - v0));
                min_distance = std::min(min_distance, distance);
            }
            
            return -min_distance;
        } else {
            // 点在外部，计算到最近面的距离
            double min_distance = std::numeric_limits<double>::max();
            
            for (size_t i = 0; i < faces_.size(); i += 3) {
                const fcl::Vector3d& v0 = vertices_[faces_[i]];
                const fcl::Vector3d& v1 = vertices_[faces_[i + 1]];
                const fcl::Vector3d& v2 = vertices_[faces_[i + 2]];

                // 计算点到三角形的距离
                fcl::Vector3d normal = (v1 - v0).cross(v2 - v0).normalized();
                double distance = normal.dot(point - v0);
                
                if (distance > 0) {  // 点在面外侧
                    min_distance = std::min(min_distance, distance);
                }
            }
            
            return min_distance;
        }
    }

    double ConvexHullShape::getCharacteristicSize() const {
        if (vertices_.size() < 2) {
            return 0.0;
        }

        fcl::AABBd aabb = getAABB();
        fcl::Vector3d size = aabb.max_ - aabb.min_;
        return size.norm();
    }

    const fcl::Vector3d& ConvexHullShape::getVertex(size_t index) const {
        validateIndex(index);
        return vertices_[index];
    }

    void ConvexHullShape::addVertex(const fcl::Vector3d& vertex) {
        vertices_.push_back(vertex);
        markGeometryDirty();
    }

    void ConvexHullShape::addVertices(const std::vector<fcl::Vector3d>& vertices) {
        vertices_.insert(vertices_.end(), vertices.begin(), vertices.end());
        markGeometryDirty();
    }

    void ConvexHullShape::setVertices(const std::vector<fcl::Vector3d>& vertices) {
        vertices_ = vertices;
        computeConvexHull();
        markGeometryDirty();
    }

    void ConvexHullShape::clear() {
        vertices_.clear();
        faces_.clear();
        markGeometryDirty();
    }

    void ConvexHullShape::computeConvexHull() {
        if (vertices_.size() < 4) {
            faces_.clear();
            return;
        }

        // 这里应该实现真正的凸包算法（如QuickHull）
        // 为了简化，这里只是生成一个简单的四面体
        computeFaces();
    }

    void ConvexHullShape::simplify(double tolerance) {
        // 简化实现：移除距离过近的顶点
        if (vertices_.size() <= 4) return;

        std::vector<fcl::Vector3d> simplified_vertices;
        simplified_vertices.push_back(vertices_[0]);

        for (size_t i = 1; i < vertices_.size(); ++i) {
            bool too_close = false;
            for (const auto& existing : simplified_vertices) {
                if ((vertices_[i] - existing).norm() < tolerance) {
                    too_close = true;
                    break;
                }
            }
            if (!too_close) {
                simplified_vertices.push_back(vertices_[i]);
            }
        }

        if (simplified_vertices.size() >= 4) {
            vertices_ = simplified_vertices;
            computeConvexHull();
            markGeometryDirty();
        }
    }

    std::vector<fcl::Vector3d> ConvexHullShape::getBoundingBoxVertices(const fcl::Transform3d& transform) const {
        fcl::AABBd aabb = getAABB(transform);
        
        std::vector<fcl::Vector3d> vertices = {
            fcl::Vector3d(aabb.min_.x(), aabb.min_.y(), aabb.min_.z()),
            fcl::Vector3d(aabb.max_.x(), aabb.min_.y(), aabb.min_.z()),
            fcl::Vector3d(aabb.min_.x(), aabb.max_.y(), aabb.min_.z()),
            fcl::Vector3d(aabb.max_.x(), aabb.max_.y(), aabb.min_.z()),
            fcl::Vector3d(aabb.min_.x(), aabb.min_.y(), aabb.max_.z()),
            fcl::Vector3d(aabb.max_.x(), aabb.min_.y(), aabb.max_.z()),
            fcl::Vector3d(aabb.min_.x(), aabb.max_.y(), aabb.max_.z()),
            fcl::Vector3d(aabb.max_.x(), aabb.max_.y(), aabb.max_.z())
        };
        
        return vertices;
    }

    std::vector<std::pair<size_t, size_t>> ConvexHullShape::getEdges() const {
        std::set<std::pair<size_t, size_t>> edge_set;
        
        for (size_t i = 0; i < faces_.size(); i += 3) {
            size_t v0 = faces_[i];
            size_t v1 = faces_[i + 1];
            size_t v2 = faces_[i + 2];
            
            // 添加三条边（确保较小索引在前）
            edge_set.insert({std::min(v0, v1), std::max(v0, v1)});
            edge_set.insert({std::min(v1, v2), std::max(v1, v2)});
            edge_set.insert({std::min(v2, v0), std::max(v2, v0)});
        }
        
        return std::vector<std::pair<size_t, size_t>>(edge_set.begin(), edge_set.end());
    }

    std::vector<fcl::Vector3d> ConvexHullShape::getTransformedVertices(const fcl::Transform3d& transform) const {
        std::vector<fcl::Vector3d> transformed_vertices;
        transformed_vertices.reserve(vertices_.size());
        
        for (const auto& vertex : vertices_) {
            transformed_vertices.push_back(transform * vertex);
        }
        
        return transformed_vertices;
    }

    void ConvexHullShape::ensureFCLObject() const {
        if (!fcl_convex_ || geometry_dirty_) {
            if (vertices_.size() >= 4 && !faces_.empty()) {
                // 创建shared_ptr包装的顶点向量
                auto vertices_ptr = std::make_shared<std::vector<fcl::Vector3d>>();
                vertices_ptr->reserve(vertices_.size());
                for (const auto& vertex : vertices_) {
                    vertices_ptr->emplace_back(vertex.x(), vertex.y(), vertex.z());
                }

                // 创建shared_ptr包装的面索引向量
                auto faces_ptr = std::make_shared<std::vector<int>>(faces_);

                // 使用新的FCL API创建Convex对象
                fcl_convex_ = std::make_shared<fcl::Convexd>(
                    vertices_ptr,
                    static_cast<int>(faces_.size() / 3),  // 面的数量
                    faces_ptr,
                    true  // 是否拥有数据
                );
                geometry_dirty_ = false;
            }
        }
    }

    void ConvexHullShape::validateVertices() const {
        if (vertices_.size() >= 4 && faces_.empty()) {
            throw std::invalid_argument("ConvexHullShape: 顶点数量足够但缺少面信息");
        }
    }

    void ConvexHullShape::computeFaces() {
        faces_.clear();
        
        if (vertices_.size() < 4) {
            return;
        }

        // 简化实现：创建一个四面体的面
        // 实际应用中应该使用真正的凸包算法
        if (vertices_.size() >= 4) {
            // 四面体的4个面
            faces_ = {
                0, 1, 2,  // 面1
                0, 2, 3,  // 面2
                0, 3, 1,  // 面3
                1, 3, 2   // 面4
            };
        }
    }

    void ConvexHullShape::validateIndex(size_t index) const {
        if (index >= vertices_.size()) {
            throw std::out_of_range("ConvexHullShape: 顶点索引超出范围");
        }
    }

} // namespace NSCore
} // namespace NSDrones
