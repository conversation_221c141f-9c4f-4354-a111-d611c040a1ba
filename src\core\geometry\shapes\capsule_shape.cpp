#include "core/geometry/shapes/capsule_shape.h"
#include "utils/enum_utils.h"
#include <stdexcept>
#include <sstream>
#include <cmath>
#include <algorithm>

namespace NSDrones {
namespace NSCore {

    CapsuleShape::CapsuleShape(double radius, double height) 
        : radius_(radius), height_(height), fcl_capsule_(nullptr) {
        validateDimensions();
    }

    CapsuleShape::CapsuleShape() : CapsuleShape(0.5, 1.0) {
    }

    CapsuleShape::CapsuleShape(const CapsuleShape& other)
        : radius_(other.radius_), height_(other.height_), fcl_capsule_(nullptr) {
    }

    CapsuleShape& CapsuleShape::operator=(const CapsuleShape& other) {
        if (this != &other) {
            radius_ = other.radius_;
            height_ = other.height_;
            fcl_capsule_.reset();  // 重置FCL对象，延迟重新创建
        }
        return *this;
    }

    std::shared_ptr<fcl::CollisionGeometryd> CapsuleShape::getFCLGeometry() const {
        ensureFCLObject();
        return fcl_capsule_;
    }

    fcl::AABBd CapsuleShape::getAABB(const fcl::Transform3d& transform) const {
        ensureFCLObject();
        fcl::AABBd aabb;
        fcl::computeBV(*fcl_capsule_, transform, aabb);
        return aabb;
    }

    double CapsuleShape::getVolume() const {
        return getCylinderVolume() + getSphereVolume();
    }

    double CapsuleShape::getSurfaceArea() const {
        return getCylinderSurfaceArea() + getSphereSurfaceArea();
    }

    fcl::Vector3d CapsuleShape::getCentroid() const {
        return fcl::Vector3d(0.0, 0.0, 0.0);  // 几何中心在原点
    }

    fcl::Matrix3d CapsuleShape::getInertiaMatrix(double mass) const {
        // 胶囊体的惯性张量（相对于质心，Z轴为胶囊轴）
        double total_volume = getVolume();
        double cylinder_volume = getCylinderVolume();
        double sphere_volume = getSphereVolume();
        
        double cylinder_mass = mass * (cylinder_volume / total_volume);
        double sphere_mass = mass * (sphere_volume / total_volume);
        
        // 圆柱部分的惯性张量
        double r2 = radius_ * radius_;
        double h2 = height_ * height_;
        fcl::Matrix3d cylinder_inertia = fcl::Matrix3d::Zero();
        cylinder_inertia(0, 0) = cylinder_mass / 12.0 * (3.0 * r2 + h2);
        cylinder_inertia(1, 1) = cylinder_mass / 12.0 * (3.0 * r2 + h2);
        cylinder_inertia(2, 2) = cylinder_mass / 2.0 * r2;
        
        // 球形部分的惯性张量（两个半球）
        double sphere_inertia_value = (2.0 / 5.0) * sphere_mass * r2;
        fcl::Matrix3d sphere_inertia = fcl::Matrix3d::Zero();
        sphere_inertia(0, 0) = sphere_inertia_value;
        sphere_inertia(1, 1) = sphere_inertia_value;
        sphere_inertia(2, 2) = sphere_inertia_value;
        
        // 使用平行轴定理调整球形部分（两个半球分别在±height/2处）
        double offset = height_ * 0.5;
        double offset_inertia = sphere_mass * offset * offset;
        sphere_inertia(0, 0) += offset_inertia;
        sphere_inertia(1, 1) += offset_inertia;
        
        return cylinder_inertia + sphere_inertia;
    }

    std::unique_ptr<IShape> CapsuleShape::clone() const {
        return std::make_unique<CapsuleShape>(*this);
    }

    nlohmann::json CapsuleShape::serialize() const {
        nlohmann::json j;
        j["type"] = NSUtils::enumToString(getType());
        j["radius"] = radius_;
        j["height"] = height_;
        return j;
    }

    bool CapsuleShape::deserialize(const nlohmann::json& json) {
        try {
            std::string type_str = json.at("type").get<std::string>();
            auto type_opt = NSUtils::stringToEnum<ShapeType>(type_str, ShapeType::UNKNOWN);
            if (type_opt != ShapeType::CAPSULE) {
                return false;
            }
            
            radius_ = json.at("radius").get<double>();
            height_ = json.at("height").get<double>();
            
            validateDimensions();
            fcl_capsule_.reset();  // 重置FCL对象
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    std::string CapsuleShape::toString() const {
        std::ostringstream oss;
        oss << "CapsuleShape(radius=" << radius_ << ", height=" << height_ 
            << ", total_length=" << getTotalLength() << ")";
        return oss.str();
    }

    bool CapsuleShape::containsPoint(const fcl::Vector3d& point) const {
        double half_height = height_ * 0.5;
        double radial_distance = std::sqrt(point.x() * point.x() + point.y() * point.y());
        
        if (std::abs(point.z()) <= half_height) {
            // 在圆柱部分
            return radial_distance <= radius_;
        } else {
            // 在球形部分
            fcl::Vector3d sphere_center;
            if (point.z() > half_height) {
                sphere_center = fcl::Vector3d(0.0, 0.0, half_height);
            } else {
                sphere_center = fcl::Vector3d(0.0, 0.0, -half_height);
            }
            
            double distance_to_center = (point - sphere_center).norm();
            return distance_to_center <= radius_;
        }
    }

    double CapsuleShape::distanceToPoint(const fcl::Vector3d& point) const {
        double half_height = height_ * 0.5;
        double radial_distance = std::sqrt(point.x() * point.x() + point.y() * point.y());
        
        if (std::abs(point.z()) <= half_height) {
            // 在圆柱部分的高度范围内
            return radial_distance - radius_;
        } else {
            // 在球形部分
            fcl::Vector3d sphere_center;
            if (point.z() > half_height) {
                sphere_center = fcl::Vector3d(0.0, 0.0, half_height);
            } else {
                sphere_center = fcl::Vector3d(0.0, 0.0, -half_height);
            }
            
            double distance_to_center = (point - sphere_center).norm();
            return distance_to_center - radius_;
        }
    }

    double CapsuleShape::getCharacteristicSize() const {
        return getTotalLength();
    }

    void CapsuleShape::setSize(double radius, double height) {
        radius_ = radius;
        height_ = height;
        validateDimensions();
        fcl_capsule_.reset();  // 重置FCL对象
    }

    double CapsuleShape::getCylinderVolume() const {
        return M_PI * radius_ * radius_ * height_;
    }

    double CapsuleShape::getSphereVolume() const {
        return (4.0 / 3.0) * M_PI * radius_ * radius_ * radius_;
    }

    double CapsuleShape::getCylinderSurfaceArea() const {
        return 2.0 * M_PI * radius_ * height_;
    }

    double CapsuleShape::getSphereSurfaceArea() const {
        return 4.0 * M_PI * radius_ * radius_;
    }

    fcl::Vector3d CapsuleShape::getPointOnCapsule(double theta, double z,
                                                 const fcl::Transform3d& transform) const {
        double half_height = height_ * 0.5;
        double total_half_length = half_height + radius_;
        
        // 确保z在有效范围内
        z = std::clamp(z, -total_half_length, total_half_length);
        
        fcl::Vector3d point;
        
        if (std::abs(z) <= half_height) {
            // 圆柱部分
            point = fcl::Vector3d(radius_ * std::cos(theta), radius_ * std::sin(theta), z);
        } else {
            // 球形部分
            double sphere_center_z = (z > 0) ? half_height : -half_height;
            double local_z = z - sphere_center_z;
            
            // 球坐标转换
            double phi = std::acos(std::clamp(local_z / radius_, -1.0, 1.0));
            double x = radius_ * std::sin(phi) * std::cos(theta);
            double y = radius_ * std::sin(phi) * std::sin(theta);
            double sphere_z = radius_ * std::cos(phi);
            
            point = fcl::Vector3d(x, y, sphere_center_z + sphere_z);
        }
        
        return transform * point;
    }

    std::vector<fcl::Vector3d> CapsuleShape::generateUniformPoints(size_t num_circumference, 
                                                                  size_t num_height,
                                                                  const fcl::Transform3d& transform) const {
        std::vector<fcl::Vector3d> points;
        
        double half_height = height_ * 0.5;
        double total_length = getTotalLength();
        
        // 生成点
        for (size_t i = 0; i < num_circumference; ++i) {
            double theta = 2.0 * M_PI * i / num_circumference;
            for (size_t j = 0; j < num_height; ++j) {
                double z = -total_length * 0.5 + total_length * j / (num_height - 1);
                points.push_back(getPointOnCapsule(theta, z, transform));
            }
        }
        
        return points;
    }

    fcl::Vector3d CapsuleShape::getSurfaceNormal(const fcl::Vector3d& point,
                                                const fcl::Transform3d& transform) const {
        // 将点转换到局部坐标系
        fcl::Vector3d local_point = transform.inverse() * point;
        
        double half_height = height_ * 0.5;
        fcl::Vector3d normal;
        
        if (std::abs(local_point.z()) <= half_height) {
            // 圆柱部分
            double radial_distance = std::sqrt(local_point.x() * local_point.x() + local_point.y() * local_point.y());
            if (radial_distance > 1e-10) {
                normal = fcl::Vector3d(local_point.x() / radial_distance, local_point.y() / radial_distance, 0.0);
            } else {
                normal = fcl::Vector3d(1.0, 0.0, 0.0);  // 默认法向量
            }
        } else {
            // 球形部分
            fcl::Vector3d sphere_center;
            if (local_point.z() > half_height) {
                sphere_center = fcl::Vector3d(0.0, 0.0, half_height);
            } else {
                sphere_center = fcl::Vector3d(0.0, 0.0, -half_height);
            }
            
            fcl::Vector3d to_point = local_point - sphere_center;
            double distance = to_point.norm();
            if (distance > 1e-10) {
                normal = to_point / distance;
            } else {
                normal = fcl::Vector3d(0.0, 0.0, (local_point.z() > 0) ? 1.0 : -1.0);
            }
        }
        
        // 转换回全局坐标系（只应用旋转）
        return transform.rotation() * normal;
    }

    std::unique_ptr<CapsuleShape> CapsuleShape::createUnitCapsule() {
        return std::make_unique<CapsuleShape>(0.5, 1.0);
    }

    std::unique_ptr<CapsuleShape> CapsuleShape::createFromDiameter(double diameter, double height) {
        return std::make_unique<CapsuleShape>(diameter * 0.5, height);
    }

    std::unique_ptr<CapsuleShape> CapsuleShape::createFromTotalLength(double radius, double total_length) {
        double height = total_length - 2.0 * radius;
        if (height < 0.0) {
            throw std::invalid_argument("CapsuleShape: 总长度必须大于直径");
        }
        return std::make_unique<CapsuleShape>(radius, height);
    }

    void CapsuleShape::ensureFCLObject() const {
        if (!fcl_capsule_) {
            fcl_capsule_ = std::make_shared<fcl::Capsuled>(radius_, height_);
        }
    }

    void CapsuleShape::validateDimensions() const {
        if (radius_ <= 0.0 || height_ <= 0.0) {
            throw std::invalid_argument("CapsuleShape: 半径和高度必须为正数");
        }
        if (!std::isfinite(radius_) || !std::isfinite(height_)) {
            throw std::invalid_argument("CapsuleShape: 尺寸必须为有限数值");
        }
    }

} // namespace NSCore
} // namespace NSDrones
