// src/planning/task_planners/task_planner_scanarea.cpp
#include "planning/task_planners/scanarea_taskplanner.h"
#include "planning/planning_types.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/task_strategies.h"
#include "uav/uav.h"
#include "environment/environment.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"
#include "utils/geometry_manager.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include <vector>
#include <cmath>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <utility>

namespace NSDrones {
	namespace NSPlanning {

		ScanAreaTaskPlanner::ScanAreaTaskPlanner()
			: ITaskPlanner() {}

		bool ScanAreaTaskPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params,
											 const nlohmann::json& raw_config) {
			LOG_DEBUG("扫描任务规划器: 开始初始化");

			if (params) {
				// 加载扫描相关参数
				default_scan_altitude_ = params->getValueOrDefault<double>("scan.altitude", 50.0);
				default_scan_speed_ = params->getValueOrDefault<double>("scan.speed", 10.0);
				default_overlap_ratio_ = params->getValueOrDefault<double>("scan.overlap_ratio", 0.3);

				// 加载扫描模式参数
				auto pattern_opt = params->getEnumValue<NSPlanning::ScanPatternType>("task_planner.scan.pattern");
				if (pattern_opt.has_value()) {
					default_scan_pattern_ = pattern_opt.value();
				} else {
					LOG_WARN("扫描任务规划器: 无法获取扫描模式参数，使用默认值ZIGZAG");
					default_scan_pattern_ = NSPlanning::ScanPatternType::ZIGZAG;
				}

				LOG_INFO("扫描任务规划器: 参数加载完成 - 高度:{:.1f}m, 速度:{:.1f}m/s, 重叠率:{:.2f}, 模式:{}",
						default_scan_altitude_, default_scan_speed_, default_overlap_ratio_,
						NSUtils::enumToString(default_scan_pattern_));
			}

			// 加载相机参数
			if (!raw_config.empty() && raw_config.contains("camera_fov")) {
				camera_fov_deg_ = raw_config["camera_fov"].get<double>();
				LOG_DEBUG("扫描任务规划器: 相机视场角设置为{:.1f}度", camera_fov_deg_);
			}

			LOG_DEBUG("扫描任务规划器: 初始化完成");
			return true;
		}

		/**
		 * @brief 规划单机区域扫描任务（重构后的接口）
		 */
		SingleTaskPlanningResult ScanAreaTaskPlanner::planSingleTask(
			const SingleTaskPlanningRequest& request) {

			LOG_INFO("[ScanAreaTaskPlanner] 开始规划子任务 [{}]，UAV [{}]",
				request.assignment.sub_task_id, request.assignment.assigned_uav_id);

			SingleTaskPlanningResult result;
			result.success = false;
			result.sub_task_id = request.assignment.sub_task_id;
			result.uav_id = request.assignment.assigned_uav_id;

			try {
				// === 第1步：验证输入（使用公共方法） ===
				if (!validateBasicTaskRequest(request, result, NSCore::TaskType::SCAN_AREA)) {
					return result;
				}

				// === 第2步：获取任务参数 ===
				auto params_ptr = request.original_task->getTaskParameters<NSMission::ScanAreaTaskParams>();
				if (!params_ptr) {
					result.message = "无法获取 ScanArea 任务参数";
					LOG_ERROR("[ScanAreaTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第3步：获取起始状态 ===
				NSUav::UavState start_state;
				if (!getUavStartState(request.uav, start_state)) {
					result.message = "无法获取无人机起始状态";
					LOG_ERROR("[ScanAreaTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第4步：执行扫描规划 ===
				result = planScanAreaTrajectory(request, *params_ptr, start_state);

			} catch (const std::exception& e) {
				result.success = false;
				result.message = "规划异常: " + std::string(e.what());
				LOG_ERROR("[ScanAreaTaskPlanner] 子任务 [{}] 规划异常: {}", result.sub_task_id, e.what());
			}

			LOG_INFO("[ScanAreaTaskPlanner] 子任务 [{}] 规划完成，成功: {}",
				result.sub_task_id, result.success);
			return result;
		}

		/**
		 * @brief 规划区域扫描轨迹的核心实现
		 */
		SingleTaskPlanningResult ScanAreaTaskPlanner::planScanAreaTrajectory(
			const SingleTaskPlanningRequest& request,
			const NSMission::ScanAreaTaskParams& params,
			const NSUav::UavState& start_state) {

			SingleTaskPlanningResult result;
			result.success = false;
			result.sub_task_id = request.assignment.sub_task_id;
			result.uav_id = request.assignment.assigned_uav_id;

			try {
				LOG_DEBUG("[ScanAreaTaskPlanner] 开始规划扫描轨迹，边界点数: {}", params.area_boundary.size());

				// === 第1步：验证扫描参数 ===
				if (params.area_boundary.size() < 3) {
					result.message = "扫描区域边界点数(" + std::to_string(params.area_boundary.size()) + ")少于3个";
					LOG_ERROR("[ScanAreaTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				if (params.strip_width <= NSCore::Constants::GEOMETRY_EPSILON) {
					result.message = "条带宽度(" + std::to_string(params.strip_width) + ")必须为正值";
					LOG_ERROR("[ScanAreaTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				if (params.overlap_ratio < 0.0 || params.overlap_ratio >= 1.0) {
					result.message = "重叠率(" + std::to_string(params.overlap_ratio) + ")必须在[0,1)范围内";
					LOG_ERROR("[ScanAreaTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第2步：验证边界点共面性 ===
				LOG_DEBUG("[ScanAreaTaskPlanner] 检查边界点共面性");
				double max_deviation = 0.0;
				if (!GeometryManager::checkPointsCoplanar(params.area_boundary, max_deviation)) {
					result.message = "边界点不共面，最大偏差:" + std::to_string(max_deviation) + "米";
					LOG_ERROR("[ScanAreaTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}
				LOG_DEBUG("[ScanAreaTaskPlanner] 边界点共面性检查通过，偏差:{:.4f}米", max_deviation);

				// === 第3步：生成扫描路径 ===
				LOG_DEBUG("[ScanAreaTaskPlanner] 生成扫描路径");
				double height_above_plane = params.scan_height_above_plane.value_or(0.0);

				std::vector<NSCore::WGS84Point> scan_path_wgs84 = GeometryManager::generateScanPath(
					params.area_boundary,
					params.strip_width,
					params.overlap_ratio,
					params.scan_angle_deg,
					height_above_plane
				);

				if (scan_path_wgs84.size() < 2) {
					result.message = "生成的扫描路径点数不足";
					LOG_ERROR("[ScanAreaTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第4步：构建完整几何路径 ===
				std::vector<NSCore::WGS84Point> geometric_path;
				geometric_path.reserve(scan_path_wgs84.size() + 1);

				// 添加起始点
				geometric_path.push_back(start_state.position);

				// 添加扫描路径点
				geometric_path.insert(geometric_path.end(), scan_path_wgs84.begin(), scan_path_wgs84.end());

				LOG_DEBUG("[ScanAreaTaskPlanner] 构建几何路径完成，总点数: {}", geometric_path.size());

				// === 第5步：确定飞行速度（使用公共方法） ===
				double flight_speed = default_scan_speed_;

				// 校验和调整飞行速度
				double adjusted_speed;
				if (!validateAndAdjustFlightSpeed(request.uav, start_state, flight_speed, adjusted_speed, result)) {
					return result;
				}
				flight_speed = adjusted_speed;

				LOG_DEBUG("[ScanAreaTaskPlanner] 使用飞行速度: {:.2f} m/s", flight_speed);

				// === 第6步：生成轨迹 ===
				Trajectory trajectory;
				if (!smoothAndTimeParameterize(geometric_path, request.uav, start_state,
					flight_speed, trajectory, &result)) {
					result.message = "轨迹生成失败";
					LOG_ERROR("[ScanAreaTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第7步：应用避障约束（使用公共方法） ===
				if (!applyAvoidanceConstraints(trajectory, request.avoidance_constraints, result)) {
					result.message = "应用避障约束失败";
					LOG_ERROR("[ScanAreaTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第8步：验证轨迹（使用公共方法） ===
				if (!validateGeneratedTrajectory(trajectory, result)) {
					return result;
				}

				// === 第9步：设置结果 ===
				result.trajectory = std::move(trajectory);
				result.success = true;
				result.message = "区域扫描规划成功";

				LOG_INFO("[ScanAreaTaskPlanner] 子任务 [{}] 规划成功，轨迹段数: {}",
					result.sub_task_id, result.trajectory.size());

			} catch (const std::exception& e) {
				result.success = false;
				result.message = "规划异常: " + std::string(e.what());
				LOG_ERROR("[ScanAreaTaskPlanner] 子任务 [{}] 规划异常: {}", result.sub_task_id, e.what());
			}

			return result;
		}



	} // namespace NSPlanning
} // namespace NSDrones