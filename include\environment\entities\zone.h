// include/environment/zone.h
#pragma once

#include "core/entity_object.h"
#include "core/geometry/ishape.h"  // 新形状系统
#include "core/types.h"
#include "params/parameters.h"
#include <memory>
#include <vector>
#include <string>
#include <shared_mutex>
#include <nlohmann/json.hpp>

// --- 前向声明 ---
namespace NSDrones {
	namespace NSEnvironment { class Environment; }
	namespace NSCore { class IMovementStrategy; }
}

namespace NSDrones {
	namespace NSEnvironment {

		// 引入NSCore命名空间中的类型
		using namespace ::NSDrones::NSCore;
		using namespace ::NSDrones::NSUtils;

		/**
		 * @class Zone
		 * @brief 表示环境中的一个具有特定类型和几何形状的区域。
		 *        继承自 EntityObject，其动态性由是否关联了 IMovementStrategy 决定。
		 */
		class Zone : public EntityObject {
		public:
			/**
			 * @brief 构造函数。
			 * @param id 区域的唯一标识符。
			 * @param object_type_key 用于参数系统的对象类型键 (例如 "ZoneNoFly", "ZoneMissionArea")。
			 * @param name 区域的名称。
			 * @param initial_state 区域的初始状态 (如果区域是动态的，包含位置/姿态)。
			 * @param strategy (可选) 初始的移动策略。默认为 nullptr (静态)。
			 */
			Zone(ObjectID id,
				const std::string& object_type_key,
				const std::string& name,
				const EntityState& initial_state,
				std::shared_ptr<IMovementStrategy> strategy = nullptr);

			~Zone() = default;

			// 禁止拷贝和移动
			Zone(const Zone&) = delete;
			Zone& operator=(const Zone&) = delete;
			Zone(Zone&&) = delete;
			Zone& operator=(Zone&&) = delete;

			// --- 核心属性访问 ---
			ZoneType getType() const { return zone_type_; }
			bool is3D() const { return is_3d_; }

			// --- 重写 EntityObject 几何方法 ---
			/**
			 * @brief 获取区域当前的轴对齐包围盒 (AABB)。
			 *        重写 EntityObject 的方法。
			 * @return 对象的 AABB。如果无形状或无效状态，返回无效 AABB。
			 */
			virtual BoundingBox getBoundingBox() const override;

			// --- 区域特定的几何检查接口 (使用 EntityObject 的状态和环境引用 env_) ---
			/**
			 * @brief 检查点是否在区域内部。
			 * @param ecef_point 要检查的点 (ECEF坐标)。
			 * @param tolerance (可选) 容差。
			 * @return 如果点在内部返回 true。
			 */
			virtual bool isInside(const EcefPoint& ecef_point, double tolerance = Constants::GEOMETRY_EPSILON) const;

			/**
			 * @brief 检查点是否在区域内部（别名方法，向后兼容）。
			 * @param ecef_point 要检查的点 (ECEF坐标)。
			 * @return 如果点在内部返回 true。
			 */
			bool containsPoint(const EcefPoint& ecef_point) const { return isInside(ecef_point); }

			/**
			 * @brief 检查线段是否与区域相交。
			 * @param ecef_p1 线段起点 (ECEF坐标)。
			 * @param ecef_p2 线段终点 (ECEF坐标)。
			 * @param tolerance (可选) 容差。
			 * @return 如果线段与区域相交返回 true。
			 */
			virtual bool intersects(const EcefPoint& ecef_p1, const EcefPoint& ecef_p2, double tolerance = Constants::GEOMETRY_EPSILON) const;

			// --- 外扩/安全裕度相关 (可以保留或调整) ---
			virtual void updateExpandedShape(double margin);
			bool hasValidExpandedShape() const;
			double getExpandedMargin() const;
			virtual bool isInsideExpanded(const EcefPoint& ecef_point, double tolerance = Constants::GEOMETRY_EPSILON) const;
			virtual bool intersectsExpanded(const EcefPoint& ecef_p1, const EcefPoint& ecef_p2, double tolerance = Constants::GEOMETRY_EPSILON) const;

			// --- 子区域管理 (线程安全) ---
			void addSubZone(std::shared_ptr<Zone> sub_zone); // 子区域仍然是 Zone 类型
			bool removeSubZone(const ObjectID& sub_zone_id);
			std::vector<std::shared_ptr<Zone>> getSubZones() const;

			// --- 重写 EntityObject 方法 ---
			/**
			 * @brief 初始化区域对象。
			 *        由工厂函数调用。
			 * @param final_params Config 准备的参数集。
			 * @param raw_instance_json_config 原始 JSON 配置。
			 * @return 如果成功返回 true。
			 */
			bool initialize(
				std::shared_ptr<NSParams::ParamValues> final_params,
				const nlohmann::json& raw_instance_json_config
			) override;

			/** @brief 返回 Zone 类的固定名称标签。 */
			std::string getClassName() const override { return "EntityObject.Zone"; }

		private:
			ZoneType zone_type_;
			bool is_3d_ = false;
			double expanded_margin_ = 0.0;
			ConstIShapePtr expanded_shape_ = nullptr;
			bool expanded_shape_valid_ = false;
			std::vector<std::shared_ptr<Zone>> sub_zones_;
			mutable std::shared_mutex sub_zones_mutex_;
		};

		using ZonePtr = std::shared_ptr<Zone>;
		using ConstZonePtr = std::shared_ptr<const Zone>;

	} // namespace NSEnvironment
} // namespace NSDrones