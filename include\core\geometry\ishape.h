#pragma once

#include <memory>
#include <string>
#include <vector>
#include <fcl/fcl.h>
#include <fcl/geometry/shape/box.h>
#include <fcl/geometry/shape/sphere.h>
#include <fcl/geometry/shape/cylinder.h>
#include <fcl/geometry/shape/cone.h>
#include <fcl/geometry/shape/capsule.h>
#include <fcl/geometry/shape/ellipsoid.h>
#include <fcl/geometry/shape/convex.h>
#include <fcl/geometry/bvh/BVH_model.h>
#include <nlohmann/json.hpp>

namespace NSDrones {
	namespace NSCore {

		/**
		 * @brief 几何形状类型枚举
		 */
		enum class ShapeType {
			// 基础几何元素
			POINT,         // 点
			LINE,          // 线段
			PLANE,         // 平面
			POLYGON,       // 多边形

			// 3D基础形状
			BOX,           // 长方体
			SPHERE,        // 球体
			CYLINDER,      // 圆柱体
			CONE,          // 圆锥体
			CAPSULE,       // 胶囊体
			ELLIPSOID,     // 椭球体

			// 复杂形状
			CONVEX_HULL,   // 凸包
			MESH,          // 三角网格
			COMPOUND,      // 复合形状

			// 特殊类型
			UNKNOWN        // 未知或无效形状
		};

		/**
		 * @brief 几何形状基础接口
		 *
		 * 基于FCL构建的现代化几何形状系统，提供：
		 * - 高性能的几何计算
		 * - 统一的碰撞检测接口
		 * - 灵活的形状组合
		 * - 完整的序列化支持
		 */
		class IShape {
		public:
			virtual ~IShape() = default;

			/**
			 * @brief 获取形状类型
			 */
			virtual ShapeType getType() const = 0;

			/**
			 * @brief 获取FCL几何对象
			 * @return FCL几何对象的共享指针
			 */
			virtual std::shared_ptr<fcl::CollisionGeometryd> getFCLGeometry() const = 0;

			/**
			 * @brief 计算在给定变换下的轴对齐包围盒
			 * @param transform 变换矩阵
			 * @return AABB包围盒
			 */
			virtual fcl::AABBd getAABB(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const = 0;

			/**
			 * @brief 计算形状的体积
			 * @return 体积值
			 */
			virtual double getVolume() const = 0;

			/**
			 * @brief 计算形状的表面积
			 * @return 表面积值
			 */
			virtual double getSurfaceArea() const = 0;

			/**
			 * @brief 获取形状的质心（相对于局部坐标系）
			 * @return 质心坐标
			 */
			virtual fcl::Vector3d getCentroid() const = 0;

			/**
			 * @brief 计算惯性张量
			 * @param mass 质量
			 * @return 3x3惯性张量矩阵
			 */
			virtual fcl::Matrix3d getInertiaMatrix(double mass) const = 0;

			/**
			 * @brief 克隆形状
			 * @return 新的形状实例
			 */
			virtual std::unique_ptr<IShape> clone() const = 0;

			/**
			 * @brief 序列化为JSON
			 * @return JSON对象
			 */
			virtual nlohmann::json serialize() const = 0;

			/**
			 * @brief 从JSON反序列化
			 * @param json JSON对象
			 * @return 是否成功
			 */
			virtual bool deserialize(const nlohmann::json& json) = 0;

			/**
			 * @brief 获取形状的字符串描述
			 * @return 描述字符串
			 */
			virtual std::string toString() const = 0;

			/**
			 * @brief 检查点是否在形状内部
			 * @param point 查询点（局部坐标系）
			 * @return 是否在内部
			 */
			virtual bool containsPoint(const fcl::Vector3d& point) const = 0;

			/**
			 * @brief 计算到点的最短距离
			 * @param point 查询点（局部坐标系）
			 * @return 最短距离（负值表示在内部）
			 */
			virtual double distanceToPoint(const fcl::Vector3d& point) const = 0;

			/**
			 * @brief 获取形状的特征尺寸（用于LOD等）
			 * @return 特征尺寸
			 */
			virtual double getCharacteristicSize() const = 0;

			/**
			 * @brief 获取形状的维度（0=点，1=线，2=面，3=体）
			 * @return 几何维度
			 */
			virtual int getDimension() const = 0;

			/**
			 * @brief 检查形状是否为基础几何元素（点、线、面）
			 * @return 如果是基础几何元素返回true
			 */
			virtual bool isPrimitive() const {
				ShapeType type = getType();
				return type == ShapeType::POINT ||
					type == ShapeType::LINE ||
					type == ShapeType::PLANE ||
					type == ShapeType::POLYGON;
			}

			/**
			 * @brief 检查形状是否为3D实体
			 * @return 如果是3D实体返回true
			 */
			virtual bool isSolid() const {
				return getDimension() == 3;
			}

			// 静态工厂方法
			/**
			 * @brief 从JSON创建形状
			 * @param json JSON对象
			 * @return 形状实例
			 */
			static std::unique_ptr<IShape> createFromJson(const nlohmann::json& json);

			/**
			 * @brief 从类型名称创建空形状
			 * @param type_name 类型名称
			 * @return 形状实例
			 */
			static std::unique_ptr<IShape> createByTypeName(const std::string& type_name);

			// 基础几何元素创建方法
			/**
			 * @brief 创建点形状
			 * @param position 点的位置
			 * @return 点形状实例
			 */
			static std::unique_ptr<IShape> createPoint(const fcl::Vector3d& position = fcl::Vector3d::Zero());

			/**
			 * @brief 创建线段形状
			 * @param start 起点
			 * @param end 终点
			 * @return 线段形状实例
			 */
			static std::unique_ptr<IShape> createLine(const fcl::Vector3d& start, const fcl::Vector3d& end);

			/**
			 * @brief 创建平面形状
			 * @param normal 平面法向量
			 * @param distance 到原点的距离
			 * @return 平面形状实例
			 */
			static std::unique_ptr<IShape> createPlane(const fcl::Vector3d& normal, double distance);

			/**
			 * @brief 创建多边形形状
			 * @param vertices 顶点列表
			 * @return 多边形形状实例
			 */
			static std::unique_ptr<IShape> createPolygon(const std::vector<fcl::Vector3d>& vertices);
		};

		// 注意：ShapeType和字符串的转换使用magic_enum在enum_utils.h中实现

		/**
		 * @brief 创建FCL变换矩阵
		 * @param translation 平移向量
		 * @param rotation 旋转矩阵
		 * @return FCL变换矩阵
		 */
		fcl::Transform3d createTransform(const fcl::Vector3d& translation = fcl::Vector3d::Zero(),
			const fcl::Matrix3d& rotation = fcl::Matrix3d::Identity());

		/**
		 * @brief 从欧拉角创建FCL变换矩阵
		 * @param translation 平移向量
		 * @param roll 绕X轴旋转角度（弧度）
		 * @param pitch 绕Y轴旋转角度（弧度）
		 * @param yaw 绕Z轴旋转角度（弧度）
		 * @return FCL变换矩阵
		 */
		fcl::Transform3d createTransformFromEuler(const fcl::Vector3d& translation,
			double roll, double pitch, double yaw);

		/**
		 * @brief 从四元数创建FCL变换矩阵
		 * @param translation 平移向量
		 * @param quaternion 四元数 [w, x, y, z]
		 * @return FCL变换矩阵
		 */
		fcl::Transform3d createTransformFromQuaternion(const fcl::Vector3d& translation,
			const Eigen::Vector4d& quaternion);

		// --- 形状类型别名 ---
		using IShapePtr = std::shared_ptr<IShape>;
		using ConstIShapePtr = std::shared_ptr<const IShape>;
	} // namespace NSCore
} // namespace NSDrones