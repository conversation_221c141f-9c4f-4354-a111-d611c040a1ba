#include "core/geometry/shapes/ellipsoid_shape.h"
#include "utils/enum_utils.h"
#include <stdexcept>
#include <sstream>
#include <cmath>
#include <algorithm>
#include <random>

namespace NSDrones {
namespace NSCore {

    EllipsoidShape::EllipsoidShape(double radius_x, double radius_y, double radius_z) 
        : radius_x_(radius_x), radius_y_(radius_y), radius_z_(radius_z), fcl_ellipsoid_(nullptr) {
        validateRadii();
    }

    EllipsoidShape::EllipsoidShape() : EllipsoidShape(1.0, 1.0, 1.0) {
    }

    EllipsoidShape::EllipsoidShape(const EllipsoidShape& other)
        : radius_x_(other.radius_x_), radius_y_(other.radius_y_), radius_z_(other.radius_z_), fcl_ellipsoid_(nullptr) {
    }

    EllipsoidShape& EllipsoidShape::operator=(const EllipsoidShape& other) {
        if (this != &other) {
            radius_x_ = other.radius_x_;
            radius_y_ = other.radius_y_;
            radius_z_ = other.radius_z_;
            fcl_ellipsoid_.reset();  // 重置FCL对象，延迟重新创建
        }
        return *this;
    }

    std::shared_ptr<fcl::CollisionGeometryd> EllipsoidShape::getFCLGeometry() const {
        ensureFCLObject();
        return fcl_ellipsoid_;
    }

    fcl::AABBd EllipsoidShape::getAABB(const fcl::Transform3d& transform) const {
        ensureFCLObject();
        fcl::AABBd aabb;
        fcl::computeBV(*fcl_ellipsoid_, transform, aabb);
        return aabb;
    }

    double EllipsoidShape::getVolume() const {
        return (4.0 / 3.0) * M_PI * radius_x_ * radius_y_ * radius_z_;
    }

    double EllipsoidShape::getSurfaceArea() const {
        // 椭球表面积的近似公式（Knud Thomsen公式）
        double a = radius_x_;
        double b = radius_y_;
        double c = radius_z_;
        
        double p = 1.6075;  // 近似参数
        
        double ap = std::pow(a, p);
        double bp = std::pow(b, p);
        double cp = std::pow(c, p);
        
        return 4.0 * M_PI * std::pow((ap * bp + ap * cp + bp * cp) / 3.0, 1.0 / p);
    }

    fcl::Vector3d EllipsoidShape::getCentroid() const {
        return fcl::Vector3d(0.0, 0.0, 0.0);  // 几何中心在原点
    }

    fcl::Matrix3d EllipsoidShape::getInertiaMatrix(double mass) const {
        // 椭球体的惯性张量（相对于质心）
        double rx2 = radius_x_ * radius_x_;
        double ry2 = radius_y_ * radius_y_;
        double rz2 = radius_z_ * radius_z_;
        
        fcl::Matrix3d inertia = fcl::Matrix3d::Zero();
        inertia(0, 0) = mass / 5.0 * (ry2 + rz2);  // Ixx
        inertia(1, 1) = mass / 5.0 * (rx2 + rz2);  // Iyy
        inertia(2, 2) = mass / 5.0 * (rx2 + ry2);  // Izz
        
        return inertia;
    }

    std::unique_ptr<IShape> EllipsoidShape::clone() const {
        return std::make_unique<EllipsoidShape>(*this);
    }

    nlohmann::json EllipsoidShape::serialize() const {
        nlohmann::json j;
        j["type"] = NSUtils::enumToString(getType());
        j["radius_x"] = radius_x_;
        j["radius_y"] = radius_y_;
        j["radius_z"] = radius_z_;
        return j;
    }

    bool EllipsoidShape::deserialize(const nlohmann::json& json) {
        try {
            std::string type_str = json.at("type").get<std::string>();
            auto type_opt = NSUtils::stringToEnum<ShapeType>(type_str, ShapeType::UNKNOWN);
            if (type_opt != ShapeType::ELLIPSOID) {
                return false;
            }
            
            radius_x_ = json.at("radius_x").get<double>();
            radius_y_ = json.at("radius_y").get<double>();
            radius_z_ = json.at("radius_z").get<double>();
            
            validateRadii();
            fcl_ellipsoid_.reset();  // 重置FCL对象
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    std::string EllipsoidShape::toString() const {
        std::ostringstream oss;
        oss << "EllipsoidShape(radius_x=" << radius_x_ << ", radius_y=" << radius_y_ 
            << ", radius_z=" << radius_z_ << ")";
        return oss.str();
    }

    bool EllipsoidShape::containsPoint(const fcl::Vector3d& point) const {
        // 椭球方程: (x/a)² + (y/b)² + (z/c)² <= 1
        double term_x = (point.x() / radius_x_) * (point.x() / radius_x_);
        double term_y = (point.y() / radius_y_) * (point.y() / radius_y_);
        double term_z = (point.z() / radius_z_) * (point.z() / radius_z_);
        
        return (term_x + term_y + term_z) <= 1.0;
    }

    double EllipsoidShape::distanceToPoint(const fcl::Vector3d& point) const {
        // 椭球到点的距离计算比较复杂，这里使用迭代方法的简化版本
        if (containsPoint(point)) {
            // 点在内部，计算到表面的最短距离（负值）
            // 使用梯度方法找到最近点
            fcl::Vector3d grad(2.0 * point.x() / (radius_x_ * radius_x_),
                              2.0 * point.y() / (radius_y_ * radius_y_),
                              2.0 * point.z() / (radius_z_ * radius_z_));
            
            double grad_norm = grad.norm();
            if (grad_norm > 1e-10) {
                fcl::Vector3d direction = grad / grad_norm;
                
                // 在梯度方向上找到椭球表面点
                double t = 1.0;  // 初始猜测
                for (int i = 0; i < 10; ++i) {  // 简单迭代
                    fcl::Vector3d surface_point = point + t * direction;
                    double ellipsoid_value = (surface_point.x() / radius_x_) * (surface_point.x() / radius_x_) +
                                           (surface_point.y() / radius_y_) * (surface_point.y() / radius_y_) +
                                           (surface_point.z() / radius_z_) * (surface_point.z() / radius_z_);
                    
                    if (std::abs(ellipsoid_value - 1.0) < 1e-6) break;
                    
                    t *= (ellipsoid_value > 1.0) ? 0.9 : 1.1;
                }
                
                return -t;
            }
            return 0.0;
        } else {
            // 点在外部，使用近似方法
            // 将椭球缩放为单位球，计算距离，然后缩放回来
            fcl::Vector3d scaled_point(point.x() / radius_x_, point.y() / radius_y_, point.z() / radius_z_);
            double scaled_distance = scaled_point.norm() - 1.0;
            
            // 近似缩放回原始空间
            double avg_radius = (radius_x_ + radius_y_ + radius_z_) / 3.0;
            return scaled_distance * avg_radius;
        }
    }

    double EllipsoidShape::getCharacteristicSize() const {
        return 2.0 * getMaxRadius();  // 使用最大直径作为特征尺寸
    }

    void EllipsoidShape::setRadii(double radius_x, double radius_y, double radius_z) {
        radius_x_ = radius_x;
        radius_y_ = radius_y;
        radius_z_ = radius_z;
        validateRadii();
        fcl_ellipsoid_.reset();  // 重置FCL对象
    }

    void EllipsoidShape::setRadii(const fcl::Vector3d& radii) {
        setRadii(radii.x(), radii.y(), radii.z());
    }

    bool EllipsoidShape::isSphere(double tolerance) const {
        double max_radius = getMaxRadius();
        double min_radius = getMinRadius();
        return (max_radius - min_radius) <= tolerance;
    }

    fcl::Vector3d EllipsoidShape::getPointOnEllipsoid(double theta, double phi,
                                                     const fcl::Transform3d& transform) const {
        // 椭球参数方程
        // theta: 极角 (0 到 π)
        // phi: 方位角 (0 到 2π)
        double x = radius_x_ * std::sin(theta) * std::cos(phi);
        double y = radius_y_ * std::sin(theta) * std::sin(phi);
        double z = radius_z_ * std::cos(theta);
        
        fcl::Vector3d point(x, y, z);
        return transform * point;
    }

    std::vector<fcl::Vector3d> EllipsoidShape::generateUniformPoints(size_t num_points,
                                                                    const fcl::Transform3d& transform) const {
        std::vector<fcl::Vector3d> points;
        points.reserve(num_points);
        
        // 使用Fibonacci螺旋算法生成均匀分布的点
        double golden_ratio = (1.0 + std::sqrt(5.0)) / 2.0;
        
        for (size_t i = 0; i < num_points; ++i) {
            double theta = 2.0 * M_PI * i / golden_ratio;
            double phi = std::acos(1.0 - 2.0 * (i + 0.5) / num_points);
            
            points.push_back(getPointOnEllipsoid(phi, theta, transform));
        }
        
        return points;
    }

    fcl::Vector3d EllipsoidShape::getSurfaceNormal(const fcl::Vector3d& point,
                                                  const fcl::Transform3d& transform) const {
        // 将点转换到局部坐标系
        fcl::Vector3d local_point = transform.inverse() * point;
        
        // 椭球面法向量 = 梯度 / |梯度|
        fcl::Vector3d gradient(2.0 * local_point.x() / (radius_x_ * radius_x_),
                              2.0 * local_point.y() / (radius_y_ * radius_y_),
                              2.0 * local_point.z() / (radius_z_ * radius_z_));
        
        fcl::Vector3d normal = gradient.normalized();
        
        // 转换回全局坐标系（只应用旋转）
        return transform.rotation() * normal;
    }

    double EllipsoidShape::getMaxRadius() const {
        return std::max({radius_x_, radius_y_, radius_z_});
    }

    double EllipsoidShape::getMinRadius() const {
        return std::min({radius_x_, radius_y_, radius_z_});
    }

    double EllipsoidShape::getEccentricity() const {
        double max_r = getMaxRadius();
        double min_r = getMinRadius();
        
        if (max_r < 1e-10) return 0.0;
        
        return std::sqrt(1.0 - (min_r * min_r) / (max_r * max_r));
    }

    std::unique_ptr<EllipsoidShape> EllipsoidShape::createUnitEllipsoid() {
        return std::make_unique<EllipsoidShape>(1.0, 1.0, 1.0);
    }

    std::unique_ptr<EllipsoidShape> EllipsoidShape::createSphere(double radius) {
        return std::make_unique<EllipsoidShape>(radius, radius, radius);
    }

    std::unique_ptr<EllipsoidShape> EllipsoidShape::createOblate(double equatorial_radius, double polar_radius) {
        return std::make_unique<EllipsoidShape>(equatorial_radius, equatorial_radius, polar_radius);
    }

    std::unique_ptr<EllipsoidShape> EllipsoidShape::createProlate(double major_radius, double minor_radius) {
        return std::make_unique<EllipsoidShape>(minor_radius, minor_radius, major_radius);
    }

    void EllipsoidShape::ensureFCLObject() const {
        if (!fcl_ellipsoid_) {
            fcl_ellipsoid_ = std::make_shared<fcl::Ellipsoidd>(radius_x_, radius_y_, radius_z_);
        }
    }

    void EllipsoidShape::validateRadii() const {
        if (radius_x_ <= 0.0 || radius_y_ <= 0.0 || radius_z_ <= 0.0) {
            throw std::invalid_argument("EllipsoidShape: 所有半径必须为正数");
        }
        if (!std::isfinite(radius_x_) || !std::isfinite(radius_y_) || !std::isfinite(radius_z_)) {
            throw std::invalid_argument("EllipsoidShape: 半径必须为有限数值");
        }
    }

} // namespace NSCore
} // namespace NSDrones
