// include/params/param_defs.h
#pragma once

#include "params/parameters.h" 
#include "core/types.h"     
#include <string>
#include <vector>
#include <memory>

namespace NSDrones {
	namespace NSParams {
		using namespace ::NSDrones::NSUtils;
		using namespace ::NSDrones::NSCore;

		/**
		 * @struct IParams
		 * @brief 所有具体参数结构体的概念基类 (用于统一接口，如果需要)。
		 *        定义了通用的加载接口。
		 */
		struct IParamsStruct {
			virtual ~IParamsStruct() = default;
			/**
			 * @brief 从 ParamValues 加载参数值到结构体成员。
			 * @param params 包含参数值的 ParamValues 对象。
			 * @return 如果加载成功且值有效则返回 true。
			 */
			virtual bool loadFromParamValues(const ParamValues& params) = 0;
		};
		using IParamsPtr = std::shared_ptr<IParamsStruct>;
		using ConstIParamsPtr = std::shared_ptr<const IParamsStruct>;

		/**
		 * @struct UAVPhysicalProperties
		 * @brief 无人机物理属性参数。
		 */
		struct UAVPhysicalProperties : public IParamsStruct {
			double size_length = 1.0;          // 长度 (米)
			double size_width = 1.0;           // 宽度 (米)
			double size_height = 0.5;          // 高度 (米)
			double empty_weight = 5.0;         // 空重 (千克)
			double max_payload_weight = 2.0;   // 最大载荷 (千克)

			// 实现加载接口
			bool loadFromParamValues(const ParamValues& params) override;
		};

		/**
		 * @struct IDynamicParams
		 * @brief 动力学模型参数的基类。
		 *        包含所有动力学模型通用的基础参数。
		 */
		struct IDynamicParams : public IParamsStruct {
			double maxHVel = 15.0;      // 最大水平速度 (m/s)
			double maxVVelUp = 5.0;     // 最大上升速度 (m/s)
			double maxVVelDown = 3.0;   // 最大下降速度 (m/s)
			double maxHAcc = 3.0;       // 最大水平加速度 (m/s^2)
			double maxVAcc = 2.0;       // 最大垂直加速度 (m/s^2)
			double maxAlt = 1000.0;     // 最大高度 (米)
			double maxHDecel = 3.0;     // 最大水平减速度 (m/s^2, 默认=HAcc)
			double maxVDecel = 2.0;     // 最大垂直减速度 (m/s^2, 默认=VAcc)
			double minOpSpeed = 0.0;    // 最小运行速度 (m/s, 默认为0)
			double maxYawRate_dps = 90.0; // 新增: 最大偏航角速度 (度/秒)

			// 实现加载接口
			bool loadFromParamValues(const ParamValues& params) override;
		};

		/** @brief 多旋翼动力学参数 */
		struct MultirotorDynamicsParams : public IDynamicParams {
			// 可以添加多旋翼特有的参数，例如:
			// double max_attitude_angle_deg = 45.0;
			// double drag_coeff_k = 0.1;

			// 覆写加载接口以处理特定参数和覆盖
			bool loadFromParamValues(const ParamValues& params) override;
		};

		/** @brief 固定翼动力学参数 */
		struct FixedWingDynamicsParams : public IDynamicParams {
			double maxClimbRate = 4.0;   // 最大爬升率 (m/s)
			double maxSinkRate = 3.0;    // 最大下降率 (m/s)
			double maxBankAngle = 30.0;  // 最大倾斜角 (度)
			double gravity = Constants::GRAVITY; // 重力加速度 (m/s^2)
			// double drag_coeff_k = 0.05; // 简化阻力系数

			// 覆写加载接口
			bool loadFromParamValues(const ParamValues& params) override;
		};

		/** @brief VTOL 动力学参数 */
		struct VtolDynamicsParams : public IDynamicParams {
			double transitionSpeed = 15.0; // 转换速度 (m/s)

			std::shared_ptr<MultirotorDynamicsParams> hover_params = std::make_shared<MultirotorDynamicsParams>();
			std::shared_ptr<FixedWingDynamicsParams> fw_params = std::make_shared<FixedWingDynamicsParams>();

			// 覆写加载接口 (只加载顶层和组合参数)
			bool loadFromParamValues(const ParamValues& params) override;
		};


		/**
		 * @struct IEnergyParams
		 * @brief 能量模型参数的基类。
		 *        包含所有能量模型通用的基础参数。
		 */
		struct IEnergyParams : public IParamsStruct {
			double max_energy_capacity = 100.0;   // 最大能量容量 (Wh)
			double min_safe_energy_fraction = 0.1; // 最低安全能量比例 (0-1)
			double charging_efficiency = 0.9;      // 充电效率 (0-1)
			double discharging_efficiency = 0.95;  // 放电效率 (0-1)
			double baseline_power = 30.0;       ///< 基础系统/航电功耗 (W), 从 energy.base.baseline_power 加载

			virtual ~IEnergyParams() = default;
			virtual bool loadFromParamValues(const ParamValues& params) override;
		};

		/** @brief 多旋翼能量参数 */
		struct MultirotorEnergyParams : public IEnergyParams {
			// 多旋翼特定参数
			double hover_power = 150.0;            // 悬停功率 (W)
			double vertical_power_factor = 50.0;   // 垂直功率因子 (W/(m/s))
			double horizontal_power_factor = 10.0; // 水平功率因子 (W/(m/s))
			double baseline_power = 5.0;           // 基线功率 (W)
			double endurance_est_max_v_speed = 1.0; // 续航估算使用的垂直速度 (m/s)

			// 覆写加载接口
			bool loadFromParamValues(const ParamValues& params) override;
		};

		/** @brief 固定翼能量参数 */
		struct FixedWingEnergyParams : public IEnergyParams {
			// 固定翼特定参数
			double mass = 1.5;                   // 飞行质量 (kg) - 用于能量模型
			double wing_area = 0.5;              // 翼面积 (m^2)
			double drag_coeff_zero_lift = 0.02;  // 零升阻力系数 (Cd0)
			double oswald_efficiency = 0.8;      // 奥斯瓦尔德效率因子 (e, 0-1)
			double prop_efficiency = 0.75;       // 螺旋桨效率 (0-1)
			double aspect_ratio = 8.0;           // 展弦比 (AR)
			double baseline_power = 5.0;         // 基线功率 (W)
			double air_density = Constants::AIR_DENSITY_SEA_LEVEL_ISA; // 空气密度 (kg/m^3)
			double gravity = Constants::GRAVITY; // 重力加速度 (m/s^2)
			double cruise_speed_est = 15.0;      // 用于续航估算的速度 (m/s)

			// 覆写加载接口
			bool loadFromParamValues(const ParamValues& params) override;
		};

		/** @brief VTOL 能量参数 */
		struct VtolEnergyParams : public IEnergyParams {
			// VTOL 特有顶层参数
			double transition_avg_power = 300.0;   // 过渡阶段平均功率 (W)
			double transition_duration_estimation = 10.0; // 过渡阶段估算时间 (s)

			std::shared_ptr<MultirotorEnergyParams> hover_params = std::make_shared<MultirotorEnergyParams>();
			std::shared_ptr<FixedWingEnergyParams> fw_params = std::make_shared<FixedWingEnergyParams>();

			// 覆写加载接口 (只加载顶层和组合参数)
			bool loadFromParamValues(const ParamValues& params) override;
		};

	} // namespace NSParams
} // namespace NSDrones