// include/algorithm/algorithm_object.h

#pragma once
#include "core/base_object.h"
#include <string>
#include <memory>

namespace NSDrones {
	namespace NSAlgorithm {
		using namespace ::NSDrones::NSUtils;
		using namespace ::NSDrones::NSCore;
		/**
		 * @brief 算法对象基类
		 *
		 * 算法对象类是所有算法对象的基类。
		 * 它定义了算法对象的通用接口和行为。
		 *
		 */
		class AlgorithmObject : public BaseObject {
		public:
			using Ptr = std::shared_ptr<AlgorithmObject>;
			using ConstPtr = std::shared_ptr<const AlgorithmObject>;

			/**
		 * @brief 构造函数。
		 * @param id 对象的唯一 ID。
		 * @param object_type_key 对象的类型字符串，用于参数系统和工厂。
		 * @param env_ref 对所属环境的引用。
		 * @param name 对象的可读名称。
		 * @param initial_state 对象的初始状态 (位置、姿态等)。
		 * @throws DroneException 如果 id 无效。
		 */
			explicit AlgorithmObject(ObjectID id,
				const std::string& object_type_key,
				const std::string& name, std::string version = "1.0.0") :
				BaseObject(id, object_type_key, name), version_(version) {}

			/** @brief 虚析构函数，允许派生类正确清理。*/
			virtual ~AlgorithmObject() = default;

			// --- 禁止拷贝和移动 ---
			AlgorithmObject(const AlgorithmObject&) = delete;
			AlgorithmObject& operator=(const AlgorithmObject&) = delete;
			AlgorithmObject(AlgorithmObject&&) = delete;
			AlgorithmObject& operator=(AlgorithmObject&&) = delete;

			// --- 算法对象的通用接口 ---
			/**
			 * @brief 对象初始化入口点。
			 *        此方法应由对象的工厂函数或创建逻辑在对象创建后调用。
			 *        它负责使用 Config 系统准备好的、包含默认值和实例特定值的 ParamValues 对象
			 *        来完成对象的最终设置，包括参数赋值、形状创建等。
			 * @param params 由 Config 系统准备好的、包含最终参数值的 ParamValues 对象的共享指针。
			 * @return 如果初始化成功，返回 true。
			 */
			virtual bool initialize(std::shared_ptr<NSParams::ParamValues> final_params, const nlohmann::json& raw_instance_json_config) {
				return BaseObject::initialize(final_params, raw_instance_json_config);
			}

			virtual std::string getClassName() const override { return "AlgorithmObject"; }
			virtual std::string getVersion() const { return version_; }
		protected:
			std::string version_;
		};

		using AlgorithmObjectPtr = std::shared_ptr<AlgorithmObject>;
		using ConstAlgorithmObjectPtr = std::shared_ptr<const AlgorithmObject>;
	} // namespace NSAlgorithm
} // namespace NSDrones

using namespace NSDrones::NSAlgorithm;