// src/algorithm/trajectory_optimizer/bspline_trajectory_optimizer.cpp
#include "algorithm/trajectory_optimizer/trajectory_optimizer.h"
#include "core/types.h"
#include "environment/environment.h"
#include "mission/task_strategies.h"
#include "utils/logging.h"
#include "utils/stopwatch.h"
#include "utils/coordinate_converter.h"
#include "planning/planning_types.h"
#include "uav/idynamic_model.h"
#include "uav/uav_types.h"
#include <vector>
#include <cmath>
#include <stdexcept>
#include <numeric>
#include <limits>
#include <algorithm>
#include <map>
#include <string>
#include <Eigen/Core>
#include <Eigen/Geometry>
#include <type_traits>
#include <utility>
#include <cmath>
#include <functional>
#include <fstream>
#include <filesystem>

#include <tinysplinecxx.h>
#include <nlohmann/json.hpp>
using json = nlohmann::json;

// --- 匿名命名空间包含辅助结构和函数 ---
namespace {

	/**
	 * @brief 将 RouteSegment 转换为 tinyspline 使用的控制点格式。
	 */
	std::vector<double> routeSegmentToTinysplinePoints(const RouteSegment& segment) {
		std::vector<double> points;
		points.reserve(segment.size() * 3); // 每个点有 3 个维度
		for (const auto& state : segment) {
			// 将WGS84坐标转换为ECEF坐标用于样条计算
			EcefPoint ecef_pos = NSDrones::NSUtils::CoordinateConverter::wgs84ToECEF(state.position);
			points.push_back(ecef_pos.x());
			points.push_back(ecef_pos.y());
			points.push_back(ecef_pos.z());
		}
		return points;
	}

	/**
	 * @brief 安全地创建B样条，包含多种降级策略
	 */
	tinyspline::BSpline createBSplineSafely(const std::vector<double>& points,
											size_t dimension,
											int preferred_degree,
											bool& success) {
		success = false;
		size_t num_points = points.size() / dimension;

		if (num_points < 2) {
			LOG_ERROR("[TinysplineHelper] 点数不足 ({})，无法创建B样条", num_points);
			return tinyspline::BSpline();
		}

		// 策略1：尝试使用三次自然样条插值
		if (num_points >= 4 && preferred_degree >= 3) {
			try {
				auto spline = tinyspline::BSpline::interpolateCubicNatural(points, dimension);
				success = true;
				LOG_DEBUG("[TinysplineHelper] 成功创建三次自然样条");
				return spline;
			} catch (const std::exception& e) {
				LOG_DEBUG("[TinysplineHelper] 三次自然样条创建失败: {}", e.what());
			}
		}

		// 策略2：尝试使用控制点方法，逐步降低阶数
		for (int degree = std::min(preferred_degree, static_cast<int>(num_points - 1)); degree >= 1; --degree) {
			try {
				tinyspline::BSpline spline(num_points, dimension, degree);
				spline.setControlPoints(points);
				success = true;
				LOG_DEBUG("[TinysplineHelper] 成功创建 {} 阶控制点样条", degree);
				return spline;
			} catch (const std::exception& e) {
				LOG_DEBUG("[TinysplineHelper] {} 阶样条创建失败: {}", degree, e.what());
			}
		}

		LOG_ERROR("[TinysplineHelper] 所有B样条创建策略均失败");
		return tinyspline::BSpline();
	}

	/**
	 * @brief 安全地计算样条导数
	 */
	bool computeSplineDerivatives(const tinyspline::BSpline& spline,
								 tinyspline::BSpline& deriv1,
								 tinyspline::BSpline& deriv2,
								 tinyspline::BSpline& deriv3) {
		try {
			deriv1 = spline.derive();
			deriv2 = deriv1.derive();
			deriv3 = deriv2.derive();
			return true;
		} catch (const std::exception& e) {
			LOG_ERROR("[TinysplineHelper] 计算样条导数失败: {}", e.what());
			return false;
		}
	}

	/**
	 * @brief 验证样条的有效性
	 */
	bool validateSpline(const tinyspline::BSpline& spline) {
		if (spline.numControlPoints() == 0) {
			LOG_DEBUG("[TinysplineHelper] 样条无效：控制点数为0");
			return false;
		}

		if (spline.dimension() != 3) {
			LOG_DEBUG("[TinysplineHelper] 样条无效：维度不为3");
			return false;
		}

		// 尝试评估样条在几个点
		try {
			spline.eval(0.0);
			spline.eval(0.5);
			spline.eval(1.0);
			return true;
		} catch (const std::exception& e) {
			LOG_DEBUG("[TinysplineHelper] 样条评估失败: {}", e.what());
			return false;
		}
	}

	/**
	 * @brief 验证优化器配置参数的合理性
	 */
	bool validateOptimizerOptions(const NSDrones::NSAlgorithm::TrajectoryOptimizer::Options& options) {
		bool valid = true;

		// 检查基本参数
		if (options.bspline_degree < 1 || options.bspline_degree > 5) {
			LOG_WARN("[ConfigValidator] B样条阶数 ({}) 超出合理范围 [1, 5]", options.bspline_degree);
			valid = false;
		}

		if (options.output_path_points < 2) {
			LOG_WARN("[ConfigValidator] 输出路径点数 ({}) 少于最小值 2", options.output_path_points);
			valid = false;
		}

		// 检查权重参数（应为非负）
		if (options.smoothness_weight_pos < 0 || options.smoothness_weight_vel < 0 ||
			options.smoothness_weight_acc < 0 || options.smoothness_weight_jerk < 0) {
			LOG_WARN("[ConfigValidator] 平滑度权重包含负值");
			valid = false;
		}

		if (options.collision_weight < 0 || options.feasibility_weight_vel < 0 ||
			options.feasibility_weight_acc < 0 || options.feasibility_weight_turn < 0) {
			LOG_WARN("[ConfigValidator] 约束权重包含负值");
			valid = false;
		}

		// 检查采样参数
		if (options.smoothness_integration_samples < 1 || options.collision_check_samples < 1 ||
			options.feasibility_check_samples < 1) {
			LOG_WARN("[ConfigValidator] 采样点数包含无效值");
			valid = false;
		}

		// 检查优化器参数
		if (options.max_iterations < 1) {
			LOG_WARN("[ConfigValidator] 最大迭代次数 ({}) 无效", options.max_iterations);
			valid = false;
		}

		if (options.optimizer_learning_rate <= 0 || options.optimizer_learning_rate > 1.0) {
			LOG_WARN("[ConfigValidator] 学习率 ({:.6f}) 超出合理范围 (0, 1]", options.optimizer_learning_rate);
			valid = false;
		}

		if (options.gradient_epsilon <= 0) {
			LOG_WARN("[ConfigValidator] 梯度计算步长 ({:.2e}) 无效", options.gradient_epsilon);
			valid = false;
		}

		return valid;
	}

	/**
	 * @brief 将 tinyspline 返回的 std::vector<double> (点) 转换为 EcefPoint。
	 */
	EcefPoint tinysplineResultToEcefPoint(const std::vector<double>& result) {
		if (result.size() >= 3) {
			return EcefPoint(result[0], result[1], result[2]);
		}
		else {
			// 使用 NSDrones 命名空间
			LOG_ERROR("Tinyspline 结果维度 ({}) 不足以转换为 EcefPoint。", result.size());
			return EcefPoint(0.0, 0.0, 0.0);
		}
	}

	/**
	 * @brief 将 tinyspline 返回的 std::vector<double> (向量) 转换为 Vector3D。
	 */
	Vector3D tinysplineResultToVector3D(const std::vector<double>& result) {
		if (result.size() >= 3) {
			return Vector3D(result[0], result[1], result[2]);
		}
		else {
			// 使用 NSDrones 命名空间
			LOG_ERROR("Tinyspline 结果维度 ({}) 不足以转换为 Vector3D。", result.size());
			return Vector3D::Zero();
		}
	}

	// --- 成本计算相关 ---
	struct CostData {

		const tinyspline::BSpline* spline = nullptr;
		const tinyspline::BSpline* derivative1 = nullptr;
		const tinyspline::BSpline* derivative2 = nullptr;
		const tinyspline::BSpline* derivative3 = nullptr;

		// 使用完整命名空间路径避免 using 声明冲突
		const NSDrones::NSAlgorithm::TrajectoryOptimizer::Options* options = nullptr;
		const NSDrones::NSEnvironment::Environment* environment = nullptr;
		const NSDrones::NSUav::IDynamicModel* dynamics = nullptr;
		const NSDrones::NSMission::ITaskStrategyMap* strategies = nullptr;

		Time total_time = 1.0;

		// 成本项
		double smoothness_cost_pos = 0.0;
		double smoothness_cost_vel = 0.0;
		double smoothness_cost_acc = 0.0;
		double smoothness_cost_jerk = 0.0;
		double collision_cost = 0.0;
		double feasibility_cost_vel = 0.0;
		double feasibility_cost_acc = 0.0;
		double feasibility_cost_turn = 0.0;

		double total_cost = 0.0;
	};

	// 成本计算函数 (评估给定样条的成本)
	void calculateSplineCost(CostData& cost_data) {
		LOG_TRACE("进入成本计算函数 calculateSplineCost (优化版)... Time={:.3f}", cost_data.total_time);
		if (!cost_data.spline || !cost_data.derivative1 || !cost_data.derivative2 || !cost_data.derivative3 ||
			!cost_data.options || !cost_data.dynamics || !cost_data.environment) {
			LOG_ERROR("成本计算接收到无效的数据指针（包括环境）！设置成本为 INF。");
			cost_data.total_cost = Constants::INF;
			return;
		}

		const NSDrones::NSEnvironment::Environment& environment = *(cost_data.environment);
		const tinyspline::BSpline& spline = *(cost_data.spline);
		const tinyspline::BSpline& deriv1 = *(cost_data.derivative1);
		const tinyspline::BSpline& deriv2 = *(cost_data.derivative2);
		const tinyspline::BSpline& deriv3 = *(cost_data.derivative3);
		const auto& options = *(cost_data.options);
		const auto& dynamics = *(cost_data.dynamics);
		const Time total_time = std::max(Constants::TIME_EPSILON, cost_data.total_time);
		const double time_inv = 1.0 / total_time;
		const double time_inv_sq = time_inv * time_inv;
		const double time_inv_cub = time_inv_sq * time_inv;

		size_t num_cps = spline.numControlPoints();
		size_t dim = spline.dimension();

		// 重置成本
		cost_data.smoothness_cost_pos = 0.0;
		cost_data.smoothness_cost_vel = 0.0;
		cost_data.smoothness_cost_acc = 0.0;
		cost_data.smoothness_cost_jerk = 0.0;
		cost_data.collision_cost = 0.0;
		cost_data.feasibility_cost_vel = 0.0;
		cost_data.feasibility_cost_acc = 0.0;
		cost_data.feasibility_cost_turn = 0.0;
		cost_data.total_cost = 0.0;

		// --- 1. 平滑度成本 ---
		if (options.smoothness_weight_pos > 0 && num_cps > 1) {
			auto controlPoints = spline.controlPoints();
			for (size_t i = 0; i < num_cps - 1; ++i) {
				EcefPoint ecef_p1(controlPoints[i * dim], controlPoints[i * dim + 1], controlPoints[i * dim + 2]);
				EcefPoint ecef_p2(controlPoints[(i + 1) * dim], controlPoints[(i + 1) * dim + 1], controlPoints[(i + 1) * dim + 2]);
				Vector3D diff = ecef_p2 - ecef_p1;
				cost_data.smoothness_cost_pos += diff.squaredNorm();
			}
		}

		int smooth_samples = options.smoothness_integration_samples;
		if (smooth_samples > 1) {
			double sample_step = 1.0 / (smooth_samples - 1);
			for (int i = 0; i < smooth_samples; ++i) {
				double t = static_cast<double>(i) * sample_step;
				if (options.smoothness_weight_vel > 0 && deriv1.numControlPoints() > 0) {
					Vector3D d1_geom = tinysplineResultToVector3D(deriv1.eval(t).result());
					cost_data.smoothness_cost_vel += (d1_geom * time_inv).squaredNorm();
				}
				if (options.smoothness_weight_acc > 0 && deriv2.numControlPoints() > 0) {
					Vector3D d2_geom = tinysplineResultToVector3D(deriv2.eval(t).result());
					cost_data.smoothness_cost_acc += (d2_geom * time_inv_sq).squaredNorm();
				}
				if (options.smoothness_weight_jerk > 0 && deriv3.numControlPoints() > 0) {
					Vector3D d3_geom = tinysplineResultToVector3D(deriv3.eval(t).result());
					cost_data.smoothness_cost_jerk += (d3_geom * time_inv_cub).squaredNorm();
				}
			}
			cost_data.smoothness_cost_vel *= sample_step;
			cost_data.smoothness_cost_acc *= sample_step;
			cost_data.smoothness_cost_jerk *= sample_step;
		}
		LOG_TRACE("  平滑度成本: Pos={:.4f}, Vel={:.4f}, Acc={:.4f}, Jerk={:.4f}", cost_data.smoothness_cost_pos, cost_data.smoothness_cost_vel, cost_data.smoothness_cost_acc, cost_data.smoothness_cost_jerk);

		// --- 2. 碰撞成本 ---
		int collision_samples = options.collision_check_samples;
		if (collision_samples > 0 && options.collision_weight > 0) {
			int collision_count = 0;
			LOG_TRACE("  检查碰撞 ({} 个采样点)...", collision_samples);
			double sample_step = 1.0 / std::max(1, collision_samples - 1);
			for (int i = 0; i < collision_samples; ++i) {
				double t = static_cast<double>(i) * sample_step;
				EcefPoint ecef_p = tinysplineResultToEcefPoint(spline.eval(t).result());
				Time sample_time = t * total_time; // 估算时间戳
				if (!environment.isPositionValid(ecef_p, sample_time, options.collision_safety_margin, false, true)) {
					LOG_TRACE("    碰撞点 @ t={:.3f}", t);
					cost_data.collision_cost += 1.0;
					collision_count++;
				}
			}
			cost_data.collision_cost /= std::max(1, collision_samples);
			LOG_TRACE("  碰撞成本 (比例): {:.4f} ({} / {} 点)", cost_data.collision_cost, collision_count, collision_samples);
		}

		// --- 3. 可行性成本 ---
		int feasibility_samples = options.feasibility_check_samples;
		if (feasibility_samples > 1 && (options.feasibility_weight_vel > 0 || options.feasibility_weight_acc > 0 || options.feasibility_weight_turn > 0)) {
			LOG_TRACE("  检查可行性 ({} 个采样点)...", feasibility_samples);
			double sample_step = 1.0 / (feasibility_samples - 1);
			NSDrones::NSUav::UavState prev_state; // 存储上一完整状态
			Vector3D prev_vel_geom = Vector3D::Zero(); // 存储上一几何速度
			bool first = true;

			for (int i = 0; i < feasibility_samples; ++i) {
				double t = static_cast<double>(i) * sample_step;
				Time current_time = t * total_time;

				EcefPoint ecef_pos = tinysplineResultToEcefPoint(spline.eval(t).result());
				Vector3D vel_geom = Vector3D::Zero();
				Vector3D acc_geom = Vector3D::Zero();
				if (deriv1.numControlPoints() > 0) vel_geom = tinysplineResultToVector3D(deriv1.eval(t).result());
				if (deriv2.numControlPoints() > 0) acc_geom = tinysplineResultToVector3D(deriv2.eval(t).result());

				NSDrones::NSUav::UavState current_state;
				// 转换ECEF坐标为WGS84坐标用于UavState
				current_state.position = NSDrones::NSUtils::CoordinateConverter::ecefToWGS84(ecef_pos);
				current_state.velocity = vel_geom * time_inv;
				current_state.acceleration = acc_geom * time_inv_sq;
				current_state.time_stamp = current_time;
				// 姿态和模式需要根据实际情况填充，或使用 dynamics 模型预测
				// current_state.orientation = ...;
				// current_state.mode = ...;

				if (options.feasibility_weight_vel > 0) {
					double speed = current_state.velocity.norm();
					double max_h_speed = dynamics.getMaxHorizontalSpeed(current_state);
					double min_op_speed = dynamics.getMinOperationalSpeed(current_state);
					if (speed > max_h_speed + Constants::VELOCITY_EPSILON ||
						(speed < min_op_speed - Constants::VELOCITY_EPSILON
							&& speed > Constants::VELOCITY_EPSILON)) {
						LOG_TRACE("    速度限制违反 @ t={:.3f}, Speed={:.2f} (Lim: {:.2f}-{:.2f})", t, speed, min_op_speed, max_h_speed);
						cost_data.feasibility_cost_vel += 1.0;
					}
				}

				if (options.feasibility_weight_acc > 0) {
					double accel_norm = current_state.acceleration.norm();
					double max_h_accel = dynamics.getMaxHorizontalAcceleration(current_state);
					if (accel_norm > max_h_accel + Constants::ACCEL_EPSILON) {
						LOG_TRACE("    加速度限制违反 @ t={:.3f}, Accel={:.2f} (Lim: {:.2f})", t, accel_norm, max_h_accel);
						cost_data.feasibility_cost_acc += 1.0;
					}
				}

				if (options.feasibility_weight_turn > 0 && !first
						&& prev_vel_geom.squaredNorm() > Constants::GEOMETRY_EPSILON_SQ
						&& vel_geom.squaredNorm() > Constants::GEOMETRY_EPSILON_SQ) {
					double dot = prev_vel_geom.normalized().dot(vel_geom.normalized());
					dot = std::clamp(dot, -1.0, 1.0);
					double angle_change = std::acos(dot);
					double time_step = sample_step * total_time;
					if (time_step > Constants::TIME_EPSILON) {
						double turn_rate = angle_change / time_step;
						double max_turn_rate = dynamics.getMaxTurnRate(current_state); // 获取转弯率限制
						if (turn_rate > max_turn_rate + Constants::ANGLE_EPSILON) {
							LOG_TRACE("    转弯率限制违反 @ t={:.3f}, Rate={:.2f}rad/s (Lim: {:.2f})", t, turn_rate, max_turn_rate);
							cost_data.feasibility_cost_turn += 1.0;
						}
					}
				}

				prev_state = current_state;
				prev_vel_geom = vel_geom;
				first = false;
			}
			double norm_factor = 1.0 / std::max(1, feasibility_samples);
			cost_data.feasibility_cost_vel *= norm_factor;
			cost_data.feasibility_cost_acc *= norm_factor;
			cost_data.feasibility_cost_turn *= norm_factor;
			LOG_TRACE("  可行性成本: Vel={:.4f}, Acc={:.4f}, Turn={:.4f}", cost_data.feasibility_cost_vel, cost_data.feasibility_cost_acc, cost_data.feasibility_cost_turn);
		}

		// --- 5. 计算总成本 ---
		cost_data.total_cost = options.smoothness_weight_pos * cost_data.smoothness_cost_pos
			+ options.smoothness_weight_vel * cost_data.smoothness_cost_vel
			+ options.smoothness_weight_acc * cost_data.smoothness_cost_acc
			+ options.smoothness_weight_jerk * cost_data.smoothness_cost_jerk
			+ options.collision_weight * cost_data.collision_cost
			+ options.feasibility_weight_vel * cost_data.feasibility_cost_vel
			+ options.feasibility_weight_acc * cost_data.feasibility_cost_acc
			+ options.feasibility_weight_turn * cost_data.feasibility_cost_turn;

		LOG_TRACE("计算总成本 = {:.4f}", cost_data.total_cost);
	}

	// 计算数值梯度
	std::vector<double> calculateGradientNumerically(
		const std::vector<double>& points,
		size_t dim,
		CostData& base_cost_data, // base_cost_data.environment 应该已经被正确设置
		const NSDrones::NSAlgorithm::TrajectoryOptimizer::Options& options,
		std::function<void(CostData&)> cost_calculator)
	{
		std::vector<double> gradient(points.size(), 0.0);
		if (points.empty() || !base_cost_data.spline || !base_cost_data.environment) {
			LOG_WARN("calculateGradientNumerically: 无效的输入或环境指针，返回零梯度。");
			return gradient;
		}

		double epsilon = options.gradient_epsilon;
		CostData perturbed_cost_data;
		perturbed_cost_data.spline = base_cost_data.spline;
		perturbed_cost_data.derivative1 = base_cost_data.derivative1;
		perturbed_cost_data.derivative2 = base_cost_data.derivative2;
		perturbed_cost_data.derivative3 = base_cost_data.derivative3;
		perturbed_cost_data.options = base_cost_data.options;
		perturbed_cost_data.environment = base_cost_data.environment;
		perturbed_cost_data.dynamics = base_cost_data.dynamics;
		perturbed_cost_data.strategies = base_cost_data.strategies;
		perturbed_cost_data.total_time = base_cost_data.total_time;

		std::vector<double> perturbed_points = points;

		// 创建临时的样条对象用于梯度计算，避免修改原始 cost_data 中的指针
		tinyspline::BSpline temp_spline = *base_cost_data.spline;
		tinyspline::BSpline temp_deriv1, temp_deriv2, temp_deriv3;
		// 检查原始指针是否有效再解引用
		if (base_cost_data.derivative1) temp_deriv1 = *base_cost_data.derivative1;
		if (base_cost_data.derivative2) temp_deriv2 = *base_cost_data.derivative2;
		if (base_cost_data.derivative3) temp_deriv3 = *base_cost_data.derivative3;

		// 将指针指向临时对象
		perturbed_cost_data.spline = &temp_spline;
		// 只有当原始指针有效时，才设置导数指针，否则置 nullptr
		perturbed_cost_data.derivative1 = base_cost_data.derivative1 ? &temp_deriv1 : nullptr;
		perturbed_cost_data.derivative2 = base_cost_data.derivative2 ? &temp_deriv2 : nullptr;
		perturbed_cost_data.derivative3 = base_cost_data.derivative3 ? &temp_deriv3 : nullptr;

		// 计算基准成本
		cost_calculator(perturbed_cost_data);
		double base_cost = perturbed_cost_data.total_cost;
		if (base_cost == Constants::INF) {
			LOG_WARN("基准成本为 INF，无法计算梯度。");
			return gradient; // 返回零梯度
		}

		for (size_t i = 0; i < points.size(); ++i) {
			perturbed_points[i] += epsilon;

			// 调用 setControlPoints，暂时忽略返回值
			temp_spline.setControlPoints(perturbed_points);

			// 重新计算导数 (因为控制点变了)
			try {
				temp_deriv1 = temp_spline.derive();
				temp_deriv2 = temp_deriv1.derive();
				temp_deriv3 = temp_deriv2.derive();
				perturbed_cost_data.derivative1 = &temp_deriv1;
				perturbed_cost_data.derivative2 = &temp_deriv2;
				perturbed_cost_data.derivative3 = &temp_deriv3;
			}
			catch (const std::exception& e) {
				LOG_WARN("数值梯度计算中重新计算导数失败 (索引 {}): {}。控制点可能已设置但样条无效。", i, e.what());
				perturbed_points[i] = points[i]; // 恢复点
				gradient[i] = 0.0; // 梯度设为 0
				perturbed_cost_data.derivative1 = nullptr;
				perturbed_cost_data.derivative2 = nullptr;
				perturbed_cost_data.derivative3 = nullptr;
				// 注意：这里不 continue，允许计算没有导数贡献的成本
			}

			// 计算扰动后的成本
			cost_calculator(perturbed_cost_data);
			double perturbed_cost = perturbed_cost_data.total_cost;

			if (perturbed_cost == Constants::INF) {
				LOG_WARN("扰动成本为 INF (索引 {})，梯度分量设为 0。", i);
				gradient[i] = 0.0;
			}
			else {
				gradient[i] = (perturbed_cost - base_cost) / epsilon;
			}

			perturbed_points[i] = points[i]; // 恢复点
		}

		return gradient;
	}

	// (待实现/简化版) 估算最优时间
	Time estimateOptimalTime(const tinyspline::BSpline& spline,
		const NSDrones::NSUav::IDynamicModel& dynamics,
		const TrajectoryOptimizer::Options& options)
	{
		LOG_DEBUG("开始估算最优时间...");
		Time estimated_time = 0.0;
		int samples = 100; // 采样点数量，用于估算
		if (samples < 2 || spline.numControlPoints() == 0) return options.min_trajectory_duration;

		tinyspline::BSpline deriv1, deriv2;
		try {
			deriv1 = spline.derive();
			deriv2 = deriv1.derive();
		}
		catch (const std::exception& e) {
			LOG_WARN("估算最优时间时计算导数失败: {}，返回最小持续时间。", e.what());
			return options.min_trajectory_duration;
		}

		double path_length = 0.0;
		double max_curvature_accel = 0.0;
		EcefPoint prev_ecef = tinysplineResultToEcefPoint(spline.eval(0.0).result());

		for (int i = 1; i < samples; ++i) {
			double t = static_cast<double>(i) / (samples - 1);
			EcefPoint current_ecef = tinysplineResultToEcefPoint(spline.eval(t).result());
			Vector3D d1 = Vector3D::Zero();
			Vector3D d2 = Vector3D::Zero();
			// 检查导数样条是否有效(有控制点)
			if (deriv1.numControlPoints() > 0) d1 = tinysplineResultToVector3D(deriv1.eval(t).result());
			if (deriv2.numControlPoints() > 0) d2 = tinysplineResultToVector3D(deriv2.eval(t).result());

			path_length += (current_ecef - prev_ecef).norm();

			double d1_norm = d1.norm();
			if (d1_norm > Constants::GEOMETRY_EPSILON) {
				double curvature_accel_geom = (d1.cross(d2)).norm() / d1_norm;
				max_curvature_accel = std::max(max_curvature_accel, curvature_accel_geom);
			}
			prev_ecef = current_ecef;
		}
		LOG_DEBUG("路径总长度 (估算): {:.2f} m", path_length);
		LOG_DEBUG("最大曲率加速度几何部分 (估算): {:.2f}", max_curvature_accel);

		NSDrones::NSUav::UavState typical_state; // 使用默认状态获取限制
		double max_vel = dynamics.getMaxHorizontalSpeed(typical_state);
		double max_acc = dynamics.getMaxHorizontalAcceleration(typical_state);

		Time time_from_vel = (max_vel > Constants::VELOCITY_EPSILON) ?
			(path_length / max_vel) : Constants::INF;
		Time time_from_acc = (max_acc > Constants::ACCEL_EPSILON) ?
			(2.0 * sqrt(path_length / max_acc)) : Constants::INF;
		Time time_from_curve = (max_acc > Constants::ACCEL_EPSILON &&
			max_curvature_accel > Constants::GEOMETRY_EPSILON) ?
			sqrt(max_curvature_accel / max_acc) : 0.0;

		LOG_DEBUG("基于速度的时间下限: {:.3f} s", time_from_vel);
		LOG_DEBUG("基于加速度的时间下限 (粗估): {:.3f} s", time_from_acc);
		LOG_DEBUG("基于曲率的时间下限 (粗估): {:.3f} s", time_from_curve);

		// 取一个合理的下限，并应用最小持续时间约束
		estimated_time = std::max({ time_from_vel, time_from_acc, time_from_curve });
		estimated_time = std::max(estimated_time, options.min_trajectory_duration);

		LOG_INFO("估算最优时间完成: {:.3f} s", estimated_time);
		// 可以在这里添加一个小的余量，例如 * 1.05 或 * 1.1，但这会影响速度/加速度
		return estimated_time;
	}

} // 匿名 namespace 结束


namespace NSDrones {
	namespace NSAlgorithm {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSUav;
		using namespace NSDrones::NSMission;
		// --- TrajectoryOptimizer 成员函数实现将在此处添加/修改 ---

		// 构造函数
		TrajectoryOptimizer::TrajectoryOptimizer(ObjectID id,
		                               const std::string& type_tag,
		                               const std::string& name,
		                               const std::string& version)
			: ITrajectoryOptimizer(), 
			  AlgorithmObject(id, type_tag, name, version), 
			  options_()
		{
			// 尝试从默认配置文件加载（如果存在且未通过 initialize 配置）
			// std::string default_config_path = "trajectory_optimizer_config.json"; // Or get from a global setting
			// loadOptionsFromFile(default_config_path);
			LOG_DEBUG("TrajectoryOptimizer 实例已创建。等待 initialize() 调用以加载特定配置。");
		}

		// initialize 实现
		bool TrajectoryOptimizer::initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) {
			LOG_INFO("TrajectoryOptimizer::initialize 开始...");

			if (!AlgorithmObject::initialize(params, raw_config)) {
				LOG_ERROR("[EnergyEvaluator] 基类 AlgorithmObject 初始化失败。ID: {}, Name: {}", getId(), getName());
				return false;
			}
			// 从 ParamValues 读取参数
			try {
				options_.bspline_degree = params->getValueOrDefault<int>("optimizer.bspline_degree", options_.bspline_degree);
				options_.output_path_points = params->getValueOrDefault<int>("optimizer.output_path_points", options_.output_path_points);
				options_.smoothness_weight_pos = params->getValueOrDefault<double>("optimizer.smoothness_weight_pos", options_.smoothness_weight_pos);
				options_.smoothness_weight_vel = params->getValueOrDefault<double>("optimizer.smoothness_weight_vel", options_.smoothness_weight_vel);
				options_.smoothness_weight_acc = params->getValueOrDefault<double>("optimizer.smoothness_weight_acc", options_.smoothness_weight_acc);
				options_.smoothness_weight_jerk = params->getValueOrDefault<double>("optimizer.smoothness_weight_jerk", options_.smoothness_weight_jerk);
				options_.collision_weight = params->getValueOrDefault<double>("optimizer.collision_weight", options_.collision_weight);
				options_.feasibility_weight_vel = params->getValueOrDefault<double>("optimizer.feasibility_weight_vel", options_.feasibility_weight_vel);
				options_.feasibility_weight_acc = params->getValueOrDefault<double>("optimizer.feasibility_weight_acc", options_.feasibility_weight_acc);
				options_.feasibility_weight_turn = params->getValueOrDefault<double>("optimizer.feasibility_weight_turn", options_.feasibility_weight_turn);
				options_.collision_safety_margin = params->getValueOrDefault<double>("optimizer.collision_safety_margin", options_.collision_safety_margin);
				options_.min_trajectory_duration = params->getValueOrDefault<double>("optimizer.min_trajectory_duration", options_.min_trajectory_duration);
				options_.smoothness_integration_samples = params->getValueOrDefault<int>("optimizer.smoothness_integration_samples", options_.smoothness_integration_samples);
				options_.collision_check_samples = params->getValueOrDefault<int>("optimizer.collision_check_samples", options_.collision_check_samples);
				options_.feasibility_check_samples = params->getValueOrDefault<int>("optimizer.feasibility_check_samples", options_.feasibility_check_samples);
				options_.max_iterations = params->getValueOrDefault<int>("optimizer.max_iterations", options_.max_iterations);
				options_.cost_tolerance = params->getValueOrDefault<double>("optimizer.cost_tolerance", options_.cost_tolerance);
				options_.optimizer_learning_rate = params->getValueOrDefault<double>("optimizer.optimizer_learning_rate", options_.optimizer_learning_rate);
				options_.gradient_epsilon = params->getValueOrDefault<double>("optimizer.gradient_epsilon", options_.gradient_epsilon);

				// 还可以从 raw_config 读取特定于实例的覆盖值（如果需要）
				// 例如： if (raw_config.contains("specific_optimizer_setting")) { ... }

				// === 验证配置参数 ===
				if (!validateOptimizerOptions(options_)) {
					LOG_ERROR("[TrajectoryOptimizer] 配置参数验证失败，请检查参数设置");
					return false;
				}

				LOG_INFO("[TrajectoryOptimizer] 成功从 ParamValues 加载并验证优化器参数");
			} catch (const std::exception& e) {
				LOG_ERROR("[TrajectoryOptimizer] 从 ParamValues 加载参数时发生错误: {}", e.what());
				return false;
			}
			return true;
		}

		// --- 优化轨迹核心函数 ---
		RouteSegment TrajectoryOptimizer::optimizer(
			const RouteSegment& initial_segment,
			const IDynamicModel& dynamics,
			const ITaskStrategyMap& strategies)
		{
			NSUtils::Stopwatch sw;
			LOG_INFO("开始 B 样条轨迹优化 (优化版)... 输入点数: {}", initial_segment.size());

			if (initial_segment.size() < 2) {
				LOG_WARN("初始路径点数不足，返回原始路径。");
				return initial_segment;
			}

			// --- 步骤 1: 初始化 B 样条 ---
			LOG_DEBUG("[TrajectoryOptimizer] 步骤 1: 初始化 B 样条...");
			std::vector<double> initial_points = routeSegmentToTinysplinePoints(initial_segment);
			size_t num_points = initial_segment.size();
			size_t dimension = 3;

			LOG_DEBUG("[TrajectoryOptimizer] 输入点数: {}, 期望阶数: {}", num_points, options_.bspline_degree);

			// === 使用安全的样条创建方法 ===
			bool spline_creation_success = false;
			tinyspline::BSpline spline = createBSplineSafely(initial_points, dimension, options_.bspline_degree, spline_creation_success);

			if (!spline_creation_success) {
				LOG_ERROR("[TrajectoryOptimizer] B样条创建失败，返回原始路径");
				return initial_segment;
			}

			// === 验证样条有效性 ===
			if (!validateSpline(spline)) {
				LOG_ERROR("[TrajectoryOptimizer] B样条验证失败，返回原始路径");
				return initial_segment;
			}

			LOG_DEBUG("[TrajectoryOptimizer] B样条创建成功: 控制点数={}, 阶数={}, 维度={}",
				spline.numControlPoints(), spline.degree(), spline.dimension());

			// --- 步骤 2: 估算初始时间和计算初始导数 ---
			LOG_DEBUG("步骤 2: 估算初始时间和计算导数...");
			CostData cost_data;
			cost_data.options = &options_;
			auto environment = Environment::getInstance();
			if (!environment) {
				LOG_ERROR("TrajectoryOptimizer: Environment 实例不存在");
				return {};
			}
			cost_data.environment = environment.get();
			cost_data.dynamics = &dynamics;
			cost_data.strategies = &strategies;

			cost_data.total_time = estimateOptimalTime(spline, dynamics, options_);
			LOG_DEBUG("估算初始总时间: {:.3f} s", cost_data.total_time);

			tinyspline::BSpline deriv1, deriv2, deriv3;
			std::vector<double> current_control_points;
			std::vector<double> best_control_points;
			double min_cost = Constants::INF;

			// --- 步骤 3: 迭代优化控制点 ---
			LOG_INFO("[TrajectoryOptimizer] 步骤 3: 开始迭代优化控制点 ({} 次迭代)...", options_.max_iterations);
			current_control_points = spline.controlPoints();
			best_control_points = current_control_points;
			double learning_rate = options_.optimizer_learning_rate;
			double last_cost = Constants::INF;
			bool optimization_succeeded = false;
			int consecutive_failures = 0;
			const int max_consecutive_failures = 3; // 允许的连续失败次数

			for (int iter = 0; iter < options_.max_iterations; ++iter) {
				LOG_DEBUG("[TrajectoryOptimizer] 优化迭代 {} / {}", iter + 1, options_.max_iterations);

				try {
					// === 验证控制点数量 ===
					size_t expected_size = spline.numControlPoints() * dimension;
					if (current_control_points.size() != expected_size) {
						LOG_ERROR("[TrajectoryOptimizer] 迭代 {} 控制点数量 ({}) 与期望 ({}) 不匹配，终止优化",
							iter + 1, current_control_points.size(), expected_size);
						break;
					}

					// === 更新样条控制点 ===
					spline.setControlPoints(current_control_points);

					// === 计算导数 ===
					if (!computeSplineDerivatives(spline, deriv1, deriv2, deriv3)) {
						LOG_ERROR("[TrajectoryOptimizer] 迭代 {} 计算样条导数失败", iter + 1);
						consecutive_failures++;
						continue;
					}

					// === 设置成本计算数据 ===
					cost_data.spline = &spline;
					cost_data.derivative1 = &deriv1;
					cost_data.derivative2 = &deriv2;
					cost_data.derivative3 = &deriv3;

					// 重置连续失败计数
					consecutive_failures = 0;
				}
				catch (const std::exception& e) {
					LOG_ERROR("[TrajectoryOptimizer] 迭代 {} 设置控制点或计算导数失败: {}", iter + 1, e.what());
					consecutive_failures++;

					if (consecutive_failures >= max_consecutive_failures) {
						LOG_ERROR("[TrajectoryOptimizer] 连续 {} 次失败，终止优化", consecutive_failures);
						break;
					}

					// 尝试恢复：使用最佳控制点
					LOG_WARN("[TrajectoryOptimizer] 尝试恢复到最佳控制点");
					current_control_points = best_control_points;
					continue;
				}

				// === 计算当前成本 ===
				calculateSplineCost(cost_data);
				double current_cost = cost_data.total_cost;
				LOG_DEBUG("[TrajectoryOptimizer] 迭代 {} 当前成本: {:.6f}", iter + 1, current_cost);

				// === 更新最佳结果 ===
				if (current_cost < min_cost) {
					min_cost = current_cost;
					best_control_points = current_control_points;
					LOG_DEBUG("[TrajectoryOptimizer] 更新最佳成本: {:.6f}", min_cost);
				}

				// === 检查终止条件 ===
				if (current_cost == Constants::INF) {
					LOG_ERROR("[TrajectoryOptimizer] 迭代 {} 成本为无穷大，终止优化", iter + 1);
					break;
				}

				// 收敛检查
				if (iter > 0) {
					double cost_change = std::abs(last_cost - current_cost);
					if (cost_change < options_.cost_tolerance) {
						LOG_INFO("[TrajectoryOptimizer] 成本变化 {:.6f} 小于容差 {:.6f}，收敛完成",
							cost_change, options_.cost_tolerance);
						optimization_succeeded = true;
						break;
					}
				}
				last_cost = current_cost;

				// === 计算数值梯度 ===
				LOG_TRACE("[TrajectoryOptimizer] 迭代 {} 计算数值梯度...", iter + 1);
				std::vector<double> gradient = calculateGradientNumerically(
					current_control_points, dimension, cost_data, options_, calculateSplineCost);

				// === 验证梯度有效性 ===
				bool gradient_valid = true;
				double gradient_norm = 0.0;
				for (size_t i = 0; i < gradient.size(); ++i) {
					if (!std::isfinite(gradient[i])) {
						LOG_WARN("[TrajectoryOptimizer] 迭代 {} 检测到无效梯度值 [{}] = {}", iter + 1, i, gradient[i]);
						gradient_valid = false;
						break;
					}
					gradient_norm += gradient[i] * gradient[i];
				}

				if (!gradient_valid) {
					LOG_WARN("[TrajectoryOptimizer] 迭代 {} 梯度无效，跳过更新", iter + 1);
					continue;
				}

				gradient_norm = std::sqrt(gradient_norm);
				LOG_TRACE("[TrajectoryOptimizer] 迭代 {} 梯度范数: {:.6f}", iter + 1, gradient_norm);

				// 梯度过小检查
				if (gradient_norm < 1e-10) {
					LOG_INFO("[TrajectoryOptimizer] 迭代 {} 梯度范数过小 ({:.2e})，可能已收敛", iter + 1, gradient_norm);
					optimization_succeeded = true;
					break;
				}

				// === 验证梯度大小匹配 ===
				if (gradient.size() != current_control_points.size()) {
					LOG_ERROR("[TrajectoryOptimizer] 迭代 {} 梯度大小 ({}) 与控制点大小 ({}) 不匹配",
						iter + 1, gradient.size(), current_control_points.size());
					break;
				}

				// === 自适应学习率 ===
				double adaptive_lr = learning_rate;
				if (iter > 0 && current_cost > last_cost) {
					// 成本增加，减小学习率
					adaptive_lr *= 0.5;
					LOG_DEBUG("[TrajectoryOptimizer] 迭代 {} 成本增加，学习率调整为: {:.6f}", iter + 1, adaptive_lr);
				}

				// === 更新控制点 ===
				for (size_t i = 0; i < current_control_points.size(); ++i) {
					current_control_points[i] -= adaptive_lr * gradient[i];
				}

				LOG_TRACE("[TrajectoryOptimizer] 迭代 {} 控制点更新完成", iter + 1);

			} // end optimization loop

			// === 优化统计信息 ===
			LOG_INFO("[TrajectoryOptimizer] 控制点优化完成:");
			LOG_INFO("  - 执行迭代数: {} / {}", iter, options_.max_iterations);
			LOG_INFO("  - 最佳成本: {:.6f}", min_cost);
			LOG_INFO("  - 收敛状态: {}", optimization_succeeded ? "已收敛" : "未收敛");
			LOG_INFO("  - 连续失败次数: {}", consecutive_failures);

			// --- 步骤 4: 使用找到的最佳控制点设置最终样条 ---
			if (min_cost == Constants::INF) {
				LOG_ERROR("优化未能找到成本有限的解，将返回原始路径。");
				LOG_INFO("优化总耗时: {:.3f} ms", sw.elapsedMilliseconds());
				return initial_segment;
			}

			// === 使用最佳控制点设置最终样条 ===
			LOG_DEBUG("[TrajectoryOptimizer] 步骤 4: 设置最终样条为最佳控制点...");
			try {
				spline.setControlPoints(best_control_points);

				// 重新计算导数
				if (!computeSplineDerivatives(spline, deriv1, deriv2, deriv3)) {
					LOG_ERROR("[TrajectoryOptimizer] 最终样条导数计算失败，返回原始路径");
					return initial_segment;
				}

				// 验证最终样条
				if (!validateSpline(spline)) {
					LOG_ERROR("[TrajectoryOptimizer] 最终样条验证失败，返回原始路径");
					return initial_segment;
				}

				LOG_DEBUG("[TrajectoryOptimizer] 最终样条设置成功");
			}
			catch (const std::exception& e) {
				LOG_ERROR("[TrajectoryOptimizer] 设置最终样条异常: {}，返回原始路径", e.what());
				return initial_segment;
			}

			// --- 步骤 5: 重新估算最终轨迹时间 ---
			LOG_DEBUG("步骤 5: 基于优化后的几何形状重新估算轨迹时间...");
			Time final_total_time = estimateOptimalTime(spline, dynamics, options_);
			LOG_INFO("最终优化后的轨迹时间 (估算): {:.3f} s", final_total_time);

			// --- 步骤 6: 重新采样最终路径 ---
			LOG_DEBUG("[TrajectoryOptimizer] 步骤 6: 重新采样最终优化后的路径 ({} 个点)...", options_.output_path_points);
			RouteSegment optimized_path;
			int num_output_points = std::max(2, options_.output_path_points);
			optimized_path.reserve(num_output_points);
			const double final_time_inv = 1.0 / final_total_time;

			Time start_time = initial_segment.front().time_stamp; // 保持起始时间戳
			int sampling_failures = 0;
			const int max_sampling_failures = num_output_points / 4; // 允许25%的采样失败

			for (int i = 0; i < num_output_points; ++i) {
				double t = (num_output_points > 1) ? static_cast<double>(i) / (num_output_points - 1) : 0.0;
				Time time_stamp = start_time + t * final_total_time;

				try {
					// === 评估样条位置 ===
					EcefPoint ecef_pos = tinysplineResultToEcefPoint(spline.eval(t).result());

					// === 评估速度（一阶导数） ===
					Vector3D vel = Vector3D::Zero();
					if (deriv1.numControlPoints() > 0) {
						vel = tinysplineResultToVector3D(deriv1.eval(t).result()) * final_time_inv;
					}

					// === 评估加速度（二阶导数） ===
					Vector3D acc = Vector3D::Zero();
					if (deriv2.numControlPoints() > 0) {
						acc = tinysplineResultToVector3D(deriv2.eval(t).result()) * (final_time_inv * final_time_inv);
					}

					// === 验证数值有效性 ===
					if (!ecef_pos.allFinite() || !vel.allFinite() || !acc.allFinite()) {
						LOG_WARN("[TrajectoryOptimizer] 采样点 {} (t={:.3f}) 包含无效数值", i, t);
						sampling_failures++;
						continue;
					}

					// === 转换坐标系 ===
					WGS84Point wgs84_pos = NSUtils::CoordinateConverter::ecefToWGS84(ecef_pos);

					// === 计算姿态（基于速度方向） ===
					Orientation att = Orientation::Identity();
					if (vel.norm() > Constants::VELOCITY_EPSILON) {
						// 简化姿态计算：偏航角指向速度方向
						Vector3D vel_normalized = vel.normalized();
						double yaw = std::atan2(vel_normalized.y(), vel_normalized.x());
						att = Orientation::fromEulerAngles(0.0, 0.0, yaw);
					}

					// === 创建路径点 ===
					RoutePoint route_point;
					route_point.position = wgs84_pos;
					route_point.time_stamp = time_stamp;
					route_point.velocity = vel;
					route_point.acceleration = acc;
					route_point.orientation = att;

					// 继承载荷动作（如果有）
					if (!initial_segment.empty() && !initial_segment[0].payload_actions.empty()) {
						route_point.payload_actions = initial_segment[0].payload_actions;
					}

					optimized_path.push_back(route_point);

				} catch (const std::exception& e) {
					LOG_ERROR("[TrajectoryOptimizer] 采样点 {} (t={:.3f}) 评估失败: {}", i, t, e.what());
					sampling_failures++;

					if (sampling_failures > max_sampling_failures) {
						LOG_ERROR("[TrajectoryOptimizer] 采样失败次数 ({}) 超过阈值 ({})，返回原始路径",
							sampling_failures, max_sampling_failures);
						return initial_segment;
					}
				}
			}

			// === 验证最终路径 ===
			if (optimized_path.size() < 2) {
				LOG_ERROR("[TrajectoryOptimizer] 最终路径点数 ({}) 不足，返回原始路径", optimized_path.size());
				return initial_segment;
			}

			LOG_DEBUG("[TrajectoryOptimizer] 路径重采样完成，生成 {} 个有效点 (失败 {} 个)",
				optimized_path.size(), sampling_failures);

			// --- 步骤 7: 最终可行性检查 ---
			LOG_DEBUG("步骤 7: 检查最终优化路径的可行性...");
			bool feasible = isFeasible(optimized_path, dynamics, strategies);

			LOG_INFO("优化总耗时: {:.3f} ms", sw.elapsedMilliseconds());

			// === 输出最终结果 ===
			if (feasible) {
				LOG_INFO("[TrajectoryOptimizer] ===== B样条轨迹优化成功 =====");
				LOG_INFO("  输入路径点数: {}", initial_segment.size());
				LOG_INFO("  输出路径点数: {}", optimized_path.size());
				LOG_INFO("  最终成本: {:.6f}", min_cost);
				LOG_INFO("  轨迹时间: {:.3f} 秒", final_total_time);
				LOG_INFO("  优化耗时: {:.3f} 毫秒", sw.elapsedMilliseconds());
				LOG_INFO("  成本改善: {}", min_cost < Constants::INF ? "是" : "否");
				LOG_INFO("  可行性检查: 通过");
				LOG_INFO("=======================================");
				return optimized_path;
			}
			else {
				LOG_WARN("[TrajectoryOptimizer] ===== 轨迹优化失败 =====");
				LOG_WARN("  原因: 优化后的轨迹未通过可行性检查");
				LOG_WARN("  最终成本: {:.6f}", min_cost);
				LOG_WARN("  优化耗时: {:.3f} 毫秒", sw.elapsedMilliseconds());
				LOG_WARN("  返回: 原始路径");
				LOG_WARN("=====================================");
				return initial_segment;
			}
		}

		bool TrajectoryOptimizer::isFeasible(
			const RouteSegment& trajectory,
			const IDynamicModel& dynamics,
			const ITaskStrategyMap& strategies) const
		{
			NSUtils::Stopwatch sw; // 启用 Stopwatch
			LOG_TRACE("检查轨迹可行性... 点数: {}, 策略数: {}", trajectory.size(), strategies.size());

			if (trajectory.empty()) {
				LOG_WARN("输入轨迹为空，判定为不可行。");
				return false;
			}
			if (trajectory.size() == 1) {
				LOG_TRACE("轨迹只有一个点，检查该点碰撞。");
				auto environment = Environment::getInstance();
				if (environment) {
					// 将WGS84坐标转换为ECEF坐标
					EcefPoint ecef_pos = NSUtils::CoordinateConverter::wgs84ToECEF(trajectory[0].position);
					return environment->isPositionValid(ecef_pos, trajectory[0].time_stamp, options_.collision_safety_margin, false, true);
				}
				return false;
			}

			if (!checkCollision(trajectory)) {
				LOG_WARN("可行性检查：检测到碰撞！");
				return false;
			}

			for (size_t i = 0; i < trajectory.size(); ++i) {
				const auto& pt = trajectory[i];
				UavState current_state;
				current_state.position = pt.position;
				current_state.time_stamp = pt.time_stamp;
				current_state.velocity = pt.velocity;
				current_state.orientation = pt.orientation;

				double speed = current_state.velocity.norm();
				double max_h_speed = dynamics.getMaxHorizontalSpeed(current_state);
				if (speed > max_h_speed + Constants::VELOCITY_EPSILON) {
					LOG_WARN("可行性检查：点 {} 速度 ({:.2f} m/s) 超过最大水平限制 ({:.2f} m/s)。", i, speed, max_h_speed);
					return false;
				}

				if (i > 0) {
					const auto& prev_pt = trajectory[i - 1];
					UavState prev_state;
					prev_state.position = prev_pt.position;
					prev_state.time_stamp = prev_pt.time_stamp;
					prev_state.velocity = prev_pt.velocity;
					prev_state.orientation = prev_pt.orientation;
					double dt = current_state.time_stamp - prev_state.time_stamp;

					if (dt > Constants::TIME_EPSILON) {
						Vector3D accel = (current_state.velocity - prev_state.velocity) / dt;
						double max_h_accel = dynamics.getMaxHorizontalAcceleration(current_state);
						if (accel.norm() > max_h_accel + Constants::ACCEL_EPSILON) {
							LOG_WARN("可行性检查：点 {}->{} 加速度 ({:.2f} m/s^2) 超过最大水平限制 ({:.2f} m/s^2)。", i - 1, i, accel.norm(), max_h_accel);
							// return false; // 可选失败
						}
						if (!dynamics.isStateTransitionFeasible(prev_state, current_state, dt)) {
							LOG_WARN("可行性检查：状态转换 {}->{} 在 dt={:.4f}s 内不可行。", i - 1, i, dt);
							// return false; // 可选失败
						}
					}
					else if (dt < -Constants::TIME_EPSILON) {
						LOG_WARN("可行性检查：点 {}->{} 时间戳倒退 (dt={:.4f}s)。", i - 1, i, dt);
						return false;
					}
				}
			}

			LOG_TRACE("可行性检查通过。耗时: {:.3f} ms", sw.elapsedMilliseconds()); // 记录耗时
			return true;
		}

		void TrajectoryOptimizer::setParameters(const std::map<std::string, double>& parameters) {
			LOG_DEBUG("设置 BSplineTrajectoryOptimizer 参数 (优化版)... 收到 {} 个参数", parameters.size());
			int updated_count = 0;
			int unknown_count = 0;

			auto update_int = [&](const std::string& key, int& option_field) {
				auto it = parameters.find(key);
				if (it != parameters.end()) {
					option_field = static_cast<int>(it->second);
					LOG_TRACE("更新参数 '{}' = {}", key, option_field);
					updated_count++;
					return true;
				}
				return false;
			};

			auto update_double = [&](const std::string& key, double& option_field) {
				auto it = parameters.find(key);
				if (it != parameters.end()) {
					option_field = it->second;
					LOG_TRACE("更新参数 '{}' = {:.4g}", key, option_field);
					updated_count++;
					return true;
				}
				return false;
			};

			// --- 样条基本参数 ---
			update_int("bspline_degree", options_.bspline_degree);
			update_int("output_path_points", options_.output_path_points);

			// --- 成本函数权重 ---
			update_double("smoothness_weight_pos", options_.smoothness_weight_pos);
			update_double("smoothness_weight_vel", options_.smoothness_weight_vel);
			update_double("smoothness_weight_acc", options_.smoothness_weight_acc);
			update_double("smoothness_weight_jerk", options_.smoothness_weight_jerk);
			update_double("collision_weight", options_.collision_weight);
			update_double("feasibility_weight_vel", options_.feasibility_weight_vel);
			update_double("feasibility_weight_acc", options_.feasibility_weight_acc);
			update_double("feasibility_weight_turn", options_.feasibility_weight_turn);

			// --- 约束和检查参数 ---
			update_double("collision_safety_margin", options_.collision_safety_margin);
			update_double("min_trajectory_duration", options_.min_trajectory_duration);
			update_int("smoothness_integration_samples", options_.smoothness_integration_samples);
			update_int("collision_check_samples", options_.collision_check_samples);
			update_int("feasibility_check_samples", options_.feasibility_check_samples);

			// --- 优化器参数 ---
			update_int("max_iterations", options_.max_iterations);
			update_double("cost_tolerance", options_.cost_tolerance);
			update_double("optimizer_learning_rate", options_.optimizer_learning_rate);
			update_double("gradient_epsilon", options_.gradient_epsilon);

			// 检查是否有未处理的参数
			for (const auto& pair : parameters) {
				// 查找是否已被处理 (通过再次调用 update 函数模拟检查)
				if (!update_int(pair.first, options_.bspline_degree) && // 使用任意一个 int/double 字段测试即可
					!update_double(pair.first, options_.smoothness_weight_pos)) {
					LOG_WARN("参数 '{}' 未被 BSplineTrajectoryOptimizer 处理。", pair.first);
					unknown_count++;
				}
			}
			LOG_DEBUG("参数设置完成。更新了 {} 个参数，{} 个未知参数。", updated_count, unknown_count);
		}

		/**
		 * @brief 优化轨迹（重构后的统一接口）
		 */
		TrajectoryOptimizationResult TrajectoryOptimizer::optimize(const TrajectoryOptimizationRequest& request) {
			LOG_INFO("[TrajectoryOptimizer] 开始轨迹优化，输入轨迹段数: {}", request.initial_trajectory.size());

			TrajectoryOptimizationResult result;
			result.success = false;

			try {
				// === 第1步：输入验证 ===
				if (request.initial_trajectory.empty()) {
					result.message = "输入轨迹为空";
					LOG_ERROR("[TrajectoryOptimizer] {}", result.message);
					return result;
				}

				if (!request.dynamics) {
					result.message = "动力学模型指针无效";
					LOG_ERROR("[TrajectoryOptimizer] {}", result.message);
					return result;
				}

				// === 第2步：转换为RouteSegment进行处理 ===
				RouteSegment initial_segment;
				for (const auto& traj_segment : request.initial_trajectory) {
					initial_segment.insert(initial_segment.end(), traj_segment.begin(), traj_segment.end());
				}

				if (initial_segment.size() < 2) {
					result.message = "合并后的轨迹点数少于2个";
					LOG_WARN("[TrajectoryOptimizer] {}，返回原始轨迹", result.message);
					result.optimized_trajectory = request.initial_trajectory;
					result.success = true;
					return result;
				}

				// === 第3步：调用现有的优化方法 ===
				NSUtils::Stopwatch stopwatch;
				stopwatch.start();

				ITaskStrategyMap strategies_map;
				if (request.strategies) {
					strategies_map = *request.strategies;
				}

				RouteSegment optimized_segment = optimizer(initial_segment, *request.dynamics, strategies_map);

				stopwatch.stop();

				// === 第4步：转换回Trajectory格式 ===
				if (optimized_segment.empty()) {
					result.message = "优化失败，返回原始轨迹";
					LOG_WARN("[TrajectoryOptimizer] {}", result.message);
					result.optimized_trajectory = request.initial_trajectory;
				} else {
					// 简化处理：将整个RouteSegment作为一个轨迹段
					Trajectory optimized_trajectory;
					optimized_trajectory.push_back(optimized_segment);
					result.optimized_trajectory = std::move(optimized_trajectory);
					result.message = "轨迹优化成功";
				}

				result.success = true;
				LOG_INFO("[TrajectoryOptimizer] 轨迹优化完成，耗时: {:.3f}秒", stopwatch.elapsedSeconds());

			} catch (const std::exception& e) {
				result.success = false;
				result.message = "轨迹优化异常: " + std::string(e.what());
				LOG_ERROR("[TrajectoryOptimizer] 优化异常: {}", e.what());
				result.optimized_trajectory = request.initial_trajectory; // 返回原始轨迹
			}

			return result;
		}

		/**
		 * @brief 检查轨迹可行性（重构后的统一接口）
		 */
		bool TrajectoryOptimizer::isFeasible(const Trajectory& trajectory, const IDynamicModel& dynamics) const {
			LOG_DEBUG("[TrajectoryOptimizer] 检查轨迹可行性，轨迹段数: {}", trajectory.size());

			if (trajectory.empty()) {
				LOG_DEBUG("[TrajectoryOptimizer] 轨迹为空，视为可行");
				return true;
			}

			try {
				// 转换为RouteSegment进行检查
				RouteSegment route_segment;
				for (const auto& traj_segment : trajectory) {
					route_segment.insert(route_segment.end(), traj_segment.begin(), traj_segment.end());
				}

				if (route_segment.empty()) {
					LOG_DEBUG("[TrajectoryOptimizer] 合并后的轨迹为空，视为可行");
					return true;
				}

				// 调用现有的可行性检查方法
				ITaskStrategyMap empty_strategies;
				return isFeasible(route_segment, dynamics, empty_strategies);

			} catch (const std::exception& e) {
				LOG_ERROR("[TrajectoryOptimizer] 可行性检查异常: {}", e.what());
				return false;
			}
		}

		/**
		 * @brief 设置优化器参数（重构后的统一接口）
		 */
		void TrajectoryOptimizer::setParameters(const std::map<std::string, double>& parameters) {
			LOG_DEBUG("[TrajectoryOptimizer] 设置优化器参数，参数数量: {}", parameters.size());

			// 直接调用内部参数设置逻辑，避免递归
			int updated_count = 0;
			int unknown_count = 0;

			auto update_int = [&](const std::string& key, int& option_field) {
				auto it = parameters.find(key);
				if (it != parameters.end()) {
					option_field = static_cast<int>(it->second);
					LOG_TRACE("[TrajectoryOptimizer] 更新参数 '{}' = {}", key, option_field);
					updated_count++;
					return true;
				}
				return false;
			};

			auto update_double = [&](const std::string& key, double& option_field) {
				auto it = parameters.find(key);
				if (it != parameters.end()) {
					option_field = it->second;
					LOG_TRACE("[TrajectoryOptimizer] 更新参数 '{}' = {:.4g}", key, option_field);
					updated_count++;
					return true;
				}
				return false;
			};

			// === 样条基本参数 ===
			update_int("bspline_degree", options_.bspline_degree);
			update_int("output_path_points", options_.output_path_points);

			// === 成本函数权重 ===
			update_double("smoothness_weight_pos", options_.smoothness_weight_pos);
			update_double("smoothness_weight_vel", options_.smoothness_weight_vel);
			update_double("smoothness_weight_acc", options_.smoothness_weight_acc);
			update_double("smoothness_weight_jerk", options_.smoothness_weight_jerk);
			update_double("collision_weight", options_.collision_weight);
			update_double("feasibility_weight_vel", options_.feasibility_weight_vel);
			update_double("feasibility_weight_acc", options_.feasibility_weight_acc);
			update_double("feasibility_weight_turn", options_.feasibility_weight_turn);

			// === 约束和检查参数 ===
			update_double("collision_safety_margin", options_.collision_safety_margin);
			update_double("min_trajectory_duration", options_.min_trajectory_duration);
			update_int("smoothness_integration_samples", options_.smoothness_integration_samples);
			update_int("collision_check_samples", options_.collision_check_samples);
			update_int("feasibility_check_samples", options_.feasibility_check_samples);

			// === 优化器参数 ===
			update_int("max_iterations", options_.max_iterations);
			update_double("cost_tolerance", options_.cost_tolerance);
			update_double("optimizer_learning_rate", options_.optimizer_learning_rate);
			update_double("gradient_epsilon", options_.gradient_epsilon);

			// 检查未知参数
			for (const auto& pair : parameters) {
				bool found = false;
				// 检查是否为已知参数
				std::vector<std::string> known_params = {
					"bspline_degree", "output_path_points",
					"smoothness_weight_pos", "smoothness_weight_vel", "smoothness_weight_acc", "smoothness_weight_jerk",
					"collision_weight", "feasibility_weight_vel", "feasibility_weight_acc", "feasibility_weight_turn",
					"collision_safety_margin", "min_trajectory_duration",
					"smoothness_integration_samples", "collision_check_samples", "feasibility_check_samples",
					"max_iterations", "cost_tolerance", "optimizer_learning_rate", "gradient_epsilon"
				};

				for (const auto& known : known_params) {
					if (pair.first == known) {
						found = true;
						break;
					}
				}

				if (!found) {
					LOG_WARN("[TrajectoryOptimizer] 未知参数 '{}' = {:.4g}", pair.first, pair.second);
					unknown_count++;
				}
			}

			LOG_DEBUG("[TrajectoryOptimizer] 参数设置完成，更新了 {} 个参数，{} 个未知参数", updated_count, unknown_count);
		}

		bool TrajectoryOptimizer::checkCollision(const Trajectory& trajectory) const {
			LOG_DEBUG("[TrajectoryOptimizer] 检查轨迹碰撞，轨迹段数: {}", trajectory.size());

			if (trajectory.empty()) {
				LOG_DEBUG("[TrajectoryOptimizer] 轨迹为空，无碰撞");
				return true;
			}

			try {
				// 转换为RouteSegment进行检查
				RouteSegment route_segment;
				for (const auto& traj_segment : trajectory) {
					route_segment.insert(route_segment.end(), traj_segment.begin(), traj_segment.end());
				}

				if (route_segment.size() < 2) {
					LOG_DEBUG("[TrajectoryOptimizer] 轨迹点数少于2个，无碰撞");
					return true;
				}

				// 调用现有的碰撞检查方法
				return checkCollision(route_segment);

			} catch (const std::exception& e) {
				LOG_ERROR("[TrajectoryOptimizer] 碰撞检查异常: {}", e.what());
				return false;
			}
		}

		bool TrajectoryOptimizer::checkCollision(const RouteSegment& trajectory) const {
			Stopwatch sw; // 启用 Stopwatch
			if (trajectory.size() < 2) { return true; }

			auto environment = Environment::getInstance();
			if (!environment) {
				LOG_ERROR("TrajectoryOptimizer::checkCollision: Environment 实例不存在");
				return false;
			}

			for (size_t i = 0; i < trajectory.size() - 1; ++i) {
				const auto& p1 = trajectory[i];
				const auto& p2 = trajectory[i + 1];

				// 将WGS84坐标转换为ECEF坐标
				EcefPoint ecef_p1 = NSUtils::CoordinateConverter::wgs84ToECEF(p1.position);
				EcefPoint ecef_p2 = NSUtils::CoordinateConverter::wgs84ToECEF(p2.position);

				if (!environment->isSegmentValid(ecef_p1, ecef_p2, p1.time_stamp, p2.time_stamp, options_.collision_safety_margin, false, true)) {
					LOG_TRACE("碰撞检查：段 {}->{} {} @{:.2f}s -> {} @{:.2f}s 与环境冲突。",
						i, i + 1,
						p1.position.toString(), p1.time_stamp,
						p2.position.toString(), p2.time_stamp);
					return false;
				}
			}
			LOG_TRACE("碰撞检查通过。耗时: {:.3f} ms", sw.elapsedMilliseconds()); // 记录耗时
			return true;
		}

	} // namespace NSAlgorithm
} // namespace NSDrones