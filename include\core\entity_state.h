// include/core/entity_state.h
#pragma once

#include "core/types.h"
#include "utils/logging.h"
#include <string>
#include <vector>
#include <chrono>
#include <optional>
#include <limits>
#include <cmath>

//namespace NSDrones { namespace NSUav { class UavState; } }
namespace NSDrones {
	namespace NSCore {
		// ObjectID 和 INVALID_OBJECT_ID 已在 core/types.h 中定义

		/**
		 * @struct EntityState
		 * @brief 表示一个对象（例如无人机、障碍物）的瞬时动态状态。
		 *
		 * 坐标系统说明（统一架构）：
		 * - 位置使用 WGS84 坐标系 (全局标准，便于数据交换和长距离计算)
		 * - 速度使用 WGS84 坐标系 (North-East-Down 方向，但基于WGS84)
		 * - 姿态使用四元数表示，从 WGS84/NED 坐标系到机体坐标系的旋转
		 * - 角速度、角加速度在机体坐标系中表示
		 *
		 * 设计理念：
		 * - WGS84作为主坐标系，避免多基准点混乱
		 * - 内部计算时自动转换为最优局部坐标系（NED/MGRS）
		 * - 对外接口统一，用户无需关心内部坐标转换
		 */
		struct EntityState {
			// --- 核心运动状态（统一使用WGS84坐标系） ---
			WGS84Point position;                        // 位置 (WGS84坐标系: 纬度/经度/高度)
			Orientation orientation = Orientation::Identity(); // 姿态 (四元数, WGS84/NED到机体坐标系的旋转)
			Vector3D velocity_wgs84 = Vector3D::Zero(); // 速度 (米/秒, WGS84坐标系: North/East/Down方向)
			Vector3D angular_velocity = Vector3D::Zero(); // 角速度 (弧度/秒, 机体坐标系: x=Roll, y=Pitch, z=Yaw)

			// --- 扩展状态 (可选，根据对象类型填充) ---
			Vector3D acceleration = Vector3D::Zero();        // 加速度 (米/秒^2, NED坐标系) - UAV常用
			Vector3D angular_acceleration = Vector3D::Zero();// 角加速度 (弧度/秒^2, 机体坐标系) - UAV常用
			double energy_level = 0.0;                   // 能量水平 (例如 Wh 或 %) - UAV常用
			// 可以添加更多，如温度、传感器读数等

			// --- 元数据 ---
			Time time_stamp = 0.0;                      // 状态的时间戳 (秒)
			std::string status = "UNKNOWN";             // 对象状态描述 (例如 "ACTIVE", "IDLE", "DAMAGED")
			std::vector<std::string> capabilities;      // 对象具有的能力列表 (例如 "CAMERA", "LIDAR", "DELIVERY")

			// 构造函数
			EntityState() {
				// 初始化时间戳为当前时间
				time_stamp = std::chrono::duration_cast<std::chrono::duration<double>>(
					std::chrono::system_clock::now().time_since_epoch()).count();
			}

			// 从 UAVState 构造 EntityState
			//EntityState(const NSDrones::NSUav::UavState& uav_s);

			// 验证状态是否有效 (例如，检查 NaN 或 Inf)
			bool isEmpty() const;
		};

		using EntityStatePtr = std::shared_ptr<EntityState>;


		/**
		 * @struct EntityStateSnapshot
		 * @brief 用于在状态更新时捕获必要的前后状态信息，以便更新索引。
		 *        用于 Environment::notifyObjectUpdate。
		 */
		struct EntityStateSnapshot {
			ObjectID id = NSUtils::INVALID_OBJECT_ID; // 对象 ID (从 EntityObject 获取)
			std::string object_type;           // 对象类型 (用于属性索引, 从 EntityObject 获取)
			std::optional<std::string> old_object_type; // 旧的对象类型 (如果类型改变)

			// --- 改变前的状态 (用于移除旧索引) ---
			std::optional<WGS84Point> old_position;              // 旧位置 (如果位置改变)
			std::optional<BoundingBox> old_bounds;            // 旧包围盒 (如果位置或形状改变)
			std::optional<std::string> old_status;            // 旧状态 (如果状态改变)
			std::optional<ObjectID> old_parent_id;            // 旧父对象 ID (如果父关系改变)
			std::optional<std::vector<std::string>> old_capabilities; // 旧能力 (如果能力改变)

			// --- 当前状态 (用于添加新索引或查找) ---
			// **重要:** 这些字段代表 *更新后* 的状态
			WGS84Point current_position = WGS84Point(std::numeric_limits<double>::quiet_NaN(),
													  std::numeric_limits<double>::quiet_NaN(),
													  std::numeric_limits<double>::quiet_NaN()); // 当前位置
			std::optional<BoundingBox> current_bounds;        // 当前包围盒 (更新后)
			std::string current_status;                      // 当前状态
			ObjectID current_parent_id = NSUtils::INVALID_OBJECT_ID; // 当前父对象 ID
			std::vector<std::string> current_capabilities;   // 当前能力

			// 默认构造函数
			EntityStateSnapshot() = default;

			// 标记此快照用于对象替换时的清理
			void markAsBeingReplaced() {
				// 目前是占位符，如果需要特定逻辑可以在这里添加
				// 例如，设置一个内部标志位
				LOG_TRACE("EntityStateSnapshot: 对象 {} 的快照被标记为替换。", id);
			}

			/**
			 * @brief 检查位置是否发生变化
			 * @return 如果位置发生变化则返回true
			 */
			bool hasPositionChanged() const {
				return old_position.has_value();
			}

			/**
			 * @brief 检查属性是否发生变化
			 * @return 如果属性发生变化则返回true
			 */
			bool hasAttributesChanged() const {
				return old_object_type.has_value() ||
					   old_status.has_value() ||
					   old_capabilities.has_value();
			}
		};

	} // namespace NSCore
} // namespace NSDrones