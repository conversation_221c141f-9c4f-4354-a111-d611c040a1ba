#pragma once

#include "environment/maps/igridmap.h"
#include "environment/maps/single_gridmap.h"  // 添加 SingleGridMap 的包含
#include "core/types.h"                 // 包含 WGS84BoundingBox, BoundingBox, WGS84Point, FeatureType 等核心类型
#include "params/parameters.h"          // 添加参数系统
#include "utils/logging.h"
#include "utils/file_utils.h"
#include <nlohmann/json.hpp>            // 添加 JSON 支持
#include <vector>
#include <string>
#include <memory>     // For std::shared_ptr
#include <optional>   // For std::optional
#include <unordered_map>                // 添加 unordered_map

namespace NSDrones {
	namespace NSEnvironment {
		/**
		 * @class TiledGridMap
		 * @brief 多瓦片地图管理器，统一管理多个地图文件
		 *
		 * 该类实现IGridMap接口，负责加载和管理多个地图瓦片文件，提供统一的地理数据访问接口。
		 * 支持自动瓦片发现、智能路由查询和最优分辨率选择。
		 *
		 * 核心特性：
		 * - 多瓦片管理：自动扫描和加载指定目录下的所有地图文件
		 * - 智能路由：根据查询坐标自动选择最合适的瓦片
		 * - 分辨率优化：优先使用高分辨率瓦片提供精确数据
		 * - 边界管理：自动计算和维护所有瓦片的组合边界
		 * - 动态加载：支持运行时动态加载新的瓦片文件
		 *
		 * 查询策略：
		 * - 覆盖检查：首先检查查询点是否在瓦片覆盖范围内
		 * - 分辨率选择：在多个覆盖瓦片中选择分辨率最高的
		 * - 数据验证：确保返回的数据有效且可用
		 *
		 * 瓦片组织：
		 * - 每个瓦片由独立的SingleGridMap实例管理
		 * - 瓦片ID基于文件名生成，确保唯一性
		 * - 支持不同格式的瓦片文件（GeoTIFF、PNG等）
		 *
		 * @note 线程安全：所有公共方法都是线程安全的
		 * @note 内存管理：使用智能指针管理瓦片生命周期
		 */
		class TiledGridMap : public IGridMap {
		public:
			/**
			 * @struct LayerConfig
			 * @brief 瓦片地图图层配置结构
			 *
			 * 定义了从各个瓦片中提取不同类型地理数据时使用的图层名称和配置参数。
			 * 支持自定义图层名称和目标分辨率设置。
			 */
			struct LayerConfig {
				std::string elevationLayer = "elevation";        ///< 高程数据图层名称
				std::string featureTypeLayer = "feature_type";   ///< 地物类型数据图层名称
				std::string featureHeightLayer = "feature_height"; ///< 地物高度数据图层名称
				double targetResolution = 0.0;                   ///< 目标分辨率（米/像素），0.0表示使用原生分辨率
			};

			// === 生命周期管理 ===

			/**
			 * @brief 构造函数
			 * @param config 图层配置，未提供时使用默认配置
			 * @note 构造后需要调用initialize()完成初始化
			 */
			explicit TiledGridMap(LayerConfig config = {});

			/**
			 * @brief 析构函数
			 * 自动释放所有瓦片资源
			 */
			~TiledGridMap() override;

			// === IGridMap接口实现 ===

			/**
			 * @brief 初始化瓦片地图管理器
			 * @param global_params 全局参数对象，包含地图配置信息
			 * @return 初始化成功返回true，失败返回false
			 * @note 从global_params中读取map.data_directory等配置参数
			 * @note 自动扫描数据目录并加载所有符合条件的地图文件
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> global_params) override;

			/**
			 * @brief 获取指定位置的地形高程
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 海拔高度（米），查询失败时返回std::nullopt
			 * @note 自动路由到覆盖该点且分辨率最高的瓦片
			 */
			std::optional<double> getElevation(double latitude, double longitude) const override;

			/**
			 * @brief 获取指定位置的地物类型
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 地物类型枚举，查询失败时返回std::nullopt
			 * @note 自动路由到覆盖该点且分辨率最高的瓦片
			 */
			std::optional<FeatureType> getFeature(double latitude, double longitude) const override;

			/**
			 * @brief 获取指定位置的地物高度
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 地物高度（米），查询失败时返回std::nullopt
			 * @note 自动路由到覆盖该点且分辨率最高的瓦片
			 */
			std::optional<double> getFeatureHeight(double latitude, double longitude) const override;

			/**
			 * @brief 检查地图管理器是否已初始化
			 * @return 已初始化且至少加载一个瓦片返回true，否则返回false
			 */
			bool isInitialized() const override;

			/**
			 * @brief 检查指定坐标是否被任何瓦片覆盖
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 至少被一个瓦片覆盖返回true，否则返回false
			 */
			bool isCovered(double latitude, double longitude) const override;

			// === 元数据访问接口 ===

			/**
			 * @brief 获取汇总的地图元数据
			 * @return 所有瓦片汇总后的元数据信息
			 * @note 包含最高分辨率、组合边界、所有可用图层等汇总信息
			 */
			MapMetadata getMetadata() const override;

			/**
			 * @brief 获取所有瓦片的详细元数据列表
			 * @return 瓦片元数据列表，每个元素对应一个瓦片的详细信息
			 * @note 可用于了解每个瓦片的具体信息和覆盖范围
			 */
			std::vector<MapMetadata> getTileMetadataList() const override;

			// === TiledGridMap特有接口 ===

			/**
			 * @brief 获取所有瓦片的组合边界框
			 * @return 所有已加载瓦片的WGS84组合边界
			 * @note 组合边界是所有瓦片边界的外接矩形，可能包含无数据区域
			 * @deprecated 建议使用getMetadata().wgs84_bounds获取边界信息
			 */
			WGS84BoundingBox getCombinedDataSourceWGS84Bounds() const;

			/**
			 * @brief 获取已加载的瓦片数量
			 * @return 成功加载的瓦片数量
			 */
			size_t getTileCount() const;

			/**
			 * @brief 获取指定瓦片的元数据
			 * @param tile_id 瓦片标识符
			 * @return 瓦片元数据，瓦片不存在时返回std::nullopt
			 */
			std::optional<MapMetadata> getTileMetadata(const std::string& tile_id) const;

			/**
			 * @brief 获取覆盖指定坐标的所有瓦片ID列表
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 覆盖该坐标的瓦片ID列表
			 */
			std::vector<std::string> getCoveringTiles(double latitude, double longitude) const;

			/**
			 * @brief 获取所有瓦片的元数据列表（已弃用）
			 * @return 瓦片元数据列表
			 * @deprecated 请使用getTileMetadataList()方法
			 */
			std::vector<MapMetadata> getTiles() const;

			// === 动态瓦片管理接口 ===

			/**
			 * @brief 动态添加单个瓦片
			 * @param file_path 地图文件路径
			 * @param tile_id 瓦片标识符（可选，默认使用文件名）
			 * @return 添加成功返回true，失败返回false
			 * @note 线程安全，会自动更新组合边界和元数据
			 */
			bool addTile(const std::string& file_path, const std::string& tile_id = "");

			/**
			 * @brief 动态移除指定瓦片
			 * @param tile_id 瓦片标识符
			 * @return 移除成功返回true，瓦片不存在返回false
			 * @note 线程安全，会自动更新组合边界和元数据
			 */
			bool removeTile(const std::string& tile_id);

			/**
			 * @brief 重新加载指定瓦片
			 * @param tile_id 瓦片标识符
			 * @return 重新加载成功返回true，失败返回false
			 * @note 线程安全，会自动更新组合边界和元数据
			 */
			bool reloadTile(const std::string& tile_id);

			/**
			 * @brief 清空所有瓦片
			 * @note 线程安全，会重置所有状态
			 */
			void clearAllTiles();


		private:
			// === 核心数据存储 ===
			mutable std::unordered_map<std::string, std::shared_ptr<SingleGridMap>> single_gridmaps_;  ///< 瓦片缓存（瓦片ID -> SingleGridMap实例）
			WGS84BoundingBox combinedBoundsWGS84_;                                                     ///< 所有瓦片的组合边界框

			// === 配置和状态 ===
			LayerConfig layerConfig_;                                                                  ///< 图层配置信息
			std::shared_ptr<NSParams::ParamValues> globalParams_;                                     ///< 全局参数（用于动态加载）
			bool initialized_ = false;                                                                ///< 初始化完成标志

			// === 线程安全 ===
			mutable std::shared_mutex tiles_mutex_;                                                   ///< 瓦片访问读写锁

			// === 初始化和加载方法 ===

			/**
			 * @brief 扫描地图数据目录，查找指定格式的地图文件
			 * @param data_directory 数据目录路径
			 * @param load_mode 加载模式（"geotiff"或"png"）
			 * @return 找到的地图文件路径列表
			 */
			std::vector<std::string> scanMapDataDirectory(
				const std::string& data_directory,
				const std::string& load_mode
			);

			/**
			 * @brief 加载并处理单个瓦片文件
			 * @param filePath 地图文件路径
			 * @param tileId 瓦片唯一标识符
			 * @param refOriginWGS84 参考原点（暂未使用）
			 * @return 成功加载返回瓦片元数据，失败返回std::nullopt
			 * @note 此方法创建SingleGridMap实例并将其添加到瓦片缓存中
			 */
			std::optional<MapMetadata> loadAndProcessTile(
				const std::string& filePath,
				const std::string& tileId,
				const WGS84Point& refOriginWGS84
			);

			// === 边界和元数据管理方法 ===

			/**
			 * @brief 更新所有瓦片的组合边界框
			 * @note 在添加或移除瓦片后调用，重新计算组合边界（线程安全）
			 */
			void updateCombinedBounds();

			/**
			 * @brief 更新组合边界框（内部版本）
			 * @note 调用者必须持有tiles_mutex_锁
			 */
			void updateCombinedBoundsInternal();

			/**
			 * @brief 更新合并的元数据信息
			 * @note 预留方法，用于未来的元数据缓存优化
			 */
			void updateCombinedMetadata();

			// === 瓦片选择和路由方法 ===

			/**
			 * @brief 为指定坐标选择最佳瓦片
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 最佳瓦片的智能指针，未找到时返回nullptr
			 * @note 选择策略：覆盖范围内分辨率最高的瓦片
			 */
			std::shared_ptr<SingleGridMap> selectBestTile(double latitude, double longitude) const;
		};

	} // namespace NSEnvironment
} // namespace NSDrones
