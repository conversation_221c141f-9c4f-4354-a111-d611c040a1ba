// include/uav/dynamics/multirotor_dynamics.h
#pragma once

#include "uav/idynamic_model.h"
#include "uav/uav.h"
#include <memory>                 
#include <limits>                 
#include <cmath>                 

namespace NSDrones { namespace NSUav { class Uav; } }

namespace NSDrones {
	namespace NSUav {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class MultirotorDynamics
		 * @brief 多旋翼无人机动力学模型实现。
		 *        特点是能够悬停和原地转弯。
		 *        依赖于 owner_ 引用来访问参数。
		 */
		class MultirotorDynamics : public IDynamicModel {
		public:
			/**
			 * @brief 构造函数。
			 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
			 */
			explicit MultirotorDynamics(const Uav& owner); // 接收 owner 引用

			// --- 禁止拷贝和移动 ---
			MultirotorDynamics(const MultirotorDynamics&) = delete;
			MultirotorDynamics& operator=(const MultirotorDynamics&) = delete;
			MultirotorDynamics(MultirotorDynamics&&) = delete;
			MultirotorDynamics& operator=(MultirotorDynamics&&) = delete;

			/** @brief 虚析构函数 */
			~MultirotorDynamics() override = default;

			// --- 覆写 IDynamicModel 虚函数 ---
			/** @brief 返回无人机类型。*/
			UavType getType() const override { return UavType::MULTIROTOR; }

			// --- 多旋翼特定行为实现 ---
			// 参数查找基于 "dynamics.mr.*"。
			/** @brief 获取最大转弯率 (多旋翼理论上无穷大)。*/
			double getMaxTurnRate(const UavState& state) const override;
			/** @brief 获取最小转弯半径 (多旋翼为 0)。*/
			double getMinTurnRadius(const UavState& state) const override;
			/** @brief 获取最大允许的姿态角（代替倾斜角）。参数键 "dynamics.mr.max_attitude_angle_deg"。*/
			double getMaxBankAngle(const UavState& state) const override;
			// 继承基类的速度/加速度限制实现，它们会查找 "dynamics.base.*" 或 "dynamics.mr.*"

			/** @brief 检查状态转换是否可行。*/
			bool isStateTransitionFeasible(const UavState& current, const UavState& next, Time dt) const override;

			// --- 力学计算实现 (简化占位符) ---
			/** @brief 计算升力 (主要用于平衡重力和垂直加速)。*/
			Vector3D computeLiftForce(const UavState& state, double air_density) const override;
			/** @brief 计算空气阻力 (基于简化模型)。参数键 "dynamics.mr.drag_coeff"。*/
			Vector3D computeDragForce(const UavState& state, double air_density) const override;
			/** @brief 计算电机所需的总推力向量。*/
			Vector3D computeThrustForce(const UavState& state) const override;

		private:
			// owner_ 引用由基类 IDynamicModel 持有
		};

	} // namespace NSUav
} // namespace NSDrones