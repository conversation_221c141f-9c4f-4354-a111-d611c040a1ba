#include "core/geometry/shapes/line_shape.h"
#include <stdexcept>
#include <cmath>

namespace NSDrones {
namespace NSCore {

    LineShape::LineShape(const fcl::Vector3d& start, const fcl::Vector3d& end) 
        : start_(start), end_(end), fcl_capsule_(nullptr) {
        validateEndpoints();
    }

    LineShape::LineShape() : LineShape(fcl::Vector3d::Zero(), fcl::Vector3d::UnitX()) {
    }

    LineShape::LineShape(const LineShape& other)
        : start_(other.start_), end_(other.end_), fcl_capsule_(nullptr) {
    }

    LineShape& LineShape::operator=(const LineShape& other) {
        if (this != &other) {
            start_ = other.start_;
            end_ = other.end_;
            fcl_capsule_.reset();  // 重置FCL对象，延迟重新创建
        }
        return *this;
    }

    std::shared_ptr<fcl::CollisionGeometryd> LineShape::getFCLGeometry() const {
        ensureFCLObject();
        return fcl_capsule_;
    }

    fcl::AABBd LineShape::getAABB(const fcl::Transform3d& transform) const {
        fcl::Vector3d transformed_start = transform * start_;
        fcl::Vector3d transformed_end = transform * end_;
        
        fcl::Vector3d min_point = transformed_start.cwiseMin(transformed_end).array() - LINE_RADIUS;
        fcl::Vector3d max_point = transformed_start.cwiseMax(transformed_end).array() + LINE_RADIUS;
        
        return fcl::AABBd(min_point, max_point);
    }

    fcl::Matrix3d LineShape::getInertiaMatrix(double mass) const {
        // 线段的惯性张量（简化为沿线段方向的杆）
        fcl::Vector3d direction = getDirection();
        double length = getLength();
        
        // 对于均匀杆，绕质心的惯性张量
        double I_parallel = 0.0;  // 绕轴的惯性矩为0
        double I_perpendicular = mass * length * length / 12.0;  // 绕垂直轴的惯性矩
        
        fcl::Matrix3d I = fcl::Matrix3d::Identity() * I_perpendicular;
        // 沿线段方向的惯性矩为0
        I -= I_perpendicular * direction * direction.transpose();
        
        return I;
    }

    std::unique_ptr<IShape> LineShape::clone() const {
        return std::make_unique<LineShape>(*this);
    }

    nlohmann::json LineShape::serialize() const {
        nlohmann::json j;
        j["type"] = "LINE";
        j["start"] = {start_.x(), start_.y(), start_.z()};
        j["end"] = {end_.x(), end_.y(), end_.z()};
        return j;
    }

    bool LineShape::deserialize(const nlohmann::json& json) {
        try {
            if (json["type"] != "LINE") {
                return false;
            }
            
            auto start = json["start"];
            auto end = json["end"];
            start_ = fcl::Vector3d(start[0], start[1], start[2]);
            end_ = fcl::Vector3d(end[0], end[1], end[2]);
            
            validateEndpoints();
            fcl_capsule_.reset();  // 重置FCL对象
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    std::string LineShape::toString() const {
        return "Line(start: [" + std::to_string(start_.x()) + ", " + 
               std::to_string(start_.y()) + ", " + std::to_string(start_.z()) + 
               "], end: [" + std::to_string(end_.x()) + ", " + 
               std::to_string(end_.y()) + ", " + std::to_string(end_.z()) + "])";
    }

    bool LineShape::containsPoint(const fcl::Vector3d& point) const {
        return distanceToPoint(point) < LINE_RADIUS;
    }

    double LineShape::distanceToPoint(const fcl::Vector3d& point) const {
        fcl::Vector3d line_vec = end_ - start_;
        fcl::Vector3d point_vec = point - start_;
        
        double line_length_sq = line_vec.squaredNorm();
        if (line_length_sq < 1e-12) {
            // 退化为点
            return point_vec.norm();
        }
        
        double t = point_vec.dot(line_vec) / line_length_sq;
        t = std::max(0.0, std::min(1.0, t));  // 限制在线段范围内
        
        fcl::Vector3d closest_point = start_ + t * line_vec;
        return (point - closest_point).norm();
    }

    void LineShape::setEndpoints(const fcl::Vector3d& start, const fcl::Vector3d& end) {
        start_ = start;
        end_ = end;
        validateEndpoints();
        fcl_capsule_.reset();  // 重置FCL对象
    }

    double LineShape::getLength() const {
        return (end_ - start_).norm();
    }

    fcl::Vector3d LineShape::getDirection() const {
        fcl::Vector3d dir = end_ - start_;
        double length = dir.norm();
        if (length < 1e-12) {
            return fcl::Vector3d::UnitX();  // 默认方向
        }
        return dir / length;
    }

    fcl::Vector3d LineShape::getPointAt(double t) const {
        return start_ + t * (end_ - start_);
    }

    std::unique_ptr<LineShape> LineShape::createUnitLine() {
        return std::make_unique<LineShape>(fcl::Vector3d::Zero(), fcl::Vector3d::UnitX());
    }

    std::unique_ptr<LineShape> LineShape::createFromPoints(const fcl::Vector3d& start, const fcl::Vector3d& end) {
        return std::make_unique<LineShape>(start, end);
    }

    void LineShape::ensureFCLObject() const {
        if (!fcl_capsule_) {
            double length = getLength();
            fcl_capsule_ = std::make_shared<fcl::Capsuled>(LINE_RADIUS, length);
        }
    }

    void LineShape::validateEndpoints() const {
        if ((end_ - start_).norm() < 1e-12) {
            throw std::invalid_argument("LineShape: 起点和终点不能相同");
        }
    }

} // namespace NSCore
} // namespace NSDrones
