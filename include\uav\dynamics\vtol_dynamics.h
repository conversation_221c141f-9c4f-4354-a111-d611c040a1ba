// include/uav/dynamics/vtol_dynamics.h
#pragma once

#include "uav/idynamic_model.h" 
#include "uav/uav.h"
#include <memory>                   

namespace NSDrones { namespace NSUav { class Uav; class MultirotorDynamics; class FixedWingDynamics; } }

namespace NSDrones {
	namespace NSUav {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class VtolDynamics
		 * @brief 垂直起降固定翼无人机动力学模型实现。
		 *        通过组合内部的多旋翼和固定翼模型实例来处理不同飞行模式。
		 *        依赖于 owner_ 引用访问不同模式下的参数。
		 */
		class VtolDynamics : public IDynamicModel {
		public:
			/**
			 * @brief 构造函数。
			 *        内部会创建悬停和固定翼子模型。
			 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
			 * @throws DroneException 如果内部模型创建失败。
			 */
			explicit VtolDynamics(const Uav& owner); // 接收 owner 引用

			// --- 禁止拷贝和移动 ---
			VtolDynamics(const VtolDynamics&) = delete;
			VtolDynamics& operator=(const VtolDynamics&) = delete;
			VtolDynamics(VtolDynamics&&) = delete;
			VtolDynamics& operator=(VtolDynamics&&) = delete;

			/** @brief 虚析构函数 */
			~VtolDynamics() override = default;

			// --- 覆写 IDynamicModel 虚函数 ---
			/** @brief 返回无人机类型。*/
			UavType getType() const override { return UavType::VTOL_FIXED_WING; }

			// --- 覆盖依赖模式的方法 ---
			// 这些方法会根据飞行模式委托给内部模型或使用组合逻辑
			// 参数查找基于 "dynamics.vtol.*", "dynamics.vtol.hover.*", "dynamics.vtol.fw.*"
			double getMaxHorizontalSpeed(const UavState& state) const override;
			double getMaxClimbSpeed(const UavState& state) const override;
			double getMaxDescendSpeed(const UavState& state) const override;
			double getMaxHorizontalAcceleration(const UavState& state) const override;
			double getMaxVerticalAcceleration(const UavState& state) const override;
			double getMaxHorizontalDeceleration(const UavState& state) const override;
			double getMaxVerticalDeceleration(const UavState& state) const override;
			double getMaxAltitude(const UavState& state) const override;
			double getMaxTurnRate(const UavState& state) const override;
			double getMinTurnRadius(const UavState& state) const override;
			double getMaxBankAngle(const UavState& state) const override;
			/** @brief VTOL 可以悬停，最小运行速度通常为 0。*/
			double getMinOperationalSpeed(const UavState& state) const override;

			/** @brief 检查状态转换是否可行，考虑模式转换。*/
			bool isStateTransitionFeasible(const UavState& current, const UavState& next, Time dt) const override;

			// --- 力学计算实现 (委托或混合) ---
			Vector3D computeLiftForce(const UavState& state, double air_density) const override;
			Vector3D computeDragForce(const UavState& state, double air_density) const override;
			Vector3D computeThrustForce(const UavState& state) const override;

		private:
			// 内部模型实例 (使用智能指针管理生命周期)
			std::shared_ptr<MultirotorDynamics> hover_model_; // 悬停模式模型
			std::shared_ptr<FixedWingDynamics> fw_model_;     // 固定翼模式模型

			/**
			 * @brief (私有) 获取当前状态对应的内部模型指针。
			 *        用于在模式特定方法中选择委托目标。
			 * @param state 当前无人机状态。
			 * @return 指向内部 IDynamicModel 的 const 指针，如果内部模型无效则返回 nullptr。
			 */
			const IDynamicModel* getCurrentModel(const UavState& state) const;
		};

	} // namespace NSUav
} // namespace NSDrones