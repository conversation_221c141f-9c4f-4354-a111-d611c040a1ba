// include/utils/stopwatch.h
#pragma once
#include <chrono> 

namespace NSDrones {
	namespace NSUtils {

		/**
		 * @brief 简单的计时器类。
		 * @details 用于测量代码段的执行时间。
		 *          基于 std::chrono 实现高精度计时。
		 */
		class Stopwatch {
		public:
			/** @brief 构造函数，自动开始计时。 */
			Stopwatch() {
				reset();
			}

			/** @brief 重置并重新开始计时。 */
			void reset() {
				start_time_ = std::chrono::high_resolution_clock::now();
			}

			/**
			 * @brief 获取从上次 reset 或构造开始经过的时间。
			 * @tparam DurationType 返回值的时间单位类型类型 (例如 std::chrono::seconds, std::chrono::milliseconds, std::chrono::microseconds)。默认为毫秒。
			 * @return 经过的时间。
			 */
			template <typename DurationType = std::chrono::milliseconds>
			double elapsed() const {
				auto end_time = std::chrono::high_resolution_clock::now();
				auto duration = std::chrono::duration_cast<std::chrono::duration<double, typename DurationType::period>>(end_time - start_time_);
				return duration.count();
			}

			/** @brief 获取从上次 reset 或构造开始经过的时间 (毫秒)。 */
			double elapsedMilliseconds() const {
				return elapsed<std::chrono::milliseconds>();
			}

			/** @brief 获取从上次 reset 或构造开始经过的时间 (秒)。 */
			double elapsedSeconds() const {
				return elapsed<std::chrono::seconds>();
			}

		private:
			// 存储计时开始时间点
			std::chrono::high_resolution_clock::time_point start_time_;
		};

	} // namespace NSUtils
} // namespace NSDrones