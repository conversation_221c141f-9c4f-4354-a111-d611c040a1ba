#include "params/parameters.h"
#include "params/paramregistry.h" // 需要 ParamRegistry (用于 ParamValues::checkConstraints 等)
#include "utils/logging.h"
#include <typeindex>
#include <limits>
#include <Eigen/Core>
#include <vector>
#include <cmath> // for std::floor
#include <variant> // for std::visit, std::holds_alternative, std::get_if
#include <map>
#include <utility> // for std::move
#include <set>     // 用于 getKeys
#include <optional>
#include "nlohmann/json.hpp"
#include "params/param_json.h"

namespace NSDrones {
	namespace NSParams {
		// --- ParamDefines 方法实现 ---
		ParamDefines::ParamDefines(std::string type_tag) : type_tag_(std::move(type_tag)) {
			LOG_TRACE("ParamDefines instance created for type_tag '{}' at {}", type_tag_, fmt::ptr(this));
		}

		ParamDefines::ParamDefines(const ParamDefines& other)
			: type_tag_(other.type_tag_),
			  definitions_(other.definitions_)
		{
			LOG_TRACE("ParamDefines instance copy-constructed for type_tag '{}' from {} to {} ({} definitions)",
					  type_tag_, fmt::ptr(&other), fmt::ptr(this), definitions_.size());
		}

		ParamDefines& ParamDefines::operator=(const ParamDefines& other) {
			if (this != &other) {
				type_tag_ = other.type_tag_;
				definitions_ = other.definitions_;
				LOG_TRACE("ParamDefines instance copy-assigned for type_tag '{}' from {} to {} ({} definitions)",
						  type_tag_, fmt::ptr(&other), fmt::ptr(this), definitions_.size());
			}
			return *this;
		}

		ParamDefines::ParamDefines(ParamDefines&& other) noexcept
			: type_tag_(std::move(other.type_tag_)),
			  definitions_(std::move(other.definitions_))
		{
			LOG_TRACE("ParamDefines instance move-constructed for type_tag '{}' from {} to {} ({} definitions)",
					  type_tag_, fmt::ptr(&other), fmt::ptr(this), definitions_.size());
		}

		ParamDefines& ParamDefines::operator=(ParamDefines&& other) noexcept {
			if (this != &other) {
				type_tag_ = std::move(other.type_tag_);
				definitions_ = std::move(other.definitions_);
				LOG_TRACE("ParamDefines instance move-assigned for type_tag '{}' from {} to {} ({} definitions)",
						  type_tag_, fmt::ptr(&other), fmt::ptr(this), definitions_.size());
			}
			return *this;
		}

		ParamDefines::~ParamDefines() {
			LOG_TRACE("ParamDefines instance destroyed for type_tag '{}' at {} (had {} definitions)",
					  type_tag_, fmt::ptr(this), definitions_.size());
		}

		const std::string& ParamDefines::getTypeTag() const {
			return type_tag_;  // type_tag_ 是只读的，无需锁保护
		}

		const std::map<std::string, ParamDefine>& ParamDefines::getDefinitions() const {
			std::shared_lock lock(definitions_mutex_);
			return definitions_;  // 返回引用，但在锁保护下
		}

		bool ParamDefines::addDefine(const ParamDefine& definition) {
			if (definition.key.empty()) {
				LOG_ERROR("参数定义集 (ParamDefines): 无法添加键为空的参数定义 (类型标签: '{}')。", type_tag_);
				return false;
			}

			// 线程安全的写操作
			{
				std::unique_lock lock(definitions_mutex_);
				definitions_[definition.key] = definition; // 核心：存储定义 (这是一个拷贝)
			}

			//LOG_TRACE("参数定义集 (ParamDefines) '{}': 已添加/更新参数定义 '{}'。", type_tag_, definition.key);
			return true;
		}

		bool ParamDefines::addDefines(const std::vector<ParamDefine>& new_definitions) {
			bool all_ok = true;
			if (new_definitions.empty()) {
				LOG_TRACE("参数定义集 (ParamDefines): 为类型 '{}' 尝试添加空的定义列表。", type_tag_);
				return true;
			}
			LOG_TRACE("参数定义集 (ParamDefines): 为类型 '{}' 添加 {} 个参数定义。", type_tag_, new_definitions.size());

			// 批量操作：使用单次锁定完成整个操作
			{
				std::unique_lock lock(definitions_mutex_);
				for (const auto& def : new_definitions) {
					if (def.key.empty()) {
						LOG_ERROR("参数定义集 (ParamDefines): 无法添加键为空的参数定义 (类型标签: '{}')。", type_tag_);
						all_ok = false;
						continue;
					}
					// 直接操作 definitions_，避免递归锁定
					definitions_[def.key] = def;
					//LOG_TRACE("参数定义集 (ParamDefines) '{}': 已添加/更新参数定义 '{}'。", type_tag_, def.key);
				}
			}
			return all_ok;
		}

		const ParamDefine* ParamDefines::getEffectiveParamDefine(const std::string& key) const {
			std::shared_lock lock(definitions_mutex_);
			auto it = definitions_.find(key);
			if (it != definitions_.end()) {
				return &(it->second);
			}
			return nullptr;
		}

		bool ParamDefines::removeDefine(const std::string& key) {
			std::unique_lock lock(definitions_mutex_);
			if (definitions_.erase(key) > 0) {
				LOG_TRACE("参数定义集 (ParamDefines): 从类型 '{}' 移除了参数定义: '{}'", type_tag_, key);
				return true;
			}
			LOG_TRACE("参数定义集 (ParamDefines): 尝试从类型 '{}' 移除参数定义 '{}' 但未找到。", type_tag_, key);
			return false;
		}

		bool ParamDefines::hasParam(const std::string& key) const {
			std::shared_lock lock(definitions_mutex_);
			return definitions_.count(key) > 0;
		}

		std::vector<std::string> ParamDefines::getKeys() const {
			std::shared_lock lock(definitions_mutex_);
			std::vector<std::string> keys;
			keys.reserve(definitions_.size());
			for (const auto& pair : definitions_) {
				keys.push_back(pair.first);
			}
			return keys;
		}

		void ParamDefines::clear() {
			std::unique_lock lock(definitions_mutex_);
			LOG_TRACE("参数定义集 (ParamDefines): 清空类型 '{}' 的所有 {} 个参数定义。", type_tag_, definitions_.size());
			definitions_.clear();
		}

		size_t ParamDefines::size() const {
			std::shared_lock lock(definitions_mutex_);
			return definitions_.size();
		}

		void ParamDefines::merge(const ParamDefines& other_set, bool overwrite_existing) {
			if (this == &other_set) { // 检查是否是自我合并
				LOG_WARN("参数定义集 (ParamDefines): 尝试将类型标签 '{}' 的参数定义集与自身合并。操作已跳过。", this->type_tag_);
				return;
			}

			// 获取 other_set 的数据副本（避免死锁）
			std::map<std::string, ParamDefine> other_definitions_copy;
			{
				std::shared_lock other_lock(other_set.definitions_mutex_);
				other_definitions_copy = other_set.definitions_;
			}

			// 执行合并操作
			{
				std::unique_lock lock(definitions_mutex_);
				for (const auto& pair : other_definitions_copy) {
					const std::string& key = pair.first;
					const ParamDefine& other_def = pair.second;
					if (overwrite_existing) {
						definitions_[key] = other_def;
						//LOG_TRACE("  合并/覆盖键: '{}' (来自 '{}') 到 '{}'", key, other_set.type_tag_, this->type_tag_);
					}
					else {
						auto [it, inserted] = definitions_.try_emplace(key, other_def); // C++17, 更高效
						if (inserted) {
							LOG_TRACE("  合并/添加新键: '{}' (来自 '{}') 到 '{}'", key, other_set.type_tag_, this->type_tag_);
						} else {
							LOG_TRACE("  合并/跳过已有键: '{}' (来自 '{}')，因为在 '{}' 中已存在且 overwrite_existing=false", key, other_set.type_tag_, this->type_tag_);
						}
					}
				}
				LOG_DEBUG("参数定义集 (ParamDefines): 类型 '{}' 合并后包含 {} 个定义。", this->type_tag_, definitions_.size());
			}
		}

		// --- 静态工厂方法实现 ---

		std::optional<ParamDefines> ParamDefines::fromJson(
			const nlohmann::json& definitions_json,
			const std::string& type_tag_override,
			std::string* out_final_type_tag) {

			// 1. 确定最终的 type_tag
			std::string final_type_tag = type_tag_override;
			if (final_type_tag.empty()) {
				if (definitions_json.contains("type_tag") && definitions_json["type_tag"].is_string()) {
					final_type_tag = definitions_json["type_tag"].get<std::string>();
					LOG_TRACE("ParamDefines::fromJson - 从 JSON 对象内部获取到 type_tag: '{}'", final_type_tag);
				} else {
					LOG_ERROR("ParamDefines::fromJson - 未提供 type_tag_override 且 JSON 对象中缺少有效的 'type_tag' 字符串字段。");
					return std::nullopt;
				}
			}
			if (final_type_tag.empty()) {
				LOG_ERROR("ParamDefines::fromJson - 最终确定的 type_tag 为空.");
				return std::nullopt;
			}

			// 设置输出参数
			if (out_final_type_tag) {
				*out_final_type_tag = final_type_tag;
			}

			LOG_DEBUG("ParamDefines::fromJson - 开始为类型标签 '{}' 从 JSON 对象解析参数定义...", final_type_tag);

			// 2. 检查 JSON 对象是否包含 "parameters" 数组
			if (!definitions_json.contains("parameters") || !definitions_json["parameters"].is_array()) {
				LOG_ERROR("ParamDefines::fromJson - 类型标签 '{}' 的 JSON 定义缺少 'parameters' 数组或其不是数组。", final_type_tag);
				return std::nullopt;
			}
			const auto& params_array = definitions_json["parameters"];
			LOG_TRACE("ParamDefines::fromJson - 类型标签 '{}' 包含 {} 个参数定义条目。", final_type_tag, params_array.size());

			ParamDefines new_defines(final_type_tag);
			bool all_entries_ok = true; // 用于跟踪是否有任何条目解析失败

			// 3. 解析每个参数定义
			for (const auto& param_entry_json : params_array) {
				if (!param_entry_json.is_object()) {
					LOG_WARN("ParamDefines::fromJson - 类型标签 '{}' 的 'parameters' 数组中发现一个非对象条目，已跳过。", final_type_tag);
					continue; // 跳过此条目
				}

				ParamDefine pd; // 创建一个临时的 ParamDefine 对象
				std::string key_str;

				// 解析 key
				if (param_entry_json.contains("key") && param_entry_json["key"].is_string()) {
					key_str = param_entry_json["key"].get<std::string>();
				} else {
					LOG_WARN("ParamDefines::fromJson - 类型标签 '{}' 的参数定义缺少有效的 'key' 字符串，已跳过。JSON: {}", final_type_tag, param_entry_json.dump(2));
					all_entries_ok = false;
					continue;
				}
				if (key_str.empty()) {
					LOG_WARN("ParamDefines::fromJson - 类型标签 '{}' 的参数定义 'key' 为空字符串，已跳过。JSON: {}", final_type_tag, param_entry_json.dump(2));
					all_entries_ok = false;
					continue;
				}
				pd.key = key_str;
				LOG_TRACE("ParamDefines::fromJson - 处理参数定义 key='{}' (类型标签 '{}')", pd.key, final_type_tag);

				// 解析基本字段
				pd.name = param_entry_json.value("name", pd.key);
				pd.description = param_entry_json.value("description", "");
				pd.defined_in_type_tag_ = final_type_tag; // 记录定义来源

				// 解析类型和枚举信息（这里只是简化版本，完整逻辑需要更多代码）
				// 为了简化，我们先实现基本的类型解析
				if (param_entry_json.contains("enum_type") && param_entry_json["enum_type"].is_string()) {
					pd.enum_type_name_ = param_entry_json["enum_type"].get<std::string>();
					pd.type = typeid(std::string);
				} else {
					std::string type_str = param_entry_json.value("type", "unknown");
					auto type_idx_opt = NSParams::stringToTypeIndex(type_str);
					if (type_idx_opt.has_value()) {
						pd.type = *type_idx_opt;
					} else {
						LOG_WARN("ParamDefines::fromJson - 参数 '{}' (类型标签 '{}') 具有未知的类型字符串 '{}'。将使用 typeid(nullptr_t)。", pd.key, final_type_tag, type_str);
						pd.type = typeid(nullptr_t);
					}
				}

				// 解析默认值（简化版本）
				if (param_entry_json.contains("default")) {
					const auto& default_json_val = param_entry_json["default"];
					auto [parsed_default_val, err_def_val] = NSParams::jsonToParamValue(default_json_val, pd.type);
					if (err_def_val == ParamJsonError::Success) {
						pd.default_value = parsed_default_val;
					} else {
						LOG_WARN("ParamDefines::fromJson - 参数 '{}' (类型标签 '{}') 的默认值解析失败。错误: {}. 将使用默认构造值。", pd.key, final_type_tag, make_error_code(err_def_val).message());
					}
				}

				// 解析约束（简化版本）
				if (param_entry_json.contains("constraints") && param_entry_json["constraints"].is_object()) {
					const auto& constraints_json = param_entry_json["constraints"];
					auto [parsed_constraints, err_con] = NSParams::jsonToParamConstraint(constraints_json);
					if (err_con == ParamJsonError::Success) {
						pd.constraints = parsed_constraints;
					} else {
						LOG_WARN("ParamDefines::fromJson - 解析参数 '{}' (类型标签 '{}') 的约束时失败。错误: {}.", pd.key, final_type_tag, make_error_code(err_con).message());
						all_entries_ok = false;
					}
				}

				// 添加到定义集
				if (!new_defines.addDefine(std::move(pd))) {
					LOG_WARN("ParamDefines::fromJson - 无法为类型标签 '{}' 添加参数定义 key='{}' (可能已存在或无效)。", final_type_tag, key_str);
					all_entries_ok = false;
				}
			} // 遍历 parameters 数组结束

			if (!all_entries_ok) {
				LOG_WARN("ParamDefines::fromJson - 类型标签 '{}' 的部分参数定义在从 JSON 对象解析时遇到问题。", final_type_tag);
			}
			LOG_DEBUG("ParamDefines::fromJson - 类型标签 '{}' 的参数定义从 JSON 对象解析完成，共添加 {} 个有效参数定义。", final_type_tag, new_defines.size());
			return new_defines;
		}

		// --- 辅助验证函数 ---
		/**
		 * @brief 检查JSON类型是否与参数定义类型兼容
		 * @param json_value JSON值
		 * @param target_type 目标类型
		 * @return 如果兼容则返回true
		 */
		bool isJsonTypeCompatible(const nlohmann::json& json_value, const std::type_index& target_type) {
			if (target_type == typeid(bool)) {
				return json_value.is_boolean();
			} else if (target_type == typeid(int) || target_type == typeid(long) || target_type == typeid(size_t)) {
				return json_value.is_number_integer();
			} else if (target_type == typeid(double) || target_type == typeid(float)) {
				return json_value.is_number();
			} else if (target_type == typeid(std::string)) {
				return json_value.is_string();
			} else if (target_type == typeid(std::vector<int>)) {
				return json_value.is_array() && (json_value.empty() || json_value[0].is_number_integer());
			} else if (target_type == typeid(std::vector<double>)) {
				return json_value.is_array() && (json_value.empty() || json_value[0].is_number());
			} else if (target_type == typeid(std::vector<std::string>)) {
				return json_value.is_array() && (json_value.empty() || json_value[0].is_string());
			} else if (target_type == typeid(std::vector<std::vector<double>>)) {
				return json_value.is_array() && (json_value.empty() ||
					(json_value[0].is_array() && (json_value[0].empty() || json_value[0][0].is_number())));
			} else if (target_type == typeid(std::vector<std::vector<std::string>>)) {
				return json_value.is_array() && (json_value.empty() ||
					(json_value[0].is_array() && (json_value[0].empty() || json_value[0][0].is_string())));
			} else if (target_type == typeid(Eigen::Vector2d) || target_type == typeid(Eigen::Vector3d)) {
				return json_value.is_array() && json_value.size() >= 2;
			} else if (target_type == typeid(Eigen::Quaterniond)) {
				return json_value.is_array() && json_value.size() == 4;
			}
			// 对于其他类型，假设兼容（保持向后兼容性）
			return true;
		}

		/**
		 * @brief 验证枚举值是否有效
		 * @param value 枚举值字符串
		 * @param enum_type_name 枚举类型名称
		 * @return 如果有效则返回true
		 */
		bool isValidEnumValue(const std::string& value, const std::string& enum_type_name) {
			// 实现真正的枚举值验证
			if (value.empty()) {
				LOG_TRACE("[参数验证] 枚举值为空，验证失败");
				return false;
			}

			try {
				// 使用NSUtils的枚举工具进行验证
				std::any enum_any = NSUtils::convertStringToEnumAny(enum_type_name, value, std::any{});

				if (!enum_any.has_value()) {
					LOG_WARN("[参数验证] 枚举类型'{}'中的值'{}'无效", enum_type_name, value);
					return false;
				}

				// 验证转换后的枚举值是否能正确转换回字符串
				std::string converted_back = NSUtils::enumToStringAny(enum_any, enum_type_name);
				if (converted_back.rfind("ERROR", 0) == 0 || converted_back.rfind("UNKNOWN_ENUM_TYPE", 0) == 0) {
					LOG_WARN("[参数验证] 枚举类型'{}'中的值'{}'转换验证失败：{}", enum_type_name, value, converted_back);
					return false;
				}

				LOG_TRACE("[参数验证] 枚举类型'{}'中的值'{}'验证通过", enum_type_name, value);
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("[参数验证] 枚举值验证异常：枚举类型'{}'，值'{}'，错误：{}", enum_type_name, value, e.what());
				return false;
			}
		}

		/**
		 * @brief 验证参数值是否在定义的范围内
		 * @param value 参数值
		 * @param definition 参数定义
		 * @return 如果在范围内则返回true
		 */
		bool validateValueRange(const ParamValue& value, const ParamDefine& definition) {
			// 检查是否有约束条件
			if (!definition.hasConstraints()) {
				LOG_TRACE("[参数验证] 参数无约束条件，验证通过");
				return true;
			}

			// 使用visitor模式处理不同的约束类型
			return std::visit([&value](const auto& constraint) -> bool {
				using ConstraintType = std::decay_t<decltype(constraint)>;

				if constexpr (std::is_same_v<ConstraintType, std::monostate>) {
					// 无约束
					return true;
				}
				else if constexpr (std::is_same_v<ConstraintType, NumericConstraint>) {
					// 数值约束检查
					if (std::holds_alternative<double>(value)) {
						double val = std::get<double>(value);
						bool valid = constraint.validate(val);
						if (!valid) {
							LOG_WARN("[参数验证] double值{:.6f}不满足数值约束", val);
						}
						return valid;
					} else if (std::holds_alternative<int>(value)) {
						int val = std::get<int>(value);
						bool valid = constraint.validate(val);
						if (!valid) {
							LOG_WARN("[参数验证] int值{}不满足数值约束", val);
						}
						return valid;
					} else {
						LOG_WARN("[参数验证] 数值约束应用于非数值类型，验证失败");
						return false;
					}
				}
				else if constexpr (std::is_same_v<ConstraintType, StringConstraint>) {
					// 字符串约束检查
					if (std::holds_alternative<std::string>(value)) {
						const std::string& val = std::get<std::string>(value);
						bool valid = constraint.validate(val);
						if (!valid) {
							LOG_WARN("[参数验证] 字符串值'{}'不满足字符串约束", val);
						}
						return valid;
					} else {
						LOG_WARN("[参数验证] 字符串约束应用于非字符串类型，验证失败");
						return false;
					}
				}
				else if constexpr (std::is_same_v<ConstraintType, VectorConstraint>) {
					// 向量约束检查
					if (std::holds_alternative<Eigen::Vector2d>(value)) {
						const auto& val = std::get<Eigen::Vector2d>(value);
						bool valid = constraint.validate(val);
						if (!valid) {
							LOG_WARN("[参数验证] Vector2d值[{:.3f},{:.3f}]不满足向量约束", val.x(), val.y());
						}
						return valid;
					} else if (std::holds_alternative<Eigen::Vector3d>(value)) {
						const auto& val = std::get<Eigen::Vector3d>(value);
						bool valid = constraint.validate(val);
						if (!valid) {
							LOG_WARN("[参数验证] Vector3d值[{:.3f},{:.3f},{:.3f}]不满足向量约束", val.x(), val.y(), val.z());
						}
						return valid;
					} else {
						LOG_WARN("[参数验证] 向量约束应用于非向量类型，验证失败");
						return false;
					}
				}
				else if constexpr (std::is_same_v<ConstraintType, VectorSizeConstraint>) {
					// 向量大小约束检查
					bool valid = false;
					std::visit([&constraint, &valid](const auto& vec_val) {
						using ValueType = std::decay_t<decltype(vec_val)>;
						if constexpr (std::is_same_v<ValueType, std::vector<int>> ||
									  std::is_same_v<ValueType, std::vector<double>> ||
									  std::is_same_v<ValueType, std::vector<bool>> ||
									  std::is_same_v<ValueType, std::vector<std::string>> ||
									  std::is_same_v<ValueType, std::vector<std::vector<double>>>) {
							valid = constraint.validate(vec_val);
							if (!valid) {
								LOG_WARN("[参数验证] 向量大小{}不满足大小约束", vec_val.size());
							}
						}
					}, value);

					if (!valid && (std::holds_alternative<std::vector<int>>(value) ||
								   std::holds_alternative<std::vector<double>>(value) ||
								   std::holds_alternative<std::vector<bool>>(value) ||
								   std::holds_alternative<std::vector<std::string>>(value) ||
								   std::holds_alternative<std::vector<std::vector<double>>>(value))) {
						LOG_WARN("[参数验证] 向量大小约束验证失败");
					} else if (!valid) {
						LOG_WARN("[参数验证] 向量大小约束应用于非向量类型，验证失败");
					}
					return valid;
				}
				else {
					LOG_WARN("[参数验证] 遇到未知的约束类型，验证失败");
					return false;
				}
			}, definition.constraints);
		}

		// 辅助函数：将 ParamValue 转换为字符串以供日志记录 (桩实现)
		std::string paramValueToString(const ParamValue& value) {
			// 这是一个基础的桩实现，后续可以扩展以提供更详细的输出
			return std::visit([](const auto& v) -> std::string {
				using T = std::decay_t<decltype(v)>;
				if constexpr (std::is_same_v<T, std::monostate>) { return "[monostate]"; }
				else if constexpr (std::is_same_v<T, int>) { return std::to_string(v); }
				else if constexpr (std::is_same_v<T, double>) { return std::to_string(v); }
				else if constexpr (std::is_same_v<T, bool>) { return v ? "true" : "false"; }
				else if constexpr (std::is_same_v<T, std::string>) { return "\"" + v + "\""; }
				else { return "[complex_type_or_vector_not_fully_logged]"; }
			}, value);
		}

		using json = nlohmann::json; // 别名

		// --- ParamValues 方法实现 ---

		ParamValues::ParamValues(std::string type_tag) : type_tag_(std::move(type_tag)) {
			LOG_TRACE("ParamValues instance created for type_tag '{}' at {}", type_tag_, fmt::ptr(this));
		}

		ParamValues::ParamValues(const ParamValues& other)
			: type_tag_(other.type_tag_),
			  values_(other.values_)
		{
			LOG_TRACE("ParamValues instance copy-constructed for type_tag '{}' from {} to {} ({} values)",
					  type_tag_, fmt::ptr(&other), fmt::ptr(this), values_.size());
		}

		ParamValues& ParamValues::operator=(const ParamValues& other) {
			if (this != &other) {
				type_tag_ = other.type_tag_;
				values_ = other.values_;
				LOG_TRACE("ParamValues instance copy-assigned for type_tag '{}' from {} to {} ({} values)",
						  type_tag_, fmt::ptr(&other), fmt::ptr(this), values_.size());
			}
			return *this;
		}

		ParamValues::ParamValues(ParamValues&& other) noexcept
			: type_tag_(std::move(other.type_tag_)),
			  values_(std::move(other.values_))
		{
			LOG_TRACE("ParamValues instance move-constructed for type_tag '{}' from {} to {} ({} values)",
					  type_tag_, fmt::ptr(&other), fmt::ptr(this), values_.size());
			// other is now in a valid but unspecified state
		}

		ParamValues& ParamValues::operator=(ParamValues&& other) noexcept {
			if (this != &other) {
				type_tag_ = std::move(other.type_tag_);
				values_ = std::move(other.values_);
				LOG_TRACE("ParamValues instance move-assigned for type_tag '{}' from {} to {} ({} values)",
						  type_tag_, fmt::ptr(&other), fmt::ptr(this), values_.size());
				// other is now in a valid but unspecified state
			}
			return *this;
		}

		ParamValues::~ParamValues() {
			LOG_TRACE("ParamValues instance destroyed for type_tag '{}' at {} (had {} values)",
					  type_tag_, fmt::ptr(this), values_.size());
		}

		const std::string& ParamValues::getTypeTag() const {
			return type_tag_;  // type_tag_ 是只读的，无需锁保护
		}

		size_t ParamValues::size() const {
			std::shared_lock lock(values_mutex_);
			return values_.size();
		}

		std::optional<ParamValue> ParamValues::getValueVariant(const std::string& key) const {
			std::shared_lock lock(values_mutex_);
			auto it = values_.find(key);
			return (it != values_.end()) ? std::optional<ParamValue>{it->second} : std::nullopt;
		}

		bool ParamValues::hasParam(const std::string& key) const {
			std::shared_lock lock(values_mutex_);
			return values_.count(key);
		}

		std::unordered_map<std::string, ParamValue> ParamValues::getValues() const {
			std::shared_lock lock(values_mutex_);
			return values_;  // 返回副本，确保线程安全
		}

		void ParamValues::clear() {
			std::unique_lock lock(values_mutex_);
			values_.clear();
		}

		bool ParamValues::setValueVariant(const std::string& key, ParamValue val, const ParamRegistry& registry) {
			const ParamDefine* p_define = registry.getEffectiveParamDefine(type_tag_, key);

			if (p_define) { // 检查指针是否有效
				std::type_index actual_value_type = std::visit(
					[](const auto& PValue) { return std::type_index(typeid(PValue)); },
					val
				);
				if (p_define->type != actual_value_type) {
					LOG_WARN("参数值集 (ParamValues): 设置参数 '{}' ('{}') 时类型不匹配。定义期望类型: '{}' (来自 ParamDefine), 实际值类型: '{}'.",
							 key, type_tag_, typeIndexToString(p_define->type), typeIndexToString(actual_value_type));
					// 根据策略，这里可以选择返回 false，或者尝试转换（如果支持），或者允许（如果 variant 兼容）
					// 当前实现为警告后仍然设置，这可能不是最佳实践，除非类型系统允许隐式转换或variant赋值。
				}
				LOG_TRACE("参数值集 (ParamValues): 参数 '{}' ('{}') 的值已更新 (类型索引: {}, 类型名: {}).", key, type_tag_, val.index(), typeIndexToString(actual_value_type));

				// 线程安全的写操作
				{
					std::unique_lock lock(values_mutex_);
					values_[key] = std::move(val);
				}
				return true;
			} else {
				LOG_WARN("参数值集 (ParamValues): 尝试为键 '{}' ('{}') 设置值，但在注册表中找不到其参数定义。该参数可能未定义或类型标签错误。值仍被设置，但无类型检查或约束。", key, type_tag_);

				// 线程安全的写操作
				{
					std::unique_lock lock(values_mutex_);
					values_[key] = std::move(val);
				}
				return true; // 当前行为：允许设置未知参数，但有警告
			}
		}

		bool ParamValues::checkConstraints(const std::string& key, const ParamValue& value_to_check, const ParamRegistry& registry) {
			const ParamDefine* p_define = registry.getEffectiveParamDefine(type_tag_, key);

			if (!p_define) {
				LOG_TRACE("参数值集 (ParamValues): 约束检查: 未找到参数 '{}' (类型 '{}') 的定义，跳过约束检查。", key, type_tag_);
				return true;
			}
			const ParamDefine& define_ref = *p_define;
			const ParamConstraint& constraint_variant = define_ref.constraints;

			// Visitor for ParamConstraint variant, pass define_ref and constraint_variant for logging if needed
			bool valid = std::visit([&value_to_check, &key, &define_ref, &constraint_variant](const auto& constraint_instance) -> bool {
				using ConstraintType = std::decay_t<decltype(constraint_instance)>;

				if constexpr (std::is_same_v<ConstraintType, std::monostate>) {
					return true; // No constraint
				}
				else if constexpr (std::is_same_v<ConstraintType, NumericConstraint>) {
					if (const int* v_int = std::get_if<int>(&value_to_check)) { return constraint_instance.validate(*v_int); }
					else if (const double* v_double = std::get_if<double>(&value_to_check)) { return constraint_instance.validate(*v_double); }
					else {
						LOG_WARN("参数值集 (ParamValues): 约束检查: 参数 '{}' (定义类型 '{}') 期望数值类型以应用NumericConstraint，实际值类型索引 {} (值: {}).",
								 key, typeIndexToString(define_ref.type), value_to_check.index(), paramValueToString(value_to_check));
						return false;
					}
				}
				else if constexpr (std::is_same_v<ConstraintType, StringConstraint>) {
					if (const std::string* v_str = std::get_if<std::string>(&value_to_check)) { return constraint_instance.validate(*v_str); }
					else {
						LOG_WARN("参数值集 (ParamValues): 约束检查: 参数 '{}' (定义类型 '{}') 期望字符串类型以应用StringConstraint，实际值类型索引 {} (值: {}).",
								 key, typeIndexToString(define_ref.type), value_to_check.index(), paramValueToString(value_to_check));
						return false;
					}
				}
				else if constexpr (std::is_same_v<ConstraintType, VectorConstraint>) {
					if (const Eigen::Vector2d* v_v2d = std::get_if<Eigen::Vector2d>(&value_to_check)) { return constraint_instance.validate(*v_v2d); }
					else if (const Eigen::Vector3d* v_v3d = std::get_if<Eigen::Vector3d>(&value_to_check)) { return constraint_instance.validate(*v_v3d); }
					// 注意：VectorConstraint 目前没有为 Eigen::Quaterniond 实现 validate。如果需要，应添加。
					else {
						LOG_WARN("参数值集 (ParamValues): 约束检查: 参数 '{}' (定义类型 '{}') 期望Eigen::Vector2d/3d类型以应用VectorConstraint，实际值类型索引 {} (值: {}).",
								 key, typeIndexToString(define_ref.type), value_to_check.index(), paramValueToString(value_to_check));
						return false;
					}
				}
				else if constexpr (std::is_same_v<ConstraintType, VectorSizeConstraint>) {
					bool ok = false;
					std::visit([&](const auto& vec_val) {
						using HeldValueType = std::decay_t<decltype(vec_val)>;
						if constexpr (std::is_same_v<HeldValueType, std::vector<int>>) {
							ok = constraint_instance.validate(vec_val);
						} else if constexpr (std::is_same_v<HeldValueType, std::vector<double>>) {
							ok = constraint_instance.validate(vec_val);
						} else if constexpr (std::is_same_v<HeldValueType, std::vector<bool>>) {
							ok = constraint_instance.validate(vec_val);
						} else if constexpr (std::is_same_v<HeldValueType, std::vector<std::string>>) {
							ok = constraint_instance.validate(vec_val);
						} else if constexpr (std::is_same_v<HeldValueType, std::vector<std::vector<double>>>) {
							ok = constraint_instance.validate(vec_val);
						} else {
							// For other types in ParamValue (Eigen types, scalars, monostate, etc.),
							// 'ok' remains false, as VectorSizeConstraint is not applicable.
						}
						}, value_to_check);

					if (!ok) {
						if (std::holds_alternative<std::vector<int>>(value_to_check) ||
							std::holds_alternative<std::vector<double>>(value_to_check) ||
							std::holds_alternative<std::vector<bool>>(value_to_check) ||
							std::holds_alternative<std::vector<std::string>>(value_to_check) ||
							std::holds_alternative<std::vector<std::vector<double>>>(value_to_check)
						) {
							LOG_WARN("参数值集 (ParamValues): 约束检查: 参数 '{}' (定义类型 '{}') 的 std::vector 大小不满足 VectorSizeConstraint. 值为: {}",
									 key, typeIndexToString(define_ref.type), paramValueToString(value_to_check));
						} else {
							LOG_WARN("参数值集 (ParamValues): 约束检查: 参数 '{}' (定义类型 '{}') 期望 std::vector 类型以应用VectorSizeConstraint，实际值类型索引 {} (值: {}).",
									 key, typeIndexToString(define_ref.type), value_to_check.index(), paramValueToString(value_to_check));
						}
					}
					return ok;
				}
				else {
					LOG_WARN("参数值集 (ParamValues): 约束检查: 参数 '{}' (定义类型 '{}') 遇到未处理的约束类型。约束类型索引 {}。",
							 key, typeIndexToString(define_ref.type), constraint_variant.index());
					return false;
				}
				}, constraint_variant);

			if (!valid) {
				LOG_ERROR("参数值集 (ParamValues): 参数 '{}' (定义类型 '{}', 当前值: {}) 不满足其在参数定义中的约束条件 (此定义来自类型标签 '{}', 源于 '{}').",
						  key, typeIndexToString(define_ref.type), paramValueToString(value_to_check),
						  type_tag_ /*ParamValues's type_tag*/, define_ref.defined_in_type_tag_.value_or("unknown_source_tag"));
			}
			return valid;
		}

		bool ParamValues::setValueFromJson(const std::string& key, const nlohmann::json& json_value, const ParamDefine& definition, const ParamRegistry& registry) {
			LOG_TRACE("ParamValues::setValueFromJson (TypeTag: '{}'): 尝试为键 '{}' 从 JSON 设置值. 定义类型: '{}', JSON 类型: {}",
					  this->type_tag_, key, typeIndexToString(definition.type), json_value.type_name());

			// 首先进行基本的类型兼容性检查
			if (!isJsonTypeCompatible(json_value, definition.type)) {
				LOG_ERROR("ParamValues::setValueFromJson (TypeTag: '{}'): 参数 '{}' 的 JSON 类型 {} 与定义类型 '{}' 不兼容",
						  this->type_tag_, key, json_value.type_name(), typeIndexToString(definition.type));
				return false;
			}

			if (definition.hasEnumType() && definition.getEnumTypeName()) {
				// --- 枚举类型的特殊处理 ---
				if (json_value.is_string()) {
					std::string incoming_str = json_value.get<std::string>();

					// 验证枚举值是否有效
					if (!isValidEnumValue(incoming_str, *definition.getEnumTypeName())) {
						LOG_ERROR("ParamValues::setValueFromJson (TypeTag: '{}'): 枚举参数 '{}' 的值 '{}' 不是有效的枚举值",
								  this->type_tag_, key, incoming_str);
						return false;
					}

					// NSUtils::convertStringToEnumAny 会处理空字符串并应用编译期默认值
					std::any result_any = NSUtils::convertStringToEnumAny(
						*definition.getEnumTypeName(),
						incoming_str,
						std::any{} // 传递空的any，让工具函数应用其注册的默认值
					);

					if (result_any.has_value()) {
						std::string string_to_store = NSUtils::enumToStringAny(result_any, *definition.getEnumTypeName());
						if (string_to_store.rfind("ERROR", 0) != 0 && string_to_store.rfind("UNKNOWN_ENUM_TYPE", 0) != 0) {
							// setValueVariant 将进行约束检查
							return setValueVariant(key, string_to_store, registry);
						} else {
							LOG_WARN("ParamValues::setValueFromJson (TypeTag: '{}'): 枚举参数 '{}' (类型 '{}'): JSON 值 '{}' 无法转换为规范字符串 (结果: '{}')。参数未设置。",
								this->type_tag_, key, *definition.getEnumTypeName(), incoming_str, string_to_store);
							return false;
						}
					} else { // convertStringToEnumAny 返回空 any (理论上不应发生，因其会用默认值)
						LOG_ERROR("ParamValues::setValueFromJson (TypeTag: '{}'): NSUtils::convertStringToEnumAny 为参数 '{}' (枚举类型 '{}') 和 JSON 值 '{}' 返回空结果。参数未设置。",
							this->type_tag_, key, *definition.getEnumTypeName(), incoming_str);
						return false;
					}
				} else { // JSON 值对枚举类型不是字符串 - 严格模式下视为错误
					LOG_ERROR("ParamValues::setValueFromJson (TypeTag: '{}'): 参数 '{}' (枚举类型 '{}') 在 ParamDefine 中定义为枚举，但提供的 JSON 值不是字符串 (实际类型: {})。参数未设置。",
						this->type_tag_, key, *definition.getEnumTypeName(), json_value.type_name());
					return false; // 严格模式：不再尝试使用 definition.default_value
				}
			} else {
				// --- 非枚举类型的通用处理 ---
				// 使用 NSParams::jsonToParamValue 进行转换
				auto [value_to_set, conversion_error] = NSParams::jsonToParamValue(json_value, definition.type);
				if (conversion_error == ParamJsonError::Success) {
					// 在设置之前进行范围验证
					if (!validateValueRange(value_to_set, definition)) {
						LOG_ERROR("ParamValues::setValueFromJson (TypeTag: '{}'): 参数 '{}' 的值超出了定义的范围约束",
								  this->type_tag_, key);
						return false;
					}

					// 线程安全的写操作，直接设置值（避免重复查询ParamDefines）
					{
						std::unique_lock lock(values_mutex_);
						values_[key] = std::move(value_to_set);
					}

					LOG_TRACE("参数值集 (ParamValues): 参数 '{}' ('{}') 的值已更新 (直接设置，避免重复查询).", key, type_tag_);
					return true;
				} else {
					LOG_ERROR("ParamValues::setValueFromJson (TypeTag: '{}'): 无法为键 '{}' 将 JSON 值 '{}' (JSON 类型: {}) 转换为 ParamDefine 中指定的类型 '{}'. 错误: {}. 参数未设置。",
							  this->type_tag_, key, json_value.dump(2), json_value.type_name(), typeIndexToString(definition.type), make_error_code(conversion_error).message());
					return false;
				}
			}
		}

		bool ParamValues::setValueFromJsonGuessType(const std::string& key, const nlohmann::json& json_value, const ParamRegistry& registry) {
			LOG_TRACE("参数值集 (ParamValues): 尝试通过猜测类型为键 '{}' ('{}') 设置值，JSON 类型: {}", key, type_tag_, json_value.type_name());

			// 优先使用 ParamDefine (如果存在于注册表中)
			const ParamDefine* p_define_guess = registry.getEffectiveParamDefine(type_tag_, key);
			if (p_define_guess) {
				LOG_TRACE("参数值集 (ParamValues): 键 '{}' ('{}') 在注册表中找到定义，将使用定义类型 '{}' 进行 JSON 解析。",
						  key, type_tag_, typeIndexToString(p_define_guess->type));
				return setValueFromJson(key, json_value, *p_define_guess, registry);
			}

			LOG_TRACE("参数值集 (ParamValues): 键 '{}' ('{}') 在注册表中未找到定义，继续纯猜测类型。", key, type_tag_);

			ParamValue target_value;
			ParamJsonError conversion_err = ParamJsonError::InvalidType; // 默认为不支持，如果没有任何转换成功

			if (json_value.is_boolean()) {
				auto [val, err] = NSParams::jsonToParamValue(json_value, typeid(bool));
				if (err == ParamJsonError::Success) { target_value = val; conversion_err = ParamJsonError::Success; }
			}
			else if (json_value.is_number_integer()) {
				auto [val, err] = NSParams::jsonToParamValue(json_value, typeid(int));
				if (err == ParamJsonError::Success) { target_value = val; conversion_err = ParamJsonError::Success; }
			}
			else if (json_value.is_number_float()) {
				auto [val, err] = NSParams::jsonToParamValue(json_value, typeid(double));
				if (err == ParamJsonError::Success) { target_value = val; conversion_err = ParamJsonError::Success; }
			}
			else if (json_value.is_string()) {
				auto [val, err] = NSParams::jsonToParamValue(json_value, typeid(std::string));
				if (err == ParamJsonError::Success) { target_value = val; conversion_err = ParamJsonError::Success; }
			}
			else if (json_value.is_array()) {
				// 尝试猜测数组的内部类型。这部分比较复杂，因为 param_json::jsonToParamValue 需要确切的 type_index。
				// 简单策略：如果数组非空，检查第一个元素
				if (!json_value.empty()) {
					const auto& first_element = json_value.front();
					if (first_element.is_number_integer()) {
						auto [val, err] = NSParams::jsonToParamValue(json_value, typeid(std::vector<int>));
						if (err == ParamJsonError::Success) { target_value = val; conversion_err = ParamJsonError::Success; }
					}
					else if (first_element.is_number_float()) { // 包括普通浮点数和可能是vector<vector<double>>的内层数组
						// 尝试 vector<double> 先
						auto [val_vd, err_vd] = NSParams::jsonToParamValue(json_value, typeid(std::vector<double>));
						if (err_vd == ParamJsonError::Success) {
							target_value = val_vd;
							conversion_err = ParamJsonError::Success;
						} else {
							// 如果 vector<double> 失败，并且第一个元素是数组，则尝试 vector<vector<double>>
							if (first_element.is_array()) {
								auto [val_vvd, err_vvd] = NSParams::jsonToParamValue(json_value, typeid(std::vector<std::vector<double>>));
								if (err_vvd == ParamJsonError::Success) {
									target_value = val_vvd;
									conversion_err = ParamJsonError::Success;
								}
							}
						}
					}
					else if (first_element.is_boolean()) {
						auto [val, err] = NSParams::jsonToParamValue(json_value, typeid(std::vector<bool>));
						if (err == ParamJsonError::Success) { target_value = val; conversion_err = ParamJsonError::Success; }
					}
					else if (first_element.is_string()) {
						// 尝试 vector<string> 先
						auto [val_vs, err_vs] = NSParams::jsonToParamValue(json_value, typeid(std::vector<std::string>));
						if (err_vs == ParamJsonError::Success) {
							target_value = val_vs;
							conversion_err = ParamJsonError::Success;
						} else {
							// 如果 vector<string> 失败，并且第一个元素是数组，则尝试 vector<vector<string>>
							if (first_element.is_array()) {
								auto [val_vvs, err_vvs] = NSParams::jsonToParamValue(json_value, typeid(std::vector<std::vector<std::string>>));
								if (err_vvs == ParamJsonError::Success) {
									target_value = val_vvs;
									conversion_err = ParamJsonError::Success;
								}
							}
						}
					}
				}
				// 猜测 Eigen 类型 (如果 conversion_err 仍然不是 Success 且大小匹配)
				if (conversion_err != ParamJsonError::Success && json_value.size() == 2) {
					auto [val, err] = NSParams::jsonToParamValue(json_value, typeid(Eigen::Vector2d));
					if (err == ParamJsonError::Success) { target_value = val; conversion_err = ParamJsonError::Success; }
				}
				if (conversion_err != ParamJsonError::Success && json_value.size() == 3) {
					auto [val, err] = NSParams::jsonToParamValue(json_value, typeid(Eigen::Vector3d));
					if (err == ParamJsonError::Success) { target_value = val; conversion_err = ParamJsonError::Success; }
				}
				if (conversion_err != ParamJsonError::Success && json_value.size() == 4) { // For Quaterniond
					auto [val, err] = NSParams::jsonToParamValue(json_value, typeid(Eigen::Quaterniond));
					if (err == ParamJsonError::Success) { target_value = val; conversion_err = ParamJsonError::Success; }
				}
			} else if (json_value.is_null()) {
                auto [val, err] = NSParams::jsonToParamValue(json_value, typeid(std::monostate));
                if (err == ParamJsonError::Success) { target_value = val; conversion_err = ParamJsonError::Success; }
            }

			if (conversion_err == ParamJsonError::Success) {
				LOG_TRACE("参数值集 (ParamValues): 键 '{}' ('{}'): JSON 类型 {} 已猜测并转换为 C++ 类型索引 {} (类型名: {}).",
						  key, type_tag_, json_value.type_name(), target_value.index(),
						  typeIndexToString(std::visit([](auto&& arg){ return std::type_index(typeid(arg)); }, target_value)));
				return setValueVariant(key, target_value, registry);
			}
			else {
				LOG_WARN("参数值集 (ParamValues): 无法从 JSON 值 (类型: {}) 猜测并转换为键 '{}' ('{}') 支持的 ParamValue 类型。JSON: {}",
						 json_value.type_name(), key, type_tag_, json_value.dump(2));
				return false;
			}
		}

		bool ParamValues::valuesFromJson(const json& params_json_obj,
			const ParamRegistry& registry,
			const std::map<std::string, ParamDefine>* effective_defines_map_ptr, // Renamed for clarity
			bool is_instance_specific, // Parameter kept for potential future use in logging/error handling
			bool guess_types_for_undefined) {
			if (!params_json_obj.is_object()) {
				LOG_ERROR("(TypeTag: '{}', InstanceSpecific: {}): 输入的 JSON 不是一个对象。类型: {}",
						  this->type_tag_, is_instance_specific, params_json_obj.type_name());
				return false;
			}

			//LOG_TRACE("(TypeTag: '{}', InstanceSpecific: {}, Guess: {}): 开始从 JSON 对象加载 {} 个条目.",
					  //this->type_tag_, is_instance_specific, guess_types_for_undefined, params_json_obj.size());

			bool all_ok = true;
			std::shared_ptr<const ParamDefines> local_effective_defines_ptr; // New: to hold shared_ptr

			// Determine which map of definitions to use
			const std::map<std::string, ParamDefine>* current_defines_map = effective_defines_map_ptr;
			if (!current_defines_map) {
				LOG_DEBUG("(TypeTag: '{}'): 未提供 effective_defines_map, 将从注册表获取类型标签 '{}' 的有效定义.", this->type_tag_, this->type_tag_);
				local_effective_defines_ptr = registry.getOrBuildEffectiveParamDefines(this->type_tag_);

				if (local_effective_defines_ptr) {
					current_defines_map = &(local_effective_defines_ptr->getDefinitions());
					LOG_TRACE("(TypeTag: '{}'): 成功获取 {} 条有效定义.", this->type_tag_, current_defines_map->size());
				} else {
					LOG_WARN(" (TypeTag: '{}'): 无法获取类型标签 '{}' 的有效参数定义集. 如果不允许猜测，许多参数可能无法加载.", this->type_tag_, this->type_tag_);
					// current_defines_map 保持 nullptr
				}
			}

			for (auto it = params_json_obj.items().begin(); it != params_json_obj.items().end(); ++it) {
				const std::string& key = it.key();
				const json& json_value = it.value();
				const ParamDefine* define = nullptr;

				if (current_defines_map) {
					auto def_it = current_defines_map->find(key);
					if (def_it != current_defines_map->end()) {
						define = &(def_it->second);
					}
				}

				if (define) {
					LOG_TRACE(" (TypeTag: '{}'): 键 '{}' 找到定义, 定义类型: '{}'", this->type_tag_, key, typeIndexToString(define->type));
					if (!setValueFromJson(key, json_value, *define, registry)) {
						LOG_WARN("(TypeTag: '{}', InstanceSpecific: {}): 从 JSON 加载键 '{}' 的值失败 (使用定义)。",
								 this->type_tag_, is_instance_specific, key);
						all_ok = false;
					}
				} else if (guess_types_for_undefined) {
					LOG_WARN("(TypeTag: '{}', InstanceSpecific: {}): 键 '{}' 无定义，尝试猜测类型。",
							  this->type_tag_, is_instance_specific, key);
					if (!setValueFromJsonGuessType(key, json_value, registry)) {
						LOG_WARN("(TypeTag: '{}', InstanceSpecific: {}): 从 JSON 加载键 '{}' 的值失败 (猜测类型)。",
								 this->type_tag_, is_instance_specific, key);
						all_ok = false;
					}
				} else {
					LOG_ERROR("(TypeTag: '{}', InstanceSpecific: {}): 键 '{}' 无定义，且不允许猜测类型，参数未加载且视为错误。",
							 this->type_tag_, is_instance_specific, key);
					// all_ok 保持不变，但调用者可能基于此错误决定是否继续，或者 all_ok 应设为 false
					// 暂时维持 all_ok 不变，因为"跳过"本身不中断流程，但日志级别已提升
					// 如果希望此情况导致 valuesFromJson 返回 false，则应设置 all_ok = false;
					all_ok = false; // 将此情况视为整体加载失败的一部分
				}
			}
			LOG_TRACE("(TypeTag: '{}', InstanceSpecific: {}, Guess: {}): JSON 对象加载完成. All OK: {}.",
					  this->type_tag_, is_instance_specific, guess_types_for_undefined, all_ok);
			return all_ok;
		}

		json ParamValues::valuesToJson(const std::vector<std::string>& keys_to_export) const {
			json result_json = json::object();
			std::vector<std::string> actual_keys_to_export = keys_to_export;

			// 线程安全的读操作
			{
				std::shared_lock lock(values_mutex_);

				if (actual_keys_to_export.empty()) { // 如果未指定键，则导出所有键
					LOG_TRACE("参数值集 (ParamValues): valuesToJson - 未指定键，将导出类型标签 '{}' 的所有 {} 个参数。", type_tag_, values_.size());
					for (const auto& pair : values_) {
						actual_keys_to_export.push_back(pair.first);
					}
					std::sort(actual_keys_to_export.begin(), actual_keys_to_export.end()); // 保证顺序
				} else {
					LOG_TRACE("参数值集 (ParamValues): valuesToJson - 为类型标签 '{}' 导出指定的 {} 个键。", type_tag_, actual_keys_to_export.size());
				}

				for (const std::string& key : actual_keys_to_export) {
					auto it = values_.find(key);
					if (it != values_.end()) {
						// MODIFIED: Use NSParams::paramValueToJson from param_json.h
						result_json[key] = NSParams::paramValueToJson(it->second);
					} else {
						LOG_WARN("参数值集 (ParamValues): valuesToJson - 请求导出键 '{}' (类型标签 '{}')，但在当前值映射中未找到。", key, type_tag_);
					}
				}
			}
			return result_json;
		}

		json ParamValues::valuesToJson() const { // 重载版本，导出所有参数
			return valuesToJson(std::vector<std::string>());
		}

		bool ParamValues::loadFromJson(const json& json_data, const ParamRegistry& registry) {
			LOG_TRACE("参数值集 (ParamValues): 开始从 JSON 加载 (类型标签: '{}'), 将清除现有值。JSON: {}", type_tag_, json_data.dump(2));
			clear();
			// For loadFromJson, we typically don't want to guess types for undefined parameters.
			// effective_defines_map is nullptr so it will be fetched based on this->type_tag_.
			// is_instance_specific can be false by default for a full load.
			return valuesFromJson(json_data, registry, nullptr, false, false);
		}

		std::shared_ptr<ParamValues> ParamValues::clone() const {
			LOG_TRACE("参数值集 (ParamValues): 克隆 ParamValues (类型标签: '{}')。", type_tag_);
			auto new_values = std::make_shared<ParamValues>(this->type_tag_);

			// 线程安全的读操作
			{
				std::shared_lock lock(values_mutex_);
				new_values->values_ = this->values_; // 直接复制 map
			}
			return new_values;
		}

		bool ParamValues::loadDefaults(const ParamRegistry& registry) {
			LOG_TRACE("参数值集 (ParamValues): 开始为类型标签 '{}' 加载默认值。", type_tag_);
			if (type_tag_.empty()) {
				LOG_WARN("参数值集 (ParamValues): 类型标签为空，无法加载默认值。", type_tag_);
				return false;
			}

			// 使用 getOrBuildEffectiveParamDefines 并处理 optional
			std::shared_ptr<const ParamDefines> effective_defs_ptr = registry.getOrBuildEffectiveParamDefines(type_tag_);

			if (effective_defs_ptr) {
				const ParamDefines& defs = *effective_defs_ptr;
				LOG_INFO("ParamValues::loadDefaults (TypeTag: '{}'): Found {} effective definitions to process for defaults.", type_tag_, defs.getDefinitions().size());
				// 线程安全的批量默认值加载
				{
					std::unique_lock lock(values_mutex_);
					for (const auto& pair : defs.getDefinitions()) {
						const std::string& key = pair.first;
						const ParamDefine& def = pair.second;

						if (values_.find(key) == values_.end()) { // 如果值中尚无此参数（在锁保护下检查）
							// C2672 on std::visit and std::holds_alternative:
							// 确保 def.default_value 是一个有效的 ParamValue variant
							// std::visit 的访问器应该可以处理 ParamValue 中的所有类型，或者这里我们只关心它是否为 monostate
							bool is_default_monostate_direct = std::holds_alternative<std::monostate>(def.default_value);

							if (!is_default_monostate_direct) { // 使用更简单直接的方式
								// LOG_TRACE("Attempting to set default for key '{}' using its definition.", key);
								// 直接设置值，避免递归锁定
								values_[key] = def.default_value;
								// LOG_DEBUG("ParamValues::loadDefaults (TypeTag: '{}'): Loaded default value for key '{}'. Type: '{}'",
								// 		  type_tag_, key, typeIndexToString(def.type));
							} else {
								LOG_TRACE("ParamValues::loadDefaults (TypeTag: '{}'): Key '{}' has monostate default value, not loading.", type_tag_, key);
							}
						} else {
							LOG_TRACE("ParamValues::loadDefaults (TypeTag: '{}'): Key '{}' already has a value, not loading default.", type_tag_, key);
						}
					}
				}
				return true;
			} else {
				LOG_WARN("ParamValues::loadDefaults (TypeTag: '{}'): Could not retrieve effective parameter definitions from registry. No defaults loaded.", type_tag_);
				return false;
			}
		}

		std::vector<std::string> ParamValues::getKeys() const {
			std::shared_lock lock(values_mutex_);
			std::vector<std::string> keys_vec;
			keys_vec.reserve(values_.size());
			for (const auto& pair : values_) {
				keys_vec.push_back(pair.first);
			}
			return keys_vec;
		}

		std::vector<std::pair<std::string, ParamValue>> ParamValues::getValues(const std::vector<std::string>& keys) const {
			std::shared_lock lock(values_mutex_);
			std::vector<std::pair<std::string, ParamValue>> result;
			result.reserve(keys.size());

			for (const std::string& key : keys) {
				auto it = values_.find(key);
				if (it != values_.end()) {
					result.emplace_back(key, it->second);
				}
			}
			return result;
		}

		bool ParamValues::setValues(const std::vector<std::pair<std::string, ParamValue>>& params, const ParamRegistry& registry) {
			bool all_success = true;

			// 批量设置，使用单个锁保护整个操作
			{
				std::unique_lock lock(values_mutex_);
				for (const auto& [key, value] : params) {
					// 在锁保护下直接设置，避免递归锁定
					// 注意：这里跳过了约束检查以提高性能，如果需要约束检查，应该在锁外进行
					values_[key] = value;
				}
			}

			// 如果需要约束检查，可以在这里添加（在锁外进行）
			// 但这会降低性能，因为需要重新获取锁

			LOG_DEBUG("ParamValues::setValues (TypeTag: '{}'): 批量设置了 {} 个参数", type_tag_, params.size());
			return all_success;
		}

		// 便捷的toString函数，用于日志记录
		std::string toString(const ParamValue& value) {
			return paramValueToString(value);
		}

	} // namespace NSParams
} // namespace NSDrones