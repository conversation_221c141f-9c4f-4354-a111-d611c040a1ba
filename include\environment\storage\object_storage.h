// include/environment/object_storage.h
#pragma once

#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <memory>
#include <shared_mutex>
#include <type_traits>
#include <string>

// 前向声明，避免循环依赖
namespace NSDrones {
	namespace NSCore {
		class EntityObject;
		enum class ZoneType;
	}
	namespace NSEnvironment {
		class Environment;
		class Zone;
		class Obstacle;
	}
	namespace NSUav {
		class Uav;
	}
}

namespace NSDrones {
	namespace NSEnvironment {
		using namespace ::NSDrones::NSCore;
		using namespace ::NSDrones::NSUtils;

		/**
		 * @brief 对象类型特性模板 - 用于识别和操作不同类型的对象
		 * @tparam T 对象类型
		 */
		template<typename T>
		struct ObjectTraits;

		// EntityObject 特化
		template<>
		struct ObjectTraits<EntityObject> {
			static constexpr const char* type_name = "EntityObject";
			inline static bool isMovable(const EntityObject& obj);
		};

		// Zone 特化声明
		template<>
		struct ObjectTraits<Zone> {
			static constexpr const char* type_name = "Zone";
			inline static bool isMovable(const Zone& obj);
			inline static ZoneType getZoneType(const Zone& obj);
		};

		// Obstacle 特化声明
		template<>
		struct ObjectTraits<Obstacle> {
			static constexpr const char* type_name = "Obstacle";
			inline static bool isMovable(const Obstacle& obj);
		};

		// Uav 特化声明
		template<>
		struct ObjectTraits<NSUav::Uav> {
			static constexpr const char* type_name = "Uav";
			inline static bool isMovable(const NSUav::Uav& obj);
		};

		/**
		 * @class ObjectStorage
		 * @brief 管理环境中的所有对象，提供类型索引和快速查询功能
		 *
		 * 这个类负责存储和索引所有环境中的对象，支持按类型、动静态属性和特定属性进行查询。
		 * 使用模板设计允许不同的底层存储容器实现。
		 */
		template<typename ObjectContainer = std::unordered_map<ObjectID, std::shared_ptr<EntityObject>>>
		class ObjectStorage {
		public:
			/**
			 * @brief 构造函数
			 */
			ObjectStorage() = default;

			/**
			 * @brief 析构函数
			 */
			~ObjectStorage() = default;

			// --- 基本对象管理 ---
			/**
			 * @brief 添加对象到存储
			 * @tparam T 对象类型
			 * @param obj 要添加的对象智能指针
			 * @return 如果成功添加则返回true，否则返回false
			 */
			template<typename T>
			bool addObject(std::shared_ptr<T> obj);

			/**
			 * @brief 从存储中移除对象
			 * @param id 要移除的对象ID
			 * @return 如果成功移除则返回true，否则返回false
			 */
			bool removeObject(const ObjectID& id);

			/**
			 * @brief 获取指定ID的对象
			 * @tparam T 对象期望的类型
			 * @param id 对象ID
			 * @return 对象指针，如果未找到或类型不匹配则返回nullptr
			 */
			template<typename T = EntityObject>
			std::shared_ptr<T> getObject(const ObjectID& id) const;

			/**
			 * @brief 获取指定类型的所有对象
			 * @tparam T 对象类型
			 * @return 指定类型的对象向量
			 */
			template<typename T = EntityObject>
			std::vector<std::shared_ptr<T>> getObjectsByType() const;

			// --- 动静态对象查询 ---
			/**
			 * @brief 获取所有可移动对象
			 * @return 可移动对象的向量
			 */
			std::vector<std::shared_ptr<EntityObject>> getMovableObjects() const;

			/**
			 * @brief 获取所有静态对象
			 * @return 静态对象的向量
			 */
			std::vector<std::shared_ptr<EntityObject>> getStaticObjects() const;

			// --- 特定类型查询 ---
			/**
			 * @brief 获取所有区域对象
			 * @return 区域对象的向量
			 */
			std::vector<std::shared_ptr<Zone>> getZones() const;

			/**
			 * @brief 根据类型获取区域对象
			 * @param type 区域类型
			 * @return 指定类型的区域对象向量
			 */
			std::vector<std::shared_ptr<Zone>> getZonesByType(ZoneType type) const;

			/**
			 * @brief 获取所有障碍物对象
			 * @return 障碍物对象的向量
			 */
			std::vector<std::shared_ptr<Obstacle>> getObstacles() const;

			// --- 统计信息 ---
			/**
			 * @brief 获取存储的对象总数
			 * @return 对象总数
			 */
			size_t getObjectCount() const;

			/**
			 * @brief 获取指定类型的对象数量
			 * @param type_name 类型名称
			 * @return 指定类型的对象数量
			 */
			size_t getObjectCountByType(const std::string& type_name) const;

			/**
			 * @brief 清空所有对象
			 */
			void clear();

			/**
			 * @brief 检查对象是否为动态对象
			 * @param id 对象ID
			 * @return 如果对象是动态的返回true，否则返回false
			 */
			bool isObjectDynamic(const ObjectID& id) const;

			/**
			 * @brief 获取所有对象
			 * @return 所有对象的向量
			 */
			std::vector<std::shared_ptr<EntityObject>> getAllObjects() const;

		private:
			// --- 私有成员变量 ---
			// 主对象存储
			ObjectContainer objects_; //所有对象
			mutable std::shared_mutex objects_mutex_;

			// 第一级：类型索引 - 存储不同类型对象的ID集合
			std::unordered_map<std::string, std::unordered_set<ObjectID>> type_index_; //各种标签类别的索引
			mutable std::shared_mutex type_index_mutex_;

			// 第二级：动静态索引 - 基于是否有MovementStrategy快速区分
			std::unordered_set<ObjectID> movable_object_ids_; //所有可移动对象
			std::unordered_set<ObjectID> static_object_ids_; //所有不可移动对象
			mutable std::shared_mutex mobility_index_mutex_; //保护动静态索引的互斥锁

			// 第三级：特定类型索引
			std::unordered_set<ObjectID> zone_ids_; //所有区域
			std::unordered_map<ZoneType, std::unordered_set<ObjectID>> zone_type_index_; //特定类型的区域
			std::unordered_set<ObjectID> obstacle_ids_; //所有障碍物
			mutable std::shared_mutex special_type_mutex_; //保护特定类型索引的互斥锁

			// --- 私有辅助方法 ---
			/**
			 * @brief 根据ID集合获取对象
			 * @param ids 对象ID集合
			 * @return 对象指针向量
			 */
			std::vector<std::shared_ptr<EntityObject>> getObjectsByIds(const std::unordered_set<ObjectID>& ids) const;

			/**
			 * @brief 更新特定类型索引
			 * @tparam T 对象类型
			 * @param obj 对象指针
			 */
			template<typename T>
			void updateSpecialTypeIndices(std::shared_ptr<T> obj);
		};

	} // namespace NSEnvironment
} // namespace NSDrones