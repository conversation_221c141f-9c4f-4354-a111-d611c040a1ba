{"description": "Parameters specific to Multirotor UAVs, inheriting from EntityObject.Uav.", "parameters": [{"key": "physics.rotor_count", "type": "int", "description": "旋翼数量", "default": 4, "constraints": {"type": "numeric", "min": 3, "max": 12}}, {"key": "physics.motor_time_constant_s", "type": "double", "description": "电机响应时间常数 (s)", "default": 0.05, "constraints": {"type": "numeric", "min": 0.01, "max": 0.5}}, {"key": "physics.rotor_thrust_coeff", "type": "double", "description": "旋翼推力系数 (单位依赖于动力学模型)", "default": 2e-06, "constraints": {"type": "numeric", "min": 1e-07, "max": 1e-05}}, {"key": "physics.rotor_torque_coeff", "type": "double", "description": "旋翼扭矩系数 (单位依赖于动力学模型)", "default": 5e-08, "constraints": {"type": "numeric", "min": 1e-09, "max": 1e-07}}, {"key": "energy.mr.hover_power", "type": "double", "description": "悬停时的功率消耗 (W)", "default": 150.0, "constraints": {"type": "numeric", "min": 10.0, "max": 1000.0}}, {"key": "energy.mr.horizontal_power_factor", "type": "double", "description": "水平飞行附加功耗因子 (W/(m/s) 或 W/(m/s)^2 取决于模型)", "default": 10.0, "constraints": {"type": "numeric", "min": 0.0, "max": 50.0}}, {"key": "energy.mr.vertical_power_factor", "type": "double", "description": "垂直飞行附加功耗因子 (W/(m/s))", "default": 50.0, "constraints": {"type": "numeric", "min": 0.0, "max": 200.0}}, {"key": "energy.mr.baseline_power", "type": "double", "description": "基线/航电系统功耗 (W)", "default": 5.0, "constraints": {"type": "numeric", "min": 0.0, "max": 100.0}}, {"key": "energy.mr.endurance_est_max_v_speed", "type": "double", "description": "用于续航估算时假设的垂直速度 (m/s)", "default": 1.0, "constraints": {"type": "numeric", "min": 0.0, "max": 5.0}}]}