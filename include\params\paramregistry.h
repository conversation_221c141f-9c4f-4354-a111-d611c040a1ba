// include/params/paramregistry.h
#pragma once

#include <string>
#include <vector>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <filesystem>
#include <optional>
#include <map>

#include "utils/logging.h"
#include "utils/thread_safe_cache.h"
#include "params/parameters.h"
#include "params/param_json.h"

namespace NSDrones {
	namespace NSParams {
		namespace fs = std::filesystem;

		/**
		 * @brief 参数注册表类，用于管理所有类型的参数定义。
		 * 这是一个单例类，负责加载、存储和检索参数定义 (ParamDefines)。
		 * 参数定义通过 type_tag (类型标签字符串) 进行索引，支持层级结构。
		 */
		class ParamRegistry {
		private:

			/**
			 * @brief 默认构造函数 (私有，保证单例模式)。
			 */
			ParamRegistry();
			/**
			 * @brief 带缓存配置的构造函数 (私有，保证单例模式)。
			 * @param cache_max_age 缓存最大存活时间
			 * @param cache_max_size 缓存最大条目数
			 */
			ParamRegistry(std::chrono::minutes cache_max_age, size_t cache_max_size);
			~ParamRegistry(); // 保留私有析构函数，并添加日志

			/**
			 * @brief 获取给定 type_tag 的父级 type_tag。
			 * 例如，如果 type_tag 是 "A.B.C"，则返回 "A.B"。如果已经是顶级，则返回空字符串。
			 * @param type_tag 子级类型标签。
			 * @return std::string 父级类型标签，如果无父级则为空。
			 */
			std::string getParentTypeTag(const std::string& type_tag) const;

			// 存储原始注册的参数定义，按类型标签组织
			// 值是 ParamDefines 对象的共享指针，因为 ParamDefines 对象本身可能较大
			std::map<std::string, std::shared_ptr<ParamDefines>> registered_defines_map_;
			mutable std::shared_mutex registry_mutex_;	// 保护对注册表的访问

			// 使用ThreadSafeCache缓存计算后的有效参数定义集
			mutable NSUtils::ThreadSafeCache<std::shared_ptr<const ParamDefines>> effective_defines_cache_;

			// 缓存配置参数（可配置）
			std::chrono::minutes cache_max_age_{10}; ///< 缓存最大存活时间（默认10分钟）
			size_t cache_max_size_{5000}; ///< 缓存最大条目数（默认2000，增加以容纳更多参数定义）

		public:
			// 删除拷贝构造函数和赋值操作符，保证单例模式
			ParamRegistry(const ParamRegistry&) = delete;
			ParamRegistry& operator=(const ParamRegistry&) = delete;
			// 删除移动构造函数和赋值操作符
			ParamRegistry(ParamRegistry&&) = delete;
			ParamRegistry& operator=(ParamRegistry&&) = delete;

			/**
			 * @brief 获取 ParamRegistry 的单例实例。
			 * @return ParamRegistry& 对单例对象的引用。
			 */
			static ParamRegistry& getInstance();

			/**
			 * @brief 从指定目录加载所有参数定义文件 (.json)。
			 * 文件名 (不含扩展名) 将被用作 type_tag。
			 * @param directory_path 包含参数定义文件的目录路径。
			 * @return bool 如果所有文件都成功加载则返回 true，否则返回 false。
			 */
			bool loadAllParamDefinesFromDir(const std::string& directory_path);

			/**
			 * @brief 注册一个 ParamDefines 对象。
			 * 如果已存在具有相同 type_tag 的定义，则会覆盖它。
			 * @param param_defines 要注册的参数定义集合。
			 * @return bool 如果注册成功则返回 true，如果 type_tag 为空则返回 false。
			 */
			bool registerParamDefines(ParamDefines param_defines);

			/**
			 * @brief 获取指定 type_tag 的直接参数定义集合。
			 * 不进行层级继承查找。
			 * @param type_tag 要查询的类型标签。
			 * @return const ParamDefines* 如果找到则返回指向 ParamDefines 对象的指针，否则返回 nullptr。
			 */
			const ParamDefines* getParamDefines(const std::string& type_tag) const;

			/**
			 * @brief 获取或构建并缓存指定类型标签的完整有效参数定义集合 (考虑继承)
			 * @param type_tag 要查询的类型标签。
			 * @return std::shared_ptr<const ParamDefines> 如果找到任何定义（直接或继承的），则返回包含合并后 ParamDefines 的 optional，否则返回 std::nullopt。
			 */
			std::shared_ptr<const ParamDefines> getOrBuildEffectiveParamDefines(const std::string& type_tag) const;

			/**
			 * @brief 获取指定 type_tag 下特定参数键的定义。
			 * 会考虑参数继承。
			 * @param type_tag 类型标签。
			 * @param key 参数键名。
			 * @return const ParamDefine* 如果找到参数定义则返回指针，否则返回 nullptr。
			 */
			const ParamDefine* getEffectiveParamDefine(const std::string& type_tag, const std::string& key) const;

			/**
			 * @brief 获取指定 type_tag (包括继承) 的所有参数键名。
			 * @param type_tag 类型标签。
			 * @return std::vector<std::string> 包含所有参数键名的向量，已排序。如果无法获取有效参数集，则返回空向量。
			 */
			std::vector<std::string> getEffectiveParamKeys(const std::string& type_tag) const;

			/**
			 * @brief 获取所有已直接注册的 type_tag 名称。
			 * 不反映继承关系，只返回那些有直接定义文件的 type_tag。
			 * @return std::vector<std::string> 包含所有已注册 type_tag 的向量，已排序。
			 */
			std::vector<std::string> getAllRegisteredTypeTags() const;

			/**
			 * @brief 清除注册表中所有已加载的参数定义。
			 */
			void clearAllParamDefinitions();

			/**
			 * @brief 直接从 JSON 对象注册参数定义。
			 * JSON 对象应包含一个 "parameters" 数组，每个元素定义一个参数。
			 * 可以选择性地在 JSON 对象中包含 "type_tag" 字段。
			 * @param definitions_json 包含参数定义的 JSON 对象。
			 * @param type_tag_override 如果非空，则强制使用此 type_tag；否则尝试从 JSON 内的 "type_tag" 字段获取，如果还没有则失败。
			 * @return bool 如果注册成功（包括覆盖）则返回 true，否则返回 false。
			 */
			bool registerParamDefinesFromJson(const nlohmann::json& definitions_json, std::string type_tag_override = "");

			/**
			 * @brief 从 JSON 文件加载并注册参数定义。
			 * 内部会读取文件内容，解析为 JSON，然后调用 registerParamDefinesFromJson。
			 * @param file_path 参数定义 JSON 文件的路径。
			 * @param type_tag_override 如果非空，则强制使用此 type_tag；否则尝试从文件名推断 type_tag。
			 * @return bool 如果加载和注册成功则返回 true，否则返回 false。
			 */
			bool registerParamDefinesFromFile(const fs::path& file_path, std::string type_tag_override = "");

			/**
			 * @brief 获取所有type_tag及其参数key的只读快照
			 * @return std::vector<std::pair<std::string, std::vector<std::string>>> 包含所有type_tag及其参数key的只读快照
			 */
			std::vector<std::pair<std::string, std::vector<std::string>>> getAllTypeTagAndKeysView() const;

			/**
			 * @brief 根据 type_tag 的参数定义动态创建一个新的 ParamValues 对象，并用默认值填充。
			 * @param type_tag 要为其创建默认 ParamValues 的类型标签。
			 * @return std::shared_ptr<ParamValues> 指向新创建的、已填充默认值的 ParamValues 对象的共享指针。
			 *         如果 type_tag 无效或无法获取其定义，则可能返回 nullptr。
			 */
			std::shared_ptr<ParamValues> createDefaultParamValues(const std::string& type_tag) const;

			// 新增: 从 JSON 对象解析参数定义
			std::optional<ParamDefines> parseJsonToParamDefines(
				const nlohmann::json& definitions_json,
				const std::string& type_tag_override = "") const;

			// --- 缓存管理方法 ---

			/**
			 * @brief 配置缓存参数
			 * @param cache_max_age 缓存最大存活时间
			 * @param cache_max_size 缓存最大条目数
			 */
			void configureCacheSettings(std::chrono::minutes cache_max_age, size_t cache_max_size);

			/// 获取缓存统计信息
			std::string getCacheStatistics() const {
				return effective_defines_cache_.getStatistics();
			}

			/// 清理过期缓存条目
			void cleanupExpiredCache() {
				effective_defines_cache_.removeExpired();
				LOG_DEBUG("ParamRegistry: 已清理过期缓存条目");
			}

			/// 清空所有缓存
			void clearCache() {
				effective_defines_cache_.clear();
				LOG_DEBUG("ParamRegistry: 已清空所有缓存");
			}
		};

	} // namespace NSParams
} // namespace NSDrones