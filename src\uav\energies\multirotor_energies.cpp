// src/uav/energies/multirotor_energies.cpp
#include "uav/energies/multirotor_energies.h"
#include "uav/uav.h"       
#include "utils/logging.h"         
#include <cmath>                 
#include <stdexcept>              
#include <algorithm>               
#include <limits>                

namespace NSDrones {
	namespace NSUav {

		/**
		 * @brief MultirotorEnergies 构造函数。
		 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
		 */
		MultirotorEnergies::MultirotorEnergies(const Uav& owner)
			: IEnergyModel(owner) // 调用基类构造函数，传递 owner
		{
			LOG_DEBUG("多旋翼能量模型已创建，所有者: {}", owner_.getId());
		}

		/**
		 * @brief 计算多旋翼能量消耗。
		 *        基于参数 "energy.mr.*"。
		 */
		double MultirotorEnergies::computeEnergyConsumption(const UavState& state, Time dt) const {
			// 检查时间步长是否有效
			if (dt <= Constants::TIME_EPSILON) {
				LOG_TRACE("计算多旋翼能量消耗：时间步长过小 ({:.4f}s)，返回 0。", dt);
				return 0.0;
			}

			// 1. 从 owner_ 获取所需参数 (使用 getParamOrDefault 提供默认值)
			// 键名示例: "energy.mr.baseline_power", "energy.mr.hover_power", "energy.base.discharging_efficiency"
			double baseline_power = owner_.getParamOrDefault<double>("energy.mr.baseline_power", 5.0); // W
			double hover_power = owner_.getParamOrDefault<double>("energy.mr.hover_power", 150.0);    // W
			double v_power_factor = owner_.getParamOrDefault<double>("energy.mr.vertical_power_factor", 50.0); // W/(m/s)
			double h_power_factor = owner_.getParamOrDefault<double>("energy.mr.horizontal_power_factor", 10.0);// W/(m/s)
			double efficiency = owner_.getParamOrDefault<double>("energy.base.discharging_efficiency", 0.95); // 比例

			// 确保效率有效 (0 < efficiency <= 1)
			efficiency = std::clamp(efficiency, Constants::EPSILON, 1.0);
			if (efficiency <= Constants::EPSILON) {
				LOG_WARN_ONCE("多旋翼能量计算：放电效率 ({:.3f}) 无效，将使用 1.0。", efficiency);
				efficiency = 1.0; // 使用 1.0 作为回退
			}
			// 确保功率参数非负
			baseline_power = std::max(0.0, baseline_power);
			hover_power = std::max(0.0, hover_power);
			v_power_factor = std::max(0.0, v_power_factor);
			h_power_factor = std::max(0.0, h_power_factor);

			// 2. 获取速度分量
			double vertical_speed = state.velocity.z();           // 垂直速度 (m/s)
			double horizontal_speed = state.velocity.head<2>().norm(); // 水平速度大小 (m/s)

			// 3. 计算功率 (基于简化的线性模型)
			// 功率 = 基线 + 悬停 + 垂直运动功率 + 水平运动功率
			double power = baseline_power;    // 基线功率
			power += hover_power;           // 悬停基础功率
			// 垂直功率与垂直速度绝对值成正比
			power += std::abs(vertical_speed) * v_power_factor;
			// 水平功率与水平速度成正比
			power += horizontal_speed * h_power_factor;

			// 确保计算出的总功率非负 (虽然各分量已处理，再次确认)
			power = std::max(0.0, power);
			LOG_TRACE("计算多旋翼功率 (所有者 ID: {})：V_h={:.2f}, V_v={:.2f}, P_base={:.1f}, P_hover={:.1f}, P_v_factor={:.1f}, P_h_factor={:.1f}, P_total={:.1f}W",
				owner_.getId(), horizontal_speed, vertical_speed, baseline_power, hover_power,
				v_power_factor, h_power_factor, power);

			// 4. 计算能量消耗 (考虑放电效率)
			// 能量消耗 (焦耳) = 功率 (瓦特) * 时间 (秒) / 放电效率
			double energy_consumed_joules = (power * dt) / efficiency;

			// 5. 转换为 Wh (假设 max_energy_capacity 单位是 Wh)
			// 1 Wh = 3600 Joules
			double energy_consumed_wh = energy_consumed_joules / 3600.0;
			LOG_TRACE("  时间步长: {:.4f}s, 效率: {:.3f}, 消耗能量: {:.4f} J ({:.6f} Wh)", dt, efficiency, energy_consumed_joules, energy_consumed_wh);

			return energy_consumed_wh; // 返回 Wh
		}

		/**
		 * @brief 估算多旋翼剩余续航时间。
		 */
		Time MultirotorEnergies::estimateEnduranceTime(
			const UavState& current_state,
			double current_energy) const
		{
			LOG_TRACE("估算多旋翼剩余续航时间 (所有者 ID: {})：当前能量 {:.2f} Wh", owner_.getId(), current_energy);

			// 1. 计算可安全使用的能量
			double safe_energy = current_energy - getMinSafeEnergyLevel(); // getMinSafeEnergyLevel 已处理容量和比例
			LOG_TRACE("  最低安全能量: {:.2f} Wh, 可用安全能量: {:.2f} Wh", getMinSafeEnergyLevel(), safe_energy);
			if (safe_energy <= Constants::EPSILON) {
				LOG_DEBUG("  可用安全能量不足 ({:.2f} Wh)，剩余续航为 0。", safe_energy);
				return 0.0; // 可用能量不足
			}

			// 2. 估算“典型”或当前状态下的功率消耗
			// 从 owner_ 获取所需参数
			double baseline_power = owner_.getParamOrDefault<double>("energy.mr.baseline_power", 5.0);
			double hover_power = owner_.getParamOrDefault<double>("energy.mr.hover_power", 150.0);
			double v_power_factor = owner_.getParamOrDefault<double>("energy.mr.vertical_power_factor", 50.0);
			// 获取用于续航估算的最大垂直速度 (可选参数，默认为 1 m/s)
			double endurance_v_speed = owner_.getParamOrDefault<double>("energy.mr.endurance_est_max_v_speed", 1.0);
			double efficiency = owner_.getParamOrDefault<double>("energy.base.discharging_efficiency", 0.95);

			// 确保参数非负
			baseline_power = std::max(0.0, baseline_power);
			hover_power = std::max(0.0, hover_power);
			v_power_factor = std::max(0.0, v_power_factor);
			endurance_v_speed = std::max(0.0, endurance_v_speed);

			// 简化估算：假设低速飞行，主要考虑基线、悬停和小的垂直运动
			double estimated_power = baseline_power + hover_power + endurance_v_speed * v_power_factor;
			// 确保估算功率非负
			estimated_power = std::max(0.0, estimated_power);
			LOG_TRACE("  估算典型功率 (使用 Vv={:.1f} m/s): {:.1f} W", endurance_v_speed, estimated_power);

			// 3. 考虑放电效率
			efficiency = std::clamp(efficiency, Constants::EPSILON, 1.0); // 限制在 (0, 1]
			if (efficiency <= Constants::EPSILON) {
				LOG_WARN_ONCE("多旋翼续航估算：放电效率 ({:.3f}) 无效，将使用 1.0。", efficiency);
				efficiency = 1.0; // 使用 1.0 作为回退
			}
			double effective_power_draw = estimated_power / efficiency; // 有效功率消耗 (电池输出功率)
			LOG_TRACE("  放电效率: {:.3f}, 有效功率消耗: {:.1f} W", efficiency, effective_power_draw);

			// 4. 计算剩余时间
			// 检查有效功率是否为正
			if (effective_power_draw <= Constants::EPSILON) {
				LOG_WARN("有效功率消耗接近零 ({:.1f}W)，无法估算续航时间（可能悬停功率等为零），返回无穷大。", effective_power_draw);
				return Constants::INF; // 功率消耗为零，理论上续航无限
			}

			// 时间 (小时) = 能量 (Wh) / 功率 (W)
			double remaining_hours = safe_energy / effective_power_draw;
			Time remaining_seconds = remaining_hours * 3600.0; // 转换为秒
			LOG_DEBUG("  估算剩余续航时间: {:.2f} 小时 ({:.1f} 秒)", remaining_hours, remaining_seconds);

			return remaining_seconds; // 返回秒
		}

	} // namespace NSUav
} // namespace NSDrones