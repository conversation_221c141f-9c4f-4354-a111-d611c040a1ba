// include/config.h
#pragma once

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>
#include <filesystem>
#include "nlohmann/json.hpp"
#include <optional>
#include <mutex>
#include "params/paramregistry.h"
#include "params/parameters.h"
#include "core/entity_object.h"
#include <stdexcept>
#include <unordered_map>
#include <typeinfo>
#include "utils/logging.h"
#include <type_traits>
#include <utility>

namespace NSDrones {
	namespace NSParams { class ParamRegistry; class ParamValues; }
	namespace NSEnvironment {
		class Environment; 
		class Obstacle; class Zone;
	}
	namespace NSUav { class Uav; }
	namespace NSPlanning { class ITaskPlanner; }
	namespace NSAlgorithm {
		class AlgorithmObject;
		class IPathPlanner;
		class ITrajectoryOptimizer;
		class ITrajectoryEvaluator;
		class ITaskAllocator;
	}
}

namespace fs = std::filesystem;

namespace NSDrones {

	template<typename T, typename = void>
	struct has_initialize_method : std::false_type {};

	template<typename T>
	struct has_initialize_method<T,
		std::void_t<decltype(std::declval<T&>().initialize(
			std::declval<std::shared_ptr<NSParams::ParamValues>>(),
			std::declval<const nlohmann::json&>()
		))>
	> : std::true_type {};

	/**
	 * @class Config
	 * @brief 管理配置加载、对象实例化和全局依赖项。
	 * 负责：
	 * 1. 解析主配置文件和相关数据路径。
	 * 2. 初始化参数注册表 (ParamRegistry) 并加载参数定义。
	 * 3. 管理全局 Environment 实例。
	 * 4. 注册对象和组件工厂。
	 * 5. 预加载对象实例的配置。
	 * 6. 提供类型安全的工厂方法来创建和初始化对象实例。
	 */
	class Config : public std::enable_shared_from_this<Config> {
	public:
		// --- 配置文件中使用的 JSON 键常量 ---
		static const std::string KEY_GLOBAL_PARAMETER_DEFINITIONS_NODE;
		static const std::string KEY_GLOBAL_PARAMETER_VALUES_NODE;
		static const std::string KEY_PARAMETERS;
		static const std::string KEY_TYPE_TAG;
		static const std::string KEY_INSTANCE_ID;
		static const std::string KEY_ID;
		static const std::string KEY_SI_ORIGIN_X;
		static const std::string KEY_SI_ORIGIN_Y;
		static const std::string KEY_SI_ORIGIN_Z;
		static const std::string KEY_SI_BOUNDS_MIN_X;
		static const std::string KEY_SI_BOUNDS_MIN_Y;
		static const std::string KEY_SI_BOUNDS_MIN_Z;
		static const std::string KEY_SI_BOUNDS_MAX_X;
		static const std::string KEY_SI_BOUNDS_MAX_Y;
		static const std::string KEY_SI_BOUNDS_MAX_Z;
		static const std::string KEY_SI_LEVELS;
		static const std::string KEY_SI_BASE_RESOLUTION;
		static const std::string KEY_SI_LEVEL_FACTOR;

		static const std::string GLOBAL_PARAMS_TYPE_TAG;

		/**
		 * @brief 构造函数。
		 * @param config_file 主配置文件 (例如 "configfile.json") 的路径。
		 * @throws std::runtime_error 如果主配置文件解析失败。
		 */
		explicit Config(const std::string& config_file);

		/**
		 * @brief 初始化配置系统。
		 *        执行顺序：创建Environment -> 加载全局参数定义 -> 加载 defines/ 目录定义 -> 应用全局参数值到Environment -> Environment初始化空间索引 -> Environment加载地图 -> 注册对象工厂 -> 预加载对象实例配置。
		 * @return 如果所有步骤成功，返回 true。
		 */
		bool initialize();

		/**
		 * @brief 注册所有内置的对象和规划器组件工厂。
		 *        **注意:** 此方法不应注册 Uav 的能量和动力学模型工厂，这些应由 Uav 内部管理。
		 * @return 如果注册成功，返回 true。
		 */
		bool registerObjectFactories();

		/**
		 * @brief 加载 data/defines/ 目录下所有 .json 文件的参数定义到 ParamRegistry。
		 *        文件名 (不含扩展名) 将用作参数定义的 type_tag。
		 * @return 如果所有文件都成功加载和注册，返回 true。
		 */
		bool loadDirectoryParamDefines();

		/**
		 * @brief 从主配置文件 (configfile.json) 中的 "global_parameter_definitions" 节点加载全局参数定义。
		 *        使用 `GLOBAL_PARAMS_TYPE_TAG` 作为类型标签注册。
		 * @return 如果加载和注册成功，返回 true。
		 */
		bool loadGlobalParamDefines();

		/**
		 * @brief 批量预加载 data/objects/ 目录下所有 .json 文件中的对象实例配置到内存缓存。
		 *        这只加载原始 JSON 片段，不创建实际对象。
		 * @return 所有成功加载的唯一实例 ID 列表。
		 */
		std::vector<std::string> preloadAllObjectInstanceFiles();

		/**
		 * @brief 预加载指定名称 (ID) 的对象实例配置。
		 *        如果缓存中已存在，则直接返回 true。否则，扫描 data/objects/ 目录查找并加载。
		 * @param instance_name 要预加载的对象实例 ID。
		 * @return 如果实例配置已在缓存中或成功加载，返回 true。
		 */
		bool preloadObjectInstanceByName(const std::string& instance_name);

		/**
		 * @brief (供内部或测试使用) 从指定的单个 .json 文件加载对象实例配置到缓存。
		 * @param file_path 包含对象实例数组的 .json 文件路径。
		 * @param loaded_names (可选输出) 指向 vector 的指针，用于存储此文件中成功加载的实例 ID。
		 * @return 如果文件解析成功且所有实例条目都有效 (即使部分实例已存在或格式稍有问题)，返回 true。
		 *         如果文件无法打开或顶层不是数组，返回 false。
		 */
		bool loadObjectInstancesFromFileInternal(const std::filesystem::path& file_path, std::vector<std::string>* loaded_names = nullptr);

		/**
		 * @brief 获取参数注册表的指针。
		 *        供需要访问参数定义的地方使用 (例如 Environment)。
		 * @return 指向 ParamRegistry 的指针。
		 */
		NSParams::ParamRegistry* getParamRegistry() const { return param_registry_; }

		/**
		 * @brief 获取 Environment 实例的共享指针。
		 *        Environment 实例由 Config 在 initialize() 期间创建和管理。
		 * @return 指向 Environment 实例的共享指针。如果在 initialize() 之前调用或创建失败，可能返回 nullptr。
		 */
		std::shared_ptr<Environment> getEnvironment() const;

		/**
		 * @brief 获取在初始化期间从 data/objects/ 预加载的所有对象实例的名称列表。
		 * @return 一个包含所有预加载实例 ID 的字符串向量。
		 */
		std::vector<std::string> getPreloadedInstanceNames() const;

		/**
		 * @brief 获取指定实例的type_tag。
		 * @param instance_id 实例ID。
		 * @return 实例的type_tag字符串，如果实例不存在则返回空字符串。
		 */
		std::string getInstanceTypeTag(const std::string& instance_id) const;

		/**
		 * @brief (核心工厂方法) 根据实例 ID 创建并初始化对象实例。
		 *        这是一个模板方法，需要指定期望返回的对象类型 T。
		 *        流程：
		 *        1. 从缓存获取实例的原始配置 (type_tag 和 parameters_json)。
		 *        2. 获取对应 type_tag 的默认 ParamValues (克隆体)。
		 *        3. 将实例特定的 parameters_json 应用到克隆的 ParamValues 上。
		 *        4. 根据 type_tag 查找并调用相应的对象工厂或组件工厂创建对象 (返回基类或 void 指针)。
		 *        5. 将返回的泛型指针转换为期望类型 T。
		 *        6. 如果类型 T 具有 initialize(ParamValues, json) 方法，则调用它进行最终初始化。
		 *
		 * @tparam T 期望创建的对象类型 (例如 NSUav::Uav, IPathPlanner)。
		 * @param instance_id 要创建的对象的实例 ID。
		 * @return 指向创建和初始化后的对象的共享指针 (类型为 T)。如果任何步骤失败，返回 nullptr。
		 */
		template<typename T>
		std::shared_ptr<T> createObjectInstance(const std::string& instance_id) const {
			LOG_DEBUG("请求创建实例 '{}', 期望类型 '{}'", instance_id, typeid(T).name());

			auto environment = Environment::getInstance();
			if (!environment) {
				LOG_ERROR("Environment 未初始化，无法创建实例 '{}'。", instance_id);
				return nullptr;
			}

			std::string type_tag;
			nlohmann::json parameters_json;

			// 1. 从缓存检索原始参数 JSON
			{
				std::lock_guard<std::mutex> lock(raw_params_cache_mutex_);
				auto it = raw_instance_params_cache_.find(instance_id);
				if (it == raw_instance_params_cache_.end()) {
					LOG_ERROR("在原始参数缓存中未找到实例 '{}' 的配置。", instance_id);
					return nullptr;
				}
				type_tag = it->second.first;
				parameters_json = it->second.second; // 这是来自 objects/*.json 的实例特定 JSON
				LOG_TRACE("检索到实例 '{}' 的原始参数：类型标签='{}', JSON 大小={}",
						  instance_id, type_tag, parameters_json.size());
			}

			// 2. 获取对应类型标签的默认 ParamValues (克隆副本)
			LOG_DEBUG("正在为实例 '{}' (类型标签 '{}') 获取默认 ParamValues...", instance_id, type_tag);
			std::shared_ptr<NSParams::ParamValues> final_param_values = getClonedDefaultParamValuesForType(type_tag);
			if (!final_param_values) {
				LOG_ERROR("无法为实例 '{}' 获取或克隆类型标签 '{}' 的默认 ParamValues。", type_tag, instance_id);
				return nullptr;
			}
			LOG_DEBUG("成功克隆实例 '{}' (类型标签 '{}') 的默认 ParamValues。", instance_id, type_tag);

			// 3. 将实例特定的 JSON 参数覆盖到克隆的默认值上
			if (!parameters_json.empty() && parameters_json.is_object()) {
				LOG_DEBUG("正在为实例 '{}' 应用 JSON 中定义的 {} 个特定参数...", instance_id, parameters_json.size());
				if (!final_param_values->valuesFromJson(parameters_json, *param_registry_, nullptr, true)) {
					LOG_ERROR("为实例 '{}' (类型 '{}') 应用实例特定参数失败。无法创建对象。", instance_id, type_tag);
					return nullptr;
				} else {
					LOG_DEBUG("成功为实例 '{}' 应用实例特定参数。", instance_id);
				}
			} else {
				LOG_DEBUG("实例 '{}' 的 JSON 配置中未提供 'parameters' 对象。将使用类型 '{}' 的默认参数。", instance_id, type_tag);
			}

			// 4. 使用正确的工厂创建对象实例
			LOG_DEBUG("正在查找实例 '{}' (类型标签 '{}') 的工厂...", instance_id, type_tag);
			std::shared_ptr<T> actual_object_ptr = nullptr;

			// 首先检查EntityObject工厂
			auto it_obj_factory = object_factories_.find(type_tag);
			if (it_obj_factory != object_factories_.end()) {
				LOG_DEBUG("在 object_factories_ 中找到类型标签 '{}'。正在调用对象工厂创建实例 '{}'...", type_tag, instance_id);
				try {
					std::shared_ptr<EntityObject> base_ptr = it_obj_factory->second(*environment, instance_id, parameters_json);
					actual_object_ptr = std::dynamic_pointer_cast<T>(base_ptr);
				} catch (const std::exception& e) {
					LOG_CRITICAL("对象工厂在创建实例 '{}' (类型 '{}') 时抛出异常: {}", instance_id, type_tag, e.what());
					return nullptr;
				} catch (...) {
					LOG_CRITICAL("对象工厂在创建实例 '{}' (类型 '{}') 时抛出未知异常。", instance_id, type_tag);
					return nullptr;
				}
			} else {
				// 检查算法对象工厂（按类型分类）
				if constexpr (std::is_same_v<T, NSAlgorithm::ITaskAllocator>) {
					auto it_factory = task_allocator_factories_.find(type_tag);
					if (it_factory != task_allocator_factories_.end()) {
						LOG_DEBUG("在 task_allocator_factories_ 中找到类型标签 '{}'。", type_tag);
						try {
							actual_object_ptr = it_factory->second(*environment, static_cast<const Config*>(this), instance_id, parameters_json);
						} catch (const std::exception& e) {
							LOG_CRITICAL("任务分配器工厂创建实例 '{}' 时抛出异常: {}", instance_id, e.what());
							return nullptr;
						}
					}
				} else if constexpr (std::is_same_v<T, NSAlgorithm::IPathPlanner>) {
					auto it_factory = path_planner_factories_.find(type_tag);
					if (it_factory != path_planner_factories_.end()) {
						LOG_DEBUG("在 path_planner_factories_ 中找到类型标签 '{}'。", type_tag);
						try {
							actual_object_ptr = it_factory->second(*environment, static_cast<const Config*>(this), instance_id, parameters_json);
						} catch (const std::exception& e) {
							LOG_CRITICAL("路径规划器工厂创建实例 '{}' 时抛出异常: {}", instance_id, e.what());
							return nullptr;
						}
					}
				} else if constexpr (std::is_same_v<T, NSAlgorithm::ITrajectoryOptimizer>) {
					auto it_factory = trajectory_optimizer_factories_.find(type_tag);
					if (it_factory != trajectory_optimizer_factories_.end()) {
						LOG_DEBUG("在 trajectory_optimizer_factories_ 中找到类型标签 '{}'。", type_tag);
						try {
							actual_object_ptr = it_factory->second(*environment, static_cast<const Config*>(this), instance_id, parameters_json);
						} catch (const std::exception& e) {
							LOG_CRITICAL("轨迹优化器工厂创建实例 '{}' 时抛出异常: {}", instance_id, e.what());
							return nullptr;
						}
					}
				} else if constexpr (std::is_same_v<T, NSAlgorithm::ITrajectoryEvaluator>) {
					auto it_factory = trajectory_evaluator_factories_.find(type_tag);
					if (it_factory != trajectory_evaluator_factories_.end()) {
						LOG_DEBUG("在 trajectory_evaluator_factories_ 中找到类型标签 '{}'。", type_tag);
						try {
							actual_object_ptr = it_factory->second(*environment, static_cast<const Config*>(this), instance_id, parameters_json);
						} catch (const std::exception& e) {
							LOG_CRITICAL("轨迹评估器工厂创建实例 '{}' 时抛出异常: {}", instance_id, e.what());
							return nullptr;
						}
					}
				} else {
					// 回退到通用工厂
					auto it_planner_factory = planner_component_factories_.find(type_tag);
					if (it_planner_factory != planner_component_factories_.end()) {
						LOG_DEBUG("在 planner_component_factories_ 中找到类型标签 '{}'。", type_tag);
						try {
							auto generic_ptr = it_planner_factory->second(*environment, static_cast<const Config*>(this), instance_id, parameters_json);
							actual_object_ptr = std::static_pointer_cast<T>(generic_ptr);
						} catch (const std::exception& e) {
							LOG_CRITICAL("通用组件工厂创建实例 '{}' 时抛出异常: {}", instance_id, e.what());
							return nullptr;
						}
					}
				}

				if (!actual_object_ptr) {
					LOG_ERROR("未找到类型标签 '{}' (实例 ID '{}') 注册的工厂。", type_tag, instance_id);
					return nullptr;
				}
			}

			// 5. 验证对象创建成功
			if (!actual_object_ptr) {
				LOG_ERROR("工厂未能成功创建实例 '{}' (类型标签 '{}')。工厂可能返回了 nullptr。", instance_id, type_tag);
				return nullptr;
			}
			LOG_DEBUG("工厂成功创建了实例 '{}' 的对象指针，类型: '{}'。", instance_id, typeid(T).name());

			// 6. 如果对象类型 T 具有 initialize(ParamValues, json) 方法，则调用它
			if constexpr (has_initialize_method<T>::value) {
				LOG_DEBUG("类型 '{}' 具有 initialize 方法。正在为实例 '{}' 调用 initialize...", typeid(T).name(), instance_id);
				try {
					// 将最终合并的参数和原始实例 JSON 传递给 initialize
					actual_object_ptr->initialize(final_param_values, parameters_json);
					LOG_DEBUG("实例 '{}' 的 initialize 方法调用成功。", instance_id);
				} catch (const std::exception& e) {
					LOG_ERROR("调用实例 '{}' (类型 '{}') 的 initialize 方法时发生异常: {}", instance_id, typeid(T).name(), e.what());
					// 初始化失败，返回 nullptr，因为对象可能处于不一致状态
					return nullptr;
				} catch (...) {
					LOG_ERROR("调用实例 '{}' (类型 '{}') 的 initialize 方法时发生未知异常。", instance_id, typeid(T).name());
					return nullptr;
				}
			} else {
				LOG_DEBUG("类型 '{}' 没有所需的 initialize(ParamValues, json) 方法。跳过实例 '{}' 的 initialize 调用。", typeid(T).name(), instance_id);
				// 检查是否是 EntityObject 的派生类但缺少 initialize (可能是一个警示)
				if constexpr (std::is_base_of_v<EntityObject, T>) {
					 // EntityObject 本身有 initialize, 但签名不同。这里检查的是我们期望的签名。
					 // 如果派生类没有覆盖 EntityObject::initialize 或者没有我们期望的签名，会在这里记录。
					 // 暂时移除此警告，因为它可能过于嘈杂，因为 EntityObject 的 initialize 签名不同
					 LOG_WARN("实例 '{}' 派生自 EntityObject 但没有期望的 initialize(ParamValues, json) 方法。", instance_id);
				}
			}

			LOG_INFO("成功创建并初始化实例 '{}' (类型标签 '{}', 请求类型 '{}')。",
					 instance_id, type_tag, typeid(T).name());
			return actual_object_ptr;
		}

		/**
		 * @brief 从主配置文件中按 JSON 指针路径获取只读配置值。
		 * @tparam T 期望获取的值的类型。
		 * @param json_pointer_path JSON 指针路径 (例如 "/simulation/time_step")。
		 * @param default_value 如果路径未找到或类型不匹配时返回的默认值。
		 * @return 获取到的配置值；如果失败则返回 default_value。
		 */
		template<typename T>
		T getConfigValueByPath(const std::string& json_pointer_path, T default_value) const {
			LOG_TRACE("尝试获取主配置路径 '{}' 的值，默认值: {}", json_pointer_path, default_value);
			try {
				nlohmann::json::json_pointer ptr(json_pointer_path);
				if (main_json_config_.contains(ptr)) {
					return main_json_config_.at(ptr).get<T>();
				} else {
					LOG_WARN("在主配置中未找到路径 '{}'。将返回默认值。", json_pointer_path);
					return default_value;
				}
			} catch (const nlohmann::json::exception& e) {
				LOG_ERROR("访问主配置路径 '{}' 时发生 JSON 异常: {}. 将返回默认值。", json_pointer_path, e.what());
				return default_value;
			} catch (const std::exception& e) {
				LOG_ERROR("访问主配置路径 '{}' 时发生未知异常: {}. 将返回默认值。", json_pointer_path, e.what());
				return default_value;
			}
		}

		// --- 工厂注册辅助方法 ---
		/**
		 * @brief 注册一个基础对象 (继承自 EntityObject) 的工厂函数。
		 * @tparam T 要注册的对象类型，必须是 EntityObject 的派生类。
		 * @param type_tag 与此类型关联的字符串标签 (用于在 JSON 配置中标识)。
		 * @param factory_func 创建 T 类型实例的 lambda 或函数。
		 *        工厂函数接收 Environment&, 实例 ID 和原始 JSON 参数。
		 */
		template <typename T>
		void registerObjectType(const std::string& type_tag, std::function<std::shared_ptr<T>(Environment&, const std::string&, const nlohmann::json&)> factory_func) {
			static_assert(std::is_base_of<EntityObject, T>::value, "类型 T 必须派生自 EntityObject才能使用 registerObjectType");

			LOG_DEBUG("注册对象工厂：类型标签 '{}' -> C++ 类型 '{}'", type_tag, typeid(T).name());

			// 使用 lambda 将具体类型的工厂包装成返回 EntityObject 指针的工厂存储
			object_factories_[type_tag] = [factory_func](Environment& env, const std::string& id, const nlohmann::json& params) -> std::shared_ptr<EntityObject> {
				std::shared_ptr<T> specific_ptr = factory_func(env, id, params);
				// 隐式转换为 std::shared_ptr<EntityObject>
				return specific_ptr;
			};
		}

		// 类型别名，表示规划器组件工厂函数的签名
		// 它接收 Environment&, const Config* (用于依赖解析), 实例 ID, 原始 JSON 参数，返回 std::shared_ptr<void>
		using PlannerComponentFactoryFunc = std::function<std::shared_ptr<void>(Environment&, const Config*, const std::string&, const nlohmann::json&)>;

		/**
		 * @brief 为规划器组件 (如 IPathPlanner, ITaskPlanner 等，不继承 EntityObject) 注册工厂。
		 * @tparam InterfaceType 规划器组件的接口类型 (例如 IPathPlanner)。
		 * @tparam ConcreteType 具体的实现类 (例如 RRTStarPlanner)。
		 * @param type_tag 与此类型关联的字符串标签。
		 * @param factory 创建 ConcreteType 实例并返回 std::shared_ptr<void> 的 lambda 或函数。
		 *        工厂函数接收 Environment&, const Config*, 实例 ID 和原始 JSON 参数。
		 */
		template <typename InterfaceType, typename ConcreteType>
		void registerPlannerComponentFactory(const std::string& type_tag, PlannerComponentFactoryFunc factory) {
			// 可以在这里添加编译时检查，例如 ConcreteType 是否实现了 InterfaceType 的某些方法，但这比较复杂。
			// static_assert(std::is_base_of<InterfaceType, ConcreteType>::value, "ConcreteType must implement InterfaceType"); // 对纯接口可能不适用
			LOG_DEBUG("注册规划器组件工厂：类型标签 '{}' (接口: '{}', 实现: '{}')",
				type_tag, typeid(InterfaceType).name(), typeid(ConcreteType).name());

			if (planner_component_factories_.count(type_tag)) {
				LOG_WARN("正在替换已存在的类型标签 '{}' 的规划器组件工厂。", type_tag);
			}
			// 使用 std::move 提高效率 (虽然对于 std::function 可能影响不大)
			planner_component_factories_[type_tag] = std::move(factory);
		}

		/**
		 * @brief 从主配置文件的 'planning_setup' 部分创建并初始化一个核心算法组件实例。
		 *
		 * @tparam InterfaceType 期望的算法接口类型 (例如 ITaskAllocator, IPathPlanner)。
		 * @param algorithm_key 'planning_setup' 中的键名 (例如 "task_allocator", "path_planner")。
		 * @return std::shared_ptr<InterfaceType> 如果成功则返回创建的算法实例，否则返回 nullptr。
		 */
		template<typename InterfaceType>
		std::shared_ptr<InterfaceType> createAlgorithmInstanceFromPlanningSetup(const std::string& algorithm_key);

		/**
		 * @brief 创建TaskSpace实例（特殊处理，因为TaskSpace不是EntityObject）
		 * @param instance_id TaskSpace实例ID
		 * @return 创建的TaskSpace实例，失败返回nullptr
		 */
		std::shared_ptr<TaskSpace> createTaskSpaceInstance(const std::string& instance_id) const;

		~Config(); // 公有析构函数

	private:
		Config(); // 私有构造函数

		Config(const Config&) = delete;
		Config& operator=(const Config&) = delete;

		// --- 私有成员函数声明 ---
		// 解析主配置文件并设置内部路径成员
		bool parseMainConfigAndResolvePaths(const std::filesystem::path& main_config_file_path);

		// 获取指定类型标签的默认 ParamValues 的克隆副本 (带缓存优化)
		std::shared_ptr<NSParams::ParamValues> getClonedDefaultParamValuesForType(const std::string& type_tag) const;

		// 新增：为特定实例创建并配置 ParamValues (私有辅助函数)
		std::shared_ptr<NSParams::ParamValues> createAndConfigureParamValues(
			const std::string& type_tag,
			const nlohmann::json& instance_specific_params_json) const;

		// --- 分离的工厂注册函数 ---
		/** @brief 注册实体对象 (UAV, Obstacle, Zone) 的工厂 */
		bool registerEntityObjectFactories();
		/** @brief 注册核心规划算法组件 (PathPlanner, Optimizer, Evaluator, Allocator) 的工厂 */
		bool registerAlgorithmObjectFactories();
		/** @brief 注册具体任务规划器 (FollowPath, LoiterPoint, ScanArea 等) 的工厂 */
		bool registerTaskPlannerFactories();

		// --- 核心私有成员 ---
		NSParams::ParamRegistry* param_registry_; // 新：指向单例参数注册表的指针
		nlohmann::json main_json_config_;                         // 解析后的主配置文件内容
		std::filesystem::path data_path_;                         // 数据根目录 (例如 ./data)
		std::filesystem::path defines_path_;                      // 参数定义目录 (例如 ./data/defines)
		std::filesystem::path objects_path_;                      // 对象实例配置目录 (例如 ./data/objects)
		std::filesystem::path terrain_path_;                      // 地形数据目录 (例如 ./data/terrain)

		// 缓存最近一次请求的默认 ParamValues 模板，以加速克隆
		mutable std::mutex recent_default_pv_mutex_; // 保护下方两个缓存成员的互斥锁 (因为 getCloned... 是 const)
        mutable std::string recent_type_tag_for_default_pv_template_;
        mutable std::shared_ptr<NSParams::ParamValues> recent_default_pv_template_;

		// 缓存从 objects/*.json 文件预加载的原始实例参数 JSON 片段
		// 键: 实例 ID, 值: pair(类型标签, 参数 JSON 对象)
		std::unordered_map<std::string /*instance_id*/, std::pair<std::string /*type_tag*/, nlohmann::json /*parameters_json*/>> raw_instance_params_cache_;
		mutable std::mutex raw_params_cache_mutex_; // 保护原始参数缓存的互斥锁

		// 存储所有预加载的对象实例的名称
		std::vector<std::string> preloaded_instance_names_;

		// --- 工厂存储 ---
		// 基础对象工厂映射: 类型标签 -> 创建 EntityObject 实例的工厂函数
		std::unordered_map<std::string, std::function<std::shared_ptr<EntityObject>
			(Environment&, const std::string&, const nlohmann::json&)>> object_factories_;

		// 算法对象工厂映射: 按具体类型分类存储
		std::unordered_map<std::string, std::function<std::shared_ptr<NSAlgorithm::ITaskAllocator>(Environment&, const Config*, const std::string&, const nlohmann::json&)>> task_allocator_factories_;
		std::unordered_map<std::string, std::function<std::shared_ptr<NSAlgorithm::IPathPlanner>(Environment&, const Config*, const std::string&, const nlohmann::json&)>> path_planner_factories_;
		std::unordered_map<std::string, std::function<std::shared_ptr<NSAlgorithm::ITrajectoryOptimizer>(Environment&, const Config*, const std::string&, const nlohmann::json&)>> trajectory_optimizer_factories_;
		std::unordered_map<std::string, std::function<std::shared_ptr<NSAlgorithm::ITrajectoryEvaluator>(Environment&, const Config*, const std::string&, const nlohmann::json&)>> trajectory_evaluator_factories_;

		// 保留原有的通用工厂映射用于任务规划器等其他组件
		std::unordered_map<std::string, PlannerComponentFactoryFunc> planner_component_factories_;
	};

} // namespace NSDrones

