#include "core/geometry/shapes/sphere_shape.h"
#include "utils/enum_utils.h"
#include <stdexcept>
#include <sstream>
#include <cmath>
#include <random>

namespace NSDrones{
namespace NSCore {

    SphereShape::SphereShape(double radius) : radius_(radius), fcl_sphere_(nullptr) {
        validateRadius();
    }

    SphereShape::SphereShape() : SphereShape(1.0) {
    }

    SphereShape::SphereShape(const SphereShape& other)
        : radius_(other.radius_), fcl_sphere_(nullptr) {
    }

    SphereShape& SphereShape::operator=(const SphereShape& other) {
        if (this != &other) {
            radius_ = other.radius_;
            fcl_sphere_.reset();  // 重置FCL对象，延迟重新创建
        }
        return *this;
    }

    std::shared_ptr<fcl::CollisionGeometryd> SphereShape::getFCLGeometry() const {
        ensureFCLObject();
        return fcl_sphere_;
    }

    fcl::AABBd SphereShape::getAABB(const fcl::Transform3d& transform) const {
        ensureFCLObject();
        fcl::AABBd aabb;
        fcl::computeBV(*fcl_sphere_, transform, aabb);
        return aabb;
    }

    double SphereShape::getVolume() const {
        return (4.0 / 3.0) * M_PI * radius_ * radius_ * radius_;
    }

    double SphereShape::getSurfaceArea() const {
        return 4.0 * M_PI * radius_ * radius_;
    }

    fcl::Vector3d SphereShape::getCentroid() const {
        return fcl::Vector3d(0.0, 0.0, 0.0);  // 几何中心在原点
    }

    fcl::Matrix3d SphereShape::getInertiaMatrix(double mass) const {
        // 球体的惯性张量（相对于质心）
        double inertia_value = (2.0 / 5.0) * mass * radius_ * radius_;
        
        fcl::Matrix3d inertia = fcl::Matrix3d::Zero();
        inertia(0, 0) = inertia_value;  // Ixx
        inertia(1, 1) = inertia_value;  // Iyy
        inertia(2, 2) = inertia_value;  // Izz
        
        return inertia;
    }

    std::unique_ptr<IShape> SphereShape::clone() const {
        return std::make_unique<SphereShape>(*this);
    }

    nlohmann::json SphereShape::serialize() const {
        nlohmann::json j;
        j["type"] = NSUtils::enumToString(getType());
        j["radius"] = radius_;
        return j;
    }

    bool SphereShape::deserialize(const nlohmann::json& json) {
        try {
            std::string type_str = json.at("type").get<std::string>();
            auto type_opt = NSUtils::stringToEnum<ShapeType>(type_str, ShapeType::UNKNOWN);
            if (type_opt != ShapeType::SPHERE) {
                return false;
            }

            radius_ = json.at("radius").get<double>();

            validateRadius();
            fcl_sphere_.reset();  // 重置FCL对象
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    std::string SphereShape::toString() const {
        std::ostringstream oss;
        oss << "SphereShape(radius=" << radius_ << ")";
        return oss.str();
    }

    bool SphereShape::containsPoint(const fcl::Vector3d& point) const {
        double distance_squared = point.squaredNorm();
        return distance_squared <= (radius_ * radius_);
    }

    double SphereShape::distanceToPoint(const fcl::Vector3d& point) const {
        double distance = point.norm();
        return distance - radius_;  // 负值表示在内部
    }

    double SphereShape::getCharacteristicSize() const {
        return 2.0 * radius_;  // 直径作为特征尺寸
    }

    void SphereShape::setRadius(double radius) {
        radius_ = radius;
        validateRadius();
        fcl_sphere_.reset();  // 重置FCL对象
    }

    fcl::Vector3d SphereShape::getPointOnSphere(double theta, double phi, 
                                               const fcl::Transform3d& transform) const {
        // 球坐标转换为笛卡尔坐标
        // theta: 极角 (0 到 π)
        // phi: 方位角 (0 到 2π)
        double x = radius_ * std::sin(theta) * std::cos(phi);
        double y = radius_ * std::sin(theta) * std::sin(phi);
        double z = radius_ * std::cos(theta);
        
        fcl::Vector3d point(x, y, z);
        return transform * point;
    }

    std::vector<fcl::Vector3d> SphereShape::generateUniformPoints(size_t num_points,
                                                                 const fcl::Transform3d& transform) const {
        std::vector<fcl::Vector3d> points;
        points.reserve(num_points);
        
        // 使用Fibonacci螺旋算法生成均匀分布的点
        double golden_ratio = (1.0 + std::sqrt(5.0)) / 2.0;
        
        for (size_t i = 0; i < num_points; ++i) {
            double theta = 2.0 * M_PI * i / golden_ratio;
            double phi = std::acos(1.0 - 2.0 * (i + 0.5) / num_points);
            
            double x = radius_ * std::sin(phi) * std::cos(theta);
            double y = radius_ * std::sin(phi) * std::sin(theta);
            double z = radius_ * std::cos(phi);
            
            fcl::Vector3d point(x, y, z);
            points.push_back(transform * point);
        }
        
        return points;
    }

    fcl::Vector3d SphereShape::getSurfaceNormal(const fcl::Vector3d& point,
                                               const fcl::Transform3d& transform) const {
        // 将点转换到局部坐标系
        fcl::Vector3d local_point = transform.inverse() * point;
        
        // 球面法向量就是从中心指向点的单位向量
        fcl::Vector3d normal = local_point.normalized();
        
        // 转换回全局坐标系（只应用旋转，不应用平移）
        return transform.rotation() * normal;
    }

    std::unique_ptr<SphereShape> SphereShape::createUnitSphere() {
        return std::make_unique<SphereShape>(1.0);
    }

    std::unique_ptr<SphereShape> SphereShape::createFromDiameter(double diameter) {
        return std::make_unique<SphereShape>(diameter * 0.5);
    }

    void SphereShape::ensureFCLObject() const {
        if (!fcl_sphere_) {
            fcl_sphere_ = std::make_shared<fcl::Sphered>(radius_);
        }
    }

    void SphereShape::validateRadius() const {
        if (radius_ <= 0.0) {
            throw std::invalid_argument("SphereShape: 半径必须为正数");
        }
        if (!std::isfinite(radius_)) {
            throw std::invalid_argument("SphereShape: 半径必须为有限数值");
        }
    }

} // namespace NSCore
} // namespace NSDrones