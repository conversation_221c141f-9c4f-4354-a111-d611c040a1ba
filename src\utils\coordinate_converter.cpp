#include "utils/coordinate_converter.h"
#include "utils/logging.h"
#include <GeographicLib/Constants.hpp>
#include <cmath>

namespace NSDrones {
namespace NSUtils {

	const GeographicLib::Geocentric& CoordinateConverter::getGeocentric() {
		// 线程安全的静态局部变量
		static const GeographicLib::Geocentric geocentric(
			GeographicLib::Constants::WGS84_a(), 
			GeographicLib::Constants::WGS84_f()
		);
		return geocentric;
	}

	EcefPoint CoordinateConverter::wgs84ToECEF(const WGS84Point& wgs84_pos) {
		const auto& earth = getGeocentric();

		double x, y, z;
		earth.Forward(wgs84_pos.latitude, wgs84_pos.longitude, wgs84_pos.altitude, x, y, z);

		LOG_TRACE("坐标转换: WGS84 [{:.6f},{:.6f},{:.1f}] -> ECEF [{:.2f},{:.2f},{:.2f}]",
			wgs84_pos.longitude, wgs84_pos.latitude, wgs84_pos.altitude, x, y, z);

		return EcefPoint(x, y, z);
	}

	WGS84Point CoordinateConverter::ecefToWGS84(const EcefPoint& ecef_pos) {
		const auto& earth = getGeocentric();

		double lat, lon, alt;
		earth.Reverse(ecef_pos.x(), ecef_pos.y(), ecef_pos.z(), lat, lon, alt);

		WGS84Point result(lon, lat, alt); // WGS84Point构造函数: (经度, 纬度, 高度)

		LOG_TRACE("坐标转换: ECEF [{:.2f},{:.2f},{:.2f}] -> WGS84 [{:.6f},{:.6f},{:.1f}]",
			ecef_pos.x(), ecef_pos.y(), ecef_pos.z(), lon, lat, alt);

		return result;
	}

	std::vector<EcefPoint> CoordinateConverter::batchWgs84ToECEF(const std::vector<WGS84Point>& wgs84_points) {
		std::vector<EcefPoint> results;
		results.reserve(wgs84_points.size());

		for (const auto& point : wgs84_points) {
			results.push_back(wgs84ToECEF(point));
		}

		LOG_DEBUG("坐标转换: 批量转换 {} 个WGS84坐标点到ECEF", wgs84_points.size());
		return results;
	}

	std::vector<WGS84Point> CoordinateConverter::batchEcefToWGS84(const std::vector<EcefPoint>& ecef_points) {
		std::vector<WGS84Point> results;
		results.reserve(ecef_points.size());

		for (const auto& point : ecef_points) {
			results.push_back(ecefToWGS84(point));
		}

		LOG_DEBUG("坐标转换: 批量转换 {} 个ECEF坐标点到WGS84", ecef_points.size());
		return results;
	}

	double CoordinateConverter::distanceECEF(const EcefPoint& ecef_pos1, const EcefPoint& ecef_pos2) {
		Vector3D diff = ecef_pos2 - ecef_pos1;
		double distance = diff.norm();

		LOG_TRACE("ECEF距离计算: [{:.2f},{:.2f},{:.2f}] - [{:.2f},{:.2f},{:.2f}] = {:.2f}m",
			ecef_pos1.x(), ecef_pos1.y(), ecef_pos1.z(),
			ecef_pos2.x(), ecef_pos2.y(), ecef_pos2.z(), distance);

		return distance;
	}

	Vector3D CoordinateConverter::velocityECEF(const EcefPoint& ecef_pos1, const EcefPoint& ecef_pos2, double time_delta) {
		if (std::abs(time_delta) < 1e-9) {
			LOG_WARN("ECEF速度计算: 时间间隔过小 ({:.9f}s)，返回零速度", time_delta);
			return Vector3D::Zero();
		}

		Vector3D displacement = ecef_pos2 - ecef_pos1;
		Vector3D velocity = displacement / time_delta;

		LOG_TRACE("ECEF速度计算: 位移 [{:.2f},{:.2f},{:.2f}] / 时间 {:.3f}s = 速度 [{:.2f},{:.2f},{:.2f}] m/s",
			displacement.x(), displacement.y(), displacement.z(), time_delta,
			velocity.x(), velocity.y(), velocity.z());

		return velocity;
	}

	Vector3D CoordinateConverter::accelerationECEF(const Vector3D& ecef_vel1, const Vector3D& ecef_vel2, double time_delta) {
		if (std::abs(time_delta) < 1e-9) {
			LOG_WARN("ECEF加速度计算: 时间间隔过小 ({:.9f}s)，返回零加速度", time_delta);
			return Vector3D::Zero();
		}

		Vector3D velocity_change = ecef_vel2 - ecef_vel1;
		Vector3D acceleration = velocity_change / time_delta;
		
		LOG_TRACE("ECEF加速度计算: 速度变化 [{:.2f},{:.2f},{:.2f}] / 时间 {:.3f}s = 加速度 [{:.2f},{:.2f},{:.2f}] m/s²",
			velocity_change.x(), velocity_change.y(), velocity_change.z(), time_delta,
			acceleration.x(), acceleration.y(), acceleration.z());
		
		return acceleration;
	}

	EcefPoint CoordinateConverter::predictPositionECEF(const EcefPoint& ecef_pos, const Vector3D& ecef_velocity, double time_delta) {
		EcefPoint predicted_pos = ecef_pos + ecef_velocity * time_delta;

		LOG_TRACE("ECEF位置预测: 当前 [{:.2f},{:.2f},{:.2f}] + 速度 [{:.2f},{:.2f},{:.2f}] * 时间 {:.3f}s = 预测 [{:.2f},{:.2f},{:.2f}]",
			ecef_pos.x(), ecef_pos.y(), ecef_pos.z(),
			ecef_velocity.x(), ecef_velocity.y(), ecef_velocity.z(), time_delta,
			predicted_pos.x(), predicted_pos.y(), predicted_pos.z());

		return predicted_pos;
	}

	EcefPoint CoordinateConverter::predictPositionECEF(const EcefPoint& ecef_pos, const Vector3D& ecef_velocity, const Vector3D& ecef_acceleration, double time_delta) {
		// 使用运动学公式: s = s0 + v0*t + 0.5*a*t²
		EcefPoint predicted_pos = ecef_pos + ecef_velocity * time_delta + 0.5 * ecef_acceleration * (time_delta * time_delta);

		LOG_TRACE("ECEF位置预测(含加速度): 当前 [{:.2f},{:.2f},{:.2f}] + 速度项 [{:.2f},{:.2f},{:.2f}] + 加速度项 [{:.2f},{:.2f},{:.2f}] = 预测 [{:.2f},{:.2f},{:.2f}]",
			ecef_pos.x(), ecef_pos.y(), ecef_pos.z(),
			(ecef_velocity * time_delta).x(), (ecef_velocity * time_delta).y(), (ecef_velocity * time_delta).z(),
			(0.5 * ecef_acceleration * time_delta * time_delta).x(), (0.5 * ecef_acceleration * time_delta * time_delta).y(), (0.5 * ecef_acceleration * time_delta * time_delta).z(),
			predicted_pos.x(), predicted_pos.y(), predicted_pos.z());

		return predicted_pos;
	}

} // namespace NSUtils
} // namespace NSDrones
