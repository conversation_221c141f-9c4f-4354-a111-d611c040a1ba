// src/config.cpp
#include "config.h"
#include "drones.h"

namespace fs = std::filesystem;

using json = nlohmann::json;
using namespace NSDrones::NSParams;
using namespace NSDrones::NSCore;
using namespace NSDrones::NSUtils;
using namespace NSDrones::NSEnvironment;
using namespace NSDrones::NSUav; // 添加 UAV 命名空间

namespace NSDrones {

	// --- Config类静态常量定义 ---
	const std::string Config::KEY_GLOBAL_PARAMETER_DEFINITIONS_NODE = "global_parameter_definitions";
	const std::string Config::KEY_GLOBAL_PARAMETER_VALUES_NODE = "global_parameter_values";
	const std::string Config::KEY_PARAMETERS = "parameters";
	const std::string Config::KEY_TYPE_TAG = "type_tag";
	const std::string Config::KEY_INSTANCE_ID = "instance_id";
	const std::string Config::KEY_ID = "id";
	// 空间索引相关KEY（仅配置用，代码未直接用）
	const std::string Config::KEY_SI_ORIGIN_X = "spatial_index.origin.x"; // 仅配置用
	const std::string Config::KEY_SI_ORIGIN_Y = "spatial_index.origin.y"; // 仅配置用
	const std::string Config::KEY_SI_ORIGIN_Z = "spatial_index.origin.z"; // 仅配置用
	const std::string Config::KEY_SI_BOUNDS_MIN_X = "spatial_index.bounds.min_x"; // 仅配置用
	const std::string Config::KEY_SI_BOUNDS_MIN_Y = "spatial_index.bounds.min_y"; // 仅配置用
	const std::string Config::KEY_SI_BOUNDS_MIN_Z = "spatial_index.bounds.min_z"; // 仅配置用
	const std::string Config::KEY_SI_BOUNDS_MAX_X = "spatial_index.bounds.max_x"; // 仅配置用
	const std::string Config::KEY_SI_BOUNDS_MAX_Y = "spatial_index.bounds.max_y"; // 仅配置用
	const std::string Config::KEY_SI_BOUNDS_MAX_Z = "spatial_index.bounds.max_z"; // 仅配置用
	const std::string Config::KEY_SI_LEVELS = "spatial_index.levels"; // 仅配置用
	const std::string Config::KEY_SI_BASE_RESOLUTION = "spatial_index.base_resolution"; // 仅配置用
	const std::string Config::KEY_SI_LEVEL_FACTOR = "spatial_index.level_factor"; // 仅配置用

	const std::string Config::GLOBAL_PARAMS_TYPE_TAG = "global_environment_params"; // 新增定义

	/**
	 * @brief 构造函数，指定主配置文件路径
	 */
	Config::Config(const std::string& config_file)
		: param_registry_(&NSParams::ParamRegistry::getInstance()) {
		LOG_INFO("正在使用配置文件 '{}' 初始化...", config_file);
		if (!parseMainConfigAndResolvePaths(config_file)) {
			LOG_CRITICAL("解析主配置文件 '{}' 失败。配置系统可能无法正常工作。", config_file);
			throw std::runtime_error("Failed to parse main config file: " + config_file);
		}
		LOG_DEBUG("主配置文件解析完成。数据路径: '{}', 定义路径: '{}', 对象路径: '{}'",
			data_path_.string(), defines_path_.string(), objects_path_.string());
		LOG_INFO("Config instance created at {}. (Not yet initialized)", fmt::ptr(this));
	}

	Config::~Config() {
		LOG_INFO("Config instance at {} is being destroyed.", fmt::ptr(this));
		// 如果有 uninitialize 逻辑，确保它在析构前被调用或在这里处理
		// 清理工厂等
		object_factories_.clear();
		//algorithm_factories_.clear();
		LOG_DEBUG("Config: Cleared object and algorithm factories during destruction.");
	}

	// --- 主初始化函数 ---
	/**
	 * @brief 初始化配置系统，创建Environment, 加载主配置、参数定义、全局参数等
	 * @return 初始化成功返回true
	 */
	bool Config::initialize() {
		LOG_INFO("开始核心初始化流程 (initialize)...⏳");
		if (!param_registry_) {
			LOG_ERROR("ParamRegistry 未初始化!");
			return false;
		}

		// 初始化并注册所有枚举类型
		LOG_INFO("步骤 0.1/5 - 正在初始化并注册所有枚举类型...");
		NSDrones::NSUtils::initializeAndRegisterAllEnums();
		LOG_INFO("步骤 0.1/5 - 所有枚举类型已初始化并注册。");

		LOG_INFO("步骤 0.2/5 - 正在创建 Environment 实例..."); // 调整步骤编号
		try {
			auto environment = Environment::createInstance(*param_registry_);
			if (!environment) {
				LOG_CRITICAL("创建 Environment 实例失败。");
				return false;
			}
		}
		catch (const std::exception& e) {
			LOG_CRITICAL("创建 Environment 实例时发生异常: {}", e.what());
			return false;
		}
		LOG_INFO("步骤 0.2/5 - Environment 实例创建成功。"); // 调整步骤编号

		LOG_INFO("步骤 1/5 - 正在加载全局参数定义...");
		if (!loadGlobalParamDefines()) { // 调用修改后的 loadGlobalParamDefines
			LOG_ERROR("加载全局参数定义失败。");
			return false;
		}
		LOG_INFO("步骤 1/5 - 全局参数定义加载完成。");

		LOG_INFO("步骤 2/5 - 正在加载 'defines' 目录参数定义...");
		if (!loadDirectoryParamDefines()) {
			LOG_ERROR("加载 'defines' 目录参数定义失败。");
			return false;
		}
		LOG_INFO("步骤 2/5 - 'defines' 目录参数定义加载完成。");

		LOG_INFO("步骤 3/5 - 正在请求 Environment 执行其综合初始化...");
		nlohmann::json global_values_json_node;
		if (main_json_config_.contains(KEY_GLOBAL_PARAMETER_VALUES_NODE) && main_json_config_[KEY_GLOBAL_PARAMETER_VALUES_NODE].is_object()) {
			global_values_json_node = main_json_config_[KEY_GLOBAL_PARAMETER_VALUES_NODE];
		}
		else {
			LOG_INFO("主配置文件中未找到 '{}' 节点或其不是对象，Environment 将使用其全局参数的默认值。", KEY_GLOBAL_PARAMETER_VALUES_NODE);
			global_values_json_node = nlohmann::json::object();
		}

		auto environment = Environment::getInstance();
		if (!environment || !environment->initialize(global_values_json_node)) {
			LOG_ERROR("Environment 主要初始化流程失败！");
			return false;
		}
		else {
			LOG_INFO("Environment 主要初始化流程已成功完成。");
		}
		LOG_INFO("步骤 3/5 - Environment 综合初始化完成。");

		// Environment 已经在其 initialize() 方法中正确处理了坐标系统管理器和全局任务空间的设置
		// 包括从配置文件读取 WGS84 原点并创建全局坐标空间，因此这里不需要重复设置
		LOG_INFO("步骤 3.5/5 - Environment 依赖组件设置完成（坐标系统已在 Environment::initialize() 中设置）。");

		// 暂时注释掉CollisionEngine的创建，因为接口可能还未完全实现
		// auto collisionEngine = std::make_shared<CollisionEngine>();
		// environment->setCollisionEngine(collisionEngine);
		LOG_INFO("CollisionEngine 创建已暂时跳过。");

		LOG_INFO("步骤 4/5 - 正在注册对象工厂...");
		if (!this->registerObjectFactories()) {
			LOG_CRITICAL("注册对象工厂失败。");
			return false;
		}
		LOG_INFO("步骤 4/5 - 对象工厂注册完成。");

		LOG_INFO("步骤 5/5 - 正在预加载对象实例...");
		preloadAllObjectInstanceFiles();
		LOG_INFO("步骤 5/5 - 对象实例预加载完成。");

		LOG_INFO("核心初始化流程 (initialize) 成功完成。✅");
		return true;
	}

	std::vector<std::string> Config::getPreloadedInstanceNames() const {
		LOG_DEBUG("请求获取预加载的实例名称列表 (共 {} 个)。", preloaded_instance_names_.size());
		return this->preloaded_instance_names_;
	}

	// --- 私有辅助方法: 解析主配置文件并解析路径 ---
	bool Config::parseMainConfigAndResolvePaths(const fs::path& main_config_file_path) {
		LOG_INFO("步骤 1/4 - 正在解析主配置文件并设定相关路径: '{}'", main_config_file_path.generic_string());
		if (!NSUtils::isFileExists(main_config_file_path)) {
			LOG_ERROR("致命错误 - 主配置文件未找到: '{}'", main_config_file_path.generic_string());
			return false;
		}

		std::optional<std::string> content = NSUtils::readFileToString(main_config_file_path);
		if (!content) {
			LOG_ERROR("致命错误 - 无法读取主配置文件内容: '{}'", main_config_file_path.generic_string());
			return false;
		}
		LOG_DEBUG("成功读取主配置文件内容 (大小: {} 字节)。", content->length());

		try {
			this->main_json_config_ = json::parse(*content, nullptr, true, true);
		}
		catch (const json::parse_error& e) {
			LOG_CRITICAL("解析主配置文件JSON失败: {}. 错误详情: {}", main_config_file_path.generic_string(), e.what());
			return false;
		}
		LOG_DEBUG("主配置文件 JSON 内容成功解析。");

		// 主配置文件的父目录就是 data 目录
		this->data_path_ = main_config_file_path.parent_path();
		this->defines_path_ = this->data_path_ / "defines"; // data/defines
		this->objects_path_ = this->data_path_ / "objects"; // data/objects
		this->terrain_path_ = this->data_path_ / "terrain"; // data/terrain

		LOG_INFO("数据根目录 (data_path_): '{}'", this->data_path_.generic_string());
		LOG_INFO("参数定义目录 (defines_path_): '{}'", this->defines_path_.generic_string());
		LOG_INFO("对象实例目录 (objects_path_): '{}'", this->objects_path_.generic_string());
		LOG_INFO("地形数据目录 (terrain_path_): '{}'", this->terrain_path_.generic_string());

		LOG_INFO("主配置文件解析和路径设定成功完成。");
		return true;
	}

	/**
	 * @brief 注册所有内置的对象类型和规划器组件的工厂函数。
	 *        此方法现在委托给三个独立的私有方法来分别注册不同类型的工厂。
	 * @return 如果 Environment 有效且所有注册步骤（实体、算法、任务）都成功，则返回 true。
	 */
	bool Config::registerObjectFactories() {
		LOG_INFO("开始注册所有对象和规划器组件的工厂 (分阶段进行)...");

		// 前置检查：确保 Environment 实例有效
		auto environment = Environment::getInstance();
		if (!environment) {
			LOG_ERROR("注册工厂失败 - Environment 实例为空。无法继续注册。");
			return false;
		}
		LOG_DEBUG("Environment 实例有效，开始调用具体注册函数。");

		bool success = true;

		// 1. 注册实体对象工厂
		success &= registerEntityObjectFactories();

		// 2. 注册核心算法组件工厂
		success &= registerAlgorithmObjectFactories();

		// 3. 注册任务规划器工厂
		success &= registerTaskPlannerFactories();

		if (success) {
			LOG_INFO("所有对象和规划器组件的工厂均已成功注册完毕。");
		}
		else {
			LOG_ERROR("在注册一个或多个工厂组时发生错误。请检查之前的日志。");
		}

		return success;
	}

	/**
	 * @brief (私有) 注册基础实体对象 (UAV, Obstacle, Zone) 的工厂。
	 *        这些对象通常继承自 EntityObject。
	 * @return 总是返回 true (当前实现没有失败条件，但保留 bool 返回值以便未来扩展)。
	 */
	bool Config::registerEntityObjectFactories() {
		LOG_INFO("注册阶段 1/3 - 注册基础实体对象工厂 (UAV, Obstacle, Zone)...");

		// 确保 Environment 实例有效
		auto environment = Environment::getInstance();
		if (!environment) {
			LOG_ERROR("registerEntityObjectFactories 失败 - Environment 实例为空。");
			return false;
		}

		// 注册 VTOL UAV 工厂
		this->object_factories_["EntityObject.Uav.VTOL"] =
			[](Environment& env, const std::string& id_str, const nlohmann::json& raw_instance_params) -> std::shared_ptr<EntityObject> {
			LOG_TRACE("配置(Config) - VTOL UAV工厂: 尝试创建实例ID: '{}'", id_str);
			ObjectID id(id_str);
			auto uav_obj = std::make_shared<NSUav::Uav>(id, "EntityObject.Uav.VTOL");
			LOG_INFO("配置(Config) - VTOL UAV工厂: 已创建 VTOL UAV 基础实例 '{}'。原始实例参数JSON大小: {}. 完整参数将在后续初始化中设置。", id_str, raw_instance_params.size());
			return uav_obj;
		};
		LOG_DEBUG("已在 Config 内部注册实体对象工厂: EntityObject.Uav.VTOL -> (创建 NSUav::Uav 实例)");

		// 注册 Multirotor UAV 工厂
		this->object_factories_["EntityObject.Uav.Multirotor"] =
			[](Environment& env, const std::string& id_str, const nlohmann::json& raw_instance_params) -> std::shared_ptr<EntityObject> {
			LOG_TRACE("配置(Config) - Multirotor UAV工厂: 尝试创建实例ID: '{}'", id_str);
			ObjectID id(id_str);
			auto uav_obj = std::make_shared<NSUav::Uav>(id, "EntityObject.Uav.Multirotor");
			LOG_INFO("配置(Config) - Multirotor UAV工厂: 已创建 Multirotor UAV 基础实例 '{}'。等待第二阶段参数初始化。", id_str);
			return uav_obj;
		};
		LOG_DEBUG("已在 Config 内部注册实体对象工厂: EntityObject.Uav.Multirotor -> (创建 NSUav::Uav 实例)");

		// 注册 FixedWing UAV 工厂
		this->object_factories_["EntityObject.Uav.FixedWing"] =
			[](Environment& env, const std::string& id_str, const nlohmann::json& raw_instance_params) -> std::shared_ptr<EntityObject> {
			LOG_TRACE("配置(Config) - FixedWing UAV工厂: 尝试创建实例ID: '{}'", id_str);
			ObjectID id(id_str);
			auto uav_obj = std::make_shared<NSUav::Uav>(id, "EntityObject.Uav.FixedWing");
			LOG_INFO("配置(Config) - FixedWing UAV工厂: 已创建 FixedWing UAV 基础实例 '{}'。等待第二阶段参数初始化。", id_str);
			return uav_obj;
		};
		LOG_DEBUG("已在 Config 内部注册实体对象工厂: EntityObject.Uav.FixedWing -> (创建 NSUav::Uav 实例)");

		// 注册 Obstacle (障碍物) 工厂 - 使用完整的类型标签
		this->object_factories_["EntityObject.Obstacle.Building"] =
			[](Environment& env, const std::string& id_str, const nlohmann::json& raw_instance_params) -> std::shared_ptr<EntityObject> {
			LOG_TRACE("配置(Config) - Obstacle工厂: 尝试创建障碍物，实例ID: '{}'", id_str);
			ObjectID id(id_str);
			// 先创建 Obstacle 对象（传递 nullptr，让构造函数处理策略创建）
			auto obstacle_obj = std::make_shared<Obstacle>(id, "EntityObject.Obstacle.Building", id, EntityState(), nullptr);
			LOG_INFO("配置(Config) - Obstacle工厂: 成功创建障碍物实例 '{}'。等待第二阶段参数初始化。", id_str);
			return obstacle_obj;
		};
		LOG_DEBUG("已在 Config 内部注册实体对象工厂: EntityObject.Obstacle.Building -> (创建 Obstacle 实例)");

		// 注册 Zone (区域) 工厂 - 使用完整的类型标签
		this->object_factories_["EntityObject.Zone.NoFly"] =
			[](Environment& env, const std::string& id_str, const nlohmann::json& raw_instance_params) -> std::shared_ptr<EntityObject> {
			LOG_TRACE("配置(Config) - Zone.NoFly工厂: 尝试创建禁飞区域，实例ID: '{}'", id_str);
			ObjectID id(id_str);
			auto zone_obj = std::make_shared<Zone>(id, "EntityObject.Zone.NoFly", id, EntityState(), nullptr);
			LOG_INFO("配置(Config) - Zone.NoFly工厂: 成功创建禁飞区域实例 '{}'。等待第二阶段参数初始化。", id_str);
			return zone_obj;
		};
		LOG_DEBUG("已在 Config 内部注册实体对象工厂: EntityObject.Zone.NoFly -> (创建 Zone 实例)");

		this->object_factories_["EntityObject.Zone.Recon"] =
			[](Environment& env, const std::string& id_str, const nlohmann::json& raw_instance_params) -> std::shared_ptr<EntityObject> {
			LOG_TRACE("配置(Config) - Zone.Recon工厂: 尝试创建侦察区域，实例ID: '{}'", id_str);
			ObjectID id(id_str);
			auto zone_obj = std::make_shared<Zone>(id, "EntityObject.Zone.Recon", id, EntityState(), nullptr);
			LOG_INFO("配置(Config) - Zone.Recon工厂: 成功创建侦察区域实例 '{}'。等待第二阶段参数初始化。", id_str);
			return zone_obj;
		};
		LOG_DEBUG("已在 Config 内部注册实体对象工厂: EntityObject.Zone.Recon -> (创建 Zone 实例)");

		// 注意：TaskSpace不再通过EntityObject工厂创建，而是通过专门的createTaskSpaceInstance方法创建
		// 这样可以避免TaskSpace与EntityObject的混淆，因为TaskSpace是基础设施组件，不是实体对象

		LOG_INFO("注册阶段 1/3 - 基础实体对象工厂注册完毕。");
		return true; // 假设总是成功
	}

	std::shared_ptr<TaskSpace> Config::createTaskSpaceInstance(const std::string& instance_id) const {
		LOG_DEBUG("请求创建TaskSpace实例 '{}'", instance_id);

		auto environment = Environment::getInstance();
		if (!environment) {
			LOG_ERROR("Environment 未初始化，无法创建TaskSpace实例 '{}'", instance_id);
			return nullptr;
		}

		std::string type_tag;
		nlohmann::json parameters_json;

		// 1. 从缓存检索原始参数 JSON
		{
			std::lock_guard<std::mutex> lock(raw_params_cache_mutex_);
			auto it = raw_instance_params_cache_.find(instance_id);
			if (it == raw_instance_params_cache_.end()) {
				LOG_ERROR("在原始参数缓存中未找到TaskSpace实例 '{}' 的配置", instance_id);
				return nullptr;
			}
			type_tag = it->second.first;
			parameters_json = it->second.second;
			LOG_TRACE("检索到TaskSpace实例 '{}' 的原始参数：类型标签='{}', JSON 大小={}",
				instance_id, type_tag, parameters_json.size());
		}

		// 2. 验证是否为TaskSpace类型
		if (type_tag.find("TaskSpace.") != 0) {
			LOG_ERROR("实例 '{}' 的类型标签 '{}' 不是TaskSpace类型", instance_id, type_tag);
			return nullptr;
		}

		// 3. 获取对应类型标签的默认 ParamValues
		LOG_DEBUG("正在为TaskSpace实例 '{}' (类型标签 '{}') 获取默认 ParamValues...", instance_id, type_tag);
		std::shared_ptr<NSParams::ParamValues> final_param_values = getClonedDefaultParamValuesForType(type_tag);
		if (!final_param_values) {
			LOG_ERROR("无法为TaskSpace实例 '{}' 获取或克隆类型标签 '{}' 的默认 ParamValues", instance_id, type_tag);
			return nullptr;
		}

		// 4. 将实例特定的 JSON 参数覆盖到克隆的默认值上
		if (!parameters_json.empty() && parameters_json.is_object()) {
			LOG_DEBUG("正在为TaskSpace实例 '{}' 应用 JSON 中定义的 {} 个特定参数...", instance_id, parameters_json.size());
			if (!final_param_values->valuesFromJson(parameters_json, *param_registry_, nullptr, true)) {
				LOG_ERROR("为TaskSpace实例 '{}' (类型 '{}') 应用实例特定参数失败", instance_id, type_tag);
				return nullptr;
			}
		}

		// 5. 解析参数并调用CoordinateManager的方法创建TaskSpace
		try {
			// 获取CoordinateManager
			auto coord_manager = environment->getCoordinateManager();

			bool success = false;
			if (type_tag == "TaskSpace.StaticTaskSpace") {
				// 解析静态TaskSpace参数
				auto origin_param = final_param_values->getValue<WGS84Point>("origin");
				if (!origin_param) {
					LOG_ERROR("静态TaskSpace '{}' 缺少origin参数", instance_id);
					return nullptr;
				}

				// 创建静态TaskSpace（简化版本，不再使用BVHIndexConfig和CoverageSpace）
				success = coord_manager->createStaticTaskSpace(instance_id, *origin_param, type_tag);

			}
			else if (type_tag == "TaskSpace.DynamicTaskSpace") {
				// 解析动态TaskSpace参数
				auto follow_entity_param = final_param_values->getValue<std::string>("follow_entity_id");
				if (!follow_entity_param) {
					LOG_ERROR("动态TaskSpace '{}' 缺少follow_entity_id参数", instance_id);
					return nullptr;
				}

				ObjectID reference_entity_id(*follow_entity_param);

				// 创建动态TaskSpace（简化版本，不再使用BVHIndexConfig和CoverageSpace）
				success = coord_manager->createDynamicTaskSpace(instance_id, reference_entity_id, type_tag);

			}
			else {
				LOG_ERROR("未知的TaskSpace类型 '{}'", type_tag);
				return nullptr;
			}

			if (success) {
				LOG_INFO("成功创建TaskSpace实例 '{}' (类型标签 '{}')", instance_id, type_tag);

				// 获取创建的TaskSpace实例
				auto task_space = coord_manager->getTaskSpace(instance_id);
				if (!task_space) {
					LOG_ERROR("创建TaskSpace '{}' 后无法获取实例", instance_id);
					return nullptr;
				}

				// 注意：TaskSpace和TiledBVHIndex之间的关系目前是手工维护的
				LOG_DEBUG("TaskSpace '{}' 创建完成，如需要请手工更新TiledBVHIndex配置", instance_id);

				return task_space;
			}
			else {
				LOG_ERROR("CoordinateManager创建TaskSpace实例 '{}' 失败", instance_id);
				return nullptr;
			}

		}
		catch (const std::exception& e) {
			LOG_ERROR("创建TaskSpace实例 '{}' 时发生异常: {}", instance_id, e.what());
			return nullptr;
		}
	}

	/**
	 * @brief (私有) 注册核心规划算法组件 (路径规划器、轨迹优化器、轨迹评估器、任务分配器) 的工厂。
	 * @return 总是返回 true (当前实现没有失败条件，但保留 bool 返回值以便未来扩展)。
	 */
	bool Config::registerAlgorithmObjectFactories() {
		LOG_INFO("注册阶段 2/3 - 注册核心规划算法组件工厂...");
		using namespace NSAlgorithm; // 添加 NSAlgorithm 命名空间

		// -- 路径规划器 (Path Planner) 工厂 --
		path_planner_factories_["AlgorithmObject.PathPlanner.RRTStar"] =
			[this](Environment& env, const Config* cfg_ptr, const std::string& id_str, const json& params) -> std::shared_ptr<NSAlgorithm::IPathPlanner> {
			LOG_TRACE("配置(Config) - RRT*规划器工厂: 尝试创建实例 '{}'", id_str);
			try {
				auto env_sptr = env.shared_from_this();
				if (!env_sptr) {
					LOG_ERROR("RRT* 工厂: env.shared_from_this() is nullptr for '{}'", id_str);
					return nullptr;
				}
				auto planner = std::make_shared<RRTStarPlanner>(
					ObjectID(id_str),                             // 参数1: id
					"AlgorithmObject.PathPlanner.RRTStar",              // 参数2: type_tag (工厂键作为默认type_tag)
					params.value("name", id_str),                     // 参数3: name (从json或用id)
					params.value("version", "1.0.0.factory")        // 参数4: version (从json或默认)
				);
				LOG_INFO("配置(Config) - RRT*规划器工厂: 成功创建 RRT* 规划器实例 '{}' (参数初始化待定)。", id_str);
				return planner;
			}
			catch (const std::exception& e) {
				LOG_ERROR("配置(Config) - RRT*规划器工厂 '{}': 创建时异常: {}", id_str, e.what());
				return nullptr;
			}
		};
		LOG_DEBUG("已注册算法工厂: AlgorithmObject.PathPlanner.RRTStar -> RRTStarPlanner");

		// -- 轨迹优化器 (Trajectory Optimizer) 工厂 --
		trajectory_optimizer_factories_["AlgorithmObject.TrajectoryOptimizer.BSpline"] =
			[this](Environment& env, const Config* cfg_ptr, const std::string& id_str, const json& params) -> std::shared_ptr<NSAlgorithm::ITrajectoryOptimizer> {
			LOG_TRACE("配置(Config) - 轨迹优化器工厂: 尝试创建实例 '{}'", id_str);
			try {
				std::shared_ptr<Environment> env_sptr = env.shared_from_this();
				if (!env_sptr) {
					LOG_ERROR("配置(Config) - 轨迹优化器工厂 '{}': env.shared_from_this() 返回 nullptr。无法创建优化器。", id_str);
					return nullptr;
				}
				auto optimizer = std::make_shared<TrajectoryOptimizer>(
					ObjectID(id_str),                                 // 参数1: id
					"AlgorithmObject.TrajectoryOptimizer.BSpline",          // 参数2: type_tag (工厂键作为默认type_tag)
					params.value("name", id_str),                         // 参数3: name (从json或用id)
					params.value("version", "1.0.0.bspline.factory")    // 参数4: version (从json或默认)
				);

				if (!optimizer) {
					LOG_ERROR("配置(Config) - 轨迹优化器工厂 '{}': 创建优化器实例失败。", id_str);
					return nullptr;
				}
				// 注意：初始化将在 createObjectInstance 中统一处理，这里不需要重复调用 initialize
				LOG_INFO("配置(Config) - 轨迹优化器工厂: 成功创建并尝试初始化轨迹优化器实例 '{}'。", id_str);
				return optimizer;
			}
			catch (const std::bad_weak_ptr& e) {
				LOG_ERROR("配置(Config) - 轨迹优化器工厂 '{}': env.shared_from_this() 失败 (bad_weak_ptr): {}。Environment 可能未正确设置为 enable_shared_from_this。", id_str, e.what());
				return nullptr;
			}
			catch (const std::exception& e) {
				LOG_ERROR("配置(Config) - 轨迹优化器工厂 '{}': 创建或初始化时发生异常: {}", id_str, e.what());
				return nullptr;
			}
		};
		LOG_DEBUG("已注册算法工厂: AlgorithmObject.TrajectoryOptimizer.BSpline -> TrajectoryOptimizer");

		// -- 轨迹评估器 (Trajectory Evaluator) 工厂 --
		trajectory_evaluator_factories_["AlgorithmObject.TrajectoryEvaluator.Energy"] =
			[this](Environment& env, const Config* cfg_ptr, const std::string& id_str, const json& params) -> std::shared_ptr<NSAlgorithm::ITrajectoryEvaluator> {
			LOG_TRACE("配置(Config) - 能量评估器工厂: 尝试创建实例 '{}'", id_str);
			if (!cfg_ptr) {
				LOG_ERROR("配置(Config) - 能量评估器工厂 '{}': 配置指针 (cfg_ptr) 为空。", id_str);
				return nullptr;
			}
			try {
				ObjectID object_id(id_str);
				std::string type_tag_for_params = "AlgorithmObject.TrajectoryEvaluator.Energy";
				if (params.contains(KEY_TYPE_TAG) && params[KEY_TYPE_TAG].is_string()) {
					type_tag_for_params = params[KEY_TYPE_TAG].get<std::string>();
				}
				std::string name = params.value("name", id_str);
				std::string version = params.value("version", "1.0.0.factory");

				// 创建一个虚拟的能量模型，因为EnergyEvaluator实际使用UAV自身的能量模型
				// 这个虚拟模型只是为了满足构造函数的要求
				std::shared_ptr<IEnergyModel> energy_model_ptr = nullptr;

				// 尝试从环境中获取第一个UAV来创建虚拟能量模型
				// 使用findObjectsByAttribute方法查找UAV对象（注意：属性名使用小写"type"）
				auto multirotor_uavs = env.findObjectsByAttribute<NSUav::Uav>("type", "EntityObject.Uav.Multirotor");
				auto fixedwing_uavs = env.findObjectsByAttribute<NSUav::Uav>("type", "EntityObject.Uav.FixedWing");
				auto vtol_uavs = env.findObjectsByAttribute<NSUav::Uav>("type", "EntityObject.Uav.VTOL");

				std::shared_ptr<const NSUav::Uav> reference_uav = nullptr;
				if (!multirotor_uavs.empty()) {
					reference_uav = multirotor_uavs[0];
				}
				else if (!fixedwing_uavs.empty()) {
					reference_uav = fixedwing_uavs[0];
				}
				else if (!vtol_uavs.empty()) {
					reference_uav = vtol_uavs[0];
				}

				if (reference_uav && reference_uav->getEnergyModel()) {
					// 使用const_pointer_cast将ConstIEnergyModelPtr转换为IEnergyModelPtr
					energy_model_ptr = std::const_pointer_cast<IEnergyModel>(reference_uav->getEnergyModel());
					LOG_INFO("配置(Config) - 能量评估器工厂 '{}': 使用UAV '{}' 的能量模型作为参考模型。", id_str, reference_uav->getId());
				}

				if (!energy_model_ptr) {
					LOG_WARN("配置(Config) - 能量评估器工厂 '{}': 无法获取参考能量模型，EnergyEvaluator将仅使用UAV自身的能量模型。", id_str);
					// 创建一个最小的虚拟能量模型来满足构造函数要求
					// 注意：这需要一个虚拟UAV对象，这里我们暂时返回nullptr让程序继续
					LOG_ERROR("配置(Config) - 能量评估器工厂 '{}': 无法创建能量评估器，因为需要至少一个UAV实例作为参考。", id_str);
					return nullptr;
				}
				auto evaluator = std::make_shared<EnergyEvaluator>(object_id, type_tag_for_params, energy_model_ptr, name, version);
				// 注意：初始化将在 createObjectInstance 中统一处理，这里不需要重复调用 initialize
				LOG_INFO("配置(Config) - 能量评估器工厂: 成功创建并尝试初始化能量评估器实例 '{}'。", id_str);
				return evaluator;
			}
			catch (const std::exception& e) {
				LOG_ERROR("配置(Config) - 能量评估器工厂 '{}': 创建或初始化时发生异常: {}", id_str, e.what());
				return nullptr;
			}
		};
		LOG_DEBUG("已注册算法工厂: AlgorithmObject.TrajectoryEvaluator.Energy -> EnergyEvaluator");

		// -- 任务分配器 (Task Allocator) 工厂 --
		task_allocator_factories_["AlgorithmObject.TaskAllocator.Simple"] =
			[this](Environment& env, const Config* cfg_ptr, const std::string& id_str, const json& params) -> std::shared_ptr<NSAlgorithm::ITaskAllocator> {
			LOG_TRACE("配置(Config) - TaskAllocator 工厂: 尝试创建实例 '{}'", id_str);
			if (!cfg_ptr) {
				LOG_ERROR("配置(Config) - TaskAllocator 工厂 '{}': 配置指针 (cfg_ptr) 为空。", id_str);
				return nullptr;
			}
			try {
				ObjectID object_id(id_str);
				std::string type_tag_for_params = "AlgorithmObject.TaskAllocator.Simple";
				if (params.contains(KEY_TYPE_TAG) && params[KEY_TYPE_TAG].is_string()) {
					type_tag_for_params = params[KEY_TYPE_TAG].get<std::string>();
				}
				std::string name = params.value("name", id_str);
				std::string version = params.value("version", "1.0.0.factory");
				auto allocator = std::make_shared<TaskAllocator>(object_id, type_tag_for_params, name, version);
				// 注意：初始化将在 createObjectInstance 中统一处理，这里不需要重复调用 initialize
				LOG_INFO("配置(Config) - TaskAllocator 工厂: 成功创建并尝试初始化 TaskAllocator 实例 '{}'。", id_str);
				return allocator;
			}
			catch (const std::exception& e) {
				LOG_ERROR("配置(Config) - TaskAllocator 工厂 '{}': 创建或初始化时发生异常: {}", id_str, e.what());
				return nullptr;
			}
		};
		LOG_DEBUG("已注册算法工厂: AlgorithmObject.TaskAllocator.Simple -> TaskAllocator");

		LOG_INFO("注册阶段 2/3 - 核心规划算法组件工厂注册完毕。");
		return true;
	}

	/**
	 * @brief (私有) 注册具体的任务规划器 (FollowPath, LoiterPoint, ScanArea 等) 的工厂。
	 *        使用宏来简化注册过程。
	 * @return 总是返回 true (当前实现没有失败条件，但保留 bool 返回值以便未来扩展)。
	 */
	bool Config::registerTaskPlannerFactories() {
		LOG_INFO("注册阶段 3/3 - 注册具体任务规划器工厂...");
		using namespace NSPlanning;
		using namespace NSAlgorithm;

		// 定义一个内部宏来简化任务规划器的注册
		// 这个宏会创建一个 lambda 工厂，该工厂直接从 Environment 获取算法对象依赖，然后创建具体的任务规划器。
#define REGISTER_TASK_PLANNER(Interface, Concrete, Tag) \
			registerPlannerComponentFactory<Interface, Concrete>(Tag, \
				[this](Environment& env, const Config* cfg_ptr, const std::string& id, const json& params) -> std::shared_ptr<void> { \
					LOG_TRACE("配置(Config) - " #Concrete "工厂: 尝试创建实例 '{}'", id); \
					\
					/* 直接从 Environment 获取算法对象依赖 */ \
					std::shared_ptr<IPathPlanner> path_planner_ptr = env.getPathPlanner(); \
					std::shared_ptr<ITrajectoryOptimizer> traj_optimizer_ptr = env.getTrajectoryOptimizer(); \
					\
					LOG_DEBUG("配置(Config) - " #Concrete "工厂 '{}': 从Environment获取依赖组件 - PathPlanner: {}, TrajectoryOptimizer: {}", \
							  id, (path_planner_ptr ? "已获取" : "失败/空"), \
							  (traj_optimizer_ptr ? "已获取" : "失败/空")); \
					\
					if (!path_planner_ptr) { \
						LOG_ERROR("配置(Config) - " #Concrete "工厂 '{}': 必需的依赖项未能从Environment获取: " \
								  "路径规划器: {}. " #Concrete "无法创建。", \
								  id, (path_planner_ptr ? "已获取" : "失败/空")); \
						return nullptr; \
					} \
					\
					if (!traj_optimizer_ptr) { \
						LOG_WARN("配置(Config) - " #Concrete "工厂 '{}': 轨迹优化器未从Environment获取，将使用nullptr继续创建。", id); \
					} \
					\
					LOG_DEBUG("配置(Config) - " #Concrete "工厂 '{}': 所有必需依赖组件均已成功从Environment获取。", id); \
					auto concrete_task_planner = std::make_shared<Concrete>(); \
					LOG_INFO("配置(Config) - " #Concrete "工厂: 成功创建任务规划器实例 '{}'。", id); \
					return concrete_task_planner; \
				} \
			)

		// 使用宏注册各种任务规划器
		REGISTER_TASK_PLANNER(ITaskPlanner, FollowPathTaskPlanner, "TaskPlanner.FollowPath");
		REGISTER_TASK_PLANNER(ITaskPlanner, LoiterPointTaskPlanner, "TaskPlanner.LoiterPoint");
		REGISTER_TASK_PLANNER(ITaskPlanner, ScanAreaTaskPlanner, "TaskPlanner.ScanArea");
		REGISTER_TASK_PLANNER(ITaskPlanner, SurveyCylinderTaskPlanner, "TaskPlanner.SurveyCylinder");
		REGISTER_TASK_PLANNER(ITaskPlanner, SurveyMultiPointsTaskPlanner, "TaskPlanner.SurveyMultiPoints");
		REGISTER_TASK_PLANNER(ITaskPlanner, SurveySphereTaskPlanner, "TaskPlanner.SurveySphere");
		LOG_DEBUG("已使用宏注册所有具体任务规划器工厂。");

#undef REGISTER_TASK_PLANNER // 清理宏定义

		LOG_INFO("注册阶段 3/3 - 具体任务规划器工厂注册完毕。");
		return true; // 假设总是成功
	}

	/**
	 * @brief 加载并注册 data/defines/ 目录下所有 .json 文件中定义的参数集。
	 *        每个 .json 文件的文件名 (不含扩展名) 将被用作该参数集定义的 type_tag。
	 * @return 如果 defines_path_ 存在且是目录，并且所有找到的 .json 定义文件都成功注册到 ParamRegistry，则返回 true。
	 *         如果目录不存在，或任何一个文件注册失败，则返回 false。
	 */
	bool Config::loadDirectoryParamDefines() {
		LOG_INFO("开始加载 '{}' 目录下的所有参数定义文件...", defines_path_.generic_string());

		// 检查 defines_path_ 是否存在且是一个目录
		if (!fs::exists(defines_path_)) {
			LOG_ERROR("参数定义目录 '{}' 不存在。无法加载参数定义。", defines_path_.generic_string());
			return false;
		}
		if (!fs::is_directory(defines_path_)) {
			LOG_ERROR("指定的参数定义路径 '{}' 不是一个目录。无法加载参数定义。", defines_path_.generic_string());
			return false;
		}
		LOG_DEBUG("参数定义目录 '{}' 有效，开始遍历JSON文件...", defines_path_.generic_string());

		int count_success = 0; // 成功加载的定义文件数量
		int count_fail = 0;    // 加载失败的定义文件数量
		int total_json_files = 0; // 发现的 .json 文件总数

		// 遍历目录中的所有条目
		try {
			for (const auto& entry : fs::directory_iterator(defines_path_)) {
				const auto& path = entry.path();
				// 只处理扩展名为 .json 的常规文件
				if (entry.is_regular_file() && path.extension() == ".json") {
					total_json_files++;
					// 使用文件名 (不含扩展名) 作为 type_tag
					std::string type_tag = path.stem().string();
					LOG_DEBUG("发现参数定义文件 '{}', 将使用类型标签 '{}' 进行注册。", path.generic_string(), type_tag);

					// 调用 ParamRegistry 注册此文件中的参数定义
					if (param_registry_->registerParamDefinesFromFile(path, type_tag)) {
						LOG_INFO("成功注册了类型标签 '{}' 的参数定义 (来自文件 '{}')。", type_tag, path.generic_string());
						count_success++;
					}
					else {
						// registerParamDefinesFromFile 内部应已记录具体错误
						LOG_ERROR("注册类型标签 '{}' 的参数定义失败 (文件: '{}')。详情请查看 ParamRegistry 日志。", type_tag, path.generic_string());
						count_fail++;
					}
				}
				else if (entry.is_regular_file()) {
					LOG_TRACE("跳过非JSON文件: '{}'", path.generic_string());
				}
			}
		}
		catch (const fs::filesystem_error& e) {
			LOG_ERROR("遍历参数定义目录 '{}' 时发生文件系统错误: {}", defines_path_.generic_string(), e.what());
			return false; // 目录遍历失败，关键错误
		}

		// 记录总结信息
		LOG_INFO("'{}' 目录参数定义文件加载完成。总共发现 {} 个JSON文件，成功注册 {} 个，失败 {} 个。",
			defines_path_.generic_string(), total_json_files, count_success, count_fail);

		// 如果有任何一个定义文件注册失败，则认为整个过程失败
		return count_fail == 0;
	}

	/**
	 * @brief 从主配置文件 (configfile.json) 中的 "global_parameter_definitions" 节点加载全局参数定义。
	 *        这些定义将被注册到 ParamRegistry。
	 * @return 如果加载和注册成功，或者未找到定义节点 (非错误)，则返回 true。
	 */
	bool Config::loadGlobalParamDefines() {
		LOG_DEBUG("Config::loadGlobalParamDefines - 开始加载嵌入的全局参数定义...");
		if (!param_registry_) {
			LOG_CRITICAL("Config::loadGlobalParamDefines - ParamRegistry 指针为空，无法加载定义。");
			return false;
		}

		if (main_json_config_.contains(KEY_GLOBAL_PARAMETER_DEFINITIONS_NODE)) {
			const auto& global_defs_node = main_json_config_[KEY_GLOBAL_PARAMETER_DEFINITIONS_NODE];
			if (!global_defs_node.is_object()) {
				LOG_ERROR("主配置文件中的 '{}' 节点不是一个有效的JSON对象。无法加载全局参数定义。JSON: {}",
					KEY_GLOBAL_PARAMETER_DEFINITIONS_NODE, global_defs_node.dump(2));
				return false; // 这是一个配置错误
			}

			LOG_INFO("在主配置文件中找到 '{}' 节点，尝试解析和注册全局参数定义...", KEY_GLOBAL_PARAMETER_DEFINITIONS_NODE);

			// 调用 ParamRegistry 的新公共方法来解析这个 JSON 节点
			// type_tag_override 为空，让它从 JSON 内部的 "type_tag" 字段读取
			LOG_DEBUG("Config::loadGlobalParamDefines - 调用 ParamRegistry::parseJsonToParamDefines...");
			std::optional<ParamDefines> parsed_defines_opt = param_registry_->parseJsonToParamDefines(global_defs_node, "");
			LOG_DEBUG("Config::loadGlobalParamDefines - ParamRegistry::parseJsonToParamDefines 返回，has_value(): {}", parsed_defines_opt.has_value());

			if (parsed_defines_opt.has_value()) {
				ParamDefines global_defines = std::move(parsed_defines_opt.value());
				std::string parsed_type_tag = global_defines.getTypeTag(); // 获取实际解析到的 type_tag

				LOG_INFO("成功从 '{}' 节点解析了类型标签为 '{}' 的参数定义 (共 {} 个定义)。正在注册...",
					KEY_GLOBAL_PARAMETER_DEFINITIONS_NODE, parsed_type_tag, global_defines.getDefinitions().size());

				if (!param_registry_->registerParamDefines(std::move(global_defines))) {
					LOG_ERROR("注册从 '{}' 解析的全局参数定义 (类型标签 '{}') 失败。",
						KEY_GLOBAL_PARAMETER_DEFINITIONS_NODE, parsed_type_tag);
					return false; // 注册失败是错误
				}
				else {
					LOG_INFO("成功注册了从 '{}' 解析的全局参数定义 (类型标签 '{}')。",
						KEY_GLOBAL_PARAMETER_DEFINITIONS_NODE, parsed_type_tag);
					return true;
				}
			}
			else {
				LOG_ERROR("从主配置文件中的 '{}' 节点解析全局参数定义失败。详细错误见 ParamRegistry 日志。", KEY_GLOBAL_PARAMETER_DEFINITIONS_NODE);
				return false; // 解析失败是错误
			}
		}
		else {
			LOG_INFO("主配置文件中未找到 '{}' 节点。将不加载嵌入的全局参数定义。这可能不是错误，如果全局定义是通过其他方式 (如 define_files) 提供的。",
				KEY_GLOBAL_PARAMETER_DEFINITIONS_NODE);
			return true; // 未找到节点不一定是错误，可能配置中就没有嵌入的全局定义
		}
	}

	/**
	 * @brief 批量预加载 data/objects/ 目录下所有 .json 文件中定义的对象实例的原始配置。
	 *        此函数仅读取 JSON 文件，并将实例 ID、类型标签和参数 JSON 存储在内存缓存 (raw_instance_params_cache_) 中，
	 *        并不会创建实际的对象实例。
	 *        它会遍历指定目录，对每个找到的 .json 文件调用 loadObjectInstancesFromFileInternal。
	 * @return 返回一个包含所有成功预加载/更新的唯一实例 ID 的 std::vector<std::string>。
	 *         如果对象目录不存在或不是目录，则返回空 vector 并记录警告。
	 */
	std::vector<std::string> Config::preloadAllObjectInstanceFiles() {
		LOG_INFO("开始批量预加载 '{}' 目录下的所有对象实例原始参数JSON...", objects_path_.generic_string());

		// 检查对象实例目录是否存在且是目录
		if (!fs::exists(objects_path_)) {
			LOG_WARN("对象实例目录 '{}' 不存在。无法预加载任何实例配置。", objects_path_.generic_string());
			return preloaded_instance_names_; // 返回空列表
		}
		if (!fs::is_directory(objects_path_)) {
			LOG_WARN("指定的对象实例路径 '{}' 不是一个目录。无法预加载任何实例配置。", objects_path_.generic_string());
			return preloaded_instance_names_; // 返回空列表
		}
		LOG_DEBUG("对象实例目录 '{}' 有效，开始遍历文件...", objects_path_.generic_string());

		// 遍历目录中的所有条目
		int processed_files = 0;
		try {
			for (const auto& entry : fs::directory_iterator(objects_path_)) {
				// 只处理常规文件
				if (entry.is_regular_file()) {
					const auto& path = entry.path();
					// 只处理 .json 文件
					if (path.extension() == ".json") {
						LOG_DEBUG("发现对象实例文件 '{}', 正在调用内部加载函数处理...", path.generic_string());
						// 调用内部函数加载此文件中的所有实例配置，并将加载的ID附加到列表中
						loadObjectInstancesFromFileInternal(path, &preloaded_instance_names_);
						processed_files++;
					}
					else {
						LOG_TRACE("跳过非 JSON 文件: '{}'", path.generic_string());
					}
				}
				else if (entry.is_directory()) {
					LOG_TRACE("跳过子目录: '{}'", entry.path().generic_string());
				} // 可以根据需要添加对其他类型文件系统条目的处理
			}
		}
		catch (const fs::filesystem_error& e) {
			LOG_ERROR("遍历对象实例目录 '{}' 时发生文件系统错误: {}", objects_path_.generic_string(), e.what());
			// 即使遍历出错，仍然返回已加载的部分结果
		}

		// 对收集到的所有实例ID进行去重和排序（可选，但有助于日志和调试）
		if (!preloaded_instance_names_.empty()) {
			LOG_DEBUG("预加载完成，对 {} 个（可能重复的）实例ID进行排序和去重...", preloaded_instance_names_.size());
			std::sort(preloaded_instance_names_.begin(), preloaded_instance_names_.end());
			auto last = std::unique(preloaded_instance_names_.begin(), preloaded_instance_names_.end());
			preloaded_instance_names_.erase(last, preloaded_instance_names_.end());
			LOG_DEBUG("去重后得到 {} 个唯一实例ID。", preloaded_instance_names_.size());
		}

		// 记录最终总结信息
		LOG_INFO("所有对象实例的原始参数JSON预加载完成。共处理了 {} 个JSON文件，最终预加载/更新了 {} 个唯一实例的配置。",
			processed_files, preloaded_instance_names_.size());
		return preloaded_instance_names_;
	}

	/**
	 * @brief 按名称预加载指定对象实例的原始配置。
	 *        首先检查缓存中是否已存在该实例的配置，如果存在则直接返回 true。
	 *        如果缓存中不存在，则遍历 data/objects/ 目录下的所有 .json 文件，
	 *        调用 loadObjectInstancesFromFileInternal 来加载每个文件，直到找到具有匹配 instance_name 的实例为止。
	 * @param instance_name 要预加载的对象实例的 ID。
	 * @return 如果实例配置已在缓存中，或者成功在文件中找到并加载到缓存中，则返回 true。
	 *         如果对象目录无效，或遍历完所有文件仍未找到指定实例，则返回 false。
	 */
	bool Config::preloadObjectInstanceByName(const std::string& instance_name) {
		LOG_INFO("请求按名称预加载实例 '{}' 的原始参数JSON...", instance_name);

		// 1. 检查缓存
		{
			// 锁定缓存互斥锁以进行线程安全的读取检查
			std::lock_guard<std::mutex> lock(raw_params_cache_mutex_);
			if (raw_instance_params_cache_.count(instance_name)) {
				LOG_INFO("实例 '{}' 的原始参数JSON已存在于缓存中。无需再次加载。", instance_name);
				return true; // 缓存命中，直接成功返回
			}
		} // 互斥锁在此处自动释放
		LOG_DEBUG("实例 '{}' 不在缓存中，将扫描对象文件目录...", instance_name);

		// 2. 检查对象目录是否有效
		if (!fs::exists(objects_path_)) {
			LOG_WARN("对象实例目录 '{}' 不存在。无法查找实例 '{}'。", objects_path_.generic_string(), instance_name);
			return false;
		}
		if (!fs::is_directory(objects_path_)) {
			LOG_WARN("对象实例路径 '{}' 不是一个目录。无法查找实例 '{}'。", objects_path_.generic_string(), instance_name);
			return false;
		}

		// 3. 遍历对象目录下的 .json 文件，加载内容并检查是否包含所需实例
		LOG_DEBUG("开始扫描 '{}' 目录下的 JSON 文件以查找实例 '{}'...", objects_path_.generic_string(), instance_name);
		try {
			for (const auto& entry : fs::directory_iterator(objects_path_)) {
				if (entry.is_regular_file()) {
					const auto& path = entry.path();
					if (path.extension() == ".json") {
						LOG_DEBUG("正在扫描文件 '{}' 以查找实例 '{}'...", path.generic_string(), instance_name);
						// 调用内部加载函数（它会填充缓存）
						// 注意：即使此文件加载失败，我们仍继续尝试其他文件
						loadObjectInstancesFromFileInternal(path, nullptr);

						// 再次检查缓存，看是否已加载目标实例
						{
							std::lock_guard<std::mutex> lock(raw_params_cache_mutex_);
							if (raw_instance_params_cache_.count(instance_name)) {
								LOG_INFO("成功在文件 '{}' 中找到并预加载了实例 '{}' 的原始参数JSON。", path.generic_string(), instance_name);
								return true; // 找到目标，成功返回
							}
						} // 互斥锁释放
					} // end if .json
				} // end if regular file
			} // end for loop
		}
		catch (const fs::filesystem_error& e) {
			LOG_ERROR("按名称预加载实例 '{}' 时，遍历对象实例目录 '{}' 发生文件系统错误: {}", instance_name, objects_path_.generic_string(), e.what());
			// 即使遍历出错，也可能在错误发生前已加载，所以不直接返回 false
			// 最终还是要依赖下面的缓存检查
		}

		// 4. 如果遍历完所有文件仍未找到
		LOG_WARN("未能在 '{}' 目录下的任何对象文件中找到实例 '{}' 的定义。预加载失败。", objects_path_.generic_string(), instance_name);
		return false;
	}

	/**
	 * @brief (内部使用) 从指定的单个 .json 文件加载对象实例的原始配置到缓存。
	 *        期望文件内容是一个 JSON 数组，数组中的每个元素是一个代表对象实例的 JSON 对象。
	 *        每个实例对象应至少包含 "id" (或 "instance_id") 和 "type_tag" 字符串字段。
	 *        可选地包含一个 "parameters" 对象字段。
	 *        此函数负责解析 JSON，提取信息，并更新 raw_instance_params_cache_。
	 *        如果实例 ID 已存在于缓存中，旧条目将被覆盖并记录警告。
	 *
	 * @param file_path 要加载的 .json 文件的路径。
	 * @param loaded_instance_names (可选输出) 指向 std::vector<std::string> 的指针。
	 *                              如果非空，则此文件中成功加载/更新的每个实例的 ID 将被添加到此 vector 中。
	 * @return 如果文件成功打开、解析为 JSON 数组，并且至少处理了一个条目（即使某些条目无效），则返回 true。
	 *         如果文件无法打开、JSON 解析失败、或顶层不是数组，则返回 false。
	 *         注意：即使某些实例条目格式无效被跳过，只要文件本身有效且是数组，此函数也可能返回 true。
	 *               可以通过检查 loaded_instance_names 是否为空来判断是否成功加载了至少一个实例。
	 */
	bool Config::loadObjectInstancesFromFileInternal(const std::filesystem::path& file_path,
		std::vector<std::string>* loaded_instance_names) {
		LOG_INFO("开始从文件 '{}' 内部预加载对象实例原始参数JSON...", file_path.generic_string());

		// 1. 打开文件
		std::ifstream file_stream(file_path);
		if (!file_stream.is_open()) {
			LOG_ERROR("内部加载失败 - 无法打开文件 '{}'。", file_path.generic_string());
			return false;
		}
		LOG_DEBUG("文件 '{}' 打开成功。", file_path.generic_string());

		// 2. 解析 JSON
		json instances_json_array;
		try {
			// 从文件流直接解析 JSON
			file_stream >> instances_json_array;
			LOG_DEBUG("文件 '{}' JSON 解析成功。", file_path.generic_string());
		}
		catch (const json::parse_error& e) {
			LOG_ERROR("内部加载失败 - 解析文件 '{}' 的 JSON 内容失败: {}. 发生在字节 {} 附近。",
				file_path.generic_string(), e.what(), e.byte);
			file_stream.close(); // 确保关闭文件句柄
			return false;
		}
		catch (const std::exception& e) {
			LOG_ERROR("内部加载失败 - 解析文件 '{}' 时发生意外错误: {}", file_path.generic_string(), e.what());
			file_stream.close();
			return false;
		}
		file_stream.close(); // 解析完成或异常后关闭文件

		// 3. 检查顶层结构是否为数组
		if (!instances_json_array.is_array()) {
			LOG_ERROR("内部加载失败 - 文件 '{}' 的顶层 JSON 结构不是一个数组。期望包含一个对象实例的数组。", file_path.generic_string());
			return false;
		}
		LOG_DEBUG("文件 '{}' 顶层结构是数组，包含 {} 个条目。开始处理每个条目...", file_path.generic_string(), instances_json_array.size());

		// 标记此文件处理过程中是否遇到任何无效条目
		bool encountered_invalid_entry = false;
		int loaded_count_from_this_file = 0; // 此文件成功加载/更新的实例计数

		// 4. 遍历数组中的每个实例条目
		{
			// 在循环外加锁，减少锁的获取和释放次数
			std::lock_guard<std::mutex> lock(raw_params_cache_mutex_);

			for (const auto& instance_entry_json : instances_json_array) {
				// 确保当前条目是 JSON 对象
				if (!instance_entry_json.is_object()) {
					LOG_WARN("在文件 '{}' 中发现一个非 JSON 对象的数组条目，已跳过。内容: {}", file_path.generic_string(), instance_entry_json.dump(2));
					encountered_invalid_entry = true;
					continue; // 处理下一个条目
				}

				// 提取实例 ID (尝试 "instance_id" 或 "id")
				std::string instance_id;
				if (instance_entry_json.contains(KEY_INSTANCE_ID) && instance_entry_json.at(KEY_INSTANCE_ID).is_string()) {
					instance_id = instance_entry_json.at(KEY_INSTANCE_ID).get<std::string>();
				}
				else if (instance_entry_json.contains(KEY_ID) && instance_entry_json.at(KEY_ID).is_string()) {
					instance_id = instance_entry_json.at(KEY_ID).get<std::string>();
					LOG_TRACE("在文件 '{}' 的实例中找到 'id' 作为实例标识符: '{}'", file_path.generic_string(), instance_id);
				}
				else {
					LOG_WARN("文件 '{}' 中的一个实例缺少必需的 'instance_id' 或 'id' 字符串字段，已跳过。JSON: {}", file_path.generic_string(), instance_entry_json.dump(2));
					encountered_invalid_entry = true;
					continue;
				}

				// 检查实例 ID 是否为空
				if (instance_id.empty()) {
					LOG_WARN("文件 '{}' 中的一个实例 'instance_id' 或 'id' 字段值为空字符串，已跳过。JSON: {}", file_path.generic_string(), instance_entry_json.dump(2));
					encountered_invalid_entry = true;
					continue;
				}

				// 检查缓存中是否已存在此 ID (在锁保护下)
				if (raw_instance_params_cache_.count(instance_id)) {
					LOG_WARN("实例ID '{}' (来自文件 '{}') 已存在于缓存中。之前的定义将被覆盖。请检查是否存在重复定义。", instance_id, file_path.generic_string());
				}

				// 提取类型标签 ("type_tag")
				std::string type_tag;
				if (instance_entry_json.contains(KEY_TYPE_TAG) && instance_entry_json.at(KEY_TYPE_TAG).is_string()) {
					type_tag = instance_entry_json.at(KEY_TYPE_TAG).get<std::string>();
				}
				else {
					LOG_WARN("实例 '{}' (来自文件 '{}') 缺少必需的 'type_tag' 字符串字段，已跳过。JSON: {}", instance_id, file_path.generic_string(), instance_entry_json.dump(2));
					encountered_invalid_entry = true;
					continue;
				}

				// 检查类型标签是否为空
				if (type_tag.empty()) {
					LOG_WARN("实例 '{}' (来自文件 '{}') 的 'type_tag' 字段值为空字符串，已跳过。JSON: {}", instance_id, file_path.generic_string(), instance_entry_json.dump(2));
					encountered_invalid_entry = true;
					continue;
				}

				// 提取参数对象 ("parameters")，如果不存在或不是对象，则使用空对象
				json params_json = json::object(); // 默认空对象
				if (instance_entry_json.contains(KEY_PARAMETERS)) {
					if (instance_entry_json.at(KEY_PARAMETERS).is_object()) {
						params_json = instance_entry_json.at(KEY_PARAMETERS);
						LOG_TRACE("实例 '{}' (类型 '{}', 文件 '{}') 包含 'parameters' 对象。", instance_id, type_tag, file_path.generic_string());
					}
					else {
						LOG_WARN("实例 '{}' (类型 '{}', 文件 '{}') 包含 'parameters' 字段，但它不是一个 JSON 对象。将使用空参数。内容: {}",
							instance_id, type_tag, file_path.generic_string(), instance_entry_json.at(KEY_PARAMETERS).dump(2));
						// encountered_invalid_entry = true; // 可选：是否将此视为无效条目
					}
				}
				else {
					LOG_TRACE("实例 '{}' (类型 '{}', 文件 '{}') 未提供 'parameters' 对象。将使用空参数对象。", instance_id, type_tag, file_path.generic_string());
				}

				// 将提取的信息存储到缓存中 (在锁保护下)
				raw_instance_params_cache_[instance_id] = std::make_pair(type_tag, params_json);
				LOG_DEBUG("已成功缓存实例 '{}' (类型 '{}') 的原始参数JSON (来自文件 '{}')。", instance_id, type_tag, file_path.generic_string());

				// 如果提供了输出 vector，则添加实例 ID
				if (loaded_instance_names) {
					loaded_instance_names->push_back(instance_id);
				}
				loaded_count_from_this_file++; // 增加成功计数
			} // 结束对数组中单个实例的处理
		} // 互斥锁在此处自动释放

		// 5. 记录总结信息并返回
		if (loaded_count_from_this_file > 0) {
			LOG_INFO("成功从文件 '{}' 预加载/更新了 {} 个对象实例的原始参数JSON。{}",
				file_path.generic_string(), loaded_count_from_this_file,
				(encountered_invalid_entry ? "(处理期间跳过了一些无效条目)" : ""));
		}
		else if (!encountered_invalid_entry) { // 文件有效，但没有实例或所有实例都无效
			LOG_INFO("文件 '{}' 为空或不包含任何有效的对象实例条目。", file_path.generic_string());
		}
		else { // 文件有效，但所有条目都无效被跳过
			LOG_WARN("从文件 '{}' 加载时，所有条目均无效或被跳过，未成功加载任何实例。", file_path.generic_string());
		}
		// 只要文件能成功解析为数组就返回 true，表示文件本身没问题
		// 调用者可以通过检查 loaded_count_from_this_file 或 loaded_instance_names->empty() 来判断是否有实例被成功加载
		return true;
	}

	/**
	 * @brief 获取指定类型标签 (type_tag) 的默认 ParamValues 对象的克隆副本。
	 *        此方法首先检查内部缓存 (recent_default_pv_template_) 是否命中了最近一次请求的同类型模板。
	 *        如果命中，则直接克隆缓存的模板并返回。
	 *        如果未命中或类型不同，则调用 ParamRegistry::createDefaultParamValues 来创建新的默认值对象，
	 *        然后将新创建的对象（的共享指针）存入缓存作为模板，并返回其克隆副本。
	 *        此方法是线程安全的，使用互斥锁保护缓存的读写。
	 *
	 * @param type_tag 要获取其默认参数值的对象类型标签。
	 * @return 指向新创建的 ParamValues 克隆副本的共享指针。如果 ParamRegistry 未初始化或无法为指定 type_tag 创建默认值，则返回 nullptr。
	 */
	std::shared_ptr<NSParams::ParamValues> Config::getClonedDefaultParamValuesForType(const std::string& type_tag) const {
		LOG_TRACE("请求类型标签 '{}' 的默认 ParamValues 克隆副本...", type_tag);

		// 检查 ParamRegistry 是否有效
		if (!param_registry_) {
			LOG_ERROR("无法获取默认 ParamValues - ParamRegistry 未初始化!");
			return nullptr;
		}

		// 检查 type_tag 是否为空
		if (type_tag.empty()) {
			LOG_ERROR("无法获取默认 ParamValues - 请求的 type_tag 为空字符串。");
			return nullptr;
		}

		// 锁定缓存互斥锁
		std::lock_guard<std::mutex> lock(recent_default_pv_mutex_);

		// 检查缓存是否命中 (类型匹配且缓存指针有效)
		if (type_tag == recent_type_tag_for_default_pv_template_ && recent_default_pv_template_) {
			LOG_TRACE("缓存命中！为类型标签 '{}' 克隆缓存的默认 ParamValues 模板。", type_tag);
			// 克隆缓存中的模板并返回
			try {
				return recent_default_pv_template_->clone();
			}
			catch (const std::exception& e) {
				LOG_ERROR("克隆缓存的类型标签 '{}' 的默认 ParamValues 时发生异常: {}", type_tag, e.what());
				return nullptr;
			}
		}

		// 缓存未命中或类型不匹配，需要从 ParamRegistry 创建
		LOG_TRACE("缓存未命中或类型不同 ('{}' vs '{}')。将从 ParamRegistry 为类型标签 '{}' 创建新的默认 ParamValues。",
			recent_type_tag_for_default_pv_template_, type_tag, type_tag);
		std::shared_ptr<NSParams::ParamValues> new_default_pv_template = nullptr;
		try {
			new_default_pv_template = param_registry_->createDefaultParamValues(type_tag);
		}
		catch (const std::exception& e) {
			LOG_ERROR("从 ParamRegistry 为类型标签 '{}' 创建默认 ParamValues 时发生异常: {}", type_tag, e.what());
			// 即使创建失败，也要尝试清除可能存在的旧缓存条目（如果类型匹配）
			if (type_tag == recent_type_tag_for_default_pv_template_) {
				LOG_DEBUG("清除类型标签 '{}' 的无效缓存模板。", type_tag);
				recent_type_tag_for_default_pv_template_.clear();
				recent_default_pv_template_.reset();
			}
			return nullptr;
		}

		// 检查 ParamRegistry 是否成功创建了默认值对象
		if (new_default_pv_template) {
			// 创建成功，更新缓存
			recent_type_tag_for_default_pv_template_ = type_tag;
			recent_default_pv_template_ = new_default_pv_template; // 存储新模板的共享指针
			LOG_DEBUG("已成功创建并缓存了类型标签 '{}' 的新默认 ParamValues 模板。", type_tag);
			// 克隆新模板并返回克隆体
			try {
				return new_default_pv_template->clone();
			}
			catch (const std::exception& e) {
				LOG_ERROR("克隆新创建的类型标签 '{}' 的默认 ParamValues 时发生异常: {}", type_tag, e.what());
				// 清除刚刚设置的缓存，因为它可能不可靠
				recent_type_tag_for_default_pv_template_.clear();
				recent_default_pv_template_.reset();
				return nullptr;
			}
		}
		else {
			// ParamRegistry 未能创建默认值（可能因为类型标签无效或定义不完整）
			LOG_ERROR("ParamRegistry 未能为类型标签 '{}' 创建默认 ParamValues (可能 type_tag 未注册或定义无效)。", type_tag);
			// 清除可能存在的旧缓存条目（如果类型匹配）
			if (type_tag == recent_type_tag_for_default_pv_template_) {
				LOG_DEBUG("清除类型标签 '{}' 的无效缓存模板。", type_tag);
				recent_type_tag_for_default_pv_template_.clear();
				recent_default_pv_template_.reset();
			}
			return nullptr; // 创建失败，返回空指针
		}
		// lock_guard 在作用域结束时自动解锁
	}

	// 新增: getEnvironment() 方法的实现
	std::shared_ptr<Environment> Config::getEnvironment() const {
		// 直接返回单例实例
		return Environment::getInstance();
	}

	// 新增: getInstanceTypeTag() 方法的实现
	std::string Config::getInstanceTypeTag(const std::string& instance_id) const {
		std::lock_guard<std::mutex> lock(raw_params_cache_mutex_);
		auto it = raw_instance_params_cache_.find(instance_id);
		if (it != raw_instance_params_cache_.end()) {
			return it->second.first; // 返回type_tag
		}
		return ""; // 实例不存在，返回空字符串
	}

	// --- 模板方法 createAlgorithmInstanceFromPlanningSetup 的实现 ---
	/**
	 * @brief 从主配置文件的 'planning_setup' 部分创建并初始化一个核心算法组件实例。
	 *
	 * @tparam InterfaceType 期望的算法接口类型 (例如 ITaskAllocator, IPathPlanner)。
	 * @param algorithm_key 'planning_setup' 中的键名 (例如 "task_allocator", "path_planner")。
	 * @return std::shared_ptr<InterfaceType> 如果成功则返回创建的算法实例，否则返回 nullptr。
	 */
	template<typename InterfaceType>
	std::shared_ptr<InterfaceType> Config::createAlgorithmInstanceFromPlanningSetup(const std::string& algorithm_key) {
		LOG_INFO("尝试从 'planning_setup' 创建算法实例，键: '{}', 期望接口: '{}'", algorithm_key, typeid(InterfaceType).name());

		if (!main_json_config_.contains("planning_setup")) {
			LOG_ERROR("'planning_setup' 节点在主配置中未找到。无法创建算法实例 '{}'。", algorithm_key);
			return nullptr;
		}
		const auto& planning_setup_node = main_json_config_["planning_setup"];
		if (!planning_setup_node.is_object()) {
			LOG_ERROR("'planning_setup' 节点不是一个JSON对象。无法创建算法实例 '{}'。", algorithm_key);
			return nullptr;
		}

		if (!planning_setup_node.contains(algorithm_key)) {
			LOG_ERROR("在 'planning_setup' 中未找到键为 '{}' 的算法配置。", algorithm_key);
			return nullptr;
		}
		const auto& algorithm_config_node = planning_setup_node[algorithm_key];
		if (!algorithm_config_node.is_object()) {
			LOG_ERROR("算法键 '{}' 在 'planning_setup' 中的配置不是一个JSON对象。", algorithm_key);
			return nullptr;
		}

		if (!algorithm_config_node.contains(KEY_INSTANCE_ID)) {
			LOG_ERROR("算法配置 '{}' (在 'planning_setup' 中) 缺少必需的 '{}' 字段。", algorithm_key, KEY_INSTANCE_ID);
			return nullptr;
		}
		const auto& instance_id_node = algorithm_config_node[KEY_INSTANCE_ID];
		if (!instance_id_node.is_string()) {
			LOG_ERROR("算法配置 '{}' (在 'planning_setup' 中) 的 '{}' 字段不是字符串。", algorithm_key, KEY_INSTANCE_ID);
			return nullptr;
		}
		std::string instance_id = instance_id_node.get<std::string>();
		if (instance_id.empty()) {
			LOG_ERROR("算法配置 '{}' (在 'planning_setup' 中) 的 '{}' 字段为空。", algorithm_key, KEY_INSTANCE_ID);
			return nullptr;
		}

		// 提取 type_tag 和 parameters
		if (!algorithm_config_node.contains("type_tag")) {
			LOG_ERROR("算法配置 '{}' (在 'planning_setup' 中) 缺少必需的 'type_tag' 字段。", algorithm_key);
			return nullptr;
		}
		std::string type_tag = algorithm_config_node["type_tag"].get<std::string>();

		// 提取 parameters（如果存在）
		nlohmann::json parameters_json;
		if (algorithm_config_node.contains("parameters") && algorithm_config_node["parameters"].is_object()) {
			parameters_json = algorithm_config_node["parameters"];
		}

		LOG_DEBUG("从 'planning_setup' 中的键 '{}' 解析到: 实例ID='{}', 类型标签='{}', 参数数量={}",
			algorithm_key, instance_id, type_tag, parameters_json.size());

		// 使用 ParamRegistry 的参数继承机制创建算法实例
		try {
			// 1. 获取对应类型标签的默认 ParamValues (克隆副本)
			std::shared_ptr<NSParams::ParamValues> final_param_values = getClonedDefaultParamValuesForType(type_tag);
			if (!final_param_values) {
				LOG_ERROR("无法为算法实例 '{}' (类型 '{}') 获取或克隆默认 ParamValues。", instance_id, type_tag);
				return nullptr;
			}
			LOG_DEBUG("成功克隆算法实例 '{}' (类型标签 '{}') 的默认 ParamValues。", instance_id, type_tag);

			// 2. 将实例特定的 JSON 参数覆盖到克隆的默认值上
			if (!parameters_json.empty() && parameters_json.is_object()) {
				LOG_DEBUG("正在为算法实例 '{}' 应用 planning_setup 中定义的 {} 个特定参数...", instance_id, parameters_json.size());
				if (!final_param_values->valuesFromJson(parameters_json, *param_registry_, nullptr, true)) {
					LOG_ERROR("为算法实例 '{}' (类型 '{}') 应用实例特定参数失败。无法创建对象。", instance_id, type_tag);
					return nullptr;
				}
				else {
					LOG_DEBUG("成功为算法实例 '{}' 应用实例特定参数。", instance_id);
				}
			}
			else {
				LOG_DEBUG("算法实例 '{}' 的 planning_setup 配置中未提供 'parameters' 对象。将使用类型 '{}' 的默认参数。", instance_id, type_tag);
			}

			// 3. 使用正确的工厂创建算法实例
			LOG_DEBUG("正在查找算法实例 '{}' (类型标签 '{}') 的工厂...", instance_id, type_tag);
			std::shared_ptr<void> generic_object_ptr = nullptr;

			auto it_component_factory = planner_component_factories_.find(type_tag);
			if (it_component_factory != planner_component_factories_.end()) {
				LOG_DEBUG("在 planner_component_factories_ 中找到类型标签 '{}'。正在调用组件工厂创建算法实例 '{}'...", type_tag, instance_id);
				try {
					// 组件工厂需要 Environment&, Config*, instance_id, parameters_json
					auto environment = Environment::getInstance();
					if (!environment) {
						LOG_CRITICAL("组件工厂调用失败 - Environment 实例不存在");
						return nullptr;
					}
					generic_object_ptr = it_component_factory->second(*environment, this, instance_id, parameters_json);
				}
				catch (const std::exception& e) {
					LOG_CRITICAL("组件工厂在创建算法实例 '{}' (类型 '{}') 时抛出异常: {}", instance_id, type_tag, e.what());
					return nullptr;
				}
				catch (...) {
					LOG_CRITICAL("组件工厂在创建算法实例 '{}' (类型 '{}') 时抛出未知异常。", instance_id, type_tag);
					return nullptr;
				}
			}
			else {
				LOG_ERROR("未找到类型标签 '{}' 对应的工厂 (既不在 object_factories_ 也不在 planner_component_factories_ 中)。无法创建算法实例 '{}'。", type_tag, instance_id);
				return nullptr;
			}

			// 4. 转换为期望的类型
			if (!generic_object_ptr) {
				LOG_ERROR("工厂返回了空指针，无法创建算法实例 '{}' (类型 '{}')。", instance_id, type_tag);
				return nullptr;
			}

			std::shared_ptr<InterfaceType> typed_object_ptr = std::static_pointer_cast<InterfaceType>(generic_object_ptr);
			if (!typed_object_ptr) {
				LOG_ERROR("无法将工厂返回的对象转换为期望类型 '{}' (算法实例 '{}', 类型标签 '{}')。", typeid(InterfaceType).name(), instance_id, type_tag);
				return nullptr;
			}
			LOG_DEBUG("成功将算法实例 '{}' 转换为期望类型 '{}'。", instance_id, typeid(InterfaceType).name());

			// 5. 如果对象有 initialize 方法，调用它进行最终初始化
			if constexpr (std::is_same_v<InterfaceType, EntityObject> ||
				std::is_base_of_v<EntityObject, InterfaceType>) {
				// 对于 EntityObject 及其派生类，调用 initialize(ParamValues, json)
				if (auto entity_obj = std::dynamic_pointer_cast<EntityObject>(typed_object_ptr)) {
					LOG_DEBUG("正在调用算法实例 '{}' 的 initialize(ParamValues, json) 方法...", instance_id);
					if (!entity_obj->initialize(final_param_values, parameters_json)) {
						LOG_ERROR("算法实例 '{}' (类型 '{}') 的 initialize 方法返回 false。初始化失败。", instance_id, type_tag);
						return nullptr;
					}
					LOG_DEBUG("算法实例 '{}' 的 initialize 方法调用成功。", instance_id);
				}
			}
			else {
				// 对于 AlgorithmObject 及其派生类，也调用 initialize(ParamValues, json)
				if (auto algorithm_obj = std::dynamic_pointer_cast<NSAlgorithm::AlgorithmObject>(typed_object_ptr)) {
					LOG_DEBUG("正在调用算法实例 '{}' 的 initialize(ParamValues, json) 方法...", instance_id);
					if (!algorithm_obj->initialize(final_param_values, parameters_json)) {
						LOG_ERROR("算法实例 '{}' (类型 '{}') 的 initialize 方法返回 false。初始化失败。", instance_id, type_tag);
						return nullptr;
					}
					LOG_DEBUG("算法实例 '{}' 的 initialize 方法调用成功。", instance_id);
				}
			}

			LOG_INFO("成功从 'planning_setup' 创建并初始化算法实例 '{}' (类型 '{}', 接口 '{}')。", instance_id, type_tag, typeid(InterfaceType).name());
			return typed_object_ptr;

		}
		catch (const std::exception& e) {
			LOG_CRITICAL("从 'planning_setup' 创建算法实例 '{}' (类型 '{}') 时发生异常: {}", instance_id, type_tag, e.what());
			return nullptr;
		}
		catch (...) {
			LOG_CRITICAL("从 'planning_setup' 创建算法实例 '{}' (类型 '{}') 时发生未知异常。", instance_id, type_tag);
			return nullptr;
		}
	}

	// --- createAlgorithmInstanceFromPlanningSetup 的显式模板实例化 ---
	// 为需要从 planning_setup 创建的每个核心算法接口添加实例化
	// 这有助于确保链接器找到这些模板的实现。

	// 任务分配器接口
	template std::shared_ptr<ITaskAllocator>
		Config::createAlgorithmInstanceFromPlanningSetup<ITaskAllocator>(const std::string&);

	// 路径规划器接口
	template std::shared_ptr<IPathPlanner>
		Config::createAlgorithmInstanceFromPlanningSetup<IPathPlanner>(const std::string&);

	// 轨迹优化器接口
	template std::shared_ptr<ITrajectoryOptimizer>
		Config::createAlgorithmInstanceFromPlanningSetup<ITrajectoryOptimizer>(const std::string&);

	// 轨迹评估器接口 (例如 EnergyEvaluator)
	template std::shared_ptr<ITrajectoryEvaluator>
		Config::createAlgorithmInstanceFromPlanningSetup<ITrajectoryEvaluator>(const std::string&);

	// 如果有其他直接从 planning_setup 创建的顶层算法组件，也需要在这里实例化
	// 例如，如果有一个 IGlobalPlanner 接口

	std::shared_ptr<NSParams::ParamValues> Config::createAndConfigureParamValues(
		const std::string& type_tag,
		const nlohmann::json& instance_specific_params_json) const
	{
		LOG_TRACE("Config::createAndConfigureParamValues: 为类型标签 '{}' 创建参数值...", type_tag);

		// 1. 获取对应类型标签的默认 ParamValues (克隆副本)
		std::shared_ptr<NSParams::ParamValues> final_param_values = getClonedDefaultParamValuesForType(type_tag);
		if (!final_param_values) {
			LOG_ERROR("Config::createAndConfigureParamValues: 无法为类型标签 '{}' 获取或克隆默认 ParamValues。", type_tag);
			return nullptr;
		}
		LOG_TRACE("Config::createAndConfigureParamValues: 成功克隆类型标签 '{}' 的默认 ParamValues。", type_tag);

		// 2. 将实例特定的 JSON 参数覆盖到克隆的默认值上
		//    instance_specific_params_json 是指实例配置文件中 "parameters" 字段下的 JSON 对象，
		//    或者如果整个实例配置就是参数，则是那个对象。
		//    ParamValues::valuesFromJson 会处理 JSON 结构。
		if (!instance_specific_params_json.empty() && instance_specific_params_json.is_object()) {
			LOG_TRACE("Config::createAndConfigureParamValues: 为类型标签 '{}' 应用 JSON 中定义的特定参数...", type_tag);
			if (!param_registry_) {
				LOG_ERROR("Config::createAndConfigureParamValues: ParamRegistry 未初始化，无法应用实例特定参数。");
				return nullptr;
			}
			// 从 instance_specific_params_json 中提取 "parameters" 子对象（如果存在）
			// 否则假定 instance_specific_params_json 本身就是参数对象
			nlohmann::json params_to_apply;
			if (instance_specific_params_json.contains(KEY_PARAMETERS) && instance_specific_params_json[KEY_PARAMETERS].is_object()) {
				params_to_apply = instance_specific_params_json[KEY_PARAMETERS];
			}
			else {
				// 如果没有 "parameters" 键，但它本身是个对象，就用它
				// 这需要调用者确保传递的是正确的参数JSON结构
				params_to_apply = instance_specific_params_json;
			}

			if (!params_to_apply.empty() && params_to_apply.is_object()) {
				if (!final_param_values->valuesFromJson(params_to_apply, *param_registry_, nullptr, true)) {
					LOG_ERROR("Config::createAndConfigureParamValues: 为类型标签 '{}' 应用实例特定参数失败 (来自提取的参数部分)。", type_tag);
					return nullptr;
				}
				else {
					LOG_TRACE("Config::createAndConfigureParamValues: 成功为类型标签 '{}' 应用实例特定参数 (来自提取的参数部分)。", type_tag);
				}
			}
			else {
				LOG_TRACE("Config::createAndConfigureParamValues: 类型标签 '{}' 的实例 JSON 配置中未提供有效参数对象。将使用默认参数。", type_tag);
			}
		}
		else {
			LOG_TRACE("Config::createAndConfigureParamValues: 类型标签 '{}' 的实例 JSON 配置为空或非对象。将使用默认参数。", type_tag);
		}

		LOG_DEBUG("Config::createAndConfigureParamValues: 成功为类型标签 '{}' 创建并配置了 ParamValues。", type_tag);
		return final_param_values;
	}

} // namespace NSDrones