[{"instance_id": "default_allocator", "type_tag": "AlgorithmObject.TaskAllocator.Simple", "name": "默认任务分配器", "description": "简单的任务分配器实现，按顺序分配任务给可用的无人机。", "parameters": {"allocation_strategy": "simple", "max_allocation_time_ms": 5000, "enable_reallocation": false}}, {"instance_id": "default_rrtstar", "type_tag": "AlgorithmObject.PathPlanner.RRTStar", "name": "默认RRT*路径规划器", "description": "基于RRT*算法的路径规划器，用于生成无碰撞的飞行路径。", "parameters": {"max_iterations": 8000, "step_size_m": 4.0, "goal_bias": 0.1, "rewire_radius_factor": 1.5, "collision_check_resolution_m": 0.5, "smoothing_iterations": 10}}, {"instance_id": "bspline_optimizer_default_instance", "type_tag": "AlgorithmObject.TrajectoryOptimizer.BSpline", "name": "默认B样条轨迹优化器", "description": "基于B样条的轨迹优化器，用于平滑和优化飞行轨迹。", "parameters": {"order": 3, "output_points": 50, "max_iterations": 50, "learning_rate": 0.01, "tolerance": 1e-05, "collision_weight": 1000.0, "safe_distance": 1.0, "sample_points": 30, "cost_weights.energy": 0.8}}, {"instance_id": "default_evaluator", "type_tag": "AlgorithmObject.TrajectoryEvaluator.Energy", "name": "默认能量评估器", "description": "基于能量消耗的轨迹评估器，用于评估轨迹的能量效率。", "parameters": {"calculation_fidelity": "SIMPLE", "simple_model_hover_w": 150.0, "simple_model_forward_coeff": 0.5}}, {"instance_id": "followpath_planner", "type_tag": "TaskPlanner.FollowPath", "name": "路径跟随任务规划器", "description": "用于规划路径跟随任务的任务规划器。", "parameters": {}}, {"instance_id": "loiterpoint_planner", "type_tag": "TaskPlanner.LoiterPoint", "name": "悬停点任务规划器", "description": "用于规划悬停点任务的任务规划器。", "parameters": {}}, {"instance_id": "scanarea_planner", "type_tag": "TaskPlanner.ScanArea", "name": "区域扫描任务规划器", "description": "用于规划区域扫描任务的任务规划器。", "parameters": {}}, {"instance_id": "surveycylinder_planner", "type_tag": "TaskPlanner.SurveyCylinder", "name": "圆柱体勘测任务规划器", "description": "用于规划圆柱体勘测任务的任务规划器。", "parameters": {}}, {"instance_id": "surveymultipoints_planner", "type_tag": "TaskPlanner.SurveyMultiPoints", "name": "多点勘测任务规划器", "description": "用于规划多点勘测任务的任务规划器。", "parameters": {}}, {"instance_id": "surveysphere_planner", "type_tag": "TaskPlanner.SurveySphere", "name": "球体勘测任务规划器", "description": "用于规划球体勘测任务的任务规划器。", "parameters": {}}]