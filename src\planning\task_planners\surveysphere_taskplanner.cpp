// src/planning/task_planners/task_planner_surveysphere.cpp
#include "planning/task_planners/surveysphere_taskplanner.h"
#include "planning/planning_types.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/task_strategies.h"
#include "mission/control_point.h"
#include "uav/uav.h"
#include "environment/environment.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"
#include "utils/geometry_manager.h"
#include "core/types.h"
#include <vector>
#include <cmath>
#include <string>
#include <optional>
#include <utility>

namespace NSDrones {
	namespace NSPlanning {

		SurveySphereTaskPlanner::SurveySphereTaskPlanner()
			: ITaskPlanner() {}

		// 新增: 初始化方法实现 (目前为空)
		bool SurveySphereTaskPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) {
			LOG_DEBUG("[SurveySphereTaskPlanner] 开始初始化...");

			try {
				// === 加载球体勘察特定配置参数 ===
				if (params) {
					// 默认纬度分割数
					auto latitude_divisions_opt = params->getValue<int>("planning.sphere.latitude_divisions");
					if (latitude_divisions_opt.has_value()) {
						default_latitude_divisions_ = latitude_divisions_opt.value();
						LOG_DEBUG("[SurveySphereTaskPlanner] 设置默认纬度分割数: {}", default_latitude_divisions_);
					}

					// 默认经度分割数
					auto longitude_divisions_opt = params->getValue<int>("planning.sphere.longitude_divisions");
					if (longitude_divisions_opt.has_value()) {
						default_longitude_divisions_ = longitude_divisions_opt.value();
						LOG_DEBUG("[SurveySphereTaskPlanner] 设置默认经度分割数: {}", default_longitude_divisions_);
					}

					// 默认飞行速度
					auto flight_speed_opt = params->getValue<double>("planning.sphere.default_speed");
					if (flight_speed_opt.has_value()) {
						default_flight_speed_ = flight_speed_opt.value();
						LOG_DEBUG("[SurveySphereTaskPlanner] 设置默认飞行速度: {:.2f} m/s", default_flight_speed_);
					}

					// 最小半径限制
					auto min_radius_opt = params->getValue<double>("planning.sphere.min_radius");
					if (min_radius_opt.has_value()) {
						min_radius_ = min_radius_opt.value();
						LOG_DEBUG("[SurveySphereTaskPlanner] 设置最小半径限制: {:.2f} m", min_radius_);
					}

					// 是否启用优化
					auto enable_optimization_opt = params->getValue<bool>("planning.sphere.enable_optimization");
					if (enable_optimization_opt.has_value()) {
						enable_optimization_ = enable_optimization_opt.value();
						LOG_DEBUG("[SurveySphereTaskPlanner] 设置优化开关: {}", enable_optimization_ ? "启用" : "禁用");
					}
				}

				// === 处理原始JSON配置 ===
				if (!raw_config.empty() && raw_config.is_object()) {
					LOG_DEBUG("[SurveySphereTaskPlanner] 处理原始JSON配置...");

					if (raw_config.contains("sphere_specific")) {
						const auto& sphere_config = raw_config["sphere_specific"];

						if (sphere_config.contains("scan_pattern") && sphere_config["scan_pattern"].is_string()) {
							scan_pattern_ = sphere_config["scan_pattern"].get<std::string>();
							LOG_DEBUG("[SurveySphereTaskPlanner] 设置扫描模式: {}", scan_pattern_);
						}
					}
				}

				// === 验证配置参数 ===
				if (default_latitude_divisions_ < 4 || default_latitude_divisions_ > 180) {
					LOG_WARN("[SurveySphereTaskPlanner] 纬度分割数({})超出合理范围[4,180]，重置为18", default_latitude_divisions_);
					default_latitude_divisions_ = 18;
				}

				if (default_longitude_divisions_ < 8 || default_longitude_divisions_ > 360) {
					LOG_WARN("[SurveySphereTaskPlanner] 经度分割数({})超出合理范围[8,360]，重置为36", default_longitude_divisions_);
					default_longitude_divisions_ = 36;
				}

				if (default_flight_speed_ <= 0.0 || default_flight_speed_ > 50.0) {
					LOG_WARN("[SurveySphereTaskPlanner] 默认飞行速度({:.2f})超出合理范围(0,50]，重置为8.0", default_flight_speed_);
					default_flight_speed_ = 8.0;
				}

				if (min_radius_ <= 0.0) {
					LOG_WARN("[SurveySphereTaskPlanner] 最小半径({:.2f})无效，重置为1.0", min_radius_);
					min_radius_ = 1.0;
				}

				LOG_INFO("[SurveySphereTaskPlanner] 初始化完成 - 纬度分割:{}, 经度分割:{}, 默认速度:{:.1f}m/s, 最小半径:{:.1f}m, 优化:{}",
					default_latitude_divisions_, default_longitude_divisions_, default_flight_speed_, min_radius_,
					enable_optimization_ ? "启用" : "禁用");

				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("[SurveySphereTaskPlanner] 初始化异常: {}", e.what());
				return false;
			}
		}

		/**
		 * @brief 规划单机球面勘察任务（重构后的接口）
		 */
		SingleTaskPlanningResult SurveySphereTaskPlanner::planSingleTask(
			const SingleTaskPlanningRequest& request) {

			LOG_INFO("[SurveySphereTaskPlanner] 开始规划子任务 [{}]，UAV [{}]",
				request.assignment.sub_task_id, request.assignment.assigned_uav_id);

			SingleTaskPlanningResult result;
			result.success = false;
			result.sub_task_id = request.assignment.sub_task_id;
			result.uav_id = request.assignment.assigned_uav_id;

			try {
				// === 第1步：验证输入（使用公共方法） ===
				if (!validateBasicTaskRequest(request, result, NSCore::TaskType::SURVEY_SPHERE)) {
					return result;
				}

				// === 第2步：获取任务参数 ===
				auto params_ptr = request.original_task->getTaskParameters<NSMission::SurveySphereTaskParams>();
				if (!params_ptr) {
					result.message = "无法获取 SurveySphere 任务参数";
					LOG_ERROR("[SurveySphereTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第3步：获取起始状态 ===
				NSUav::UavState start_state;
				if (!getUavStartState(request.uav, start_state)) {
					result.message = "无法获取无人机起始状态";
					LOG_ERROR("[SurveySphereTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第4步：执行球面勘察规划 ===
				result = planSphereTrajectory(request, *params_ptr, start_state);

			} catch (const std::exception& e) {
				result.success = false;
				result.message = "规划异常: " + std::string(e.what());
				LOG_ERROR("[SurveySphereTaskPlanner] 子任务 [{}] 规划异常: {}", result.sub_task_id, e.what());
			}

			LOG_INFO("[SurveySphereTaskPlanner] 子任务 [{}] 规划完成，成功: {}",
				result.sub_task_id, result.success);
			return result;
		}

		/**
		 * @brief 规划球面勘察轨迹的核心实现
		 */
		SingleTaskPlanningResult SurveySphereTaskPlanner::planSphereTrajectory(
			const SingleTaskPlanningRequest& request,
			const NSMission::SurveySphereTaskParams& params,
			const NSUav::UavState& start_state) {

			SingleTaskPlanningResult result;
			result.success = false;
			result.sub_task_id = request.assignment.sub_task_id;
			result.uav_id = request.assignment.assigned_uav_id;

			try {
				LOG_DEBUG("[SurveySphereTaskPlanner] 开始规划球面勘察轨迹，半径: {:.1f}m，拍摄点数: {}",
					params.radius, params.photo_angles.size());

				// === 第1步：验证参数 ===
				if (params.radius <= NSCore::Constants::GEOMETRY_EPSILON) {
					result.message = "无效的球面勘察参数：半径(" + std::to_string(params.radius) + ")必须为正";
					LOG_ERROR("[SurveySphereTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				if (params.photo_angles.empty()) {
					result.message = "拍摄角度列表为空";
					LOG_ERROR("[SurveySphereTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				LOG_DEBUG("[SurveySphereTaskPlanner] 参数验证通过 - 球心: {}, 半径: {:.2f}m, 拍摄点数: {}",
					params.center_point.position.toString(), params.radius, params.photo_angles.size());

				// === 第2步：生成球面拍摄点路径 ===
				LOG_DEBUG("[SurveySphereTaskPlanner] 生成球面拍摄点路径");

				std::vector<NSCore::WGS84Point> geometric_path;
				geometric_path.reserve(params.photo_angles.size() + 1);

				// 添加起始点
				geometric_path.push_back(start_state.position);

				// 获取球心位置
				NSCore::WGS84Point survey_center = params.center_point.position;

				// 为每个拍摄角度计算拍摄点位置
				for (const auto& angle : params.photo_angles) {
					// 计算球面坐标转笛卡尔坐标，相对于球心
					double az_rad = angle.azimuth_deg * NSCore::Constants::DEG_TO_RAD;
					double el_rad = angle.elevation_deg * NSCore::Constants::DEG_TO_RAD;

					// 计算相对球心的偏移向量
					NSCore::Vector3D offset(
						params.radius * std::cos(el_rad) * std::cos(az_rad), // X = r * cos(el) * cos(az)
						params.radius * std::cos(el_rad) * std::sin(az_rad), // Y = r * cos(el) * sin(az)
						params.radius * std::sin(el_rad)                   // Z = r * sin(el)
					);

					// 计算拍摄点的绝对位置
					NSCore::WGS84Point photo_point;
					photo_point.latitude = survey_center.latitude;
					photo_point.longitude = survey_center.longitude;
					photo_point.altitude = survey_center.altitude + offset.z();

					// 简化处理：这里应该使用更精确的地理坐标转换
					// 当前仅处理高度偏移，实际应该考虑经纬度偏移
					geometric_path.push_back(photo_point);

					LOG_TRACE("[SurveySphereTaskPlanner] 拍摄点: 方位角={:.1f}°, 俯仰角={:.1f}°, 位置={}",
						angle.azimuth_deg, angle.elevation_deg, photo_point.toString());
				}

				LOG_DEBUG("[SurveySphereTaskPlanner] 构建几何路径完成，总点数: {}", geometric_path.size());

				// === 第3步：确定飞行速度（使用公共方法） ===
				double flight_speed = 8.0; // 默认球面勘察速度

				// 校验和调整飞行速度
				double adjusted_speed;
				if (!validateAndAdjustFlightSpeed(request.uav, start_state, flight_speed, adjusted_speed, result)) {
					return result;
				}
				flight_speed = adjusted_speed;

				LOG_DEBUG("[SurveySphereTaskPlanner] 使用飞行速度: {:.2f} m/s", flight_speed);

				// === 第4步：生成轨迹 ===
				Trajectory trajectory;
				if (!smoothAndTimeParameterize(geometric_path, request.uav, start_state,
					flight_speed, trajectory, &result)) {
					result.message = "轨迹生成失败";
					LOG_ERROR("[SurveySphereTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第5步：为拍摄点添加拍照动作 ===
				// 简化实现：为轨迹中对应拍摄点的航点添加拍照动作
				auto& trajectory_points = trajectory.getPoints();
				if (trajectory_points.size() >= params.photo_angles.size()) {
					for (size_t i = 0; i < params.photo_angles.size() && i + 1 < trajectory_points.size(); ++i) {
						NSMission::PayloadActionCommand photo_cmd;
						photo_cmd.command_name = "TakePhoto";
						// 可以添加参数，例如指向球心
						trajectory_points[i + 1].payload_actions.push_back(photo_cmd);
						LOG_TRACE("[SurveySphereTaskPlanner] 在拍摄点 #{} 添加拍照动作", i + 1);
					}
				}

				// === 第6步：应用避障约束（使用公共方法） ===
				if (!applyAvoidanceConstraints(trajectory, request.avoidance_constraints, result)) {
					result.message = "应用避障约束失败";
					LOG_ERROR("[SurveySphereTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第7步：验证轨迹（使用公共方法） ===
				if (!validateGeneratedTrajectory(trajectory, result)) {
					return result;
				}

				// === 第8步：设置结果 ===
				result.trajectory = std::move(trajectory);
				result.success = true;
				result.message = "球面勘察规划成功";

				LOG_INFO("[SurveySphereTaskPlanner] 子任务 [{}] 规划成功，轨迹段数: {}，访问 {} 个拍摄点",
					result.sub_task_id, result.trajectory.size(), params.photo_angles.size());

			} catch (const std::exception& e) {
				result.success = false;
				result.message = "规划异常: " + std::string(e.what());
				LOG_ERROR("[SurveySphereTaskPlanner] 子任务 [{}] 规划异常: {}", result.sub_task_id, e.what());
			}

			return result;
		}
	} // namespace NSPlanning
} // namespace NSDrones