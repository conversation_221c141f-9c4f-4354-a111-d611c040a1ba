// include/uav/energies/fixedwing_energies.h
#pragma once

#include "uav/ienergy_model.h" 
#include "uav/uav.h"
#include <memory>                 

namespace NSDrones { namespace NSCore { class EntityObject; } }

namespace NSDrones {
	namespace NSUav {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class FixedWingEnergies
		 * @brief 固定翼无人机的能量消耗模型实现。
		 *        基于气动模型计算所需功率。
		 *        依赖于 owner_ 引用来访问参数。
		 */
		class FixedWingEnergies : public IEnergyModel {
		public:
			/**
			 * @brief 构造函数。
			 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
			 */
			explicit FixedWingEnergies(const Uav& owner); // 接收 owner 引用

			// --- 禁止拷贝和移动 ---
			FixedWingEnergies(const FixedWingEnergies&) = delete;
			FixedWingEnergies& operator=(const FixedWingEnergies&) = delete;
			FixedWingEnergies(FixedWingEnergies&&) = delete;
			FixedWingEnergies& operator=(FixedWingEnergies&&) = delete;

			/** @brief 虚析构函数 */
			~FixedWingEnergies() override = default;

			// --- 覆写 IEnergyModel 虚函数 ---
			/**
			 * @brief 计算给定状态和时间步长下的能量消耗。
			 *        参数查找基于 "energy.fw.*"。
			 * @param state 无人机当前状态。
			 * @param dt 时间步长 (秒)。
			 * @return 消耗的能量 (单位与参数 "energy.base.max_capacity" 一致, 例如 Wh)。
			 */
			double computeEnergyConsumption(const UavState& state, Time dt) const override;

			/**
			 * @brief 估算在给定状态和当前能量下的剩余续航时间。
			 *        通常基于最佳续航速度下的功耗进行估算。
			 * @param current_state 当前无人机状态。
			 * @param current_energy 当前剩余能量。
			 * @return 估算的剩余续航时间 (秒)。
			 */
			Time estimateEnduranceTime(const UavState& current_state, double current_energy) const override;
		};

	} // namespace NSUav
} // namespace NSDrones