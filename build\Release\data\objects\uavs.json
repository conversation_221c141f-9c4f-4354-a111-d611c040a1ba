[{"id": "AlphaMR001", "type_tag": "EntityObject.Uav.Multirotor", "name": "阿尔法多旋翼001", "parameters": {"initial_position_wgs84": [118.8, 32.1, 15.0], "initial_orientation_ypr_deg": [0.0, 0.0, 0.0], "energy.initial_battery_level_wh": 180.0, "payloads": ["camera_rgb_standard", "lidar_small"], "physics.rotor_count": 6, "physics.mass_kg": 2.2, "limits.max_speed_mps": 18.0, "shape_type": "SPHERE", "shape_sphere_radius_m": 0.5, "movement_strategy_type": "FLIGHT_STRATEGY"}}, {"id": "BravoMR002", "type_tag": "EntityObject.Uav.Multirotor", "name": "布拉沃多旋翼002", "parameters": {"initial_position_wgs84": [118.8001, 32.1001, 15.0], "initial_orientation_ypr_deg": [0.0, 0.0, 0.0], "physics.mass_kg": 3.0, "limits.max_speed_mps": 12.0, "energy.initial_battery_level_wh": 220.0, "energy.base.max_capacity": 220.0, "energy.mr.hover_power": 180.0, "payloads": ["camera_thermal_hd"], "movement_strategy_type": "FLIGHT_STRATEGY", "shape_type": "SPHERE", "shape_sphere_radius_m": 0.6}}, {"id": "CharlieFW001", "type_tag": "EntityObject.Uav.FixedWing", "name": "查理固定翼001", "parameters": {"initial_position_wgs84": [118.7995, 32.0995, 50.0], "initial_orientation_ypr_deg": [0.0, 0.0, 0.0], "physics.mass_kg": 5.0, "limits.max_speed_mps": 25.0, "energy.initial_battery_level_wh": 500.0, "energy.base.max_capacity": 500.0, "aero.min_airspeed_mps": 12.0, "aero.wing_span_m": 2.0, "propulsion.max_thrust_N": 15.0, "payloads": ["long_range_comm", "mapping_camera"], "shape_type": "SPHERE", "shape_sphere_radius_m": 0.5, "movement_strategy_type": "FLIGHT_STRATEGY"}}, {"id": "DeltaVTOL001", "type_tag": "EntityObject.Uav.VTOL", "name": "德尔塔垂起001", "parameters": {"initial_position_wgs84": [118.8005, 32.1005, 15.0], "initial_orientation_ypr_deg": [0.0, 0.0, 0.0], "physics.mass_kg": 6.0, "limits.max_speed_mps": 30.0, "energy.initial_battery_level_wh": 800.0, "energy.base.max_capacity": 800.0, "vtol.transition_speed_mps": 14.0, "vtol.hover_rotor_count": 4, "energy.vtol.hover_power": 300.0, "energy.consumption_forward_flight_coeff": 0.018, "aero.wing_span_m": 2.5, "payloads": ["delivery_package_small"], "shape_type": "SPHERE", "shape_sphere_radius_m": 0.5, "movement_strategy_type": "FLIGHT_STRATEGY"}}]