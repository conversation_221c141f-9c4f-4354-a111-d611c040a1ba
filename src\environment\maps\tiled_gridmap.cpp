#include "environment/maps/tiled_gridmap.h"
#include "environment/maps/single_gridmap.h"
#include "utils/logging.h"
#include "utils/file_utils.h" // For NSUtils::fileExists (if needed, or use own)
#include "params/paramregistry.h" // For ParamRegistry::getInstance()
#include <algorithm> // For std::min, std::max
#include <limits>    // For std::numeric_limits
#include <filesystem> // For std::filesystem::path
#include <set>  // 添加 set 头文件
#include <shared_mutex>  // 添加 shared_mutex 头文件
#include <spdlog/fmt/ostr.h>
#include <spdlog/fmt/fmt.h>
#include <spdlog/fmt/ranges.h>

namespace NSDrones {
	namespace NSEnvironment {

		// 构造函数
		TiledGridMap::TiledGridMap(LayerConfig config)
			: layerConfig_(std::move(config)) {
			LOG_INFO("TiledGridMap 构造函数 - 初始化 (空), 使用图层配置: 高程='{}', 地物='{}', 地物高度='{}', 目标分辨率={}m",
				layerConfig_.elevationLayer, layerConfig_.featureTypeLayer,
				layerConfig_.featureHeightLayer, layerConfig_.targetResolution);
		}

		// 析构函数
		TiledGridMap::~TiledGridMap() {
			LOG_DEBUG("TiledGridMap 析构函数 - 释放 {} 个瓦片资源", single_gridmaps_.size());
			// 缓存将自动释放内存，但为了更好的调试，我们显式清空
			single_gridmaps_.clear();
		}

		// 重新实现的初始化方法
		bool TiledGridMap::initialize(std::shared_ptr<NSParams::ParamValues> global_params) {
			LOG_INFO("TiledGridMap::initialize - 开始初始化瓦片地图");

			if (!global_params) {
				LOG_ERROR("TiledGridMap::initialize - 全局参数为空");
				return false;
			}

			try {
				// 步骤1：从全局参数获取地图配置
				auto map_enabled = global_params->getValueOrDefault<bool>("map.enabled", true);
				if (!map_enabled) {
					LOG_INFO("TiledGridMap: 地图功能已禁用");
					initialized_ = true;
					return true;
				}

				auto data_directory = global_params->getValueOrDefault<std::string>("map.data_directory", "terrain");
				auto load_mode = global_params->getValueOrDefault<std::string>("map.load_mode", "geotiff");
				auto auto_convert = global_params->getValueOrDefault<bool>("map.auto_convert", true);
				auto cache_directory = global_params->getValueOrDefault<std::string>("map.cache_directory", "terrain/cache");

				// 获取数据根目录用于路径解析
				auto data_root = NSUtils::getDataPath();
				LOG_DEBUG("TiledGridMap: 使用数据根目录: '{}'", data_root.generic_string());

				// 解析数据目录路径（支持相对路径和绝对路径）
				auto resolved_data_directory = NSUtils::resolvePath(data_root, std::filesystem::path(data_directory));
				if (resolved_data_directory.empty()) {
					LOG_ERROR("TiledGridMap: 无法解析数据目录路径: '{}'", data_directory);
					return false;
				}
				LOG_DEBUG("TiledGridMap: 解析后的数据目录: '{}'", resolved_data_directory.generic_string());

				// 解析缓存目录路径
				auto resolved_cache_directory = NSUtils::resolvePath(data_root, std::filesystem::path(cache_directory));
				if (resolved_cache_directory.empty()) {
					LOG_ERROR("TiledGridMap: 无法解析缓存目录路径: '{}'", cache_directory);
					return false;
				}

				LOG_INFO("TiledGridMap: 配置 - 数据目录: '{}', 加载模式: '{}', 自动转换: {}, 缓存目录: '{}'",
						 resolved_data_directory.generic_string(), load_mode, auto_convert, resolved_cache_directory.generic_string());

				// 步骤2：迭代地图数据目录，扫描文件
				std::vector<std::string> map_files = scanMapDataDirectory(resolved_data_directory.generic_string(), load_mode);
				if (map_files.empty()) {
					LOG_WARN("TiledGridMap: 在目录 '{}' 中未找到任何地图文件", resolved_data_directory.generic_string());
					initialized_ = true; // 标记为已初始化，但没有瓦片
					return true;
				}

				LOG_INFO("TiledGridMap: 找到 {} 个地图文件", map_files.size());

				// 步骤3：依托 SingleGridMap 加载数据源
				{
					std::unique_lock<std::shared_mutex> lock(tiles_mutex_);
					single_gridmaps_.clear();
				}

				bool all_success = true;
				std::unordered_map<std::string, std::shared_ptr<SingleGridMap>> loaded_tiles;

				// 保存全局参数用于后续动态加载
				globalParams_ = global_params;

				// 批量加载所有瓦片（不持有锁，避免长时间阻塞）
				for (const auto& file_path : map_files) {
					LOG_INFO("瓦片地图管理器: 正在加载地图文件: '{}'", file_path);

					// 创建SingleGridMap实例
					auto single_map = std::make_shared<SingleGridMap>();

					// 创建临时参数对象，添加文件路径信息
					auto temp_params = std::make_shared<NSParams::ParamValues>(*global_params);
					const auto& registry = NSParams::ParamRegistry::getInstance();
					temp_params->setValue("map.file_path", file_path, registry);
					temp_params->setValue("map.load_mode", load_mode, registry);

					// 尝试初始化SingleGridMap
					if (single_map->initialize(temp_params) && single_map->isInitialized()) {
						// 生成瓦片ID
						std::string tile_id = std::filesystem::path(file_path).stem().string();
						loaded_tiles[tile_id] = single_map;
						LOG_INFO("瓦片地图管理器: 成功加载瓦片 '{}' 从文件: '{}'", tile_id, file_path);
					} else {
						LOG_ERROR("瓦片地图管理器: 加载地图文件失败: '{}'", file_path);
						all_success = false;
					}
				}

				// 步骤4：批量添加成功加载的瓦片并更新元数据
				{
					std::unique_lock<std::shared_mutex> lock(tiles_mutex_);
					single_gridmaps_ = std::move(loaded_tiles);

					if (!single_gridmaps_.empty()) {
						updateCombinedBoundsInternal(); // 内部方法，调用者已持有锁
						initialized_ = true;
						LOG_INFO("瓦片地图管理器: 初始化完成，成功加载 {} 个瓦片", single_gridmaps_.size());
					} else {
						LOG_ERROR("瓦片地图管理器: 没有成功加载任何瓦片");
						return false;
					}
				}

				return all_success;

			} catch (const std::exception& e) {
				LOG_ERROR("TiledGridMap::initialize - 初始化失败: {}", e.what());
				return false;
			}
		}

		// 辅助方法实现

		// 扫描地图数据目录
		std::vector<std::string> TiledGridMap::scanMapDataDirectory(const std::string& data_directory, const std::string& load_mode) {
			std::vector<std::string> map_files;

			// 使用 file_utils 检查目录是否存在
			if (!NSUtils::isDirExists(data_directory)) {
				LOG_WARN("TiledGridMap: 数据目录 '{}' 不存在", data_directory);
				return map_files;
			}

			// 根据加载模式确定目标扩展名
			std::vector<std::string> target_extensions;
			if (load_mode == "geotiff") {
				// 只加载.tif文件（真正的GeoTIFF数据），排除.tiff文件（可视化图片）
				target_extensions = {".tif"};
			} else if (load_mode == "tiff") {
				// 加载预处理的TIFF文件，优先.tiff后缀
				target_extensions = {".tiff"};
			} else {
				LOG_ERROR("TiledGridMap: 未知的加载模式: '{}'", load_mode);
				return map_files;
			}

			LOG_DEBUG("TiledGridMap: 扫描目录 '{}' 查找文件扩展名: [{}]", data_directory,
					 fmt::join(target_extensions, ", "));

			// 使用 file_utils 的 findFilesInDirectory 方法
			std::vector<std::filesystem::path> found_files;
			bool scan_success = NSUtils::findFilesInDirectory(
				std::filesystem::path(data_directory),
				target_extensions,
				false, // 不递归搜索
				found_files
			);

			if (!scan_success) {
				LOG_ERROR("TiledGridMap: 扫描目录 '{}' 失败", data_directory);
				return map_files;
			}

			// 转换路径对象为字符串，使用generic_string()确保跨平台一致性
			map_files.reserve(found_files.size());
			for (const auto& file_path : found_files) {
				map_files.push_back(file_path.generic_string());
				LOG_DEBUG("TiledGridMap: 找到地图文件: '{}'", file_path.generic_string());
			}

			LOG_INFO("TiledGridMap: 在目录 '{}' 中找到 {} 个地图文件", data_directory, map_files.size());
			return map_files;
		}

		// 更新组合边界（线程安全版本）
		void TiledGridMap::updateCombinedBounds() {
			std::unique_lock<std::shared_mutex> lock(tiles_mutex_);
			updateCombinedBoundsInternal();
		}

		// 更新组合边界（内部版本，调用者需要持有锁）
		void TiledGridMap::updateCombinedBoundsInternal() {
			if (single_gridmaps_.empty()) {
				combinedBoundsWGS84_ = WGS84BoundingBox(); // 无效边界
				LOG_DEBUG("瓦片地图管理器: 没有瓦片，设置无效边界");
				return;
			}

			// 初始化组合边界为第一个瓦片的边界
			auto first_tile = single_gridmaps_.begin();
			combinedBoundsWGS84_ = first_tile->second->getMetadata().wgs84_bounds;

			// 扩展边界以包含所有瓦片
			for (const auto& [tile_id, single_map] : single_gridmaps_) {
				if (single_map) {
					combinedBoundsWGS84_.extend(single_map->getMetadata().wgs84_bounds);
				}
			}

			LOG_INFO("瓦片地图管理器: 更新组合边界完成，组合边界: {}", combinedBoundsWGS84_.toString());
		}

		// === IGridMap接口实现 ===

		std::optional<double> TiledGridMap::getElevation(double latitude, double longitude) const {
			if (!initialized_) {
				return std::nullopt;
			}

			auto best_tile = selectBestTile(latitude, longitude);
			if (best_tile) {
				return best_tile->getElevation(latitude, longitude);
			}

			LOG_DEBUG("瓦片地图管理器: 查询点({:.6f}, {:.6f})未被任何瓦片覆盖，无法获取高程数据",
				latitude, longitude);
			return std::nullopt;
		}

		std::optional<FeatureType> TiledGridMap::getFeature(double latitude, double longitude) const {
			if (!initialized_) {
				return std::nullopt;
			}

			auto best_tile = selectBestTile(latitude, longitude);
			if (best_tile) {
				return best_tile->getFeature(latitude, longitude);
			}

			LOG_DEBUG("瓦片地图管理器: 查询点({:.6f}, {:.6f})未被任何瓦片覆盖，无法获取地物类型数据",
				latitude, longitude);
			return std::nullopt;
		}

		std::optional<double> TiledGridMap::getFeatureHeight(double latitude, double longitude) const {
			if (!initialized_) {
				return std::nullopt;
			}

			auto best_tile = selectBestTile(latitude, longitude);
			if (best_tile) {
				return best_tile->getFeatureHeight(latitude, longitude);
			}

			LOG_DEBUG("瓦片地图管理器: 查询点({:.6f}, {:.6f})未被任何瓦片覆盖，无法获取地物高度数据",
				latitude, longitude);
			return std::nullopt;
		}

		bool TiledGridMap::isInitialized() const {
			return initialized_;
		}

		// === TiledGridMap特有接口实现 ===

		WGS84BoundingBox TiledGridMap::getCombinedDataSourceWGS84Bounds() const {
			std::shared_lock<std::shared_mutex> lock(tiles_mutex_);
			LOG_WARN("瓦片地图管理器: getCombinedDataSourceWGS84Bounds()方法已被弃用，建议使用getMetadata().wgs84_bounds");
			return combinedBoundsWGS84_;
		}

		bool TiledGridMap::isCovered(double latitude, double longitude) const {
			if (!initialized_) {
				return false;
			}

			std::shared_lock<std::shared_mutex> lock(tiles_mutex_);

			// 遍历所有瓦片，检查是否有任何瓦片实际覆盖该点
			for (const auto& [tile_id, single_map] : single_gridmaps_) {
				if (single_map && single_map->isCovered(latitude, longitude)) {
					return true;
				}
			}

			return false;
		}

		MapMetadata TiledGridMap::getMetadata() const {
			std::shared_lock<std::shared_mutex> lock(tiles_mutex_);

			LOG_DEBUG("瓦片地图管理器: 获取汇总元数据，包含 {} 个瓦片", single_gridmaps_.size());

			if (single_gridmaps_.empty()) {
				LOG_WARN("瓦片地图管理器: 没有瓦片，返回无效元数据");
				return MapMetadata();
			}

			// 创建汇总元数据
			MapMetadata summary_metadata;
			summary_metadata.map_id = "tiled_map";
			summary_metadata.map_name = "瓦片地图";
			summary_metadata.file_format = "tiled";
			summary_metadata.file_path = fmt::format("瓦片地图_{}个瓦片", single_gridmaps_.size());

			// 使用组合边界
			summary_metadata.wgs84_bounds = combinedBoundsWGS84_;

			// 计算汇总统计信息
			double min_resolution_x = std::numeric_limits<double>::max();
			double min_resolution_y = std::numeric_limits<double>::max();
			int total_grid_width = 0;
			int total_grid_height = 0;
			std::set<std::string> all_layers;

			for (const auto& [tile_id, single_map] : single_gridmaps_) {
				if (single_map) {
					auto tile_metadata = single_map->getMetadata();
					if (tile_metadata.isValid()) {
						// 收集最高精度（最小分辨率）
						min_resolution_x = std::min(min_resolution_x, tile_metadata.resolution.first);
						min_resolution_y = std::min(min_resolution_y, tile_metadata.resolution.second);

						// 累加网格尺寸（近似总像素数）
						total_grid_width += tile_metadata.grid_size.first;
						total_grid_height += tile_metadata.grid_size.second;

						// 收集所有图层
						for (const auto& layer : tile_metadata.available_layers) {
							all_layers.insert(layer);
						}
					}
				}
			}

			// 设置汇总信息
			summary_metadata.resolution = {min_resolution_x, min_resolution_y};
			summary_metadata.grid_size = {total_grid_width, total_grid_height};
			summary_metadata.available_layers = std::vector<std::string>(all_layers.begin(), all_layers.end());

			LOG_DEBUG("瓦片地图管理器: 汇总元数据 - {} 个瓦片，最高分辨率: ({:.3f}, {:.3f})米/像素，总像素: {}x{}，可用图层: [{}]",
				single_gridmaps_.size(), min_resolution_x, min_resolution_y,
				total_grid_width, total_grid_height, fmt::join(summary_metadata.available_layers, ", "));

			return summary_metadata;
		}

		std::vector<MapMetadata> TiledGridMap::getTileMetadataList() const {
			std::shared_lock<std::shared_mutex> lock(tiles_mutex_);

			std::vector<MapMetadata> tiles;
			tiles.reserve(single_gridmaps_.size());

			for (const auto& [tile_id, single_map] : single_gridmaps_) {
				if (single_map) {
					auto metadata = single_map->getMetadata();
					metadata.map_id = tile_id; // 确保map_id正确设置为瓦片ID
					tiles.push_back(metadata);
				}
			}

			LOG_DEBUG("瓦片地图管理器: 返回 {} 个瓦片的元数据列表", tiles.size());
			return tiles;
		}

		std::vector<MapMetadata> TiledGridMap::getTiles() const {
			LOG_WARN("瓦片地图管理器: getTiles()方法已被弃用，请使用getTileMetadataList()");
			return getTileMetadataList();
		}

		// === TiledGridMap特有接口实现 ===

		size_t TiledGridMap::getTileCount() const {
			std::shared_lock<std::shared_mutex> lock(tiles_mutex_);
			return single_gridmaps_.size();
		}

		std::optional<MapMetadata> TiledGridMap::getTileMetadata(const std::string& tile_id) const {
			std::shared_lock<std::shared_mutex> lock(tiles_mutex_);

			auto it = single_gridmaps_.find(tile_id);
			if (it != single_gridmaps_.end() && it->second) {
				auto metadata = it->second->getMetadata();
				metadata.map_id = tile_id;  // 确保map_id正确设置
				return metadata;
			}

			return std::nullopt;
		}

		std::vector<std::string> TiledGridMap::getCoveringTiles(double latitude, double longitude) const {
			std::shared_lock<std::shared_mutex> lock(tiles_mutex_);
			std::vector<std::string> covering_tiles;

			for (const auto& [tile_id, single_map] : single_gridmaps_) {
				if (single_map && single_map->isCovered(latitude, longitude)) {
					covering_tiles.push_back(tile_id);
				}
			}

			LOG_DEBUG("瓦片地图管理器: 坐标({:.6f}, {:.6f})被{}个瓦片覆盖",
				latitude, longitude, covering_tiles.size());

			return covering_tiles;
		}

		std::shared_ptr<SingleGridMap> TiledGridMap::selectBestTile(double latitude, double longitude) const {
			std::shared_lock<std::shared_mutex> lock(tiles_mutex_);

			std::shared_ptr<SingleGridMap> best_tile = nullptr;
			std::string best_tile_id;
			double best_resolution = std::numeric_limits<double>::max();

			for (const auto& [tile_id, single_map] : single_gridmaps_) {
				if (!single_map || !single_map->isCovered(latitude, longitude)) {
					continue;
				}

				// 计算几何平均分辨率
				auto resolution = single_map->getResolution();
				double current_resolution = std::sqrt(resolution.first * resolution.second);

				if (current_resolution > 0 && current_resolution < best_resolution) {
					best_resolution = current_resolution;
					best_tile = single_map;
					best_tile_id = tile_id;
				}
			}

			if (best_tile) {
				LOG_TRACE("瓦片地图管理器: 为坐标({:.6f}, {:.6f})选择瓦片'{}', 分辨率: {:.3f}米/像素",
					latitude, longitude, best_tile_id, best_resolution);
			}

			return best_tile;
		}

		void TiledGridMap::updateCombinedMetadata() {
			// 预留方法，用于未来的元数据缓存优化
			LOG_DEBUG("瓦片地图管理器: 更新组合元数据（当前为空实现）");
		}

		// === 动态瓦片管理接口实现 ===

		bool TiledGridMap::addTile(const std::string& file_path, const std::string& tile_id) {
			try {
				// 生成瓦片ID（如果未提供）
				std::string actual_tile_id = tile_id.empty() ?
					std::filesystem::path(file_path).stem().string() : tile_id;

				LOG_INFO("瓦片地图管理器: 开始添加瓦片 '{}' 从文件: '{}'", actual_tile_id, file_path);

				// 检查瓦片是否已存在
				{
					std::shared_lock<std::shared_mutex> read_lock(tiles_mutex_);
					if (single_gridmaps_.find(actual_tile_id) != single_gridmaps_.end()) {
						LOG_WARN("瓦片地图管理器: 瓦片 '{}' 已存在，跳过添加", actual_tile_id);
						return false;
					}
				}

				// 创建并初始化SingleGridMap（不持有锁）
				auto single_map = std::make_shared<SingleGridMap>();

				if (!globalParams_) {
					LOG_ERROR("瓦片地图管理器: 全局参数未初始化，无法添加瓦片");
					return false;
				}

				// 创建临时参数对象
				auto temp_params = std::make_shared<NSParams::ParamValues>(*globalParams_);
				const auto& registry = NSParams::ParamRegistry::getInstance();
				temp_params->setValue("map.file_path", file_path, registry);

				auto load_mode = globalParams_->getValueOrDefault<std::string>("map.load_mode", "geotiff");
				temp_params->setValue("map.load_mode", load_mode, registry);

				// 尝试初始化
				if (!single_map->initialize(temp_params) || !single_map->isInitialized()) {
					LOG_ERROR("瓦片地图管理器: 初始化瓦片 '{}' 失败", actual_tile_id);
					return false;
				}

				// 添加到瓦片集合并更新元数据（持有写锁）
				{
					std::unique_lock<std::shared_mutex> write_lock(tiles_mutex_);
					single_gridmaps_[actual_tile_id] = single_map;
					updateCombinedBoundsInternal();

					LOG_INFO("瓦片地图管理器: 成功添加瓦片 '{}', 当前总数: {}",
						actual_tile_id, single_gridmaps_.size());
				}

				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("瓦片地图管理器: 添加瓦片时发生异常: {}", e.what());
				return false;
			}
		}

		bool TiledGridMap::removeTile(const std::string& tile_id) {
			try {
				std::unique_lock<std::shared_mutex> lock(tiles_mutex_);

				auto it = single_gridmaps_.find(tile_id);
				if (it == single_gridmaps_.end()) {
					LOG_WARN("瓦片地图管理器: 瓦片 '{}' 不存在，无法移除", tile_id);
					return false;
				}

				single_gridmaps_.erase(it);
				updateCombinedBoundsInternal();

				LOG_INFO("瓦片地图管理器: 成功移除瓦片 '{}', 当前总数: {}",
					tile_id, single_gridmaps_.size());

				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("瓦片地图管理器: 移除瓦片时发生异常: {}", e.what());
				return false;
			}
		}

		bool TiledGridMap::reloadTile(const std::string& tile_id) {
			try {
				std::string file_path;

				// 获取原有瓦片的文件路径
				{
					std::shared_lock<std::shared_mutex> read_lock(tiles_mutex_);
					auto it = single_gridmaps_.find(tile_id);
					if (it == single_gridmaps_.end()) {
						LOG_WARN("瓦片地图管理器: 瓦片 '{}' 不存在，无法重新加载", tile_id);
						return false;
					}
					file_path = it->second->getMetadata().file_path;
				}

				// 先移除旧瓦片，再添加新瓦片
				if (removeTile(tile_id)) {
					return addTile(file_path, tile_id);
				}

				return false;

			} catch (const std::exception& e) {
				LOG_ERROR("瓦片地图管理器: 重新加载瓦片时发生异常: {}", e.what());
				return false;
			}
		}

		void TiledGridMap::clearAllTiles() {
			try {
				std::unique_lock<std::shared_mutex> lock(tiles_mutex_);

				size_t tile_count = single_gridmaps_.size();
				single_gridmaps_.clear();
				combinedBoundsWGS84_ = WGS84BoundingBox(); // 重置为无效边界
				initialized_ = false;

				LOG_INFO("瓦片地图管理器: 已清空所有瓦片，共移除 {} 个瓦片", tile_count);

			} catch (const std::exception& e) {
				LOG_ERROR("瓦片地图管理器: 清空瓦片时发生异常: {}", e.what());
			}
		}

	} // namespace NSEnvironment
} // namespace NSDrones
