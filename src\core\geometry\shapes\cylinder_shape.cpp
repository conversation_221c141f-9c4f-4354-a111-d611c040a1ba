#include "core/geometry/shapes/cylinder_shape.h"
#include "utils/enum_utils.h"
#include <stdexcept>
#include <sstream>
#include <cmath>
#include <algorithm>

namespace NSDrones{
namespace NSCore {

    CylinderShape::CylinderShape(double radius, double height) 
        : radius_(radius), height_(height), fcl_cylinder_(nullptr) {
        validateDimensions();
    }

    CylinderShape::CylinderShape() : CylinderShape(1.0, 2.0) {
    }

    CylinderShape::CylinderShape(const CylinderShape& other)
        : radius_(other.radius_), height_(other.height_), fcl_cylinder_(nullptr) {
    }

    CylinderShape& CylinderShape::operator=(const CylinderShape& other) {
        if (this != &other) {
            radius_ = other.radius_;
            height_ = other.height_;
            fcl_cylinder_.reset();  // 重置FCL对象，延迟重新创建
        }
        return *this;
    }

    std::shared_ptr<fcl::CollisionGeometryd> CylinderShape::getFCLGeometry() const {
        ensureFCLObject();
        return fcl_cylinder_;
    }

    fcl::AABBd CylinderShape::getAABB(const fcl::Transform3d& transform) const {
        ensureFCLObject();
        fcl::AABBd aabb;
        fcl::computeBV(*fcl_cylinder_, transform, aabb);
        return aabb;
    }

    double CylinderShape::getVolume() const {
        return M_PI * radius_ * radius_ * height_;
    }

    double CylinderShape::getSurfaceArea() const {
        return 2.0 * M_PI * radius_ * (radius_ + height_);
    }

    fcl::Vector3d CylinderShape::getCentroid() const {
        return fcl::Vector3d(0.0, 0.0, 0.0);  // 几何中心在原点
    }

    fcl::Matrix3d CylinderShape::getInertiaMatrix(double mass) const {
        // 圆柱体的惯性张量（相对于质心，Z轴为圆柱轴）
        double r2 = radius_ * radius_;
        double h2 = height_ * height_;
        
        fcl::Matrix3d inertia = fcl::Matrix3d::Zero();
        inertia(0, 0) = mass / 12.0 * (3.0 * r2 + h2);  // Ixx
        inertia(1, 1) = mass / 12.0 * (3.0 * r2 + h2);  // Iyy
        inertia(2, 2) = mass / 2.0 * r2;                // Izz
        
        return inertia;
    }

    std::unique_ptr<IShape> CylinderShape::clone() const {
        return std::make_unique<CylinderShape>(*this);
    }

    nlohmann::json CylinderShape::serialize() const {
        nlohmann::json j;
        j["type"] = NSUtils::enumToString(getType());
        j["radius"] = radius_;
        j["height"] = height_;
        return j;
    }

    bool CylinderShape::deserialize(const nlohmann::json& json) {
        try {
            std::string type_str = json.at("type").get<std::string>();
            auto type_opt = NSUtils::stringToEnum<ShapeType>(type_str, ShapeType::UNKNOWN);
            if (type_opt != ShapeType::CYLINDER) {
                return false;
            }

            radius_ = json.at("radius").get<double>();
            height_ = json.at("height").get<double>();

            validateDimensions();
            fcl_cylinder_.reset();  // 重置FCL对象
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    std::string CylinderShape::toString() const {
        std::ostringstream oss;
        oss << "CylinderShape(radius=" << radius_ << ", height=" << height_ << ")";
        return oss.str();
    }

    bool CylinderShape::containsPoint(const fcl::Vector3d& point) const {
        double half_height = height_ * 0.5;
        
        // 检查Z轴范围
        if (std::abs(point.z()) > half_height) {
            return false;
        }
        
        // 检查径向距离
        double radial_distance_squared = point.x() * point.x() + point.y() * point.y();
        return radial_distance_squared <= (radius_ * radius_);
    }

    double CylinderShape::distanceToPoint(const fcl::Vector3d& point) const {
        double half_height = height_ * 0.5;
        double radial_distance = std::sqrt(point.x() * point.x() + point.y() * point.y());
        
        // 计算到圆柱面的距离
        double radial_dist = radial_distance - radius_;
        double axial_dist = std::abs(point.z()) - half_height;
        
        if (containsPoint(point)) {
            // 点在内部，返回到最近表面的负距离
            double min_radial = radius_ - radial_distance;
            double min_axial = half_height - std::abs(point.z());
            return -std::min(min_radial, min_axial);
        }
        
        // 点在外部
        if (radial_dist <= 0.0 && axial_dist <= 0.0) {
            // 在圆柱体内部（这种情况已经在上面处理了）
            return 0.0;
        } else if (radial_dist <= 0.0) {
            // 在径向范围内，但超出轴向范围
            return axial_dist;
        } else if (axial_dist <= 0.0) {
            // 在轴向范围内，但超出径向范围
            return radial_dist;
        } else {
            // 同时超出径向和轴向范围
            return std::sqrt(radial_dist * radial_dist + axial_dist * axial_dist);
        }
    }

    double CylinderShape::getCharacteristicSize() const {
        // 使用对角线长度作为特征尺寸
        return std::sqrt(4.0 * radius_ * radius_ + height_ * height_);
    }

    void CylinderShape::setSize(double radius, double height) {
        radius_ = radius;
        height_ = height;
        validateDimensions();
        fcl_cylinder_.reset();  // 重置FCL对象
    }

    double CylinderShape::getBaseArea() const {
        return M_PI * radius_ * radius_;
    }

    double CylinderShape::getLateralArea() const {
        return 2.0 * M_PI * radius_ * height_;
    }

    fcl::Vector3d CylinderShape::getPointOnCylinder(double theta, double z,
                                                   const fcl::Transform3d& transform) const {
        // 确保z在有效范围内
        double half_height = height_ * 0.5;
        z = std::clamp(z, -half_height, half_height);
        
        // 圆柱坐标转换为笛卡尔坐标
        double x = radius_ * std::cos(theta);
        double y = radius_ * std::sin(theta);
        
        fcl::Vector3d point(x, y, z);
        return transform * point;
    }

    std::vector<fcl::Vector3d> CylinderShape::generateUniformPoints(size_t num_circumference, 
                                                                   size_t num_height, 
                                                                   bool include_caps,
                                                                   const fcl::Transform3d& transform) const {
        std::vector<fcl::Vector3d> points;
        
        double half_height = height_ * 0.5;
        
        // 生成侧面点
        for (size_t i = 0; i < num_circumference; ++i) {
            double theta = 2.0 * M_PI * i / num_circumference;
            for (size_t j = 0; j < num_height; ++j) {
                double z = -half_height + height_ * j / (num_height - 1);
                points.push_back(getPointOnCylinder(theta, z, transform));
            }
        }
        
        // 生成顶底面点
        if (include_caps) {
            for (size_t i = 0; i < num_circumference; ++i) {
                double theta = 2.0 * M_PI * i / num_circumference;
                
                // 顶面
                fcl::Vector3d top_point(radius_ * std::cos(theta), radius_ * std::sin(theta), half_height);
                points.push_back(transform * top_point);
                
                // 底面
                fcl::Vector3d bottom_point(radius_ * std::cos(theta), radius_ * std::sin(theta), -half_height);
                points.push_back(transform * bottom_point);
            }
        }
        
        return points;
    }

    fcl::Vector3d CylinderShape::getSurfaceNormal(const fcl::Vector3d& point,
                                                 const fcl::Transform3d& transform) const {
        // 将点转换到局部坐标系
        fcl::Vector3d local_point = transform.inverse() * point;
        
        double half_height = height_ * 0.5;
        fcl::Vector3d normal;
        
        // 判断点在哪个表面上
        if (std::abs(local_point.z() - half_height) < 1e-6) {
            // 顶面
            normal = fcl::Vector3d(0.0, 0.0, 1.0);
        } else if (std::abs(local_point.z() + half_height) < 1e-6) {
            // 底面
            normal = fcl::Vector3d(0.0, 0.0, -1.0);
        } else {
            // 侧面
            double radial_distance = std::sqrt(local_point.x() * local_point.x() + local_point.y() * local_point.y());
            if (radial_distance > 1e-10) {
                normal = fcl::Vector3d(local_point.x() / radial_distance, local_point.y() / radial_distance, 0.0);
            } else {
                normal = fcl::Vector3d(1.0, 0.0, 0.0);  // 默认法向量
            }
        }
        
        // 转换回全局坐标系（只应用旋转）
        return transform.rotation() * normal;
    }

    std::unique_ptr<CylinderShape> CylinderShape::createUnitCylinder() {
        return std::make_unique<CylinderShape>(1.0, 2.0);
    }

    std::unique_ptr<CylinderShape> CylinderShape::createFromDiameter(double diameter, double height) {
        return std::make_unique<CylinderShape>(diameter * 0.5, height);
    }

    void CylinderShape::ensureFCLObject() const {
        if (!fcl_cylinder_) {
            fcl_cylinder_ = std::make_shared<fcl::Cylinderd>(radius_, height_);
        }
    }

    void CylinderShape::validateDimensions() const {
        if (radius_ <= 0.0 || height_ <= 0.0) {
            throw std::invalid_argument("CylinderShape: 半径和高度必须为正数");
        }
        if (!std::isfinite(radius_) || !std::isfinite(height_)) {
            throw std::invalid_argument("CylinderShape: 尺寸必须为有限数值");
        }
    }

} // namespace NSCore
} // namespace NSDrones