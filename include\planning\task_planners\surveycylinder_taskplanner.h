// include/planning/task_planners/task_planner_surveycylinder.h
#pragma once

#include "planning/itask_planner.h"
#include "environment/environment_fwd.h"
#include "mission/task.h"
#include "mission/task_params.h"

namespace NSDrones {
	namespace NSPlanning {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class SurveyCylinderTaskPlanner
		 * @brief 负责规划 SURVEY_CYLINDER (圆柱勘察) 任务。
		 */
		class SurveyCylinderTaskPlanner : public ITaskPlanner {
		public:
			SurveyCylinderTaskPlanner();

			// 新增: 初始化方法
			bool initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) override;

			/**
			 * @brief 规划单机圆柱勘察任务（重构后的接口）
			 * @param request 单任务规划请求
			 * @return 单任务规划结果
			 */
			SingleTaskPlanningResult planSingleTask(const SingleTaskPlanningRequest& request) override;

			/**
			 * @brief 检查是否支持指定的子任务类型
			 * @param sub_target 子任务目标
			 * @return 是否支持
			 */
			bool isSubTaskSupported(const SubTaskTarget& sub_target) const override{
				return sub_target.task_type == NSMission::TaskType::SURVEY_CYLINDER;
			}

		private:
			// === 配置参数 ===
			int default_points_per_circle_ = 36;      ///< 默认每圈点数
			double default_flight_speed_ = 8.0;       ///< 默认飞行速度 (m/s)
			double min_radius_ = 1.0;                  ///< 最小半径限制 (m)
			double max_height_ = 200.0;                ///< 最大高度限制 (m)
			bool enable_optimization_ = true;         ///< 是否启用轨迹优化
			std::string scan_pattern_ = "spiral";     ///< 扫描模式 ("spiral", "zigzag", etc.)

			// === 私有辅助方法 ===

			/**
			 * @brief 规划圆柱勘察轨迹的核心实现
			 * @param request 单任务规划请求
			 * @param params 圆柱勘察任务参数
			 * @param start_state 无人机起始状态
			 * @return 单任务规划结果
			 */
			SingleTaskPlanningResult planCylinderTrajectory(
				const SingleTaskPlanningRequest& request,
				const NSMission::SurveyCylinderTaskParams& params,
				const NSUav::UavState& start_state);

			/**
			 * @brief 应用避障约束到轨迹
			 * @param trajectory 要修改的轨迹
			 * @param constraints 避障约束列表
			 * @param result 规划结果（用于记录警告）
			 * @return 应用成功返回true
			 */
			bool applyAvoidanceConstraints(
				Trajectory& trajectory,
				const std::vector<AvoidanceConstraint>& constraints,
				SingleTaskPlanningResult& result);
		};

	} // namespace NSPlanning
} // namespace NSDrones