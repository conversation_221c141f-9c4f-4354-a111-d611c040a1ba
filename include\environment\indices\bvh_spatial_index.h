// include/environment/indices/bvh_spatial_index.h
#pragma once

#include "environment/indices/ispatial_index.h"
#include "utils/logging.h"
#include <fcl/fcl.h>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <chrono>

namespace NSDrones {
namespace NSEnvironment {

    /**
     * @struct BvhObjectData
     * @brief BVH索引中存储的对象数据
     */
    struct BvhObjectData {
        ObjectID id;                    // 对象ID
        EcefPoint ecefPosition;                 // ECEF坐标位置
        BoundingBox localBounds;        // 局部包围盒
        BoundingBox ecefBounds;             // ECEF包围盒（使用BoundingBox代替ECEFBoundingBox）
        std::shared_ptr<fcl::CollisionObjectd> fclObject;  // FCL碰撞对象

        BvhObjectData() = default;
        BvhObjectData(const ObjectID& obj_id,
                     const EcefPoint& ecef_pos,
                     const BoundingBox& local_box);
    };

    /**
     * @class BvhSpatialIndex
     * @brief 基于FCL BVH和ECEF坐标系的空间索引实现
     * 
     * 特点：
     * - 使用ECEF坐标系统，适合全球范围的空间索引
     * - 基于GeographicLib进行精确的坐标转换
     * - 使用FCL的BVH结构进行高效的空间查询
     * - 支持查询缓存和内存管理
     * - 线程安全的操作
     */
    class BvhSpatialIndex : public ISpatialIndex {
    private:
        // === 核心数据结构 ===
        std::unordered_map<ObjectID, BvhObjectData> objects_;  // 对象存储
        std::unique_ptr<fcl::BroadPhaseCollisionManagerd> bvh_manager_; // FCL BVH管理器
        mutable std::shared_mutex objects_mutex_;                      // 对象访问锁
        // === 内存管理 ===
        size_t memory_limit_ = 0;  // 0表示无限制

        // === 私有方法 ===
        
        /**
         * @brief 创建FCL碰撞对象
         * @param ecef_pos ECEF位置
         * @param local_bounds 局部包围盒
         * @return FCL碰撞对象
         */
        std::shared_ptr<fcl::CollisionObjectd> createFCLObject(
            const EcefPoint& ecef_pos,
            const BoundingBox& local_bounds) const;

        /**
         * @brief 更新BVH管理器
         */
        void updateBVHManager();



        /**
         * @brief 应用查询过滤器
         * @param object_ids 对象ID列表
         * @param filter 过滤器
         * @return 过滤后的对象ID列表
         */
        std::vector<ObjectID> applyFilter(
            const std::vector<ObjectID>& object_ids,
            const std::optional<SpatialQueryFilter>& filter) const;

    public:
        /**
         * @brief 构造函数
         */
        BvhSpatialIndex();

        /**
         * @brief 析构函数
         */
        virtual ~BvhSpatialIndex();

        // === ISpatialIndex接口实现 ===

        bool initialize(const nlohmann::json& config_json,
                       const NSParams::ParamValues& global_params,
                       const std::optional<BoundingBox>& map_bounds = std::nullopt) override;

        // 对象管理
        void addObject(const SpatialObject& object) override;
        void addObjects(const std::vector<SpatialObject>& objects) override;
        void updateObject(const SpatialObjectUpdate& update) override;
        void updateObjects(const std::vector<SpatialObjectUpdate>& updates) override;
        void updateObjectPosition(const ObjectID& objectId,
                                const WGS84Point& newPosition,
                                const std::optional<WGS84Point>& oldPosition = std::nullopt) override;
        void updateObjectPositions(const std::vector<std::pair<ObjectID,
                                  std::pair<std::optional<WGS84Point>, WGS84Point>>>& positionUpdates) override;
        void removeObject(const ObjectID& objectId) override;
        void removeObjects(const std::vector<ObjectID>& objectIds) override;

        // 查询功能
        std::vector<ObjectID> findObjectsInRegion(
            const WGS84BoundingBox& region,
            const std::optional<SpatialQueryFilter>& filter = std::nullopt) const override;
        std::vector<ObjectID> findObjectsNearPoint(
            const WGS84Point& point, double radius,
            const std::optional<SpatialQueryFilter>& filter = std::nullopt) const override;
        bool objectsIntersect(const ObjectID& objectId1, const ObjectID& objectId2) const override;

        // 对象信息
        std::optional<WGS84BoundingBox> getObjectBounds(const ObjectID& objectId) const override;
        std::optional<SpatialObjectData> getObjectData(const ObjectID& objectId) const override;
        std::vector<ObjectID> getAllObjectIds() const override;

        // 缓存和内存管理（空实现，缓存由Environment统一管理）
        bool configureQueryCache(bool enable, size_t maxSize = 100,
                                std::chrono::milliseconds maxAge = std::chrono::milliseconds(5000)) override;
        void clearQueryCache() override;
        void setMemoryLimit(size_t bytesLimit) override;
        size_t getEstimatedMemoryUsage() const override;

        // 序列化
        nlohmann::json serialize() const override;
        bool deserialize(const nlohmann::json& json) override;
    };

} // namespace NSEnvironment
} // namespace NSDrones
