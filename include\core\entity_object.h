// include/core/entity_object.h
#pragma once

#include "core/base_object.h"
#include "core/entity_state.h"
#include "core/geometry/ishape.h"  // 新形状系统
#include <string>
#include <vector>
#include <memory>
#include <optional>
#include <stdexcept>
#include <mutex>
#include <shared_mutex>

// --- FCL 前向声明 ---
namespace fcl {
	template <typename S>
	class CollisionObject;
	using CollisionObjectd = CollisionObject<double>;
}

// --- 前向声明 ---
namespace NSDrones {
	namespace NSEnvironment { class Environment; }
	namespace NSCore {
		class IMovementStrategy;
	}
}

namespace NSDrones {
	namespace NSCore {
		using namespace ::NSDrones::NSUtils;

		// 使用新形状系统
		using IShapePtr = std::shared_ptr<IShape>;

		/**
		 * @class EntityObject
		 * @brief 所有模拟实体的基类。
		 *
		 * 管理对象的基本属性 (ID, 类型, 名称)、动态状态（位置、姿态、速度等）、
		 * 几何形状、参数集以及与环境的交互。
		 * 提供参数的层级查找功能。
		 * 派生类需要实现 registerParams 方法来注册自己的参数。
		 * 维护对象在环境索引中的状态，通过 notifyUpdate 通知环境。
		 *
		 * 坐标系统设计：
		 * - EntityState 存储 WGS84 全局坐标
		 * - 实体可以选择使用任务空间进行局部坐标计算
		 * - 环境索引使用局部NED坐标（通过坐标转换获得）
		 */
		class EntityObject : public BaseObject {
			// 授予 Environment 访问权限以调用内部方法（例如设置父子关系、通知）
			friend class Environment;

		protected:
			EntityStatePtr state_;						// 对象的完整动态状态数据
			IShapePtr shape_;                       // 对象的几何形状 (可以为 nullptr)

			std::shared_ptr<IMovementStrategy> movement_strategy_ = nullptr; // 移动策略成员

			// --- 坐标系统管理 ---
			std::string task_space_id_;         // 当前使用的任务空间ID（可选）

			/**
			 * @brief (内部) 通知环境此对象的状态已更新，以便更新相关索引。
			 * @param state_before_update 描述状态变化前用于索引的关键信息的快照。
			 *                          快照中应包含所有可能影响索引的旧值和新值。
			 */
			void notifyUpdate(const EntityStateSnapshot& state_before_update);

			/**
			 * @brief 从参数加载初始状态（位置和姿态）。
			 *        由 initialize() 方法调用。
			 * @return 如果成功返回 true。
			 */
			bool loadInitialStateFromParams();

			/**
			 * @brief 根据已加载的参数创建并设置对象的几何形状。
			 *        由派生类的 initialize() 方法调用。
			 * @return 如果形状成功创建并设置（或参数指定无形状），则返回 true。
			 */
			bool createAndSetShapeFromParams();

			/**
			 * @brief 根据已加载的参数创建并设置对象的移动策略。
			 *        由派生类的 initialize() 方法调用。
			 * @return 如果移动策略成功创建并设置，则返回 true。
			 */
			bool createAndSetMovementStrategyFromParams();



		public:
			/**
			 * @brief 构造函数。
			 * @param id 对象的唯一 ID。
			 * @param object_type_key 对象的类型字符串，用于参数系统和工厂。
			 * @param name 对象的可读名称。
			 * @param initial_state 对象的初始状态 (位置、姿态等)。
			 * @throws DroneException 如果 id 无效。
			 */
			explicit EntityObject(ObjectID id,
				const std::string& object_type_key,
				const std::string& name,
				const EntityState& initial_state);

			/** @brief 虚析构函数，允许派生类正确清理。*/
			virtual ~EntityObject() = default;

			// --- 禁止拷贝和移动 ---
			EntityObject(const EntityObject&) = delete;
			EntityObject& operator=(const EntityObject&) = delete;
			EntityObject(EntityObject&&) = delete;
			EntityObject& operator=(EntityObject&&) = delete;

			// --- 基本信息访问 ---
			/** @brief 获取对象 ID。*/
			virtual std::string getClassName() const override { return "EntityObject"; }
			/** @brief 获取形状 (const 共享指针)。*/
			IShapePtr getShape() const { return shape_; }
			/**
			 * @brief 获取对象当前的轴对齐包围盒 (AABB)。
			 *        **注意：** 此方法返回基于 *当前* 状态的包围盒。
			 *        如果需要预测未来的包围盒，应使用 `getPredictedBoundingBox(Time dt)`。
			 * @return 对象的 AABB。如果无形状，返回无效 AABB。
			 */
			virtual BoundingBox getBoundingBox() const;

			/**
			 * @brief 对象初始化入口点。
			 *        此方法应由对象的工厂函数或创建逻辑在对象创建后调用。
			 *        它负责使用 Config 系统准备好的、包含默认值和实例特定值的 ParamValues 对象
			 *        来完成对象的最终设置，包括参数赋值、形状创建等。
			 * @param params 由 Config 系统准备好的、包含最终参数值的 ParamValues 对象的共享指针。
			 * @return 如果初始化成功，返回 true。
			 */
			virtual bool initialize(std::shared_ptr<NSParams::ParamValues> final_params, const nlohmann::json& raw_instance_json_config);

			// --- 动态数据与关系访问/修改 ---
			/** @brief 获取完整的动态状态数据 (const 引用)。*/
			const EntityStatePtr getState() const;

			/** @brief 获取当前WGS84位置。*/
			const WGS84Point& getWGS84Position() const;

			/**
			 * @brief 获取当前局部坐标位置（用于碰撞检测和空间索引）
			 * @param task_space_id 任务空间ID，默认为"global_space"
			 * @return 局部坐标位置（NED坐标系），如果转换失败则返回std::nullopt
			 */
			std::optional<NedPoint> getNedPosition(const std::string& task_space_id = "global_space") const;

			/**
			 * @brief 设置局部坐标位置（会转换为WGS84坐标存储）
			 * @param local_pos 局部坐标位置（NED坐标系）
			 * @param task_space_id 任务空间ID，默认为"global_space"
			 */
			void setNedPosition(const NedPoint& local_pos, const std::string& task_space_id = "global_space");

			/** @brief 获取当前姿态。*/
			const Orientation& getOrientation() const;
			/** @brief 获取当前速度。*/
			const Vector3D& getVelocity() const;
			/** @brief 获取当前状态字符串。*/
			const std::string& getStatus() const;
			/** @brief 获取能力列表。*/
			const std::vector<std::string>& getCapabilities() const;
			/** @brief 获取当前时间戳。*/
			Time getTimeStamp() const;

			/** @brief 获取此对象对应的 FCL 碰撞对象。*/
			std::shared_ptr<fcl::CollisionObjectd> getFclObject() const;

			/** @brief 检查对象是否具有特定能力。 */
			bool hasCapability(const std::string& capability) const;

			/** @brief 设置对象的几何形状，并通知环境更新空间索引。*/
			virtual void setShape(IShapePtr shape);
			/** @brief 设置对象加速度 (不直接更新索引)。*/
			virtual void setAcceleration(const Vector3D& accel);

			/** @brief 设置对象WGS84位置，并通知环境更新空间索引。*/
			virtual void setWGS84Position(const WGS84Point& wgs84_pos);
			/** @brief 设置对象姿态 (可能影响包围盒，会通知环境更新空间索引)。*/
			virtual void setOrientation(const Orientation& orn);
			/** @brief 设置对象速度。
			 *         主要影响具有移动策略的对象的状态更新，或者用于无策略对象的简单预测。
			 */
			virtual void setVelocity(const Vector3D& vel);
			/** @brief 设置对象状态字符串，并通知环境更新属性索引。*/
			virtual void setStatus(const std::string& new_status);
			/** @brief 添加能力，并通知环境更新属性索引。*/
			virtual void addCapability(const std::string& capability);
			/** @brief 移除能力，并通知环境更新属性索引。*/
			virtual void removeCapability(const std::string& capability);
			/**
			 * @brief 更新整个动态状态。
			 *        **重要:** 此方法会检查哪些字段发生变化，并通知环境更新相应索引。
			 * @param new_state 新的对象状态。
			 */
			virtual void updateState(const EntityState& new_state);

			// --- 任务空间管理 ---
			/**
			 * @brief 设置实体使用的任务空间
			 * @param task_space_id 任务空间ID，空字符串表示不使用任务空间
			 */
			virtual void setTaskSpace(const std::string& task_space_id);

			/**
			 * @brief 获取当前使用的任务空间ID
			 * @return 任务空间ID，空字符串表示未使用任务空间
			 */
			const std::string& getCurrentTaskSpaceId() const { return task_space_id_; }

			/**
			 * @brief 检查是否使用了任务空间
			 * @return 是否使用任务空间
			 */
			bool hasTaskSpace() const { return !task_space_id_.empty(); }

			/**
			 * @brief 获取在当前任务空间中的局部坐标
			 * @return 局部NED坐标，如果未设置任务空间则抛出异常
			 */
			NedPoint getNedPositionInCurrentTaskSpace() const;

			/**
			 * @brief 获取在指定任务空间中的局部坐标
			 * @param task_space_id 任务空间ID
			 * @return 局部NED坐标
			 */
			NedPoint getNedPositionInTaskSpace(const std::string& task_space_id) const;

			/**
			 * @brief 更新指定任务空间的基准点位置（如果本实体是基准点）
			 * @param task_space_id 任务空间ID
			 * @param position 新的基准点位置
			 */
			void updateTaskSpaceReferencePoint(const std::string& task_space_id, const WGS84Point& position) const;

			/**
			 * @brief 处理任务空间基准点变更通知
			 * @param task_space_id 发生变更的任务空间ID
			 * @param new_origin 新的基准点位置
			 * @note 当对象注册的任务空间基准点发生变更时，TaskSpace会调用此方法通知对象
			 */
			virtual void onTaskSpaceOriginChanged(const std::string& task_space_id, const WGS84Point& new_origin);

			// --- 移动策略相关方法 ---
			bool hasMovementStrategy() const{return movement_strategy_ != nullptr;}
			void setMovementStrategy(std::shared_ptr<IMovementStrategy> strategy);
			std::shared_ptr<IMovementStrategy> getMovementStrategy() const;
			/**
			 * @brief 根据时间步长和环境更新对象的内部状态。
			 *        此方法应由模拟循环定期调用。
			 *        默认实现会调用关联的 IMovementStrategy (如果存在)。
			 *        派生类可以重写此方法以实现自定义的更新逻辑，
			 *        或者通过设置不同的移动策略来改变行为。
			 * @param dt 时间步长 (秒)。
			 */
			virtual void autoStateUpdate(Time dt);

			/**
			 * @brief (新增) 填充状态快照，用于环境索引更新。
			 *        派生类可以重写此方法以包含更多特定于其类型的信息。
			 * @param snapshot 要填充的快照对象。
			 * @param old_state_if_available 可选的，如果这是一次更新操作，则提供更新前的状态。
			 */
			virtual void populateSnapshot(EntityStateSnapshot& snapshot, const std::optional<EntityState>& old_state_if_available = std::nullopt) const;

			/**
			 * @brief 预测对象在指定时间后的WGS84位置
			 * @param dt 时间增量 (秒)
			 * @return 预测的未来WGS84坐标位置（人类可理解的地理坐标）
			 * @note 如果对象有关联的移动策略，则使用策略进行预测；否则返回当前位置
			 */
			virtual WGS84Point predictWGS84Position(Time dt) const;

			/**
			 * @brief 预测对象在指定时间后的ECEF位置
			 * @param dt 时间增量 (秒)
			 * @return 预测的未来ECEF坐标位置（用于几何计算）
			 * @note 用于需要高精度几何计算的场景
			 */
			virtual EcefPoint predictEcefPosition(Time dt) const;

			/**
			 * @brief 获取对象在 dt 时间后预测位置的包围盒。
			 * @param dt 时间增量 (秒)。
			 * @return 预测的未来包围盒 BoundingBox。
			 */
			virtual BoundingBox predictBoundingBox(Time dt) const;
		};

		using EntityObjectPtr = std::shared_ptr<EntityObject>;
		using ConstEntityObjectPtr = std::shared_ptr<const EntityObject>;

	} // namespace NSCore
} // namespace NSDrones
