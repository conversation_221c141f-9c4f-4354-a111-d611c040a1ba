// include/core/base_object.tpp
#pragma once 

#include "environment/environment.h"
#include "params/paramregistry.h"
#include "core/base_object.h" 
#include "utils/logging.h"
#include "utils/object_id.h"
#include <optional>
#include <stdexcept>
#include <sstream>
#include <typeinfo>
#include <variant>

namespace NSDrones {
	namespace NSCore {
		// --- BaseObject 模板方法实现 ---
		template <typename T>
		inline std::optional<T> BaseObject::getEffectiveParam(const std::string& key) const {
			// 声明环境变量，在整个函数中重用
			std::shared_ptr<NSEnvironment::Environment> env = nullptr;

			// 1. 检查本地 ParamValues
			if (params_) { // 检查共享指针是否有效
				auto local_val = params_->getValue<T>(key); // 解引用共享指针后调用
				if (local_val) {
					LOG_TRACE("对象 [{} ID:{}]: 参数 '{}' 使用本地值.", name_, id_, key);
					return local_val;
				}
			}

			// 2. 检查父对象 (递归)
			if (NSUtils::isValidObjectID(parent_id_)) {
				// 从环境中获取父对象的 const 引用 (Environment 负责线程安全)
				env = getEnvironment();
				std::shared_ptr<const BaseObject> parent = env ? env->getObjectById<BaseObject>(parent_id_) : nullptr; // 使用 getObjectById 获取对象
				if (parent) {
					auto parent_val = parent->getEffectiveParam<T>(key); // 递归调用
					if (parent_val) {
						LOG_TRACE("对象 [{} ID:{}]: 参数 '{}' 继承自父级 [{} ID:{}].", name_, id_, key, parent->getName(), parent->getId());
						return parent_val;
					}
				}
				else {
					// 使用线程局部静态标志避免日志泛滥
					thread_local static bool parent_warning_logged = false;
					if (!parent_warning_logged) {
						LOG_WARN("对象 [{} ID:{}]: 父对象 ID '{}' 在环境中找不到，无法查找继承参数。", name_, id_, parent_id_);
						parent_warning_logged = true;
					}
				}
			}

			// 3. 检查环境的全局 ParamSet
			if (!env) env = getEnvironment(); // 重用之前获取的环境实例
			if (env) {
				auto global_val_opt = env->getGlobalParam(key); // 调用 Environment 的方法获取全局值
				if (global_val_opt) {
				try {
					// 尝试将 ParamValue 转换为请求的类型 T
					T value = std::get<T>(*global_val_opt);
					LOG_TRACE("对象 [{} ID:{}]: 参数 '{}' 使用环境全局值.", name_, id_, key);
					return value;
				} catch (const std::exception& e) {
					LOG_WARN("对象 [{} ID:{}]: 全局参数 '{}' 类型转换失败: {}", name_, id_, key, e.what());
				}
				}
			}

			// 4. 检查参数注册表中的默认值 (使用对象的 type_tag)
			//    这是最后的查找步骤，获取该类型参数的默认值
			if (!env) env = getEnvironment(); // 确保环境实例存在
			if (!env) return std::nullopt; // 如果环境不存在，返回空值
			const NSParams::ParamRegistry& registry = env->getParamRegistry();
			// 使用对象的 type_tag_ 和 key 来获取定义
			const NSParams::ParamDefine* def = registry.getEffectiveParamDefine(this->type_tag_, key);
			if (def) { 
				// 尝试从 ParamDefine 的 default_value (variant) 中获取类型 T 的值
				const T* default_ptr = std::get_if<T>(&(def->default_value));
				if (default_ptr) { 
					LOG_TRACE("对象 [{} ID:{}]: 参数 '{}' 使用注册表默认值 (类型匹配).", name_, id_, key);
					return *default_ptr; 
				} else {
					LOG_TRACE("对象 [{} ID:{}]: 参数 '{}' 的默认值类型与请求的类型 '{}' 不匹配.",
							  name_, id_, key, typeid(T).name());
				}
			} else {
				LOG_TRACE("对象 [{} ID:{}]: 参数 '{}' 在注册表中未找到定义 (基于类型标签 '{}').", name_, id_, key, this->type_tag_);
			}

			// 5. 在任何地方都未找到 (包括类型不匹配或无定义)
			LOG_TRACE("对象 [{} ID:{}]: 参数 '{}' 未找到有效的层级值或默认值.", name_, id_, key);
			return std::nullopt; 
		}

		template <typename T>
		inline T BaseObject::getParamOrThrow(const std::string& key) const {
			auto opt_val = getEffectiveParam<T>(key); // 进行层级查找
			if (opt_val) {
				return *opt_val; // 找到则返回值
			}
			else {
				// 构造详细的错误消息，指明查找失败
				std::stringstream ss;
				ss << "对象 [" << name_ << " ID:" << id_ << "] 未找到或类型不匹配的有效参数 '" << key
					<< "' (请求类型 " << typeid(T).name() << ")。层级查找失败 (或本地参数集未初始化)。"; // 提示可能原因
				std::string error_msg = ss.str();
				LOG_ERROR(error_msg); // 记录错误日志
				throw DroneException(error_msg, ErrorCode::ParameterNotFound); // 抛出自定义异常，使用明确错误码
			}
		}

		template <typename T>
		inline T BaseObject::getParamOrDefault(const std::string& key, T default_value) const {
			// 使用 getEffectiveParam 进行层级查找，如果找不到则返回提供的 default_value
			// 使用 value_or 和移动语义优化
			auto val_opt = getEffectiveParam<T>(key);
			if (val_opt) {
				return *val_opt;
			}
			else {
				// 如果未找到，记录一个 TRACE 级别的日志说明使用了传入的默认值
				LOG_TRACE("对象 [{} ID:{}]: 参数 '{}' 未找到或本地参数未初始化，使用传入的默认值。", name_, id_, key); // 提示可能原因
				return std::move(default_value);
			}
		}

		template <typename T>
		inline bool BaseObject::setLocalParamOverride(const std::string& key, T value) {
			// 获取环境中的参数注册表引用，用于验证
			auto env = getEnvironment();
			if (!env) {
				LOG_ERROR("对象 [{} ID:{}]: Environment 实例不存在，无法设置参数覆盖 '{}'。", name_, id_, key);
				return false;
			}
			const NSParams::ParamRegistry& registry = env->getParamRegistry();

			if (!params_) { // 检查共享指针是否有效
				LOG_ERROR("对象 [{} ID:{}]: 尝试在未初始化的本地参数集上设置参数覆盖 '{}'。操作失败。", name_, id_, key);
				return false;
			}

			// 调用 ParamValues 的 setValue 方法，传入注册表进行验证
			bool success = params_->setValue<T>(key, std::move(value), registry); // 解引用共享指针
			if (!success) {
				// setValue 内部应已记录具体错误
				LOG_WARN("对象 [{} ID:{}]: 设置本地参数覆盖 '{}' 失败 (类型或约束不匹配?)。", name_, id_, key);
			}
			else {
				LOG_DEBUG("对象 [{} ID:{}]: 成功设置本地参数覆盖 '{}'。", name_, id_, key);
				// **注意:** 修改本地参数本身通常不直接触发环境索引更新。
				// 如果参数变化影响了对象的索引相关属性（如形状、状态），需要手动调用相应 setter 或 updateDynamicState。
			}
			return success; // 返回设置操作的结果
		}

	} // namespace NSCore
} // namespace NSDrones 