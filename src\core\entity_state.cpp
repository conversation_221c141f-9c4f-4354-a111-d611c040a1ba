// src/core/entity_state.cpp
#include "core/entity_state.h"
#include "uav/uav_types.h"

namespace NSDrones {
	namespace NSCore {
		//EntityState::EntityState(const NSDrones::NSUav::UavState& uav_s)
		//		: position(uav_s.position),
		//		orientation(uav_s.orientation),
		//		velocity(uav_s.velocity),
		//		angular_velocity(uav_s.angular_velocity),
		//		acceleration(uav_s.acceleration),
		//		angular_acceleration(uav_s.angular_acceleration),
		//		energy_level(uav_s.current_energy),
		//		time_stamp(uav_s.time_stamp),
		//		status(uav_s.status),
		//		capabilities(uav_s.capabilities)
		//	{
		//		// LOG_TRACE("EntityState 从 UAVState (t={:.4f}) 构造完成。", uav_s.time_stamp);
		//	}

            bool EntityState::isEmpty() const {
				return position.isValid() && velocity_wgs84.allFinite() && acceleration.allFinite() &&
					orientation.coeffs().allFinite() &&
					angular_velocity.allFinite() && angular_acceleration.allFinite() &&
					std::isfinite(time_stamp) && std::isfinite(energy_level);
			}
	}
}
