// src/uav/energies/vtol_energies.cpp
#include "uav/energies/vtol_energies.h"
#include "uav/energies/multirotor_energies.h"
#include "uav/energies/fixedwing_energies.h"
#include "uav/uav.h"       
#include "utils/logging.h"         
#include <cmath>                  
#include <algorithm>               
#include <stdexcept>              

namespace NSDrones {
	namespace NSUav {

		/**
		 * @brief VtolEnergies 构造函数。
		 *        内部会创建悬停和固定翼子模型。
		 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
		 * @throws DroneException 如果内部模型创建失败。
		 */
		VtolEnergies::VtolEnergies(const Uav& owner)
			: IEnergyModel(owner) // 调用基类构造函数，传递 owner
		{
			LOG_DEBUG("开始创建 VTOL 内部能量模型，所有者: {}", owner_.getId());
			// 创建内部模型实例，将 owner 传递给它们的构造函数
			try {
				hover_model_ = std::make_shared<MultirotorEnergies>(owner_); // 传递 owner
				LOG_DEBUG("  悬停能量模型创建成功。");
			}
			catch (const std::exception& e) {
				throw DroneException("创建 VTOL 内部悬停能量模型失败: " + std::string(e.what()), ErrorCode::DependencyError);
			}
			try {
				fw_model_ = std::make_shared<FixedWingEnergies>(owner_); // 传递 owner
				LOG_DEBUG("  固定翼能量模型创建成功。");
			}
			catch (const std::exception& e) {
				throw DroneException("创建 VTOL 内部固定翼能量模型失败: " + std::string(e.what()), ErrorCode::DependencyError);
			}
			LOG_INFO("VTOL 能量模型已创建 (所有者 ID: {})。", owner_.getId());
		}

		/**
		 * @brief (私有) 获取当前状态对应的内部能量模型指针。
		 */
		const IEnergyModel* VtolEnergies::getCurrentModel(const UavState& state) const {
			LOG_TRACE("VTOL 获取当前能量模型 (所有者 ID: {})：模式={}", owner_.getId(), static_cast<int>(state.mode));
			// 检查内部模型指针是否有效
			if (!hover_model_ || !fw_model_) {
				LOG_ERROR("VTOL 获取当前能量模型失败：内部模型指针无效 (所有者 ID: {})。", owner_.getId());
				return nullptr; // 返回空指针表示无效
			}

			switch (state.mode) {
			case FlightMode::HOVER:
				LOG_TRACE("  模式为 HOVER，返回悬停能量模型。");
				return hover_model_.get();
			case FlightMode::FIXED_WING:
				LOG_TRACE("  模式为 FIXED_WING，返回固定翼能量模型。");
				return fw_model_.get();
			case FlightMode::TRANSITION:
				// 过渡模式下，返回 nullptr，由调用者处理混合计算
				LOG_TRACE("  模式为 TRANSITION，返回 nullptr (由调用者处理混合)。");
				return nullptr;
			case FlightMode::UNKNOWN:
			default:
				LOG_WARN("VTOL 能量模型遇到未知飞行模式 ({})，返回悬停模型作为默认 (所有者 ID: {})。", static_cast<int>(state.mode), owner_.getId());
				return hover_model_.get(); // 默认返回悬停模型
			}
		}

		/**
		 * @brief 计算 VTOL 能量消耗。
		 */
		double VtolEnergies::computeEnergyConsumption(const UavState& state, Time dt) const {
			LOG_TRACE("计算 VTOL 能量消耗 (所有者 ID: {})：模式={}, dt={:.4f}s",
				owner_.getId(), static_cast<int>(state.mode), dt);
			// 检查内部模型指针
			if (!hover_model_ || !fw_model_) {
				LOG_ERROR("计算 VTOL 能量消耗失败：内部模型无效 (所有者 ID: {})。", owner_.getId());
				return 0.0;
			}

			if (state.mode == FlightMode::TRANSITION) {
				// 过渡模式：使用参数定义的平均功率
				LOG_TRACE("  计算过渡模式能量消耗...");
				double avg_power = owner_.getParamOrDefault<double>("energy.vtol.transition_avg_power", 300.0); // 获取平均功率参数
				double efficiency = owner_.getParamOrDefault<double>("energy.base.discharging_efficiency", 0.95); // 获取效率
				efficiency = std::clamp(efficiency, Constants::EPSILON, 1.0); // 限制效率范围
				if (efficiency <= Constants::EPSILON) {
					LOG_WARN_ONCE("VTOL 过渡能量计算：放电效率 ({:.3f}) 无效，将使用 1.0。", efficiency);
					efficiency = 1.0; // 避免除零
				}
				double energy_joules = (avg_power * dt) / efficiency; // 计算焦耳
				double energy_wh = energy_joules / 3600.0; // 转换为 Wh
				LOG_TRACE("    过渡平均功率={:.1f}W, 效率={:.3f}, 消耗={:.6f}Wh", avg_power, efficiency, energy_wh);
				return energy_wh;
			}

			const IEnergyModel* model = getCurrentModel(state); // 获取当前模式对应的模型
			if (!model) {
				// 如果 getCurrentModel 返回 nullptr (例如 TRANSITION 模式下未实现混合逻辑)
				LOG_ERROR("无法获取当前模式 ({}) 的能量模型，能量消耗计算失败。", static_cast<int>(state.mode));
				return 0.0; // 返回 0 或抛出异常
			}

			double energy_wh = model->computeEnergyConsumption(state, dt); // 委托给子模型计算
			LOG_TRACE("  委托给 {} 模型，消耗={:.6f}Wh", (state.mode == FlightMode::HOVER ? "悬停" : "固定翼"), energy_wh);
			return energy_wh;
		}

		/**
		 * @brief 估算 VTOL 剩余续航时间。
		 */
		Time VtolEnergies::estimateEnduranceTime(const UavState& current_state, double current_energy) const {
			LOG_TRACE("估算 VTOL 剩余续航时间 (所有者 ID: {})：模式={}, 能量={:.2f}Wh",
				owner_.getId(), static_cast<int>(current_state.mode), current_energy);
			// 检查内部模型指针
			if (!hover_model_ || !fw_model_) {
				LOG_ERROR("估算 VTOL 剩余续航失败：内部模型无效 (所有者 ID: {})。", owner_.getId());
				return 0.0;
			}

			if (current_state.mode == FlightMode::TRANSITION) {
				LOG_WARN("VTOL 过渡模式下的续航估算暂未精确实现，将返回 0。");
				return 0.0; // 简化返回 0
			}

			const IEnergyModel* model = getCurrentModel(current_state); // 获取当前模式模型
			if (!model) {
				LOG_ERROR("无法获取当前模式 ({}) 的能量模型，续航估算失败。", static_cast<int>(current_state.mode));
				return 0.0;
			}
			Time endurance = model->estimateEnduranceTime(current_state, current_energy); // 委托计算
			LOG_TRACE("  委托给 {} 模型，估算续航={:.1f}s", (current_state.mode == FlightMode::HOVER ? "悬停" : "固定翼"), endurance);
			return endurance;
		}

	} // namespace NSUav
} // namespace NSDrones