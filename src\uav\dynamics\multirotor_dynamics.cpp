// src/uav/dynamics/multirotor_dynamics.cpp
#include "uav/dynamics/multirotor_dynamics.h"
#include "uav/uav.h"        
#include "utils/logging.h"           
#include <cmath>                    
#include <limits>                   
#include <algorithm>                

namespace NSDrones {
	namespace NSUav {

		// --- 构造函数 ---
		/**
		 * @brief MultirotorDynamics 构造函数。
		 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
		 */
		MultirotorDynamics::MultirotorDynamics(const Uav& owner)
			: IDynamicModel(owner) // 调用基类构造函数，传递 owner
		{
			LOG_DEBUG("多旋翼动力学模型已创建，所有者: {}", owner_.getId());
		}


		// --- 多旋翼特定行为实现 ---

		/**
		 * @brief 获取最大转弯率 (多旋翼理论上可以瞬时改变航向)。
		 */
		double MultirotorDynamics::getMaxTurnRate(const UavState& state) const {
			// 对于多旋翼，假设可以瞬时改变航向，返回无穷大
			LOG_TRACE("获取多旋翼最大转弯率：返回无穷大。");
			return Constants::INF;
		}

		/**
		 * @brief 获取最小转弯半径 (多旋翼可以原地转弯)。
		 */
		double MultirotorDynamics::getMinTurnRadius(const UavState& state) const {
			// 多旋翼可以原地转弯，最小半径为 0
			LOG_TRACE("获取多旋翼最小转弯半径：返回 0。");
			return 0.0;
		}

		/**
		 * @brief 获取最大允许的“倾斜角”。
		 *        对于多旋翼，这通常指姿态角限制。从参数 "dynamics.mr.max_attitude_angle_deg" 获取。
		 */
		double MultirotorDynamics::getMaxBankAngle(const UavState& state) const {
			// 从 owner_ 获取参数，如果找不到则使用默认值 45 度
			// 键名示例: "dynamics.mr.max_attitude_angle_deg"
			double max_angle_deg = owner_.getParamOrDefault<double>("dynamics.mr.max_attitude_angle_deg", 45.0);
			double angle_rad = max_angle_deg * Constants::DEG_TO_RAD; // 转换为弧度
			LOG_TRACE("获取多旋翼最大姿态角限制 (所有者 ID: {}): {:.2f} 度 ({:.3f} rad)", owner_.getId(), max_angle_deg, angle_rad);
			// 返回弧度值，限制在 [0, pi/2)
			return std::clamp(angle_rad, 0.0, Constants::HALF_PI - Constants::ANGLE_EPSILON);
		}

		// --- isStateTransitionFeasible 实现 ---
		/**
		 * @brief 检查多旋翼状态转换是否可行。
		 */
		bool MultirotorDynamics::isStateTransitionFeasible(const UavState& current, const UavState& next, Time dt) const {
			LOG_TRACE("检查多旋翼状态转换可行性 (所有者 ID: {}): dt={:.4f}s", owner_.getId(), dt);
			// 检查时间步长
			if (dt <= Constants::TIME_EPSILON) {
				// 转换为ECEF坐标进行几何计算
				EcefPoint current_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(current.position);
				EcefPoint next_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(next.position);
				Vector3D pos_diff = next_ecef - current_ecef;
				// 如果时间步长为零或负，只有当位置不变时才可行
				bool position_changed = pos_diff.norm() > Constants::GEOMETRY_EPSILON;
				if (position_changed) {
					LOG_TRACE("  失败：时间步长 <= 0 ({:.4f}) 但位置改变。", dt);
					return false;
				}
				else {
					LOG_TRACE("  通过：时间步长 <= 0 且位置未变。");
					return true;
				}
			}

			// --- 1. 检查高度和速度限制 (使用当前模型的 getter 方法获取限制) ---
			if (!checkAltitudeLimit(next.position, getMaxAltitude(next))) {
				LOG_TRACE("  失败：下一状态高度 ({:.1f}) 超出最大限制 ({:.1f})。", next.position.altitude, getMaxAltitude(next));
				return false;
			}
			// 多旋翼最小运行速度通常为 0
			if (!checkSpeedLimit(next.velocity,
				getMaxHorizontalSpeed(next),
				getMaxClimbSpeed(next),
				getMaxDescendSpeed(next),
				getMinOperationalSpeed(next))) { // getMinOperationalSpeed 返回 0
				LOG_TRACE("  失败：下一状态速度限制检查失败。");
				return false;
			}
			LOG_TRACE("  基本限制检查（高度、速度）通过。");

			// --- 2. 检查加速度/减速度限制 ---
			Vector3D requiredAcc = (next.velocity - current.velocity) / dt; // 计算所需平均加速度
			double hAccMag = requiredAcc.head<2>().norm(); // 水平加速度大小
			double vAcc = requiredAcc.z();                 // 垂直加速度

			// 检查水平加速度/减速度 (多旋翼通常对称，使用同一个限制)
			double maxHAcc = getMaxHorizontalAcceleration(current); // 获取限制值
			// 检查模长是否超过限制 (使用容差比较)
			if (hAccMag > maxHAcc + Constants::VELOCITY_EPSILON) {
				LOG_TRACE("  失败：水平加速度检查失败 (需要 {:.2f} > 限制 {:.2f})。", hAccMag, maxHAcc);
				return false;
			}
			LOG_TRACE("  水平加速度检查通过 (需要 {:.2f} <= {:.2f})。", hAccMag, maxHAcc);

			// 检查垂直加速度/减速度
			double maxVAcc = getMaxVerticalAcceleration(current);   // 获取向上加速度限制
			double maxVDecel = getMaxVerticalDeceleration(current); // 获取向下加速度（减速）限制
			if (vAcc > Constants::EPSILON) { // 如果是向上加速或向下减速
				if (vAcc > maxVAcc + Constants::VELOCITY_EPSILON) { // 检查是否超过向上加速限制
					LOG_TRACE("  失败：垂直向上加速度/向下减速检查失败 (需要 {:.2f} > 限制 {:.2f})。", vAcc, maxVAcc);
					return false;
				}
			}
			else if (vAcc < -Constants::EPSILON) { // 如果是向下加速或向上减速
				if (std::abs(vAcc) > maxVDecel + Constants::VELOCITY_EPSILON) { // 检查绝对值是否超过减速度限制
					LOG_TRACE("  失败：垂直向下加速度/向上减速检查失败 (需要 {:.2f} > 限制 {:.2f})。", std::abs(vAcc), maxVDecel);
					return false;
				}
			} // 垂直加速度接近零则跳过
			LOG_TRACE("  垂直加速度/减速度检查通过。");

			// --- 3. 转弯约束 ---
			// 多旋翼通常无限制，基类检查会通过
			// double max_turn_rate = getMaxTurnRate(current); // = INF
			// double min_turn_radius = getMinTurnRadius(current); // = 0
			// if (!checkTurnConstraint(current.velocity, next.velocity, dt, max_turn_rate, min_turn_radius)) {
			//     LOG_ERROR("多旋翼转弯约束检查失败 (理论上不应发生)。"); // 这不应发生
			//     return false;
			// }
			LOG_TRACE("  转弯约束检查通过 (多旋翼无限制)。");

			LOG_TRACE("多旋翼状态转换可行性检查通过 (所有者 ID: {})。", owner_.getId());
			return true; // 所有检查通过
		}


		// --- 力学计算实现 (简化占位符) ---
		/**
		 * @brief 计算多旋翼升力 (简化)。
		 *        升力视为电机产生的总推力在世界坐标系下的垂直分量。
		 *        简化模型：升力主要用于平衡重力加垂直方向的净力 (m*a_z)。
		 */
		Vector3D MultirotorDynamics::computeLiftForce(const UavState& state, double air_density) const {
			// 需要质量信息，从 owner_ 获取物理参数
			// 键名示例: "physical.empty_weight"
			double empty_weight = owner_.getParamOrDefault<double>("physical.empty_weight", 1.5); // kg
			double payload_weight = owner_.getPayloadWeight(); // 获取当前载荷
			double total_mass = empty_weight + payload_weight; // 总质量

			// 计算所需垂直力 F_z_net = m * a_z
			double required_vertical_net_force = total_mass * state.acceleration.z();
			// 升力需要克服重力并提供垂直方向的净力: Lift = m*g + F_z_net = m * (g + a_z)
			double lift_magnitude = total_mass * (Constants::GRAVITY + state.acceleration.z());

			LOG_TRACE("计算多旋翼升力(简化)：总质量={:.2f}kg, g={:.2f}, a_z={:.2f}, 升力大小≈{:.2f}N (垂直向上)",
				total_mass, Constants::GRAVITY, state.acceleration.z(), lift_magnitude);

			// 返回一个仅包含垂直分量的力，确保非负
			return Vector3D(0, 0, std::max(0.0, lift_magnitude));
		}

		/**
		 * @brief 计算多旋翼空气阻力 (简化)。
		 */
		Vector3D MultirotorDynamics::computeDragForce(const UavState& state, double air_density) const {
			// 简化阻力模型: F_drag = - k * ||V|| * V (k 为等效阻力系数)
			// 从 owner_ 获取参数
			// 键名示例: "dynamics.mr.drag_coeff"
			double drag_coeff_k = owner_.getParamOrDefault<double>("dynamics.mr.drag_coeff", 0.1);

			double speed = state.velocity.norm(); // 获取速度大小
			if (speed < Constants::VELOCITY_EPSILON) {
				LOG_TRACE("计算多旋翼阻力：速度接近零，阻力为零。");
				return Vector3D::Zero(); // 速度为零则无阻力
			}

			Vector3D drag_force = -drag_coeff_k * speed * state.velocity; // 阻力与速度平方成正比，方向相反
			LOG_TRACE("计算多旋翼阻力(简化)：速度={:.2f}, 系数k={:.2f}, 阻力=({:.2f},{:.2f},{:.2f})",
				speed, drag_coeff_k, drag_force.x(), drag_force.y(), drag_force.z());
			return drag_force;
		}

		/**
		 * @brief 计算多旋翼所需的总推力向量 (简化)。
		 *        这个推力是电机需要产生的合力，通过倾斜机身来实现水平分量。
		 */
		Vector3D MultirotorDynamics::computeThrustForce(const UavState& state) const {
			// F_net = m * a = F_thrust + F_drag + F_gravity
			// 则 F_thrust = m * a - F_drag - F_gravity

			double empty_weight = owner_.getParamOrDefault<double>("physical.empty_weight", 1.5);
			double payload_weight = owner_.getPayloadWeight();
			double total_mass = empty_weight + payload_weight;

			// 空气密度简化：使用海平面密度
			double air_density = Constants::AIR_DENSITY_SEA_LEVEL_ISA;

			Vector3D drag = computeDragForce(state, air_density); // 计算阻力
			Vector3D gravity_force(0, 0, -total_mass * Constants::GRAVITY); // 计算重力
			Vector3D required_net_force = total_mass * state.acceleration; // 计算所需合力 m*a

			// 计算所需推力向量
			Vector3D thrust = required_net_force - drag - gravity_force;
			LOG_TRACE("计算多旋翼推力：m={:.2f}, a=({:.2f},{:.2f},{:.2f}), drag=({:.2f},{:.2f},{:.2f}), grav=({:.2f},{:.2f},{:.2f})",
				total_mass, state.acceleration.x(), state.acceleration.y(), state.acceleration.z(),
				drag.x(), drag.y(), drag.z(),
				gravity_force.x(), gravity_force.y(), gravity_force.z());
			LOG_TRACE("  所需推力向量=({:.2f},{:.2f},{:.2f})", thrust.x(), thrust.y(), thrust.z());

			// 实际推力通常受限于电机最大推力，并且方向与机体 Z 轴对齐（通过姿态控制实现合力）
			// 这里返回的是需要电机产生的总推力向量（在世界坐标系下），代表了合力的需求。
			// 确保推力至少能平衡重力（在垂直方向）？或者允许负推力（如果用于快速下降）？
			// 简化：不加限制，直接返回计算值
			return thrust;
		}


	} // namespace NSUav
} // namespace NSDrones