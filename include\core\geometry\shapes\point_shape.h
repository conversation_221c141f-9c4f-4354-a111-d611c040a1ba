#pragma once

#include "core/geometry/ishape.h"
#include <fcl/geometry/shape/sphere.h>

namespace NSDrones {
namespace NSCore {

    /**
     * @brief 点形状
     * 
     * 表示一个几何点，在FCL中用极小的球体实现。
     * 主要用于位置标记、传感器位置、目标点等。
     */
    class PointShape : public IShape {
    private:
        fcl::Vector3d position_;  // 点的位置
        static constexpr double POINT_RADIUS = 1e-6;  // 极小半径，用于FCL表示
        
        mutable std::shared_ptr<fcl::Sphered> fcl_sphere_;  // FCL球体对象（延迟创建）

    public:
        /**
         * @brief 构造函数
         * @param position 点的位置
         */
        explicit PointShape(const fcl::Vector3d& position = fcl::Vector3d::Zero());

        /**
         * @brief 拷贝构造函数
         */
        PointShape(const PointShape& other);

        /**
         * @brief 赋值操作符
         */
        PointShape& operator=(const PointShape& other);

        // IShape接口实现
        ShapeType getType() const override { return ShapeType::POINT; }
        std::shared_ptr<fcl::CollisionGeometryd> getFCLGeometry() const override;
        fcl::AABBd getAABB(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const override;
        double getVolume() const override { return 0.0; }  // 点的体积为0
        double getSurfaceArea() const override { return 0.0; }  // 点的表面积为0
        fcl::Vector3d getCentroid() const override { return position_; }
        fcl::Matrix3d getInertiaMatrix(double mass) const override;
        std::unique_ptr<IShape> clone() const override;
        nlohmann::json serialize() const override;
        bool deserialize(const nlohmann::json& json) override;
        std::string toString() const override;
        bool containsPoint(const fcl::Vector3d& point) const override;
        double distanceToPoint(const fcl::Vector3d& point) const override;
        double getCharacteristicSize() const override { return 0.0; }
        int getDimension() const override { return 0; }  // 点是0维

        // 点特有方法
        /**
         * @brief 获取点的位置
         * @return 点的位置向量
         */
        const fcl::Vector3d& getPosition() const { return position_; }

        /**
         * @brief 设置点的位置
         * @param position 新的位置
         */
        void setPosition(const fcl::Vector3d& position);

        /**
         * @brief 计算到另一个点的距离
         * @param other 另一个点
         * @return 距离
         */
        double distanceTo(const PointShape& other) const;

        /**
         * @brief 创建原点处的点
         * @return 点形状实例
         */
        static std::unique_ptr<PointShape> createOrigin();

    private:
        void ensureFCLObject() const;
    };

} // namespace NSCore
} // namespace NSDrones
