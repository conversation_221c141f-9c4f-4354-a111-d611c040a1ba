// include/uav/energies/vtol_energies.h
#pragma once

#include "uav/ienergy_model.h"    
#include <memory>                  

namespace NSDrones {
	namespace NSCore { class EntityObject; }
	namespace NSUav { class MultirotorEnergies; class FixedWingEnergies; }
}

namespace NSDrones {
	namespace NSUav {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class VtolEnergies
		 * @brief VTOL 无人机的能量消耗模型实现。
		 *        通过组合内部的多旋翼和固定翼模型实例来处理不同飞行模式。
		 *        依赖于 owner_ 引用访问不同模式下的参数。
		 */
		class VtolEnergies : public IEnergyModel {
		public:
			/**
			 * @brief 构造函数。
			 *        内部会创建悬停和固定翼子模型。
			 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
			 * @throws DroneException 如果内部模型创建失败。
			 */
			explicit VtolEnergies(const Uav& owner); // 接收 owner 引用

			// --- 禁止拷贝和移动 ---
			VtolEnergies(const VtolEnergies&) = delete;
			VtolEnergies& operator=(const VtolEnergies&) = delete;
			VtolEnergies(VtolEnergies&&) = delete;
			VtolEnergies& operator=(VtolEnergies&&) = delete;

			/** @brief 虚析构函数 */
			~VtolEnergies() override = default;

			// --- 覆写 IEnergyModel 虚函数 ---
			/**
			 * @brief 计算给定状态和时间步长下的能量消耗。
			 *        根据飞行模式委托或混合内部模型的计算。
			 *        参数查找基于 "energy.vtol.*", "energy.vtol.hover.*", "energy.vtol.fw.*"。
			 * @param state 无人机当前状态。
			 * @param dt 时间步长 (秒)。
			 * @return 消耗的能量 (单位与参数 "energy.base.max_capacity" 一致, 例如 Wh)。
			 */
			double computeEnergyConsumption(const UavState& state, Time dt) const override;

			/**
			 * @brief 估算在给定状态和当前能量下的剩余续航时间。
			 *        根据当前模式和能量水平估算。
			 * @param current_state 当前无人机状态。
			 * @param current_energy 当前剩余能量。
			 * @return 估算的剩余续航时间 (秒)。
			 */
			Time estimateEnduranceTime(const UavState& current_state, double current_energy) const override;

		private:
			std::shared_ptr<MultirotorEnergies> hover_model_; // 悬停模式模型
			std::shared_ptr<FixedWingEnergies> fw_model_;     // 固定翼模式模型
			/**
			 * @brief (私有) 获取当前状态对应的内部能量模型指针。
			 * @param state 当前无人机状态。
			 * @return 指向内部 IEnergyModel 的 const 指针，如果内部模型无效则返回 nullptr。
			 */
			const IEnergyModel* getCurrentModel(const UavState& state) const;
		};

	} // namespace NSUav
} // namespace NSDrones