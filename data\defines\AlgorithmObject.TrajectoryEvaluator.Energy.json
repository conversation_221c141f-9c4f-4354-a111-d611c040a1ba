{"description": "Parameters for Energy-based Trajectory Evaluator.", "parameters": [{"key": "calculation_fidelity", "type": "string", "name": "计算保真度", "description": "能量消耗计算的保真度级别。", "default": "SIMPLE", "enum_type": "NSDrones::NSPlanning::CalculationFidelityType", "required": false}, {"key": "simple_model_hover_w", "type": "double", "description": "[SIMPLE Fidelity] 固定的悬停功耗 (W)", "default": 150.0, "required": false, "condition": {"dependent_param_key": "calculation_fidelity", "required_value_type": "string", "required_value": "SIMPLE"}}, {"key": "simple_model_forward_coeff", "type": "double", "description": "[SIMPLE Fidelity] 固定的前飞功耗系数 (W/(m/s) 或 W/(m/s)^2)", "default": 0.5, "required": false, "condition": {"dependent_param_key": "calculation_fidelity", "required_value_type": "string", "required_value": "SIMPLE"}}]}