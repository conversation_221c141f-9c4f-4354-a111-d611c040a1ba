// include/algorithm/trajectory_optimizer/itrajectory_optimizer.h
#pragma once

#include "core/types.h"          
#include "planning/planning_types.h" 
#include "uav/idynamic_model.h"   
#include "mission/task_strategies.h"
#include <vector>
#include <map>
#include <string>
#include <memory>                   
#include <optional>
#include "nlohmann/json.hpp" // 包含完整头文件

namespace NSDrones { namespace NSParams { class ParamValues; } }

namespace NSDrones {
	namespace NSAlgorithm {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSPlanning;

		/**
		 * @class ITrajectoryOptimizer
		 * @brief 重构后的轨迹优化算法接口
		 *
		 * 使用统一数据结构，支持WGS84外部接口和ECEF内部计算
		 */
		class ITrajectoryOptimizer {
		public:
			/** @brief 默认构造函数 */
			explicit ITrajectoryOptimizer(){}

			/** @brief 虚析构函数。 */
			virtual ~ITrajectoryOptimizer() = default;

			// --- 禁止拷贝和移动 ---
			ITrajectoryOptimizer(const ITrajectoryOptimizer&) = delete;
			ITrajectoryOptimizer& operator=(const ITrajectoryOptimizer&) = delete;
			ITrajectoryOptimizer(ITrajectoryOptimizer&&) = delete;
			ITrajectoryOptimizer& operator=(ITrajectoryOptimizer&&) = delete;

		public:
			/**
			 * @brief 优化轨迹（使用统一数据结构）
			 * @param request 轨迹优化请求
			 * @return 轨迹优化结果
			 */
			virtual TrajectoryOptimizationResult optimize(const TrajectoryOptimizationRequest& request) = 0;

			/**
			 * @brief 检查轨迹可行性
			 * @param trajectory 轨迹
			 * @param dynamics 动力学模型
			 * @return 可行性检查结果
			 */
			virtual bool isFeasible(const Trajectory& trajectory, const NSUav::IDynamicModel& dynamics) const = 0;

			/**
			 * @brief 设置优化器参数
			 * @param parameters 参数映射
			 */
			virtual void setParameters(const std::map<std::string, double>& parameters) = 0;
		};

		using ITrajectoryOptimizerPtr = std::shared_ptr<ITrajectoryOptimizer>;
		using ConstITrajectoryOptimizerPtr = std::shared_ptr<const ITrajectoryOptimizer>;

	} // namespace NSAlgorithm
} // namespace NSDrones