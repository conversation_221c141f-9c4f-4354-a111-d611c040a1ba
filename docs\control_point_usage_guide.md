# 控制点系统使用指南

## 概述

控制点系统是任务规划的核心组件，用于定义无人机在执行任务过程中需要到达的关键位置及其约束条件。每个控制点可以包含位置、高度、速度、姿态、停留时间和载荷动作等多种约束。

## 核心组件

### 1. ControlPoint 结构

控制点是任务执行的基本单元，包含以下主要属性：

- **位置信息**：WGS84坐标、高度类型、高度值
- **约束条件**：速度、姿态、时间约束
- **载荷动作**：拍照、投放、扫描等操作
- **容差设置**：位置、高度、航向容差
- **元数据**：名称、描述、自定义属性

### 2. PayloadAction 系统

载荷动作系统支持多种类型的载荷操作：

- **PhotoAction**：拍照动作
- **DropAction**：投放动作  
- **ScanAction**：扫描动作
- **自定义动作**：可扩展的动作接口

## 基本使用方法

### 创建简单控制点

```cpp
#include "mission/control_point.h"

using namespace NSDrones::NSMission;

// 1. 创建基本航路点
WGS84Point position{116.4074, 39.9042, 100.0}; // 经度、纬度、高度
ControlPoint waypoint = createWaypoint(position, 120.0, "航路点1");

// 2. 使用构造函数创建
ControlPoint cp(position, ControlPointType::WAYPOINT_MUST_PASS, 120.0, 
               AltitudeType::ABOVE_GROUND_LEVEL, "航路点2");

// 3. 链式配置
ControlPoint advanced_cp(position)
    .setName("高级航路点")
    .setRequiredHeading(90.0, 5.0)  // 航向90度，容差5度
    .setSpeedLimits(5.0, 15.0)      // 速度范围5-15m/s
    .setLoiterTime(10.0)            // 停留10秒
    .setPositionTolerance(2.0);     // 位置容差2米
```

### 创建带载荷动作的控制点

```cpp
// 1. 使用工厂方法创建拍照点
ControlPoint photo_point = createPhotoPoint(
    WGS84Point{116.4074, 39.9042, 100.0},
    "camera_main",  // 相机ID
    3,              // 拍照数量
    "拍照点1"       // 点名称
);

// 2. 手动配置载荷动作
ControlPoint manual_photo_point(position);
auto photo_action = createPhotoAction("camera_main", 5, 2.0); // 5张照片，间隔2秒
manual_photo_point.setPayloadAction(photo_action)
                  .setLoiterTime(photo_action->getEstimatedDuration());

// 3. 使用载荷动作命令
PayloadActionCommand photo_cmd("TakePhoto", {{"count", 3}, {"quality", "high"}});
ControlPoint cmd_photo_point = createActionPoint(position, photo_cmd, 8.0, "命令拍照点");
```

### 创建复杂约束的控制点

```cpp
// 创建具有多种约束的控制点
ControlPoint complex_point(WGS84Point{116.4074, 39.9042, 150.0})
    .setName("复杂约束点")
    .setDescription("具有多种约束条件的控制点")
    
    // 速度约束
    .setRequiredSpeed(Vector3D{10.0, 0.0, 0.0})  // 期望速度向量
    .setSpeedLimits(8.0, 12.0)                   // 速度范围
    
    // 姿态约束
    .setRequiredHeading(45.0, 3.0)               // 航向45度，容差3度
    
    // 时间约束
    .setLoiterTime(15.0)                         // 停留15秒
    .setArrivalTime(120.0)                       // 期望120秒后到达
    .setDepartureTime(140.0)                     // 期望140秒后离开
    
    // 容差设置
    .setPositionTolerance(1.5)                   // 位置容差1.5米
    .setAltitudeTolerance(3.0)                   // 高度容差3米
    
    // 载荷动作
    .addActionCommand(PayloadActionCommand("TakePhoto", {{"count", 1}}))
    .addActionCommand(PayloadActionCommand("StartRecording", {{"duration", 10}}))
    
    // 元数据
    .addMetadata("priority", "high")
    .addMetadata("weather_dependent", "true");
```

## 控制点列表操作

### 创建和管理控制点列表

```cpp
// 创建控制点列表
ControlPointList waypoints;

// 添加多个控制点
waypoints.push_back(createWaypoint({116.4074, 39.9042, 100.0}, 100.0, "起始点"));
waypoints.push_back(createPhotoPoint({116.4080, 39.9045, 120.0}, "camera1", 2, "拍照点"));
waypoints.push_back(createScanPoint({116.4085, 39.9048, 110.0}, "lidar1", 8.0, "normal", "扫描点"));
waypoints.push_back(createWaypoint({116.4090, 39.9050, 100.0}, 100.0, "结束点"));

// 验证控制点列表
if (validateControlPointList(waypoints)) {
    LOG_INFO("控制点列表验证通过，共 {} 个点", waypoints.size());
    
    // 计算路径信息
    double path_length = calculatePathLength(waypoints);
    double flight_time = calculateEstimatedFlightTime(waypoints, 12.0); // 平均速度12m/s
    
    LOG_INFO("路径长度: {:.1f}m, 预计飞行时间: {:.1f}s", path_length, flight_time);
} else {
    LOG_ERROR("控制点列表验证失败");
}
```

### 过滤和查询控制点

```cpp
// 查找特定名称的控制点
const ControlPoint* photo_point = findControlPointByName(waypoints, "拍照点");
if (photo_point) {
    LOG_INFO("找到拍照点: {}", photo_point->getDetailedDescription());
}

// 过滤有载荷动作的控制点
ControlPointList action_points = filterPointsWithPayloadActions(waypoints);
LOG_INFO("包含载荷动作的控制点数量: {}", action_points.size());

// 按类型过滤控制点
ControlPointList must_pass_points = filterPointsByType(waypoints, 
                                                       ControlPointType::WAYPOINT_MUST_PASS);
LOG_INFO("必经点数量: {}", must_pass_points.size());
```

## 载荷动作详细使用

### 拍照动作

```cpp
// 创建拍照动作
auto photo_action = createPhotoAction("camera_main", 5, 1.5)
    ->addCameraSetting("iso", 200)
    ->addCameraSetting("exposure", "auto")
    ->addCameraSetting("focus", "infinity");

// 获取动作信息
LOG_INFO("动作描述: {}", photo_action->getDescription());
LOG_INFO("预计时长: {:.1f}s", photo_action->getEstimatedDuration());

// 转换为命令
auto cmd = photo_action->getCommand();
if (cmd.has_value()) {
    LOG_INFO("载荷命令: {}", cmd->getDescription());
}
```

### 投放动作

```cpp
// 创建投放动作
auto drop_action = createDropAction("dropper_main", 3, 2.0, "emergency_kit");

// 检查载荷类型要求
if (drop_action->requiresPayloadType("Dropper")) {
    LOG_INFO("需要投放器载荷");
}
```

### 扫描动作

```cpp
// 创建扫描动作
auto scan_action = createScanAction("lidar_main", 10.0, "high_resolution", 0.1);

// 获取扫描命令
auto scan_cmd = scan_action->getCommand();
if (scan_cmd.has_value()) {
    LOG_INFO("扫描命令: {}", scan_cmd->getDescription());
    LOG_INFO("超时时间: {:.1f}s", scan_cmd->timeout_seconds);
}
```

## 运行时检查和验证

### 到达条件检查

```cpp
// 检查是否到达控制点
WGS84Point current_position{116.4074, 39.9042, 101.5};
double current_heading = 88.0; // 当前航向

if (waypoint.isArrivalConditionMet(current_position, current_heading)) {
    LOG_INFO("已到达控制点: {}", waypoint.name);
    
    // 执行载荷动作
    if (waypoint.hasAnyPayloadAction()) {
        LOG_INFO("开始执行载荷动作，预计时长: {:.1f}s", 
                waypoint.getEstimatedDwellTime());
    }
} else {
    LOG_INFO("尚未到达控制点，继续飞行");
}
```

### 控制点验证

```cpp
// 验证单个控制点
if (waypoint.isValid()) {
    LOG_INFO("控制点有效: {}", waypoint.getDetailedDescription());
} else {
    LOG_ERROR("控制点无效，请检查约束条件");
}

// 检查各种约束
if (waypoint.hasAnySpeedConstraint()) {
    Vector3D required_speed = waypoint.getRequiredSpeedOrZero();
    LOG_INFO("速度约束: ({:.1f}, {:.1f}, {:.1f}) m/s", 
            required_speed.x(), required_speed.y(), required_speed.z());
}

if (waypoint.hasAnyTimeConstraint()) {
    LOG_INFO("时间约束: 停留{:.1f}s", waypoint.required_loiter_time);
}
```

## 最佳实践

### 1. 控制点命名规范

```cpp
// 使用有意义的名称
ControlPoint takeoff_point = createWaypoint(position, 50.0, "起飞点");
ControlPoint survey_start = createWaypoint(position, 100.0, "勘察起始点");
ControlPoint photo_point_01 = createPhotoPoint(position, "camera1", 1, "拍照点_01");
```

### 2. 合理设置容差

```cpp
// 根据任务精度要求设置容差
ControlPoint precision_point(position)
    .setPositionTolerance(0.5)    // 高精度任务：0.5米
    .setAltitudeTolerance(1.0)    // 高度容差：1米
    .setRequiredHeading(0.0, 2.0); // 航向容差：2度

ControlPoint normal_point(position)
    .setPositionTolerance(3.0)    // 普通任务：3米
    .setAltitudeTolerance(5.0);   // 高度容差：5米
```

### 3. 载荷动作组合

```cpp
// 组合多个载荷动作
ControlPoint multi_action_point(position)
    .setLoiterTime(20.0)
    .addActionCommand(PayloadActionCommand("TakePhoto", {{"count", 3}}))
    .addActionCommand(PayloadActionCommand("StartRecording", {{"duration", 15}}))
    .addActionCommand(PayloadActionCommand("CollectSample", {{"type", "air"}}));
```

控制点系统提供了灵活而强大的任务定义能力，通过合理使用各种约束和载荷动作，可以实现复杂的无人机任务规划。
