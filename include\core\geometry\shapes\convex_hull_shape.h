#pragma once

#include "core/geometry/ishape.h"
#include <fcl/geometry/shape/convex.h>
#include <vector>

namespace NSDrones {
namespace NSCore {

    /**
     * @brief 凸包形状
     * 
     * 表示由一组点构成的凸包。
     * 凸包是包含所有给定点的最小凸多面体。
     */
    class ConvexHullShape : public IShape {
    private:
        std::vector<fcl::Vector3d> vertices_;  // 顶点列表
        std::vector<int> faces_;               // 面索引列表（三角形）
        
        mutable std::shared_ptr<fcl::Convexd> fcl_convex_;  // FCL几何对象（延迟创建）
        mutable bool geometry_dirty_;  // 几何是否需要更新

    public:
        /**
         * @brief 构造函数
         * @param vertices 顶点列表
         */
        explicit ConvexHullShape(const std::vector<fcl::Vector3d>& vertices);

        /**
         * @brief 默认构造函数（空凸包）
         */
        ConvexHullShape();

        /**
         * @brief 拷贝构造函数
         */
        ConvexHullShape(const ConvexHullShape& other);

        /**
         * @brief 赋值操作符
         */
        ConvexHullShape& operator=(const ConvexHullShape& other);

        // IShape接口实现
        ShapeType getType() const override { return ShapeType::CONVEX_HULL; }
        int getDimension() const override { return 3; }  // 3D实体
        std::shared_ptr<fcl::CollisionGeometryd> getFCLGeometry() const override;
        fcl::AABBd getAABB(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const override;
        double getVolume() const override;
        double getSurfaceArea() const override;
        fcl::Vector3d getCentroid() const override;
        fcl::Matrix3d getInertiaMatrix(double mass) const override;
        std::unique_ptr<IShape> clone() const override;
        nlohmann::json serialize() const override;
        bool deserialize(const nlohmann::json& json) override;
        std::string toString() const override;
        bool containsPoint(const fcl::Vector3d& point) const override;
        double distanceToPoint(const fcl::Vector3d& point) const override;
        double getCharacteristicSize() const override;

        // ConvexHullShape特有方法
        /**
         * @brief 获取顶点数量
         */
        size_t getVertexCount() const { return vertices_.size(); }

        /**
         * @brief 获取面数量
         */
        size_t getFaceCount() const { return faces_.size() / 3; }

        /**
         * @brief 获取顶点列表
         */
        const std::vector<fcl::Vector3d>& getVertices() const { return vertices_; }

        /**
         * @brief 获取面索引列表
         */
        const std::vector<int>& getFaces() const { return faces_; }

        /**
         * @brief 获取指定索引的顶点
         * @param index 顶点索引
         * @return 顶点坐标
         */
        const fcl::Vector3d& getVertex(size_t index) const;

        /**
         * @brief 添加顶点
         * @param vertex 顶点坐标
         */
        void addVertex(const fcl::Vector3d& vertex);

        /**
         * @brief 添加多个顶点
         * @param vertices 顶点列表
         */
        void addVertices(const std::vector<fcl::Vector3d>& vertices);

        /**
         * @brief 设置顶点列表
         * @param vertices 新的顶点列表
         */
        void setVertices(const std::vector<fcl::Vector3d>& vertices);

        /**
         * @brief 清空所有顶点
         */
        void clear();

        /**
         * @brief 检查是否为空
         */
        bool isEmpty() const { return vertices_.empty(); }

        /**
         * @brief 计算凸包（从当前顶点重新计算）
         */
        void computeConvexHull();

        /**
         * @brief 简化凸包（减少顶点数量）
         * @param tolerance 简化容差
         */
        void simplify(double tolerance = 1e-6);

        /**
         * @brief 获取边界盒的8个顶点
         * @param transform 变换矩阵
         * @return 顶点列表
         */
        std::vector<fcl::Vector3d> getBoundingBoxVertices(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 获取所有边
         * @return 边列表（每条边用两个顶点索引表示）
         */
        std::vector<std::pair<size_t, size_t>> getEdges() const;

        /**
         * @brief 获取变换后的顶点
         * @param transform 变换矩阵
         * @return 变换后的顶点列表
         */
        std::vector<fcl::Vector3d> getTransformedVertices(const fcl::Transform3d& transform) const;

        /**
         * @brief 创建立方体凸包
         * @param size 立方体边长
         * @return ConvexHullShape实例
         */
        static std::unique_ptr<ConvexHullShape> createCube(double size);

        /**
         * @brief 创建长方体凸包
         * @param length X轴长度
         * @param width Y轴宽度
         * @param height Z轴高度
         * @return ConvexHullShape实例
         */
        static std::unique_ptr<ConvexHullShape> createBox(double length, double width, double height);

        /**
         * @brief 创建四面体凸包
         * @param size 四面体边长
         * @return ConvexHullShape实例
         */
        static std::unique_ptr<ConvexHullShape> createTetrahedron(double size);

        /**
         * @brief 从点云创建凸包
         * @param points 点云
         * @return ConvexHullShape实例
         */
        static std::unique_ptr<ConvexHullShape> createFromPointCloud(const std::vector<fcl::Vector3d>& points);

        /**
         * @brief 创建正多面体凸包
         * @param type 多面体类型（4=四面体，6=立方体，8=八面体，12=十二面体，20=二十面体）
         * @param size 尺寸
         * @return ConvexHullShape实例
         */
        static std::unique_ptr<ConvexHullShape> createRegularPolyhedron(int type, double size);

    private:
        /**
         * @brief 确保FCL对象已创建
         */
        void ensureFCLObject() const;

        /**
         * @brief 标记几何需要更新
         */
        void markGeometryDirty() { geometry_dirty_ = true; fcl_convex_.reset(); }

        /**
         * @brief 验证顶点数据
         */
        void validateVertices() const;

        /**
         * @brief 计算面索引（三角剖分）
         */
        void computeFaces();

        /**
         * @brief 验证索引有效性
         */
        void validateIndex(size_t index) const;
    };

} // namespace NSCore
} // namespace NSDrones
