// src/planning/task_planners/task_planner_followpath.cpp
#include "planning/task_planners/followpath_taskplanner.h"
#include "planning/planning_types.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/task_strategies.h"
#include "uav/uav.h"
#include "environment/environment.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"
#include "core/types.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include <memory>
#include <algorithm>
#include <numeric>
#include <vector>
#include <map>
#include <string>
#include <utility>

namespace NSDrones {
	namespace NSPlanning {

		/** @brief 构造函数 */
		FollowPathTaskPlanner::FollowPathTaskPlanner()
			: ITaskPlanner() {}

		// 初始化方法实现
		bool FollowPathTaskPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) {
			LOG_DEBUG("[FollowPathTaskPlanner] 开始初始化路径跟随任务规划器");

			if (params) {
				// 加载路径跟随特定参数
				follow_speed_ = params->getValueOrDefault<double>("follow.speed", 15.0);
				path_tolerance_ = params->getValueOrDefault<double>("follow.path_tolerance", 2.0);
				waypoint_tolerance_ = params->getValueOrDefault<double>("follow.waypoint_tolerance", 1.0);
				smooth_path_ = params->getValueOrDefault<bool>("follow.smooth_path", true);

				LOG_INFO("[FollowPathTaskPlanner] 加载参数: 跟随速度={:.1f}m/s, 路径容差={:.1f}m, 航点容差={:.1f}m, 平滑路径={}",
						follow_speed_, path_tolerance_, waypoint_tolerance_, smooth_path_ ? "是" : "否");
			}

			// 从原始配置中加载额外参数
			if (!raw_config.empty()) {
				if (raw_config.contains("interpolation_method")) {
					interpolation_method_ = raw_config["interpolation_method"].get<std::string>();
					LOG_DEBUG("[FollowPathTaskPlanner] 插值方法: {}", interpolation_method_);
				}
			}

			LOG_DEBUG("[FollowPathTaskPlanner] 初始化完成");
			return true;
		}

		/**
		 * @brief 规划单机路径跟随任务（重构后的接口）
		 */
		SingleTaskPlanningResult FollowPathTaskPlanner::planSingleTask(
			const SingleTaskPlanningRequest& request) {

			LOG_INFO("[FollowPathTaskPlanner] 开始规划子任务 [{}]，UAV [{}]",
				request.assignment.sub_task_id, request.assignment.assigned_uav_id);

			SingleTaskPlanningResult result;
			result.success = false;
			result.sub_task_id = request.assignment.sub_task_id;
			result.uav_id = request.assignment.assigned_uav_id;

			try {
				// === 第1步：验证输入 ===
				if (!request.uav) {
					result.message = "无人机指针无效";
					LOG_ERROR("[FollowPathTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				if (!request.original_task) {
					result.message = "原始任务指针无效";
					LOG_ERROR("[FollowPathTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// 验证任务类型
				if (request.original_task->getType() != NSCore::TaskType::FOLLOW_PATH) {
					result.message = "任务类型不匹配，期望 FOLLOW_PATH，实际 " +
						NSUtils::enumToString(request.original_task->getType());
					LOG_ERROR("[FollowPathTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第2步：获取任务参数 ===
				auto params_ptr = request.original_task->getTaskParameters<NSMission::FollowPathTaskParams>();
				if (!params_ptr) {
					result.message = "无法获取 FollowPath 任务参数";
					LOG_ERROR("[FollowPathTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				if (params_ptr->waypoints.empty()) {
					result.message = "路径点列表为空";
					LOG_ERROR("[FollowPathTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第3步：获取起始状态 ===
				NSUav::UavState start_state;
				if (!getUavStartState(request.uav, start_state)) {
					result.message = "无法获取无人机起始状态";
					LOG_ERROR("[FollowPathTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第4步：执行路径规划 ===
				result = planFollowPathTrajectory(request, *params_ptr, start_state);

			} catch (const std::exception& e) {
				result.success = false;
				result.message = "规划异常: " + std::string(e.what());
				LOG_ERROR("[FollowPathTaskPlanner] 子任务 [{}] 规划异常: {}", result.sub_task_id, e.what());
			}

			LOG_INFO("[FollowPathTaskPlanner] 子任务 [{}] 规划完成，成功: {}",
				result.sub_task_id, result.success);
			return result;
		}

		/**
		 * @brief 规划路径跟随轨迹的核心实现
		 */
		SingleTaskPlanningResult FollowPathTaskPlanner::planFollowPathTrajectory(
			const SingleTaskPlanningRequest& request,
			const NSMission::FollowPathTaskParams& params,
			const NSUav::UavState& start_state) {

			SingleTaskPlanningResult result;
			result.success = false;
			result.sub_task_id = request.assignment.sub_task_id;
			result.uav_id = request.assignment.assigned_uav_id;

			try {
				LOG_DEBUG("[FollowPathTaskPlanner] 开始规划路径跟随轨迹，路径点数: {}", params.waypoints.size());

				// === 第1步：构建几何路径 ===
				std::vector<NSCore::WGS84Point> geometric_path;
				geometric_path.reserve(params.waypoints.size() + 1);

				// 添加起始点
				geometric_path.push_back(start_state.position);

				// 添加任务路径点
				for (const auto& waypoint : params.waypoints) {
					geometric_path.push_back(waypoint.position);
				}

				LOG_DEBUG("[FollowPathTaskPlanner] 构建几何路径完成，总点数: {}", geometric_path.size());

				// === 第2步：确定飞行速度 ===
				double flight_speed = follow_speed_;

				// 检查控制点中是否指定了速度（使用第一个控制点的速度作为参考）
				if (!params.waypoints.empty() && params.waypoints[0].hasSpeedRequirement()) {
					auto speed_vector = params.waypoints[0].getRequiredSpeedOrZero();
					double point_speed = speed_vector.norm();
					if (point_speed > 0) {
						flight_speed = point_speed;
						LOG_DEBUG("[FollowPathTaskPlanner] 使用控制点指定速度: {:.2f} m/s", flight_speed);
					}
				}

				// 检查UAV的速度限制
				auto dynamics = request.uav->getDynamicsModel();
				if (dynamics) {
					double max_speed = dynamics->getMaxSpeed();
					if (flight_speed > max_speed) {
						double original_speed = flight_speed;
						flight_speed = max_speed;
						LOG_WARN("[FollowPathTaskPlanner] 期望速度 {:.2f} 超过UAV最大速度 {:.2f}，调整为最大速度",
							original_speed, max_speed);
					}
				}

				LOG_DEBUG("[FollowPathTaskPlanner] 使用飞行速度: {:.2f} m/s", flight_speed);

				// === 第3步：生成轨迹 ===
				Trajectory trajectory;
				if (!smoothAndTimeParameterize(geometric_path, request.uav, start_state,
					flight_speed, trajectory, &result)) {
					result.message = "轨迹生成失败";
					LOG_ERROR("[FollowPathTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第4步：应用避障约束（如果有） ===
				if (!request.avoidance_constraints.empty()) {
					LOG_DEBUG("[FollowPathTaskPlanner] 应用 {} 个避障约束", request.avoidance_constraints.size());

					if (!applyAvoidanceConstraints(trajectory, request.avoidance_constraints, result)) {
						result.message = "应用避障约束失败";
						LOG_ERROR("[FollowPathTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
						return result;
					}
				}

				// === 第5步：验证轨迹 ===
				if (trajectory.empty()) {
					result.message = "生成的轨迹为空";
					LOG_ERROR("[FollowPathTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第6步：设置结果 ===
				result.trajectory = std::move(trajectory);
				result.success = true;
				result.message = "路径跟随规划成功";

				LOG_INFO("[FollowPathTaskPlanner] 子任务 [{}] 规划成功，轨迹段数: {}",
					result.sub_task_id, result.trajectory.size());

			} catch (const std::exception& e) {
				result.success = false;
				result.message = "规划异常: " + std::string(e.what());
				LOG_ERROR("[FollowPathTaskPlanner] 子任务 [{}] 规划异常: {}", result.sub_task_id, e.what());
			}

			return result;
		}

		/**
		 * @brief 应用避障约束到轨迹
		 */
		bool FollowPathTaskPlanner::applyAvoidanceConstraints(
			Trajectory& trajectory,
			const std::vector<AvoidanceConstraint>& constraints,
			SingleTaskPlanningResult& result) {

			if (constraints.empty()) {
				LOG_DEBUG("[FollowPathTaskPlanner] 无避障约束需要应用");
				return true;
			}

			LOG_DEBUG("[FollowPathTaskPlanner] 开始应用 {} 个避障约束", constraints.size());

			try {
				// 简化实现：检查轨迹是否与约束冲突
				for (const auto& constraint : constraints) {
					LOG_DEBUG("[FollowPathTaskPlanner] 检查约束: 类型={}, 区域={}",
						NSUtils::enumToString(constraint.type), constraint.zone_id);

					// 这里可以实现具体的约束检查逻辑
					// 当前为简化实现，仅记录约束信息
					if (constraint.type == AvoidanceConstraintType::ALTITUDE_SEPARATION) {
						LOG_DEBUG("[FollowPathTaskPlanner] 应用高度错层约束: 最小高度={:.1f}m, 最大高度={:.1f}m",
							constraint.min_altitude, constraint.max_altitude);
					}
					else if (constraint.type == AvoidanceConstraintType::SPATIAL_AVOIDANCE) {
						LOG_DEBUG("[FollowPathTaskPlanner] 应用空间避障约束: 避障半径={:.1f}m",
							constraint.avoidance_radius);
					}
				}

				LOG_DEBUG("[FollowPathTaskPlanner] 避障约束应用完成");
				return true;

			} catch (const std::exception& e) {
				result.warnings.push_back(WarningEvent(WarningType::PLANNING_ERROR,
					"应用避障约束异常: " + std::string(e.what()),
					0.0, {}, result.uav_id));
				LOG_ERROR("[FollowPathTaskPlanner] 应用避障约束异常: {}", e.what());
				return false;
			}
		}
	} // namespace NSPlanning
} // namespace NSDrones