#pragma once

#include "core/geometry/ishape.h"
#include <vector>
#include <memory>

namespace NSDrones{
namespace NSCore {

    /**
     * @brief 复合形状组件
     * 
     * 表示复合形状中的一个子形状及其相对变换
     */
    struct ShapeComponent {
        std::unique_ptr<IShape> shape;      // 子形状
        fcl::Transform3d transform;         // 相对变换
        std::string name;                   // 组件名称（可选）

        ShapeComponent(std::unique_ptr<IShape> s, const fcl::Transform3d& t, const std::string& n = "")
            : shape(std::move(s)), transform(t), name(n) {}

        // 拷贝构造函数
        ShapeComponent(const ShapeComponent& other)
            : shape(other.shape->clone()), transform(other.transform), name(other.name) {}

        // 赋值操作符
        ShapeComponent& operator=(const ShapeComponent& other) {
            if (this != &other) {
                shape = other.shape->clone();
                transform = other.transform;
                name = other.name;
            }
            return *this;
        }
    };

    /**
     * @brief 复合形状
     * 
     * 由多个基础形状组合而成的复杂形状。
     * 每个子形状都有自己的局部变换。
     */
    class CompoundShape : public IShape {
    private:
        std::vector<ShapeComponent> components_;  // 子形状列表
        mutable fcl::AABBd cached_aabb_;         // 缓存的包围盒
        mutable bool aabb_dirty_;                // 包围盒是否需要更新

    public:
        /**
         * @brief 默认构造函数
         */
        CompoundShape();

        /**
         * @brief 拷贝构造函数
         */
        CompoundShape(const CompoundShape& other);

        /**
         * @brief 赋值操作符
         */
        CompoundShape& operator=(const CompoundShape& other);

        // IShape接口实现
        ShapeType getType() const override { return ShapeType::COMPOUND; }
        int getDimension() const override { return 3; }  // 3D实体（复合形状）
        std::shared_ptr<fcl::CollisionGeometryd> getFCLGeometry() const override;
        fcl::AABBd getAABB(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const override;
        double getVolume() const override;
        double getSurfaceArea() const override;
        fcl::Vector3d getCentroid() const override;
        fcl::Matrix3d getInertiaMatrix(double mass) const override;
        std::unique_ptr<IShape> clone() const override;
        nlohmann::json serialize() const override;
        bool deserialize(const nlohmann::json& json) override;
        std::string toString() const override;
        bool containsPoint(const fcl::Vector3d& point) const override;
        double distanceToPoint(const fcl::Vector3d& point) const override;
        double getCharacteristicSize() const override;

        // CompoundShape特有方法
        /**
         * @brief 添加子形状
         * @param shape 子形状
         * @param transform 相对变换
         * @param name 组件名称
         */
        void addComponent(std::unique_ptr<IShape> shape, 
                         const fcl::Transform3d& transform = fcl::Transform3d::Identity(),
                         const std::string& name = "");

        /**
         * @brief 移除指定索引的子形状
         * @param index 索引
         */
        void removeComponent(size_t index);

        /**
         * @brief 移除指定名称的子形状
         * @param name 名称
         */
        void removeComponent(const std::string& name);

        /**
         * @brief 清空所有子形状
         */
        void clear();

        /**
         * @brief 获取子形状数量
         */
        size_t getComponentCount() const { return components_.size(); }

        /**
         * @brief 获取指定索引的子形状
         * @param index 索引
         * @return 子形状组件
         */
        const ShapeComponent& getComponent(size_t index) const;

        /**
         * @brief 获取指定名称的子形状
         * @param name 名称
         * @return 子形状组件指针（未找到返回nullptr）
         */
        const ShapeComponent* getComponent(const std::string& name) const;

        /**
         * @brief 更新指定索引子形状的变换
         * @param index 索引
         * @param transform 新变换
         */
        void updateComponentTransform(size_t index, const fcl::Transform3d& transform);

        /**
         * @brief 更新指定名称子形状的变换
         * @param name 名称
         * @param transform 新变换
         */
        void updateComponentTransform(const std::string& name, const fcl::Transform3d& transform);

        /**
         * @brief 获取所有子形状的包围盒列表
         * @param parent_transform 父变换
         * @return 包围盒列表
         */
        std::vector<fcl::AABBd> getComponentAABBs(const fcl::Transform3d& parent_transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 检查是否为空
         */
        bool isEmpty() const { return components_.empty(); }

        /**
         * @brief 优化复合形状（移除重叠、合并相邻等）
         */
        void optimize();

        /**
         * @brief 创建简单的复合形状（两个形状）
         * @param shape1 第一个形状
         * @param transform1 第一个形状的变换
         * @param shape2 第二个形状
         * @param transform2 第二个形状的变换
         * @return CompoundShape实例
         */
        static std::unique_ptr<CompoundShape> createSimple(
            std::unique_ptr<IShape> shape1, const fcl::Transform3d& transform1,
            std::unique_ptr<IShape> shape2, const fcl::Transform3d& transform2);

    private:
        /**
         * @brief 标记包围盒需要更新
         */
        void markAABBDirty() const { aabb_dirty_ = true; }

        /**
         * @brief 更新缓存的包围盒
         */
        void updateCachedAABB() const;

        /**
         * @brief 验证索引有效性
         */
        void validateIndex(size_t index) const;
    };

} // namespace NSCore
} // namespace NSDrones