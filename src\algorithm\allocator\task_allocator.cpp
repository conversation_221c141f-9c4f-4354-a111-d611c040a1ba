// src/algorithm/allocator/task_allocator.cpp
#include "algorithm/allocator/task_allocator.h"
#include "environment/environment.h"
#include "mission/mission.h"
#include "mission/task.h"
#include "mission/capability_requirement.h"
#include "uav/uav.h"
#include "utils/logging.h"
#include "utils/enum_utils.h"
#include "params/paramregistry.h"
#include <stdexcept>
#include <set>

namespace NSDrones {
	namespace NSAlgorithm {

		TaskAllocator::TaskAllocator(ObjectID id,
			const std::string& type_tag,
			const std::string& name,
			const std::string& version)
			: AlgorithmObject(id, type_tag, name, version),
			ITaskAllocator() // ITaskAllocator 默认构造
		{
			LOG_INFO("TaskAllocator [{}] (ID: {}) constructed. Version: {}. Associated with Environment.",
				getName(), getId(), getVersion());
		}

		bool TaskAllocator::initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) {
			LOG_DEBUG("TaskAllocator [{}] (ID: {}): Initializing...", getName(), getId());
			if (!AlgorithmObject::initialize(params, raw_config)) {
				LOG_ERROR("TaskAllocator [{}] (ID: {}): Base initialization failed.", getName(), getId());
				return false;
			}
			// TaskAllocator 特定的参数可以在这里加载，如果存在的话
			// example: some_specific_param_ = getParamOrDefault<double>("my_allocator_param", 0.5);
			LOG_INFO("TaskAllocator [{}] (ID: {}) initialized successfully.", getName(), getId());
			return true;
		}

		/**
		 * @brief 任务分配算法实现
		 *
		 * 执行简单的任务分配算法，为任务计划中的每个任务分配合适的无人机。
		 * 分配策略：
		 * 1. 按任务顺序逐个处理
		 * 2. 为每个任务查找满足能力要求的无人机
		 * 3. 优先分配未被占用的无人机
		 * 4. 确保满足最小数量要求
		 *
		 * @param mission 要分配的任务计划，包含任务列表和策略
		 * @param available_uavs 当前可用的无人机列表
		 * @param current_uav_states 无人机当前状态映射（用于高级分配策略）
		 * @return 任务分配结果，包含成功状态、分配映射和未分配任务列表
		 */
		TaskAllocationResult TaskAllocator::allocate(
			const NSMission::Mission& mission,
			const std::vector<NSUav::UavPtr>& available_uavs,
			const std::map<ObjectID, NSUav::UavState>& current_uav_states)
		{
			LOG_INFO("任务分配器 [{}] (ID: {}): 开始为任务计划 [{}] 分配无人机。可用无人机数量: {}",
				getName(), getId(), mission.getId(), available_uavs.size());

			TaskAllocationResult result;
			result.success = true; // 初始假设成功

			const auto& tasks = mission.getTasks();
			if (tasks.empty()) {
				result.message = "No tasks in the mission to allocate.";
				LOG_INFO("TaskAllocator [{}] (ID: {}): Mission [{}] has no tasks.", getName(), getId(), mission.getId());
				return result;
			}

			if (available_uavs.empty()) {
				result.success = false;
				result.message = "没有可用的无人机进行分配。";
				LOG_WARN("{}", result.message);
				// 将所有任务标记为未分配
				for (const auto& task_ptr : mission.getTasks()) {
					if (task_ptr) {
						result.unallocated_tasks.push_back(task_ptr->getId());
					}
				}
				return result;
			}
			// 使用映射跟踪每个无人机的分配次数（允许重复分配）
			std::map<ObjectID, int> uav_assignment_count;
			// 遍历任务列表
			for (const auto& task_ptr : tasks) {
				if (!task_ptr) {
					LOG_WARN("任务列表中遇到空指针，跳过此任务。");
					continue;
				}
				const auto& task = *task_ptr; // 解引用获取 Task 对象
				ObjectID task_id = task.getId();
				LOG_DEBUG("尝试为任务 {} (类型: {}) 分配无人机...", task_id, NSUtils::enumToString(task.getType()));

				const auto& requirements = task.getRequirements();
				size_t needed_count = requirements.min_required_count;
				size_t assigned_count = 0;
				std::vector<ObjectID> current_task_uavs; // 存储当前任务分配的无人机ID

				LOG_TRACE("任务需求: 数量 [{}, {}], 类型: {}, 必需载荷: {}",
					requirements.min_required_count, requirements.max_required_count,
					requirements.required_uav_type.has_value() ? NSUtils::enumToString(requirements.required_uav_type.value()) : "任何",
					requirements.required_payloads.size());

				// 创建候选UAV列表，按分配次数排序（优先分配负载较轻的UAV）
				std::vector<NSUav::UavPtr> candidate_uavs;
				for (const auto& uav_ptr : available_uavs) {
					if (!uav_ptr) continue; // 跳过空指针

					// 检查无人机是否满足任务需求 (使用内部辅助函数)
					if (checkUavCapabilities(uav_ptr, requirements)) {
						candidate_uavs.push_back(uav_ptr);
					}
				}

				// 按分配次数排序候选UAV（分配次数少的优先）
				std::sort(candidate_uavs.begin(), candidate_uavs.end(),
					[&uav_assignment_count](const NSUav::UavPtr& a, const NSUav::UavPtr& b) {
						int count_a = uav_assignment_count.count(a->getId()) ? uav_assignment_count[a->getId()] : 0;
						int count_b = uav_assignment_count.count(b->getId()) ? uav_assignment_count[b->getId()] : 0;
						return count_a < count_b; // 分配次数少的排在前面
					});

				// 遍历排序后的候选无人机进行分配
				for (const auto& uav_ptr : candidate_uavs) {
					const auto& uav = *uav_ptr; // 解引用
					ObjectID uav_id = uav.getId();

					int current_assignments = uav_assignment_count.count(uav_id) ? uav_assignment_count[uav_id] : 0;
					LOG_TRACE("考虑无人机 {} (当前分配任务数: {})。", uav_id, current_assignments);

					LOG_DEBUG("无人机 {} 满足任务 {} 的需求，分配成功。", uav_id, task_id);
					current_task_uavs.push_back(uav_id);
					uav_assignment_count[uav_id] = current_assignments + 1; // 增加分配计数
					assigned_count++;

					if (assigned_count >= needed_count) { // 满足数量要求
						LOG_TRACE("任务 {} 已满足所需数量 ({})。", task_id, needed_count);
						break; // 停止为此任务查找无人机
					}
				}

				// 检查任务是否成功分配
				if (assigned_count >= needed_count) {
					// 记录分配结果
					result.allocation[task_id] = current_task_uavs;
					// 手动构建逗号分隔的 UAV ID 字符串
					std::string uav_ids_str;
					for (size_t i = 0; i < current_task_uavs.size(); ++i) {
						uav_ids_str += current_task_uavs[i];
						if (i < current_task_uavs.size() - 1) {
							uav_ids_str += ", ";
						}
					}
					LOG_INFO("任务 {} 成功分配给 {} 架无人机: [{}]", task_id, assigned_count, uav_ids_str);
				}
				else {
					LOG_WARN("任务 {} (需求: {}) 未能分配足够数量 ({}/{}) 的无人机。",
						task_id, requirements.min_required_count, assigned_count, needed_count);
					result.unallocated_tasks.push_back(task_id);
					// 将已为此任务分配的无人机释放（减少分配计数）
					for (const auto& uav_id : current_task_uavs) {
						if (uav_assignment_count.count(uav_id) && uav_assignment_count[uav_id] > 0) {
							uav_assignment_count[uav_id]--;
							if (uav_assignment_count[uav_id] == 0) {
								uav_assignment_count.erase(uav_id);
							}
							LOG_TRACE("因数量不足，释放无人机 {} (当前分配计数: {})。", uav_id,
								uav_assignment_count.count(uav_id) ? uav_assignment_count[uav_id] : 0);
						}
					}
					result.success = false; // 标记整体分配不完全成功
					if (result.message == "分配完成") {
						result.message = "部分任务未能分配。";
					}
				}
			}

			// 检查是否有任何任务未分配
			if (!result.unallocated_tasks.empty()) {
				LOG_WARN("SimpleTaskAllocator 分配完成，但有 {} 个任务未分配。", result.unallocated_tasks.size());
			}
			else if (result.success) {
				LOG_INFO("SimpleTaskAllocator 成功分配所有任务。");
			}

			return result;
		}

		// 实现内部辅助函数 checkUavCapabilities
		bool TaskAllocator::checkUavCapabilities(const NSUav::UavPtr& uav, const NSMission::CapabilityRequirement& req) const {
			if (!uav) return false;
			LOG_TRACE("  检查无人机 {} 的能力是否满足需求...", uav->getId());

			// 1. 检查无人机类型（支持类型兼容性）
			if (req.required_uav_type.has_value()) {
				UavType required_type = req.required_uav_type.value();
				UavType uav_type = uav->getType();

				bool type_compatible = false;

				// 精确匹配
				if (uav_type == required_type) {
					type_compatible = true;
				}
				// VTOL兼容性：VTOL可以执行MULTIROTOR任务（悬停模式）
				else if (required_type == UavType::MULTIROTOR && uav_type == UavType::VTOL_FIXED_WING) {
					type_compatible = true;
					LOG_TRACE("    类型兼容: VTOL无人机可以执行MULTIROTOR任务（悬停模式）。");
				}

				if (!type_compatible) {
					LOG_TRACE("    类型不匹配: 需要 {}, 实际 {}。",
						NSUtils::enumToString(required_type), NSUtils::enumToString(uav_type));
					return false;
				}
				LOG_TRACE("    类型匹配/兼容: {}。", NSUtils::enumToString(uav_type));
			}
			else {
				LOG_TRACE("    无特定类型要求。");
			}

			// 2. 检查必需的载荷/能力
			// **注意:** UAV 类需要提供 getCapabilities() 方法
			const auto& uav_caps = uav->getCapabilities(); // 假设 UAV 有 getCapabilities()
			const auto& required_payloads = req.required_payloads;
			if (!required_payloads.empty()) {
				LOG_TRACE("    检查必需载荷/能力 ({} 项)...", required_payloads.size());
				for (const auto& required_cap : required_payloads) {
					// 使用 uav->hasCapability() 检查
					if (!uav->hasCapability(required_cap)) { // 假设 UAV 有 hasCapability()
						LOG_TRACE("      缺少必需能力: {}。", required_cap);
						return false;
					}
					LOG_TRACE("      满足必需能力: {}。", required_cap);
				}
				LOG_TRACE("    所有必需能力均满足。");
			}
			else {
				LOG_TRACE("    无必需载荷/能力要求。");
			}

			// 3. 检查最小载荷能力 (如果任务有要求)
			if (req.min_payload_capacity.has_value()) {
				double required_capacity = req.min_payload_capacity.value();
				// 假设 UAV 有 getParamOrDefault<double>("payload.max_weight", 0.0)
				double uav_capacity = uav->getParamOrDefault<double>("payload.max_weight", 0.0);
				LOG_TRACE("    检查最小载荷能力: 需要 >= {:.2f} kg, 无人机最大载荷 {:.2f} kg", required_capacity, uav_capacity);
				if (uav_capacity < required_capacity - Constants::EPSILON) { // 考虑浮点误差
					LOG_TRACE("      载荷能力不足。");
					return false;
				}
				LOG_TRACE("    载荷能力满足。");
			}

			// 4. 检查续航时间 (如果任务有要求)
			// **注意:** 简单分配器通常不检查这个，或者需要能量模型和状态
			// if (req.min_endurance_time.has_value()) { ... }

			// 5. 检查航程 (如果任务有要求)
			// **注意:** 简单分配器通常不检查这个
			// if (req.min_range.has_value()) { ... }

			LOG_TRACE("  无人机 {} 满足所有检查的基本能力要求。", uav->getId());
			return true; // 如果所有检查都通过
		}

	} // namespace NSAlgorithm
} // namespace NSDrones