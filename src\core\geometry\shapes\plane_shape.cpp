#include "core/geometry/shapes/plane_shape.h"
#include <stdexcept>
#include <cmath>

namespace NSDrones {
namespace NSCore {

    PlaneShape::PlaneShape(const fcl::Vector3d& normal, double distance) 
        : normal_(normal), distance_(distance), fcl_plane_(nullptr) {
        normalizeNormal();
        validateParameters();
    }

    PlaneShape::PlaneShape(const fcl::Vector3d& p1, const fcl::Vector3d& p2, const fcl::Vector3d& p3)
        : fcl_plane_(nullptr) {
        // 从三点计算平面
        fcl::Vector3d v1 = p2 - p1;
        fcl::Vector3d v2 = p3 - p1;
        normal_ = v1.cross(v2);
        normalizeNormal();
        distance_ = normal_.dot(p1);
        validateParameters();
    }

    PlaneShape::PlaneShape() : PlaneShape(fcl::Vector3d::UnitZ(), 0.0) {
    }

    PlaneShape::PlaneShape(const PlaneShape& other)
        : normal_(other.normal_), distance_(other.distance_), fcl_plane_(nullptr) {
    }

    PlaneShape& PlaneShape::operator=(const PlaneShape& other) {
        if (this != &other) {
            normal_ = other.normal_;
            distance_ = other.distance_;
            fcl_plane_.reset();  // 重置FCL对象，延迟重新创建
        }
        return *this;
    }

    std::shared_ptr<fcl::CollisionGeometryd> PlaneShape::getFCLGeometry() const {
        ensureFCLObject();
        return fcl_plane_;
    }

    fcl::AABBd PlaneShape::getAABB(const fcl::Transform3d& transform) const {
        // 平面的AABB是无限大的，但我们返回一个大的有限边界框
        const double large_value = 1e6;
        fcl::Vector3d min_point(-large_value, -large_value, -large_value);
        fcl::Vector3d max_point(large_value, large_value, large_value);
        return fcl::AABBd(min_point, max_point);
    }

    fcl::Vector3d PlaneShape::getCentroid() const {
        // 平面的质心是距离原点最近的点
        return normal_ * distance_;
    }

    fcl::Matrix3d PlaneShape::getInertiaMatrix(double mass) const {
        // 无限平面的惯性张量是无限的，返回零矩阵作为占位符
        return fcl::Matrix3d::Zero();
    }

    std::unique_ptr<IShape> PlaneShape::clone() const {
        return std::make_unique<PlaneShape>(*this);
    }

    nlohmann::json PlaneShape::serialize() const {
        nlohmann::json j;
        j["type"] = "PLANE";
        j["normal"] = {normal_.x(), normal_.y(), normal_.z()};
        j["distance"] = distance_;
        return j;
    }

    bool PlaneShape::deserialize(const nlohmann::json& json) {
        try {
            if (json["type"] != "PLANE") {
                return false;
            }
            
            auto normal = json["normal"];
            normal_ = fcl::Vector3d(normal[0], normal[1], normal[2]);
            distance_ = json["distance"];
            
            normalizeNormal();
            validateParameters();
            fcl_plane_.reset();  // 重置FCL对象
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    std::string PlaneShape::toString() const {
        return "Plane(normal: [" + std::to_string(normal_.x()) + ", " + 
               std::to_string(normal_.y()) + ", " + std::to_string(normal_.z()) + 
               "], distance: " + std::to_string(distance_) + ")";
    }

    bool PlaneShape::containsPoint(const fcl::Vector3d& point) const {
        return std::abs(signedDistanceToPoint(point)) < 1e-6;
    }

    double PlaneShape::distanceToPoint(const fcl::Vector3d& point) const {
        return std::abs(signedDistanceToPoint(point));
    }

    void PlaneShape::setPlane(const fcl::Vector3d& normal, double distance) {
        normal_ = normal;
        distance_ = distance;
        normalizeNormal();
        validateParameters();
        fcl_plane_.reset();  // 重置FCL对象
    }

    double PlaneShape::signedDistanceToPoint(const fcl::Vector3d& point) const {
        return normal_.dot(point) - distance_;
    }

    fcl::Vector3d PlaneShape::projectPoint(const fcl::Vector3d& point) const {
        double signed_dist = signedDistanceToPoint(point);
        return point - signed_dist * normal_;
    }

    fcl::Vector3d PlaneShape::getPointOnPlane() const {
        return normal_ * distance_;
    }

    std::unique_ptr<PlaneShape> PlaneShape::createXYPlane() {
        return std::make_unique<PlaneShape>(fcl::Vector3d::UnitZ(), 0.0);
    }

    std::unique_ptr<PlaneShape> PlaneShape::createXZPlane() {
        return std::make_unique<PlaneShape>(fcl::Vector3d::UnitY(), 0.0);
    }

    std::unique_ptr<PlaneShape> PlaneShape::createYZPlane() {
        return std::make_unique<PlaneShape>(fcl::Vector3d::UnitX(), 0.0);
    }

    std::unique_ptr<PlaneShape> PlaneShape::createFromPointAndNormal(const fcl::Vector3d& point, const fcl::Vector3d& normal) {
        fcl::Vector3d normalized_normal = normal.normalized();
        double distance = normalized_normal.dot(point);
        return std::make_unique<PlaneShape>(normalized_normal, distance);
    }

    void PlaneShape::ensureFCLObject() const {
        if (!fcl_plane_) {
            fcl_plane_ = std::make_shared<fcl::Planed>(normal_, distance_);
        }
    }

    void PlaneShape::validateParameters() const {
        if (!std::isfinite(distance_)) {
            throw std::invalid_argument("PlaneShape: 距离必须为有限数值");
        }
        if (!normal_.allFinite()) {
            throw std::invalid_argument("PlaneShape: 法向量必须为有限数值");
        }
        if (normal_.norm() < 1e-12) {
            throw std::invalid_argument("PlaneShape: 法向量不能为零向量");
        }
    }

    void PlaneShape::normalizeNormal() {
        double norm = normal_.norm();
        if (norm > 1e-12) {
            normal_ /= norm;
            distance_ /= norm;  // 同时调整距离以保持平面方程的一致性
        }
    }

} // namespace NSCore
} // namespace NSDrones
