// include/indices/ispatial_index.h
#pragma once

#include "core/types.h"
#include "params/parameters.h"
#include <vector>
#include <string>
#include <chrono>
#include <optional>
#include <functional>
#include <nlohmann/json.hpp>
#include <GeographicLib/Geocentric.hpp>  // 添加GeographicLib支持

namespace NSDrones {
	namespace NSEnvironment {

		/**
		 * @struct ECEFPoint
		 * @brief ECEF (Earth-Centered, Earth-Fixed) 坐标点
		 * @note ECEF坐标系是一个以地心为原点的笛卡尔坐标系，适合全球范围的空间索引
		 */
		struct ECEFPoint {
			double x = 0.0;  // X坐标 (米)
			double y = 0.0;  // Y坐标 (米)
			double z = 0.0;  // Z坐标 (米)

			ECEFPoint() = default;
			ECEFPoint(double x_val, double y_val, double z_val) : x(x_val), y(y_val), z(z_val) {}

			/**
			 * @brief 从WGS84坐标转换为ECEF坐标
			 * @param wgs84 WGS84坐标点
			 * @return ECEF坐标点
			 */
			static ECEFPoint fromWGS84(const WGS84Point& wgs84) {
				GeographicLib::Geocentric earth(GeographicLib::Constants::WGS84_a(), GeographicLib::Constants::WGS84_f());
				double x, y, z;
				earth.Forward(wgs84.latitude, wgs84.longitude, wgs84.altitude, x, y, z);
				return ECEFPoint(x, y, z);
			}

			/**
			 * @brief 转换为WGS84坐标
			 * @return WGS84坐标点
			 */
			WGS84Point toWGS84() const {
				GeographicLib::Geocentric earth(GeographicLib::Constants::WGS84_a(), GeographicLib::Constants::WGS84_f());
				double lat, lon, alt;
				earth.Reverse(x, y, z, lat, lon, alt);
				return WGS84Point(lon, lat, alt);
			}

			/**
			 * @brief 计算到另一点的距离
			 * @param other 另一个ECEF点
			 * @return 距离（米）
			 */
			double distanceTo(const ECEFPoint& other) const {
				double dx = x - other.x;
				double dy = y - other.y;
				double dz = z - other.z;
				return std::sqrt(dx*dx + dy*dy + dz*dz);
			}
		};

		/**
		 * @struct ECEFBoundingBox
		 * @brief ECEF坐标系下的轴对齐包围盒
		 */
		struct ECEFBoundingBox {
			ECEFPoint min;  // 最小点
			ECEFPoint max;  // 最大点

			ECEFBoundingBox() = default;
			ECEFBoundingBox(const ECEFPoint& min_pt, const ECEFPoint& max_pt) : min(min_pt), max(max_pt) {}

			/**
			 * @brief 从WGS84包围盒转换
			 * @param wgs84_box WGS84包围盒
			 * @return ECEF包围盒
			 */
			static ECEFBoundingBox fromWGS84(const WGS84BoundingBox& wgs84_box) {
				// 转换8个角点并计算ECEF包围盒
				std::vector<ECEFPoint> corners;
				corners.reserve(8);

				for (int i = 0; i < 8; ++i) {
					double lon = (i & 1) ? wgs84_box.maxLongitude : wgs84_box.minLongitude;
					double lat = (i & 2) ? wgs84_box.maxLatitude : wgs84_box.minLatitude;
					double alt = (i & 4) ? wgs84_box.maxAltitude : wgs84_box.minAltitude;
					corners.push_back(ECEFPoint::fromWGS84(WGS84Point(lon, lat, alt)));
				}

				ECEFPoint min_pt(std::numeric_limits<double>::max(),
								std::numeric_limits<double>::max(),
								std::numeric_limits<double>::max());
				ECEFPoint max_pt(std::numeric_limits<double>::lowest(),
								std::numeric_limits<double>::lowest(),
								std::numeric_limits<double>::lowest());

				for (const auto& corner : corners) {
					min_pt.x = std::min(min_pt.x, corner.x);
					min_pt.y = std::min(min_pt.y, corner.y);
					min_pt.z = std::min(min_pt.z, corner.z);
					max_pt.x = std::max(max_pt.x, corner.x);
					max_pt.y = std::max(max_pt.y, corner.y);
					max_pt.z = std::max(max_pt.z, corner.z);
				}

				return ECEFBoundingBox(min_pt, max_pt);
			}

			/**
			 * @brief 检查是否包含点
			 * @param point ECEF点
			 * @return 是否包含
			 */
			bool contains(const ECEFPoint& point) const {
				return point.x >= min.x && point.x <= max.x &&
					   point.y >= min.y && point.y <= max.y &&
					   point.z >= min.z && point.z <= max.z;
			}

			/**
			 * @brief 检查是否与另一个包围盒相交
			 * @param other 另一个包围盒
			 * @return 是否相交
			 */
			bool intersects(const ECEFBoundingBox& other) const {
				return !(max.x < other.min.x || min.x > other.max.x ||
						max.y < other.min.y || min.y > other.max.y ||
						max.z < other.min.z || min.z > other.max.z);
			}
		};

		/**
		 * @struct SpatialObjectData
		 * @brief 存储在空间索引中的对象数据
		 * @note 分离位置和形状，优化频繁的位置更新性能
		 */
		struct SpatialObjectData {
			WGS84Point position;        // 对象位置（经常变化）
			BoundingBox localBounds;    // 局部边界盒，相对于position（很少变化）

			/**
			 * @brief 计算对象的WGS84边界盒
			 * @return 基于当前位置和局部边界的WGS84边界盒
			 */
			WGS84BoundingBox getWGS84Bounds() const {
				// 将局部边界盒转换到WGS84坐标系
				// 注意：这里使用简化的转换，实际实现中应该使用更精确的地理坐标转换
				// 可以考虑使用GeographicLib或类似库进行精确转换

				// 在赤道附近的近似转换因子
				double lonScale = 1.0 / (111320.0 * std::cos(position.latitude * M_PI / 180.0));
				double latScale = 1.0 / 110540.0;

				WGS84Point minPoint(
					position.longitude + localBounds.min_[0] * lonScale,
					position.latitude + localBounds.min_[1] * latScale,
					position.altitude + localBounds.min_[2]
				);
				WGS84Point maxPoint(
					position.longitude + localBounds.max_[0] * lonScale,
					position.latitude + localBounds.max_[1] * latScale,
					position.altitude + localBounds.max_[2]
				);
				return WGS84BoundingBox(minPoint, maxPoint);
			}
		};

		/**
		 * @struct SpatialObject
		 * @brief 用于添加到索引的空间对象描述
		 * @note 分离位置和形状，便于高效的位置更新
		 */
		struct SpatialObject {
			ObjectID id;                  // 对象ID
			WGS84Point position;          // 对象位置
			BoundingBox localBounds;      // 局部边界盒（从IShape获取）
		};

		/**
		 * @struct SpatialObjectUpdate
		 * @brief 用于更新索引中对象的空间信息
		 * @note 支持高效的位置更新和较少的形状更新
		 */
		struct SpatialObjectUpdate {
			ObjectID id;                              // 对象ID

			// 位置更新（频繁）
			std::optional<WGS84Point> oldPosition;    // 旧位置（用于优化）
			std::optional<WGS84Point> newPosition;    // 新位置

			// 形状更新（很少）
			std::optional<BoundingBox> newLocalBounds; // 新的局部边界盒（仅在形状变化时设置）

			/**
			 * @brief 检查是否只是位置更新
			 * @return 如果只更新位置返回true
			 */
			bool isPositionOnlyUpdate() const {
				return newPosition.has_value() && !newLocalBounds.has_value();
			}

			/**
			 * @brief 检查是否包含形状更新
			 * @return 如果包含形状更新返回true
			 */
			bool hasShapeUpdate() const {
				return newLocalBounds.has_value();
			}
		};

		/**
		 * @struct SpatialQueryFilter
		 * @brief 空间查询过滤器
		 * @note 只提供基于ObjectID的过滤，类型过滤应在上层通过EntityObject进行
		 */
		struct SpatialQueryFilter {
			// 自定义过滤函数：只基于ObjectID和空间数据进行过滤
			// 如需类型过滤，调用者应通过ObjectID查询EntityObject
			std::function<bool(const ObjectID&, const SpatialObjectData&)> customFilter;
		};



		/**
		 * @class ISpatialIndex
		 * @brief 空间索引接口，定义了空间索引的基本操作
		 *
		 * 这个接口规定了所有空间索引实现必须提供的功能，包括：
		 * - 添加、更新和移除对象
		 * - 查询特定区域或点附近的对象
		 * - 获取对象信息
		 * - 配置查询缓存和内存管理

		 * - 序列化与反序列化
		 */
		class ISpatialIndex {
		public:
			/**
			 * @brief 虚析构函数
			 */
			virtual ~ISpatialIndex() = default;

			//=== 初始化接口 ===//

			/**
			 * @brief 通过JSON配置初始化空间索引。
			 *
			 * 此方法负责从JSON配置和全局参数中解析空间索引配置，包括：
			 * - 从全局参数中获取索引配置
			 * - 从配置中获取默认参数
			 * - 根据地图边界自动生成索引配置
			 *
			 * @param config_json JSON配置对象，可能包含空间索引相关配置
			 * @param global_params 全局参数对象，用于获取索引配置等参数
			 * @param map_bounds 可选的地图边界，用于自动生成索引配置
			 * @return 如果成功初始化则返回 true，否则返回 false
			 */
			virtual bool initialize(const nlohmann::json& config_json,
				const NSParams::ParamValues& global_params,
				const std::optional<BoundingBox>& map_bounds = std::nullopt) = 0;

			//=== 对象管理 ===//

			/**
			 * @brief 添加对象到空间索引
			 * @param object 空间对象描述
			 */
			virtual void addObject(const SpatialObject& object) = 0;

			/**
			 * @brief 批量添加对象到空间索引
			 * @param objects 对象列表
			 */
			virtual void addObjects(const std::vector<SpatialObject>& objects) = 0;

			/**
			 * @brief 更新对象在空间索引中的信息
			 * @param update 对象更新描述
			 */
			virtual void updateObject(const SpatialObjectUpdate& update) = 0;

			/**
			 * @brief 批量更新对象
			 * @param updates 更新列表
			 */
			virtual void updateObjects(const std::vector<SpatialObjectUpdate>& updates) = 0;

			/**
			 * @brief 高效的位置更新（仅更新位置，不改变形状）
			 * @param objectId 对象ID
			 * @param newPosition 新位置
			 * @param oldPosition 旧位置（可选，用于优化）
			 */
			virtual void updateObjectPosition(const ObjectID& objectId,
				const WGS84Point& newPosition,
				const std::optional<WGS84Point>& oldPosition = std::nullopt) = 0;

			/**
			 * @brief 批量位置更新
			 * @param positionUpdates 位置更新列表：ObjectID -> {oldPos, newPos}
			 */
			virtual void updateObjectPositions(const std::vector<std::pair<ObjectID,
				std::pair<std::optional<WGS84Point>, WGS84Point>>>& positionUpdates) = 0;

			/**
			 * @brief 从空间索引中移除对象
			 * @param objectId 对象ID
			 */
			virtual void removeObject(const ObjectID& objectId) = 0;

			/**
			 * @brief 批量移除对象
			 * @param objectIds 对象ID列表
			 */
			virtual void removeObjects(const std::vector<ObjectID>& objectIds) = 0;

			//=== 查询功能 ===//

			/**
			 * @brief 查询指定WGS84区域内的对象
			 * @param region WGS84查询区域
			 * @param filter 可选的查询过滤器
			 * @return 区域内的对象ID列表
			 */
			virtual std::vector<ObjectID> findObjectsInRegion(
				const WGS84BoundingBox& region, const std::optional<SpatialQueryFilter>& filter = std::nullopt) const = 0;

			/**
			 * @brief 查询指定WGS84点附近的对象
			 * @param point WGS84查询点
			 * @param radius 查询半径（米）
			 * @param filter 可选的查询过滤器
			 * @return 点附近的对象ID列表
			 */
			virtual std::vector<ObjectID> findObjectsNearPoint(
				const WGS84Point& point, double radius,
                const std::optional<SpatialQueryFilter>& filter = std::nullopt) const = 0;

			/**
			 * @brief 检查两个对象是否可能相交
			 * @param objectId1 第一个对象ID
			 * @param objectId2 第二个对象ID
			 * @return 如果对象的包围盒相交返回true，否则返回false
			 */
			virtual bool objectsIntersect(const ObjectID& objectId1, const ObjectID& objectId2) const = 0;

			//=== 对象信息 ===//

			/**
			 * @brief 获取对象的WGS84边界盒
			 * @param objectId 对象ID
			 * @return 对象的WGS84边界盒，如果对象不存在则返回std::nullopt
			 */
			virtual std::optional<WGS84BoundingBox> getObjectBounds(const ObjectID& objectId) const = 0;

			/**
			 * @brief 获取对象的所有数据
			 * @param objectId 对象ID
			 * @return 对象数据，如果对象不存在则返回std::nullopt
			 */
			virtual std::optional<SpatialObjectData> getObjectData(const ObjectID& objectId) const = 0;

			/**
			 * @brief 获取索引中的所有对象ID
			 * @return 所有对象ID的列表
			 */
			virtual std::vector<ObjectID> getAllObjectIds() const = 0;

			//=== 缓存和内存管理 ===//

			/**
			 * @brief 配置查询缓存
			 * @param enable 是否启用缓存
			 * @param maxSize 最大缓存条目数
			 * @param maxAge 缓存条目最大生存时间
			 * @return 配置是否成功
			 */
			virtual bool configureQueryCache(bool enable,
				size_t maxSize = 50,
				std::chrono::milliseconds maxAge = std::chrono::milliseconds(1000)) = 0;

			/**
			 * @brief 清除查询缓存
			 */
			virtual void clearQueryCache() = 0;

			/**
			 * @brief 设置内存使用限制
			 * @param bytesLimit 内存使用上限（字节），0表示不限制
			 */
			virtual void setMemoryLimit(size_t bytesLimit) = 0;

			/**
			 * @brief 获取估计的内存使用量
			 * @return 估计的内存使用量（字节）
			 */
			virtual size_t getEstimatedMemoryUsage() const = 0;



			//=== 序列化功能 ===//

			/**
			 * @brief 将索引状态序列化到JSON
			 * @return 包含索引状态的JSON对象
			 */
			virtual nlohmann::json serialize() const = 0;

			/**
			 * @brief 从JSON反序列化索引状态
			 * @param json JSON对象
			 * @return 是否成功反序列化
			 */
			virtual bool deserialize(const nlohmann::json& json) = 0;
		};


		/**
		 * @enum SpatialIndexType
		 * @brief 空间索引类型枚举
		 */
		enum class SpatialIndexType {
			BLOCK_BASED,  // 基于块的空间索引
			OCTREE,       // 八叉树空间索引
			BVH,          // 边界体层次结构空间索引
			HYBRID        // 混合策略空间索引
		};

	} // namespace NSEnvironment
} // namespace NSDrones
