// src/uav/dynamics/fixedwing_dynamics.cpp
#include "uav/dynamics/fixedwing_dynamics.h"
#include "uav/uav.h"       
#include "utils/logging.h"           
#include <cmath>                     
#include <limits>                   
#include <algorithm>                

namespace NSDrones {
	namespace NSUav {

		// --- 构造函数 ---
		/**
		 * @brief FixedWingDynamics 构造函数。
		 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
		 */
		FixedWingDynamics::FixedWingDynamics(const Uav& owner)
			: IDynamicModel(owner) // 调用基类构造函数，传递 owner
		{
			LOG_DEBUG("固定翼动力学模型已创建，所有者: {}", owner_.getId());
		}

		// --- 固定翼特定行为实现 ---
		/**
		 * @brief 获取最大转弯率 (rad/s)。 omega = V / R
		 */
		double FixedWingDynamics::getMaxTurnRate(const UavState& state) const {
			LOG_TRACE("获取固定翼最大转弯率 (所有者 ID: {})...", owner_.getId());
			double minRadius = getMinTurnRadius(state); // 获取最小转弯半径

			// 检查最小半径是否有效（大于容差且有限）
			if (minRadius <= Constants::GEOMETRY_EPSILON || !std::isfinite(minRadius)) {
				LOG_TRACE("  最小转弯半径无效 ({:.3f})，无法转弯，返回 0。", minRadius);
				return 0.0; // 无法转弯
			}
			double speed = state.velocity.norm(); // 使用总速度
			double minSpeed = getMinOperationalSpeed(state); // 获取最小运行速度 (失速速度)

			// 检查速度是否低于最小运行速度
			if (speed < minSpeed - Constants::VELOCITY_EPSILON) {
				LOG_TRACE("  速度 ({:.2f}) 低于最小速度 ({:.2f})，无法协调转弯，返回 0。", speed, minSpeed);
				return 0.0; // 低于失速速度，无法协调转弯
			}
			// 检查速度是否有效 (大于容差)
			if (speed < Constants::VELOCITY_EPSILON) {
				LOG_TRACE("  速度接近零，无法转弯，返回 0。");
				return 0.0; // 速度为零
			}

			// 计算转弯率: omega = V / R
			double turnRate = speed / minRadius;
			LOG_DEBUG("  速度={:.2f}, 最小半径={:.2f}, 最大转弯率={:.3f} rad/s", speed, minRadius, turnRate);
			return turnRate;
		}

		/**
		 * @brief 获取最小转弯半径 (m)。 R = V^2 / (g * tan(phi_max))
		 */
		double FixedWingDynamics::getMinTurnRadius(const UavState& state) const {
			LOG_TRACE("获取固定翼最小转弯半径 (所有者 ID: {})...", owner_.getId());
			// 从 owner_ 获取参数
			// 键名示例: "dynamics.fw.maxBankAngle", "dynamics.fw.gravity"
			double maxBankAngleDeg = owner_.getParamOrDefault<double>("dynamics.fw.maxBankAngle", 30.0); // 度
			double g = owner_.getParamOrDefault<double>("dynamics.fw.gravity", Constants::GRAVITY); // m/s^2
			double minSpeed = getMinOperationalSpeed(state); // 获取失速速度 (m/s)

			double speed = state.velocity.norm(); // 使用总速度计算
			double maxBankRad = maxBankAngleDeg * Constants::DEG_TO_RAD; // 最大倾角 (弧度)

			// 检查输入值是否有效
			if (speed < minSpeed - Constants::VELOCITY_EPSILON || g < Constants::EPSILON || maxBankRad < Constants::ANGLE_EPSILON) {
				LOG_TRACE("  输入无效 (V={:.2f}, minV={:.2f}, g={:.2f}, bank={:.3f} rad)，半径视为无穷大。",
					speed, minSpeed, g, maxBankRad);
				return Constants::INF; // 输入无效，半径无限大
			}
			double tan_phi_max = std::tan(maxBankRad); // 计算最大倾角的正切值

			// 检查 tan 值是否有效 (避免倾角接近 90 度或 0 度)
			if (std::abs(tan_phi_max) < Constants::EPSILON || !std::isfinite(tan_phi_max)) {
				LOG_TRACE("  最大倾角 ({:.3f} rad) 的正切值无效或接近零，无法转弯，半径视为无穷大。", maxBankRad);
				return Constants::INF; // 无法倾斜或倾角太大，无法协调转弯
			}

			// 标准协调转弯公式: R = V^2 / (g * tan(phi_max))
			double radius = (speed * speed) / (g * tan_phi_max);
			LOG_DEBUG("  速度={:.2f}, g={:.2f}, 最大倾角={:.1f} deg, 最小转弯半径={:.2f} m", speed, g, maxBankAngleDeg, radius);
			return std::max(0.0, radius); // 半径不能为负
		}

		/**
		 * @brief 获取最大允许的倾斜角 (弧度)。
		 */
		double FixedWingDynamics::getMaxBankAngle(const UavState& state) const {
			// 从 owner_ 获取参数 "dynamics.fw.maxBankAngle"，如果找不到则使用默认值 30 度
			double max_angle_deg = owner_.getParamOrDefault<double>("dynamics.fw.maxBankAngle", 30.0);
			double angle_rad = max_angle_deg * Constants::DEG_TO_RAD; // 转换为弧度
			LOG_TRACE("获取固定翼最大倾斜角 (所有者 ID: {}): {:.2f} 度 ({:.3f} rad)", owner_.getId(), max_angle_deg, angle_rad);
			return std::clamp(angle_rad, 0.0, Constants::HALF_PI - Constants::ANGLE_EPSILON); // 限制在 [0, pi/2)
		}

		/**
		 * @brief 获取最小运行速度 (即失速速度, m/s)。
		 */
		double FixedWingDynamics::getMinOperationalSpeed(const UavState& state) const {
			// 从 owner_ 获取参数 "dynamics.fw.stallSpeed"，如果找不到则使用基类默认值
			// 基类默认值可能为 0，这对于固定翼不合适，因此这里提供一个稍高的默认值
			double minSpeed = owner_.getParamOrDefault<double>("dynamics.fw.stallSpeed", 10.0); // 默认 10 m/s
			LOG_TRACE("获取固定翼最小运行速度 (失速速度, 所有者 ID: {}): {:.2f} m/s", owner_.getId(), minSpeed);
			return std::max(0.0, minSpeed); // 确保非负
		}

		/**
		 * @brief 获取最大水平减速度 (m/s^2)。
		 */
		double FixedWingDynamics::getMaxHorizontalDeceleration(const UavState& state) const {
			// 从 owner_ 获取参数 "dynamics.fw.maxDecel"
			// 如果找不到，回退到基类减速度参数 "dynamics.base.maxHDecel"
			// 如果基类也没有，回退到基类加速度 "dynamics.base.maxHAcc"
			double hAcc = getMaxHorizontalAcceleration(state); // 获取基类或特定类的加速度
			double baseDecel = owner_.getParamOrDefault<double>("dynamics.base.maxHDecel", hAcc); // 获取基类减速度
			double maxDecel = owner_.getParamOrDefault<double>("dynamics.fw.maxDecel", baseDecel); // 获取特定类减速度
			LOG_TRACE("获取固定翼最大水平减速度 (所有者 ID: {}): {:.2f} m/s^2", owner_.getId(), maxDecel);
			return std::max(0.0, maxDecel); // 确保非负
		}


		// --- isStateTransitionFeasible 实现 ---
		/**
		 * @brief 检查固定翼状态转换是否可行。
		 */
		bool FixedWingDynamics::isStateTransitionFeasible(const UavState& current, const UavState& next, Time dt) const {
			LOG_TRACE("检查固定翼状态转换可行性 (所有者 ID: {})：dt={:.4f}s", owner_.getId(), dt);
			// 检查时间步长
			if (dt <= Constants::TIME_EPSILON) {
				bool position_changed = (current.position - next.position).norm() > Constants::GEOMETRY_EPSILON;
				if (position_changed) {
					LOG_TRACE("  失败：时间步长 <= 0 ({:.4f}) 但位置改变。", dt);
					return false;
				}
				else {
					LOG_TRACE("  通过：时间步长 <= 0 且位置未变。");
					return true;
				}
			}

			// --- 1. 检查高度和速度限制 (使用当前模型的 getter 获取限制) ---
			if (!checkAltitudeLimit(next.position, getMaxAltitude(next))) {
				LOG_TRACE("  失败：下一状态高度 ({:.1f}) 超出最大限制 ({:.1f})。", next.position.altitude, getMaxAltitude(next));
				return false;
			}
			if (!checkSpeedLimit(next.velocity,
				getMaxHorizontalSpeed(next),
				getMaxClimbSpeed(next),      // 使用 getMaxClimbSpeed
				getMaxDescendSpeed(next),    // 使用 getMaxDescendSpeed
				getMinOperationalSpeed(next))) { // 使用 getMinOperationalSpeed (失速速度)
				LOG_TRACE("  失败：下一状态速度限制检查失败。");
				return false;
			}
			// 检查当前速度是否高于失速速度（如果当前在飞行）
			double current_speed = current.velocity.norm();
			double current_min_op_speed = getMinOperationalSpeed(current);
			if (current_speed > Constants::VELOCITY_EPSILON &&
				current_speed < current_min_op_speed - Constants::VELOCITY_EPSILON) {
				LOG_TRACE("  失败：当前速度 ({:.2f}) 低于最小运行速度 ({:.2f})。", current_speed, current_min_op_speed);
				return false;
			}
			LOG_TRACE("  基本限制检查（高度、速度）通过。");

			// --- 2. 检查加速度/减速度限制 ---
			Vector3D requiredAcc = (next.velocity - current.velocity) / dt; // 计算所需平均加速度
			double hAccMag = requiredAcc.head<2>().norm(); // 水平加速度大小
			double vAcc = requiredAcc.z();                 // 垂直加速度
			double hSpeedChange = next.velocity.head<2>().norm() - current.velocity.head<2>().norm(); // 水平速度变化量

			// 检查水平加速/减速 (使用特定模型的方法获取限制)
			double maxHAcc = getMaxHorizontalAcceleration(current);
			double maxHDecel = getMaxHorizontalDeceleration(current);
			if (hSpeedChange > Constants::VELOCITY_EPSILON) { // 如果是水平加速
				if (hAccMag > maxHAcc + Constants::VELOCITY_EPSILON) {
					LOG_TRACE("  失败：水平加速度检查失败 (需要 {:.2f} > 限制 {:.2f})。", hAccMag, maxHAcc);
					return false;
				}
			}
			else if (hSpeedChange < -Constants::VELOCITY_EPSILON) { // 如果是水平减速
				if (hAccMag > maxHDecel + Constants::VELOCITY_EPSILON) { // 检查加速度模长是否超过减速限制
					LOG_TRACE("  失败：水平减速度检查失败 (加速度模 {:.2f} > 限制 {:.2f})。", hAccMag, maxHDecel);
					return false;
				}
			} // 水平速度不变则跳过

			// 检查垂直加速度/减速度
			double maxVAcc = getMaxVerticalAcceleration(current);   // 向上加速度限制
			double maxVDecel = getMaxVerticalDeceleration(current); // 向下加速度（减速）限制
			if (vAcc > Constants::EPSILON) { // 如果是向上加速或向下减速
				if (vAcc > maxVAcc + Constants::VELOCITY_EPSILON) {
					LOG_TRACE("  失败：垂直向上加速度检查失败 (需要 {:.2f} > 限制 {:.2f})。", vAcc, maxVAcc);
					return false;
				}
			}
			else if (vAcc < -Constants::EPSILON) { // 如果是向下加速或向上减速
				if (std::abs(vAcc) > maxVDecel + Constants::VELOCITY_EPSILON) { // 检查绝对值是否超过减速度限制
					LOG_TRACE("  失败：垂直向下加速度/向上减速度检查失败 (需要 {:.2f} > 限制 {:.2f})。", std::abs(vAcc), maxVDecel);
					return false;
				}
			} // 垂直加速度接近零则跳过
			LOG_TRACE("  加速度/减速度限制检查通过。");

			// --- 3. 检查转弯约束 ---
			// 使用当前模型的 getter 获取限制，再调用基类静态方法进行检查
			double max_turn_rate = getMaxTurnRate(current);
			double min_turn_radius = getMinTurnRadius(current);
			if (!checkTurnLimit(current.velocity, next.velocity, dt, max_turn_rate, min_turn_radius)) {
				// checkTurnConstraint 内部会打印失败信息
				return false;
			}
			LOG_TRACE("  转弯约束检查通过。");

			LOG_TRACE("固定翼状态转换可行性检查通过 (所有者 ID: {})。", owner_.getId());
			return true; // 所有检查通过
		}

		// --- 力学计算实现 (简化占位符) ---
		/**
		 * @brief 计算固定翼升力 (简化)。
		 */
		Vector3D FixedWingDynamics::computeLiftForce(const UavState& state, double air_density) const {
			// 简化：假设升力主要用于平衡重力，并且方向总是垂直向上（忽略倾斜）
			// 需要质量信息，从 owner_ 获取物理参数
			// 键名示例: "physical.empty_weight"
			double empty_weight = owner_.getParamOrDefault<double>("physical.empty_weight", 1.5); // kg
			double payload_weight = owner_.getPayloadWeight(); // 获取当前载荷
			double total_mass = empty_weight + payload_weight; // 总质量

			// 返回一个垂直向上的力，大小等于重力
			double lift_magnitude = total_mass * Constants::GRAVITY; // 升力大小 N
			LOG_TRACE("计算固定翼升力 (简化)：总质量={:.2f}kg, 升力大小={:.2f}N (垂直向上)", total_mass, lift_magnitude);
			return Vector3D(0, 0, lift_magnitude);
		}

		/**
		 * @brief 计算固定翼阻力 (简化)。
		 */
		Vector3D FixedWingDynamics::computeDragForce(const UavState& state, double air_density) const {
			// 简化阻力模型: F_drag = - k * ||V|| * V
			// 从 owner_ 获取阻力系数参数
			// 键名示例: "dynamics.fw.drag_coeff_k"
			double drag_coeff_k = owner_.getParamOrDefault<double>("dynamics.fw.drag_coeff_k", 0.05); // 假设的阻力系数 k

			double speed = state.velocity.norm(); // 获取速度大小
			if (speed < Constants::VELOCITY_EPSILON) {
				LOG_TRACE("计算固定翼阻力(简化): 速度接近零，阻力为零。");
				return Vector3D::Zero(); // 速度为零则无阻力
			}

			Vector3D drag = -drag_coeff_k * speed * state.velocity; // 阻力与速度平方成正比，方向相反
			LOG_TRACE("计算固定翼阻力(简化): V={:.2f}, k={:.2f}, Fd=({:.2f},{:.2f},{:.2f})", speed, drag_coeff_k, drag.x(), drag.y(), drag.z());
			return drag;
		}

		/**
		 * @brief 计算固定翼所需推力 (简化)。
		 */
		Vector3D FixedWingDynamics::computeThrustForce(const UavState& state) const {
			// F_net = m * a = F_thrust + F_lift + F_drag + F_gravity
			// 简化：推力主要用于克服阻力，并提供沿速度方向的加速度（忽略升力对推力的直接需求，忽略爬升/下降）

			double empty_weight = owner_.getParamOrDefault<double>("physical.empty_weight", 1.5);
			double payload_weight = owner_.getPayloadWeight();
			double total_mass = empty_weight + payload_weight;

			// 空气密度通常从能量模型参数获取，或使用默认值
			double air_density = owner_.getParamOrDefault<double>("energy.fw.air_density", Constants::AIR_DENSITY_SEA_LEVEL_ISA);

			Vector3D drag = computeDragForce(state, air_density); // 计算阻力
			// 假设所需推力方向与当前速度方向一致
			Vector3D vel_dir = Vector3D::UnitX(); // 默认向前
			double speed = state.velocity.norm();
			if (speed > Constants::VELOCITY_EPSILON) {
				vel_dir = state.velocity.normalized(); // 获取速度方向
			}
			else {
				LOG_TRACE("计算固定翼推力：速度为零，无法确定推力方向，返回零推力。");
				return Vector3D::Zero(); // 速度为零，无法产生有效推力
			}

			// 计算沿速度方向的加速度
			double acc_along_vel = state.acceleration.dot(vel_dir);

			// 计算所需推力大小：克服阻力 + 提供加速度
			// F_thrust ≈ ||-F_drag|| + m * a_along_vel
			// 这里假设推力直接作用在速度方向，阻力与速度反向
			double thrust_magnitude = drag.norm() + total_mass * acc_along_vel;

			// 推力不能为负 (不能反推)
			thrust_magnitude = std::max(0.0, thrust_magnitude);

			Vector3D thrust = thrust_magnitude * vel_dir; // 计算推力向量
			LOG_TRACE("计算固定翼推力(简化)：阻力大小={:.2f}N, 沿速度加速度={:.2f}m/s², 推力大小={:.2f}N, 推力向量=({:.2f},{:.2f},{:.2f})",
				drag.norm(), acc_along_vel, thrust_magnitude, thrust.x(), thrust.y(), thrust.z());
			return thrust;
		}

	} // namespace NSUav
} // namespace NSDrones