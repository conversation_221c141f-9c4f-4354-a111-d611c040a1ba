// include/planning/itask_planner.h
#pragma once

#include "core/types.h"
#include "uav/uav_fwd.h"
#include "environment/environment_fwd.h"
#include "mission/task_strategies.h"
#include "planning/planning_types.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "algorithm/evaluator/itrajectory_evaluator.h"
#include <vector>
#include <memory>
#include <map>
#include <utility>
#include "nlohmann/json.hpp"

// 前向声明
namespace NSDrones {
	namespace NSMission { class ControlPoint; }
}

namespace NSDrones {
	namespace NSPlanning {

		/**
		 * @class ITaskPlanner
		 * @brief 简化为单机单任务的规划器接口
		 *
		 * 重构后的设计原则：
		 * - 只处理单机单子任务的规划
		 * - 多机协调在更高层处理
		 * - 职责清晰，易于实现和测试
		 *
		 * ## 核心职责
		 * - **子任务解析**: 解析子任务目标和约束条件
		 * - **单机路径规划**: 为单架无人机生成路径
		 * - **轨迹优化**: 进行平滑和时间参数化
		 * - **安全检查**: 验证路径的安全性和可行性
		 * - **结果封装**: 生成单机轨迹结果
		 */
		class ITaskPlanner {
		public:
			// === 生命周期管理 ===

			/**
			 * @brief 构造函数
			 * @throws DroneException 如果环境未初始化
			 * @note 所有算法组件（路径规划器、轨迹优化器等）都从Environment中获取
			 */
			ITaskPlanner();

			/** @brief 虚析构函数 */
			virtual ~ITaskPlanner() = default;

			// === 禁止拷贝和移动 ===
			ITaskPlanner(const ITaskPlanner&) = delete;
			ITaskPlanner& operator=(const ITaskPlanner&) = delete;
			ITaskPlanner(ITaskPlanner&&) = delete;
			ITaskPlanner& operator=(ITaskPlanner&&) = delete;

			// === 核心接口方法（重构为单机单任务） ===

			/**
			 * @brief 规划单机单任务（纯虚函数）
			 * @param request 单任务规划请求
			 * @return 单任务规划结果
			 *
			 * ## 规划流程
			 * 1. 解析子任务目标和约束条件
			 * 2. 为单架无人机生成几何路径
			 * 3. 进行轨迹优化和时间参数化
			 * 4. 执行安全性检查
			 * 5. 封装单机轨迹结果
			 */
			virtual SingleTaskPlanningResult planSingleTask(
				const SingleTaskPlanningRequest& request) = 0;

			/**
			 * @brief 检查是否支持指定的子任务类型（纯虚函数）
			 * @param sub_target 子任务目标
			 * @return 是否支持
			 */
			virtual bool isSubTaskSupported(const SubTaskTarget& sub_target) const = 0;

			/**
			 * @brief 初始化任务规划器（纯虚函数）
			 * @param params 配置参数对象
			 * @param raw_config 原始JSON配置
			 * @return 初始化成功返回true，否则返回false
			 */
			virtual bool initialize(std::shared_ptr<NSParams::ParamValues> params,
									const nlohmann::json& raw_config) = 0;

		protected:
			// === 环境和算法组件访问接口 ===

			/**
			 * @brief 获取环境实例
			 * @return 环境的共享指针，如果环境未初始化则返回nullptr
			 * @note 使用单例模式获取环境，子类可直接调用
			 */
			std::shared_ptr<NSEnvironment::Environment> getEnvironment() const;

			/**
			 * @brief 从环境获取路径规划器
			 * @return 路径规划器的共享指针，如果未配置则返回nullptr
			 */
			IPathPlannerPtr getPathPlanner() const;

			/**
			 * @brief 从环境获取轨迹优化器
			 * @return 轨迹优化器的共享指针，如果未配置则返回nullptr
			 */
			ITrajectoryOptimizerPtr getTrajectoryOptimizer() const;

			/**
			 * @brief 从环境获取轨迹评估器
			 * @return 轨迹评估器的共享指针，如果未配置则返回nullptr
			 */
			ITrajectoryEvaluatorPtr getTrajectoryEvaluator() const;

			// === 简化的辅助方法 ===

			/**
			 * @brief 验证子任务分配的基本信息
			 * @param request 单任务规划请求
			 * @param result 用于记录错误信息的结果对象
			 * @return 验证通过返回true，否则返回false
			 */
			bool validateSingleTaskRequest(const SingleTaskPlanningRequest& request,
										  SingleTaskPlanningResult& result) const;

			/**
			 * @brief 规划从起始点到目标点的路径
			 * @param start_state 起始状态
			 * @param target_wgs84 目标WGS84位置
			 * @param uav 执行路径的无人机
			 * @param sub_target 子任务目标（用于获取路径约束）
			 * @param result 用于记录错误信息的结果对象
			 * @return 成功返回路径点列表，失败返回空列表
			 */
			std::vector<NSCore::WGS84Point> planPathToTarget(
				const NSUav::UavState& start_state,
				const NSCore::WGS84Point& target_wgs84,
				const NSUav::UavPtr& uav,
				const SubTaskTarget& sub_target,
				SingleTaskPlanningResult& result) const;

			/**
			 * @brief 生成简单的直线轨迹段
			 * @param start_point 起始轨迹点（包含时间、速度等）
			 * @param end_wgs84_pos 目标WGS84位置
			 * @param speed 期望的恒定速度
			 * @param dynamics 无人机动力学模型
			 * @param uav_id 无人机ID
			 * @return 生成的轨迹段（包含起点和终点），如果速度无效则为空
			 */
			TrajectorySegment generateLinearSegment(const TrajectoryPoint& start_point,
				const NSCore::WGS84Point& end_wgs84_pos,
				double speed,
				const NSUav::IDynamicModel& dynamics,
				const NSUtils::ObjectID& uav_id) const;

			/**
			 * @brief 几何路径平滑和时间参数化
			 *
			 * 执行完整的轨迹处理流程：
			 * 1. 匀速时间参数化
			 * 2. 轨迹优化（如果配置了优化器）
			 * 3. 应用任务策略
			 *
			 * @param geometric_path 输入的几何路径点（WGS84坐标）
			 * @param uav 执行此路径的无人机指针
			 * @param start_state 无人机在路径起点的状态
			 * @param desired_speed 期望速度（m/s）
			 * @param optimized_trajectory 输出参数：优化后的轨迹
			 * @param result_ptr 指向SingleTaskPlanningResult的指针（可选，用于添加告警）
			 * @param strategies 应用于此轨迹的策略（可选）
			 * @return 成功生成有效轨迹返回true
			 */
			bool smoothAndTimeParameterize(const std::vector<NSCore::WGS84Point>& geometric_path,
				const NSUav::UavPtr& uav,
				const NSUav::UavState& start_state,
				double desired_speed,
				Trajectory& optimized_trajectory,
				SingleTaskPlanningResult* result_ptr = nullptr,
				const NSMission::ITaskStrategyMap& strategies = {});

			/**
			 * @brief 获取无人机的起始状态
			 * @param uav 无人机指针
			 * @param start_state 输出的起始状态
			 * @return 成功获取返回true
			 */
			bool getUavStartState(const NSUav::UavPtr& uav, NSUav::UavState& start_state) const;

		private:
			// === 简化的安全检查方法 ===

			/**
			 * @brief 检查轨迹是否满足安全约束
			 * @param trajectory 要检查的轨迹
			 * @param uav_id 无人机ID
			 * @param sub_task_id 子任务ID
			 * @param result 规划结果，用于添加告警
			 * @return 满足安全约束返回true
			 */
			bool checkTrajectoryConstraints(const Trajectory& trajectory,
										   const NSUtils::ObjectID& uav_id,
										   const NSUtils::ObjectID& sub_task_id,
										   SingleTaskPlanningResult& result) const;

		};

		// === 类型别名定义 ===
		using TaskPlannerPtr = std::shared_ptr<ITaskPlanner>;           ///< 任务规划器智能指针类型

	} // namespace NSPlanning
} // namespace NSDrones