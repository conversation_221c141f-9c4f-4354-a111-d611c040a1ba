#include "core/geometry/shapes/polygon_shape.h"
#include <stdexcept>
#include <cmath>
#include <algorithm>

namespace NSDrones {
namespace NSCore {

    PolygonShape::PolygonShape(const std::vector<fcl::Vector3d>& vertices) 
        : vertices_(vertices), fcl_convex_(nullptr), geometry_dirty_(true) {
        validateVertices();
        computeNormal();
    }

    PolygonShape::PolygonShape() : fcl_convex_(nullptr), geometry_dirty_(true) {
        // 创建单位三角形
        vertices_ = {
            fcl::Vector3d(0.0, 0.0, 0.0),
            fcl::Vector3d(1.0, 0.0, 0.0),
            fcl::Vector3d(0.5, 1.0, 0.0)
        };
        computeNormal();
    }

    PolygonShape::PolygonShape(const PolygonShape& other)
        : vertices_(other.vertices_), normal_(other.normal_), 
          fcl_convex_(nullptr), geometry_dirty_(true) {
    }

    PolygonShape& PolygonShape::operator=(const PolygonShape& other) {
        if (this != &other) {
            vertices_ = other.vertices_;
            normal_ = other.normal_;
            fcl_convex_.reset();  // 重置FCL对象，延迟重新创建
            geometry_dirty_ = true;
        }
        return *this;
    }

    std::shared_ptr<fcl::CollisionGeometryd> PolygonShape::getFCLGeometry() const {
        ensureFCLObject();
        return fcl_convex_;
    }

    fcl::AABBd PolygonShape::getAABB(const fcl::Transform3d& transform) const {
        if (vertices_.empty()) {
            return fcl::AABBd();
        }

        fcl::Vector3d min_point = transform * vertices_[0];
        fcl::Vector3d max_point = min_point;

        for (size_t i = 1; i < vertices_.size(); ++i) {
            fcl::Vector3d transformed_vertex = transform * vertices_[i];
            min_point = min_point.cwiseMin(transformed_vertex);
            max_point = max_point.cwiseMax(transformed_vertex);
        }

        return fcl::AABBd(min_point, max_point);
    }

    double PolygonShape::getSurfaceArea() const {
        return getArea();
    }

    fcl::Vector3d PolygonShape::getCentroid() const {
        if (vertices_.empty()) {
            return fcl::Vector3d::Zero();
        }

        fcl::Vector3d centroid = fcl::Vector3d::Zero();
        for (const auto& vertex : vertices_) {
            centroid += vertex;
        }
        return centroid / static_cast<double>(vertices_.size());
    }

    fcl::Matrix3d PolygonShape::getInertiaMatrix(double mass) const {
        // 简化的2D多边形惯性张量计算
        fcl::Vector3d centroid = getCentroid();
        fcl::Matrix3d I = fcl::Matrix3d::Zero();
        
        double area = getArea();
        if (area > 1e-12) {
            // 对于平面多边形，主要的惯性矩在垂直于平面的方向
            double I_perpendicular = 0.0;
            for (const auto& vertex : vertices_) {
                fcl::Vector3d r = vertex - centroid;
                I_perpendicular += r.squaredNorm();
            }
            I_perpendicular *= mass / vertices_.size();
            
            // 设置惯性张量（简化处理）
            I = fcl::Matrix3d::Identity() * I_perpendicular;
        }
        
        return I;
    }

    std::unique_ptr<IShape> PolygonShape::clone() const {
        return std::make_unique<PolygonShape>(*this);
    }

    nlohmann::json PolygonShape::serialize() const {
        nlohmann::json j;
        j["type"] = "POLYGON";
        j["vertices"] = nlohmann::json::array();
        
        for (const auto& vertex : vertices_) {
            j["vertices"].push_back({vertex.x(), vertex.y(), vertex.z()});
        }
        
        return j;
    }

    bool PolygonShape::deserialize(const nlohmann::json& json) {
        try {
            if (json["type"] != "POLYGON") {
                return false;
            }
            
            vertices_.clear();
            for (const auto& vertex_json : json["vertices"]) {
                vertices_.emplace_back(vertex_json[0], vertex_json[1], vertex_json[2]);
            }
            
            validateVertices();
            computeNormal();
            markGeometryDirty();
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    std::string PolygonShape::toString() const {
        std::string result = "Polygon(" + std::to_string(vertices_.size()) + " vertices: ";
        for (size_t i = 0; i < vertices_.size(); ++i) {
            if (i > 0) result += ", ";
            const auto& v = vertices_[i];
            result += "[" + std::to_string(v.x()) + ", " + std::to_string(v.y()) + ", " + std::to_string(v.z()) + "]";
        }
        result += ")";
        return result;
    }

    bool PolygonShape::containsPoint(const fcl::Vector3d& point) const {
        // 简化的点在多边形内判断（2D投影）
        return containsPoint2D(point);
    }

    double PolygonShape::distanceToPoint(const fcl::Vector3d& point) const {
        if (vertices_.empty()) {
            return std::numeric_limits<double>::infinity();
        }

        double min_distance = std::numeric_limits<double>::max();
        
        // 计算到每条边的距离
        for (size_t i = 0; i < vertices_.size(); ++i) {
            size_t next_i = (i + 1) % vertices_.size();
            
            fcl::Vector3d edge_start = vertices_[i];
            fcl::Vector3d edge_end = vertices_[next_i];
            fcl::Vector3d edge_vec = edge_end - edge_start;
            fcl::Vector3d point_vec = point - edge_start;
            
            double edge_length_sq = edge_vec.squaredNorm();
            if (edge_length_sq < 1e-12) {
                // 退化边
                min_distance = std::min(min_distance, point_vec.norm());
                continue;
            }
            
            double t = point_vec.dot(edge_vec) / edge_length_sq;
            t = std::max(0.0, std::min(1.0, t));
            
            fcl::Vector3d closest_point = edge_start + t * edge_vec;
            double distance = (point - closest_point).norm();
            min_distance = std::min(min_distance, distance);
        }
        
        return min_distance;
    }

    double PolygonShape::getCharacteristicSize() const {
        if (vertices_.size() < 2) {
            return 0.0;
        }
        
        // 返回最大边长作为特征尺寸
        double max_edge_length = 0.0;
        for (size_t i = 0; i < vertices_.size(); ++i) {
            size_t next_i = (i + 1) % vertices_.size();
            double edge_length = (vertices_[next_i] - vertices_[i]).norm();
            max_edge_length = std::max(max_edge_length, edge_length);
        }
        
        return max_edge_length;
    }

    void PolygonShape::setVertices(const std::vector<fcl::Vector3d>& vertices) {
        vertices_ = vertices;
        validateVertices();
        computeNormal();
        markGeometryDirty();
    }

    void PolygonShape::addVertex(const fcl::Vector3d& vertex) {
        vertices_.push_back(vertex);
        computeNormal();
        markGeometryDirty();
    }

    double PolygonShape::getArea() const {
        if (vertices_.size() < 3) {
            return 0.0;
        }
        
        // 使用三角剖分计算面积
        double total_area = 0.0;
        fcl::Vector3d v0 = vertices_[0];
        
        for (size_t i = 1; i < vertices_.size() - 1; ++i) {
            fcl::Vector3d v1 = vertices_[i];
            fcl::Vector3d v2 = vertices_[i + 1];
            
            fcl::Vector3d cross = (v1 - v0).cross(v2 - v0);
            total_area += 0.5 * cross.norm();
        }
        
        return total_area;
    }

    double PolygonShape::getPerimeter() const {
        if (vertices_.size() < 2) {
            return 0.0;
        }
        
        double perimeter = 0.0;
        for (size_t i = 0; i < vertices_.size(); ++i) {
            size_t next_i = (i + 1) % vertices_.size();
            perimeter += (vertices_[next_i] - vertices_[i]).norm();
        }
        
        return perimeter;
    }

    bool PolygonShape::isConvex() const {
        if (vertices_.size() < 3) {
            return true;
        }
        
        // 检查所有内角是否都小于180度
        bool sign_positive = false;
        bool sign_negative = false;
        
        for (size_t i = 0; i < vertices_.size(); ++i) {
            size_t prev_i = (i + vertices_.size() - 1) % vertices_.size();
            size_t next_i = (i + 1) % vertices_.size();
            
            fcl::Vector3d v1 = vertices_[i] - vertices_[prev_i];
            fcl::Vector3d v2 = vertices_[next_i] - vertices_[i];
            
            double cross_z = v1.cross(v2).dot(normal_);
            
            if (cross_z > 1e-12) {
                sign_positive = true;
            } else if (cross_z < -1e-12) {
                sign_negative = true;
            }
            
            if (sign_positive && sign_negative) {
                return false;  // 既有正角又有负角，不是凸多边形
            }
        }
        
        return true;
    }

    bool PolygonShape::containsPoint2D(const fcl::Vector3d& point) const {
        if (vertices_.size() < 3) {
            return false;
        }
        
        // 使用射线法判断点是否在多边形内
        int intersections = 0;
        
        for (size_t i = 0; i < vertices_.size(); ++i) {
            size_t next_i = (i + 1) % vertices_.size();
            
            fcl::Vector3d v1 = vertices_[i];
            fcl::Vector3d v2 = vertices_[next_i];
            
            // 简化的2D投影（忽略Z坐标）
            if (((v1.y() > point.y()) != (v2.y() > point.y())) &&
                (point.x() < (v2.x() - v1.x()) * (point.y() - v1.y()) / (v2.y() - v1.y()) + v1.x())) {
                intersections++;
            }
        }
        
        return (intersections % 2) == 1;
    }

    std::unique_ptr<PolygonShape> PolygonShape::createTriangle() {
        return std::make_unique<PolygonShape>();  // 默认构造函数创建三角形
    }

    std::unique_ptr<PolygonShape> PolygonShape::createSquare() {
        std::vector<fcl::Vector3d> vertices = {
            fcl::Vector3d(0.0, 0.0, 0.0),
            fcl::Vector3d(1.0, 0.0, 0.0),
            fcl::Vector3d(1.0, 1.0, 0.0),
            fcl::Vector3d(0.0, 1.0, 0.0)
        };
        return std::make_unique<PolygonShape>(vertices);
    }

    std::unique_ptr<PolygonShape> PolygonShape::createRegularPolygon(int sides, double radius) {
        if (sides < 3) {
            throw std::invalid_argument("正多边形至少需要3条边");
        }
        
        std::vector<fcl::Vector3d> vertices;
        vertices.reserve(sides);
        
        for (int i = 0; i < sides; ++i) {
            double angle = 2.0 * M_PI * i / sides;
            vertices.emplace_back(radius * std::cos(angle), radius * std::sin(angle), 0.0);
        }
        
        return std::make_unique<PolygonShape>(vertices);
    }

    std::unique_ptr<PolygonShape> PolygonShape::createRectangle(double width, double height) {
        std::vector<fcl::Vector3d> vertices = {
            fcl::Vector3d(0.0, 0.0, 0.0),
            fcl::Vector3d(width, 0.0, 0.0),
            fcl::Vector3d(width, height, 0.0),
            fcl::Vector3d(0.0, height, 0.0)
        };
        return std::make_unique<PolygonShape>(vertices);
    }

    void PolygonShape::ensureFCLObject() const {
        if (!fcl_convex_ || geometry_dirty_) {
            if (vertices_.size() >= 3) {
                // 创建shared_ptr包装的顶点向量
                auto vertices_ptr = std::make_shared<std::vector<fcl::Vector3d>>();
                vertices_ptr->reserve(vertices_.size());
                for (const auto& vertex : vertices_) {
                    vertices_ptr->emplace_back(vertex.x(), vertex.y(), vertex.z());
                }

                // 对于多边形，我们不需要面信息（传入nullptr）
                // 使用新的FCL API创建Convex对象
                fcl_convex_ = std::make_shared<fcl::Convexd>(
                    vertices_ptr,
                    0,  // 面的数量为0（多边形没有面信息）
                    nullptr,  // 面索引为空
                    true  // 是否拥有数据
                );
                geometry_dirty_ = false;
            }
        }
    }

    void PolygonShape::validateVertices() const {
        if (vertices_.size() < 3) {
            throw std::invalid_argument("PolygonShape: 多边形至少需要3个顶点");
        }
        
        for (const auto& vertex : vertices_) {
            if (!vertex.allFinite()) {
                throw std::invalid_argument("PolygonShape: 所有顶点坐标必须为有限数值");
            }
        }
    }

    void PolygonShape::computeNormal() {
        if (vertices_.size() < 3) {
            normal_ = fcl::Vector3d::UnitZ();
            return;
        }
        
        // 使用前三个顶点计算法向量
        fcl::Vector3d v1 = vertices_[1] - vertices_[0];
        fcl::Vector3d v2 = vertices_[2] - vertices_[0];
        normal_ = v1.cross(v2);
        
        double norm = normal_.norm();
        if (norm > 1e-12) {
            normal_ /= norm;
        } else {
            normal_ = fcl::Vector3d::UnitZ();  // 默认法向量
        }
    }

    void PolygonShape::markGeometryDirty() {
        geometry_dirty_ = true;
        fcl_convex_.reset();
    }

} // namespace NSCore
} // namespace NSDrones
