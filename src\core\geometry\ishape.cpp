#include "core/geometry/ishape.h"
#include "core/geometry/shapes/point_shape.h"
#include "core/geometry/shapes/line_shape.h"
#include "core/geometry/shapes/plane_shape.h"
#include "core/geometry/shapes/polygon_shape.h"
#include "core/geometry/shapes/box_shape.h"
#include "core/geometry/shapes/sphere_shape.h"
#include "core/geometry/shapes/cylinder_shape.h"
#include "core/geometry/shapes/compound_shape.h"
#include "utils/enum_utils.h"
#include <stdexcept>
#include <cmath>
namespace NSDrones {
namespace NSCore {

    // FCL变换矩阵创建工具
    fcl::Transform3d createTransform(const fcl::Vector3d& translation, const fcl::Matrix3d& rotation) {
        fcl::Transform3d transform;
        transform.setIdentity();
        transform.translation() = translation;
        transform.linear() = rotation;
        return transform;
    }

    fcl::Transform3d createTransformFromEuler(const fcl::Vector3d& translation,
                                             double roll, double pitch, double yaw) {
        // 使用ZYX欧拉角顺序（航空航天标准）
        fcl::Matrix3d rotation;
        
        double cr = std::cos(roll);
        double sr = std::sin(roll);
        double cp = std::cos(pitch);
        double sp = std::sin(pitch);
        double cy = std::cos(yaw);
        double sy = std::sin(yaw);
        
        rotation << cy * cp, cy * sp * sr - sy * cr, cy * sp * cr + sy * sr,
                   sy * cp, sy * sp * sr + cy * cr, sy * sp * cr - cy * sr,
                   -sp,     cp * sr,                cp * cr;
        
        return createTransform(translation, rotation);
    }

    fcl::Transform3d createTransformFromQuaternion(const fcl::Vector3d& translation,
                                                  const Eigen::Vector4d& quaternion) {
        // 四元数格式: [w, x, y, z]
        double w = quaternion[0];
        double x = quaternion[1];
        double y = quaternion[2];
        double z = quaternion[3];
        
        // 归一化四元数
        double norm = std::sqrt(w*w + x*x + y*y + z*z);
        if (norm < 1e-10) {
            throw std::invalid_argument("四元数模长过小，无法归一化");
        }
        w /= norm; x /= norm; y /= norm; z /= norm;
        
        // 转换为旋转矩阵
        fcl::Matrix3d rotation;
        rotation << 1 - 2*(y*y + z*z), 2*(x*y - w*z),     2*(x*z + w*y),
                   2*(x*y + w*z),     1 - 2*(x*x + z*z), 2*(y*z - w*x),
                   2*(x*z - w*y),     2*(y*z + w*x),     1 - 2*(x*x + y*y);
        
        return createTransform(translation, rotation);
    }

    // IShape静态工厂方法实现
    std::unique_ptr<IShape> IShape::createFromJson(const nlohmann::json& json) {
        if (!json.contains("type")) {
            throw std::invalid_argument("JSON中缺少形状类型信息");
        }

        std::string type_str = json["type"].get<std::string>();

        std::unique_ptr<IShape> shape = createByTypeName(type_str);
        if (!shape) {
            throw std::runtime_error("无法创建形状类型: " + type_str);
        }

        if (!shape->deserialize(json)) {
            throw std::runtime_error("形状反序列化失败");
        }

        return shape;
    }

    std::unique_ptr<IShape> IShape::createByTypeName(const std::string& type_name) {
        try {
            // 使用enum_utils进行字符串到枚举的转换
            auto type_opt = NSUtils::stringToEnum<ShapeType>(type_name, ShapeType::UNKNOWN);
            if (type_opt == ShapeType::UNKNOWN) {
                return nullptr;
            }

            ShapeType type = type_opt;
            switch (type) {
                // 基础几何元素
                case ShapeType::POINT:
                    return std::make_unique<PointShape>();
                case ShapeType::LINE:
                    return std::make_unique<LineShape>();
                case ShapeType::PLANE:
                    return std::make_unique<PlaneShape>();
                case ShapeType::POLYGON:
                    return std::make_unique<PolygonShape>();

                // 3D基础形状
                case ShapeType::BOX:
                    return std::make_unique<BoxShape>();
                case ShapeType::SPHERE:
                    return std::make_unique<SphereShape>();
                case ShapeType::CYLINDER:
                    return std::make_unique<CylinderShape>();

                // 复杂形状
                case ShapeType::COMPOUND:
                    return std::make_unique<CompoundShape>();

                default:
                    return nullptr;
            }
        } catch (const std::exception&) {
            return nullptr;
        }
    }

    // 基础几何元素创建方法实现
    std::unique_ptr<IShape> IShape::createPoint(const fcl::Vector3d& position) {
        return std::make_unique<PointShape>(position);
    }

    std::unique_ptr<IShape> IShape::createLine(const fcl::Vector3d& start, const fcl::Vector3d& end) {
        return std::make_unique<LineShape>(start, end);
    }

    std::unique_ptr<IShape> IShape::createPlane(const fcl::Vector3d& normal, double distance) {
        return std::make_unique<PlaneShape>(normal, distance);
    }

    std::unique_ptr<IShape> IShape::createPolygon(const std::vector<fcl::Vector3d>& vertices) {
        return std::make_unique<PolygonShape>(vertices);
    }

} // namespace NSCore
} // namespace NSDrones