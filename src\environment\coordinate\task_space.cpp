// src/environment/coordinate/task_space.cpp
#include "environment/coordinate/task_space.h"
#include "environment/environment.h"
#include "core/entity_object.h"
#include "params/parameters.h"
#include "utils/logging.h"
#include <stdexcept>
#include <sstream>
#include <iomanip>

namespace NSDrones {
	namespace NSEnvironment {

		//=== 静态基准点策略实现 ===//
		StaticOriginStrategy::StaticOriginStrategy(const WGS84Point& point)
			: static_origin_(point) {
			LOG_DEBUG("任务空间: 创建静态基准点策略，位置: ({})", point.toString());
		}

		WGS84Point StaticOriginStrategy::getOrigin() const {
			return static_origin_;
		}

		void StaticOriginStrategy::setOrigin(const WGS84Point& position) {
			// 静态策略不支持位置更新，这是空操作
			LOG_DEBUG("任务空间: 静态基准点策略忽略位置更新请求");
		}

		std::string StaticOriginStrategy::getType() const {
			return "static";
		}

		bool StaticOriginStrategy::canSetOrigin(ObjectID entity_id) const {
			// 静态策略永远不需要更新
			return false;
		}

		//=== 动态实体策略实现 ===//

		DynamicOriginStrategy::DynamicOriginStrategy(ObjectID entity_id)
			: reference_entity_id_(entity_id) {
			LOG_DEBUG("任务空间: 创建动态实体策略，跟随实体ID: {}", entity_id);
		}

		WGS84Point DynamicOriginStrategy::getOrigin() const {
			if (!initialized_) {
				LOG_ERROR("任务空间: 动态实体策略未初始化，无法获取基准点");
				throw std::runtime_error("动态实体策略未初始化");
			}
			return current_position_;
		}

		void DynamicOriginStrategy::setOrigin(const WGS84Point& position) {
			current_position_ = position;
			initialized_ = true;
			LOG_DEBUG("任务空间: 动态实体策略更新位置: ({})", position.toString());
		}

		std::string DynamicOriginStrategy::getType() const {
			return "dynamic_entity";
		}

		bool DynamicOriginStrategy::canSetOrigin(ObjectID entity_id) const {
			return entity_id == reference_entity_id_;
		}

		//=== TaskSpace 实现 ===//

		TaskSpace::TaskSpace(ObjectID id,
			const std::string& object_type_key,
			const std::string& name,
			std::unique_ptr<IOriginStrategy> strategy)
			: BaseObject(id, object_type_key, name)
			, origin_strategy_(std::move(strategy)) {

			LOG_INFO("任务空间: 创建任务空间 '{}' (类型: {}, 策略: {})",
				id, object_type_key, origin_strategy_->getType());
		}

		TaskSpace::~TaskSpace() {
			LOG_DEBUG("任务空间: 销毁任务空间 '{}'", getId());
		}

		bool TaskSpace::initialize(std::shared_ptr<NSParams::ParamValues> final_params,
			const nlohmann::json& raw_instance_json_config) {
			LOG_INFO("任务空间: 开始初始化TaskSpace '{}'", getId());

			// 调用基类的initialize方法
			if (!BaseObject::initialize(final_params, raw_instance_json_config)) {
				LOG_ERROR("任务空间: BaseObject::initialize失败，TaskSpace '{}' 初始化终止", getId());
				return false;
			}

			LOG_INFO("任务空间: TaskSpace '{}' 初始化完成", getId());
			return true;
		}

		WGS84Point TaskSpace::getOrigin() const {
			return origin_strategy_->getOrigin();
		}

		//=== TaskSpace 坐标转换方法 ===//

		NedPoint TaskSpace::wgs84ToNED(const WGS84Point& wgs84_pos) const {
			return performWGS84ToNED(wgs84_pos, getSafeOrigin());
		}

		WGS84Point TaskSpace::nedToWGS84(const NedPoint& ned_pos) const {
			return performnedToWGS84(ned_pos, getSafeOrigin());
		}

		std::vector<NedPoint> TaskSpace::batchWgs84ToNED(const std::vector<WGS84Point>& wgs84_points) const {
			WGS84Point reference = getSafeOrigin();

			std::vector<NedPoint> results;
			results.reserve(wgs84_points.size());

			for (const auto& point : wgs84_points) {
				results.push_back(performWGS84ToNED(point, reference));
			}

			return results;
		}

		std::vector<WGS84Point> TaskSpace::batchnedToWGS84(const std::vector<NedPoint>& ned_points) const {
			WGS84Point reference = getSafeOrigin();

			std::vector<WGS84Point> results;
			results.reserve(ned_points.size());

			for (const auto& point : ned_points) {
				results.push_back(performnedToWGS84(point, reference));
			}

			return results;
		}

		NedPoint TaskSpace::performWGS84ToNED(const WGS84Point& wgs84_pos, const WGS84Point& reference) const {
			auto local_cartesian = getOrCreateLocalCartesian(reference);

			double x, y, z;
			local_cartesian->Forward(wgs84_pos.latitude, wgs84_pos.longitude, wgs84_pos.altitude, x, y, z);

			// GeographicLib 返回的是 ENU 坐标，需要转换为 NED
			return NedPoint{ y, x, -z };  // ENU -> NED: (E,N,U) -> (N,E,D)
		}

		WGS84Point TaskSpace::performnedToWGS84(const NedPoint& ned_pos, const WGS84Point& reference) const {
			auto local_cartesian = getOrCreateLocalCartesian(reference);

			// NED -> ENU: (N,E,D) -> (E,N,U)
			double x = ned_pos.east();  // E
			double y = ned_pos.north();  // N
			double z = -ned_pos.down(); // U

			double lat, lon, alt;
			local_cartesian->Reverse(x, y, z, lat, lon, alt);

			return WGS84Point{ lon, lat, alt }; // WGS84Point构造函数: (经度, 纬度, 高度)
		}

		std::shared_ptr<GeographicLib::LocalCartesian> TaskSpace::getOrCreateLocalCartesian(
			const WGS84Point& reference_point) const {
			// 检查缓存是否有效（基准点是否改变）
			if (!local_cartesian_ ||
				std::abs(origin_point_.latitude - reference_point.latitude) > 1e-9 ||
				std::abs(origin_point_.longitude - reference_point.longitude) > 1e-9 ||
				std::abs(origin_point_.altitude - reference_point.altitude) > 1e-3) {

				// 创建新的LocalCartesian对象
				local_cartesian_ = std::make_shared<GeographicLib::LocalCartesian>(
					reference_point.latitude, reference_point.longitude, reference_point.altitude);
				origin_point_ = reference_point;

				LOG_DEBUG("任务空间: 更新LocalCartesian缓存，新基准点: ({})", reference_point.toString());
			}

			return local_cartesian_;
		}

		bool TaskSpace::canSetOrigin(ObjectID entity_id) const {
			return origin_strategy_->canSetOrigin(entity_id);
		}

		WGS84Point TaskSpace::getSafeOrigin() const {
			if (!origin_strategy_) {
				throw std::runtime_error("TaskSpace: 基准点策略未初始化");
			}
			return origin_strategy_->getOrigin();
		}

		// 对象管理方法实现
		void TaskSpace::registerObject(ObjectID object_id) {
			std::lock_guard<std::mutex> lock(objects_mutex_);
			registered_objects_.insert(object_id);
			LOG_DEBUG("任务空间 '{}': 注册对象 ID={}", getId(), object_id);
		}

		void TaskSpace::unregisterObject(ObjectID object_id) {
			std::lock_guard<std::mutex> lock(objects_mutex_);
			registered_objects_.erase(object_id);
			LOG_DEBUG("任务空间 '{}': 注销对象 ID={}", getId(), object_id);
		}

		std::unordered_set<ObjectID> TaskSpace::getRegisteredObjects() const {
			std::lock_guard<std::mutex> lock(objects_mutex_);
			return registered_objects_;
		}

		void TaskSpace::updateOriginAndNotifyObjects(const WGS84Point& new_origin) {
			// 更新基准点
			origin_strategy_->setOrigin(new_origin);

			// 获取 Environment 实例
			auto env = Environment::getInstance();
			if (!env) {
				LOG_WARN("任务空间 '{}': Environment 实例不存在，无法通知对象更新坐标", getId());
				return;
			}

			// 通知所有注册的对象更新其局部坐标
			std::unordered_set<ObjectID> objects_copy;
			{
				std::lock_guard<std::mutex> lock(objects_mutex_);
				objects_copy = registered_objects_;
			}

			for (ObjectID object_id : objects_copy) {
				auto entity = env->getMutableObjectById(object_id);
				if (entity) {
					// 通知对象任务空间基准点已变更
					entity->onTaskSpaceOriginChanged(getId(), new_origin);
					LOG_DEBUG("任务空间 '{}': 已通知对象 ID={} 基准点变更", getId(), object_id);
				} else {
					LOG_WARN("任务空间 '{}': 找不到对象 ID={}，从注册列表中移除", getId(), object_id);
					std::lock_guard<std::mutex> lock(objects_mutex_);
					registered_objects_.erase(object_id);
				}
			}

			LOG_INFO("任务空间 '{}': 基准点更新完成，已通知 {} 个对象", getId(), objects_copy.size());
		}

	} // namespace NSEnvironment
} // namespace NSDrones

