{"description": "区域扫描任务规划器参数定义，用于规划覆盖指定区域的扫描路径。", "parameters": [{"key": "scan.altitude", "type": "double", "name": "扫描高度", "description": "执行区域扫描的飞行高度 (米)。", "default": 50.0, "constraints": {"type": "numeric", "min": 10.0, "max": 500.0}, "required": false}, {"key": "scan.speed", "type": "double", "name": "扫描速度", "description": "执行扫描时的飞行速度 (米/秒)。", "default": 10.0, "constraints": {"type": "numeric", "min": 2.0, "max": 25.0}, "required": false}, {"key": "scan.overlap_ratio", "type": "double", "name": "重叠率", "description": "相邻扫描条带之间的重叠比例 (0.0-1.0)。", "default": 0.3, "constraints": {"type": "numeric", "min": 0.0, "max": 0.8}, "required": false}, {"key": "task_planner.scan.pattern", "type": "string", "enum_type": "NSDrones::NSPlanning::ScanPattern", "name": "扫描模式", "description": "区域扫描的路径模式。", "default": "ZIGZAG", "enum_values": ["ZIGZAG", "PARALLEL", "SPIRAL"], "required": false}]}