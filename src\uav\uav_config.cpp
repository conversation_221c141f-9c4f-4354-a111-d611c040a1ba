// src/uav/uav_config.cpp
#include "uav/uav_config.h"
#include "uav/uav_types.h"
#include "core/types.h"
#include "utils/logging.h"
#include "params/param_defs.h"
#include <fstream>
#include <stdexcept>
#include <nlohmann/json.hpp> 
#include <typeinfo>          

namespace NSDrones {
	namespace NSUav {

		/**
		 * @brief UavConfig 构造函数。
		 * @param id 配置 ID。
		 * @param uav_type 无人机类型。
		 * @param physicalProps 物理属性参数。
		 * @param dyn_params 动力学参数。
		 * @param energy_params 能量参数。
		 * @throws DroneException 如果参数无效或类型不匹配。
		 */
		UavConfig::UavConfig(ObjectID id,
			UavType uav_type,
			std::shared_ptr<const NSParams::UAVPhysicalProperties> physicalProps,
			std::shared_ptr<const NSParams::IDynamicParams> dyn_params,
			std::shared_ptr<const NSParams::IEnergyParams> energy_params)
			: id_(std::move(id)),          // 初始化 ID
			type_(uav_type),           // 初始化类型
			name_(id_),                // 默认名称使用 ID
			physical_properties_(std::move(physicalProps)), // 初始化物理属性指针
			dynamics_params_(std::move(dyn_params)),       // 初始化动力学参数指针
			energy_params_(std::move(energy_params))       // 初始化能量参数指针
		{
			// --- 输入验证 ---
			if (!NSUtils::isValidObjectID(id_)) {
				throw DroneException("UAVConfig ID 不能为空或无效。", ErrorCode::InvalidArgument);
			}
			if (!physical_properties_) {
				throw DroneException("UAVConfig [" + id_ + "]: 物理属性参数不能为空。", ErrorCode::InvalidArgument);
			}
			if (!dynamics_params_) {
				throw DroneException("UAVConfig [" + id_ + "]: 动力学参数不能为空。", ErrorCode::InvalidArgument);
			}
			if (!energy_params_) {
				throw DroneException("UAVConfig [" + id_ + "]: 能量参数不能为空。", ErrorCode::InvalidArgument);
			}

			// --- 类型一致性检查 (运行时检查) ---
			bool type_match = false; // 标记类型是否匹配
			LOG_DEBUG("检查 UAVConfig [{}] 类型 ({}) 与参数类型是否一致...", id_, static_cast<int>(type_));
			switch (type_) {
			case UavType::MULTIROTOR:
				// 使用 dynamic_pointer_cast 检查指针是否能安全转换为期望的派生类型
				type_match = (getDynamicsParams<NSParams::MultirotorDynamicsParams>() != nullptr &&
					getEnergyParams<NSParams::MultirotorEnergyParams>() != nullptr);
				if (!type_match) LOG_ERROR("UAVConfig [{}] 类型为 MULTIROTOR，但动力学或能量参数类型不匹配!", id_);
				break;
			case UavType::FIXED_WING:
				type_match = (getDynamicsParams<NSParams::FixedWingDynamicsParams>() != nullptr &&
					getEnergyParams<NSParams::FixedWingEnergyParams>() != nullptr);
				if (!type_match) LOG_ERROR("UAVConfig [{}] 类型为 FIXED_WING，但动力学或能量参数类型不匹配!", id_);
				break;
			case UavType::VTOL_FIXED_WING:
				type_match = (getDynamicsParams<NSParams::VtolDynamicsParams>() != nullptr &&
					getEnergyParams<NSParams::VtolEnergyParams>() != nullptr);
				if (!type_match) LOG_ERROR("UAVConfig [{}] 类型为 VTOL_FIXED_WING，但动力学或能量参数类型不匹配!", id_);
				break;
			case UavType::UNKNOWN:
				// 对于 UNKNOWN 类型，不强制检查具体参数类型，只要求非空 (已在前面检查)
				type_match = true;
				LOG_WARN("创建类型为 UNKNOWN 的 UAV 配置 [{}]。", id_);
				break;
			default:
				LOG_ERROR("UAVConfig [{}] 遇到未知的 UAVType 枚举值: {}", id_, static_cast<int>(type_));
				type_match = false; // 未知类型视为不匹配
				break;
			}

			// 如果类型不匹配，抛出异常
			if (!type_match) {
				std::string msg = "UAVConfig [" + id_ + "] 类型 (" + NSUtils::enumToString(type_) // 使用转换函数获取类型字符串
					+ ") 与提供的动力学或能量参数类型不匹配。";
				LOG_ERROR(msg);
				throw DroneException(msg, ErrorCode::InvalidArgument);
			}

			LOG_DEBUG("UAVConfig [{}] (类型: {}) 已成功创建并通过类型一致性检查。", id_, NSUtils::enumToString(type_));
		}

	} // namespace NSUav
} // namespace NSDrones