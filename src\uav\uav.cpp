// src/uav/uav.cpp
#include "uav/uav.h"
#include "uav/flight_strategy.h"
#include <optional>
#include "uav/dynamics/multirotor_dynamics.h"
#include "uav/dynamics/fixedwing_dynamics.h"
#include "uav/dynamics/vtol_dynamics.h"
#include "uav/energies/multirotor_energies.h"
#include "uav/energies/fixedwing_energies.h"
#include "uav/energies/vtol_energies.h"
#include "params/paramregistry.h"
#include "environment/environment.h"
#include "utils/logging.h"
#include "core/geometry/ishape.h"
#include <stdexcept>
#include <cmath>
#include <chrono>
#include <algorithm>
#include <cctype>
#include <typeinfo>
#include "core/movement_strategy.h"
#include "utils/enum_utils.h"
#include "core/entity_object.h"
#include <mutex>
#include <memory>

namespace NSDrones {
	namespace NSUav {

		/**
		 * @brief 无人机构造函数实现
		 *
		 * 执行无人机的基本初始化，包括类型解析、成员变量初始化等。
		 * 动力学模型、能量模型等复杂组件将在 initialize() 方法中创建。
		 *
		 * 主要功能：
		 * 1. 解析无人机类型字符串并转换为枚举值
		 * 2. 初始化基本成员变量
		 * 3. 设置安全间隙半径
		 * 4. 记录详细的初始化日志
		 *
		 * @param id 无人机的全局唯一标识符
		 * @param uav_type_key 完整的类型标签（如 "EntityObject.Uav.Multirotor"）
		 * @param env 无人机所属环境的引用
		 * @param name 无人机的显示名称
		 * @param initial_state 无人机的初始状态
		 * @param clearance_radius_param 安全间隙半径
		 * @param strategy 移动策略指针（通常为 nullptr，由内部设置）
		 *
		 * @throws DroneException 当无人机类型无法识别时抛出异常
		 */
		Uav::Uav(ObjectID id,
			const std::string& uav_type_key, // 完整的类型标签，例如 "EntityObject.Uav.Multirotor"
			const std::string& name,
			const EntityState& initial_state,
			double clearance_radius_param,
			std::shared_ptr<IMovementStrategy> strategy)
			: EntityObject(std::move(id), uav_type_key, name, initial_state) // Pass all params to EntityObject
			, dynamic_model_(nullptr)
			, energy_model_(nullptr)
			, payload_handler_(nullptr)
			, navigator_(nullptr)
			, current_task_id_(NSUtils::INVALID_OBJECT_ID)
			, mission_status_(MissionStatus::NONE)
			, clearance_radius_(clearance_radius_param) // 使用传入的参数初始化
		{
			// 从 uav_type_key (完整的类型标签) 中提取 UAV 的基本类型字符串
			std::string simple_uav_type_str = uav_type_key; // 默认使用完整的 key
			size_t last_dot_pos = uav_type_key.rfind('.');
			if (last_dot_pos != std::string::npos) {
				// 取最后一个 '.'之后的部分作为简短类型字符串
				simple_uav_type_str = uav_type_key.substr(last_dot_pos + 1);
			}

			// 在转换为枚举之前规范化字符串
			std::string normalized_simple_uav_type_str = simple_uav_type_str;
			// 1. 转换为全大写
			std::transform(normalized_simple_uav_type_str.begin(), normalized_simple_uav_type_str.end(),
				normalized_simple_uav_type_str.begin(), ::toupper);

			// 2. 根据需要添加下划线或进行特定映射以匹配UavType枚举成员
			// 例如: "FIXEDWING" -> "FIXED_WING", "VTOL" -> "VTOL_FIXED_WING"
			// 注意: "MULTIROTOR" 通常已经是正确的格式 "MULTIROTOR"
			if (normalized_simple_uav_type_str == "FIXEDWING") {
				normalized_simple_uav_type_str = "FIXED_WING";
			} else if (normalized_simple_uav_type_str == "VTOL") {
				// 假设从type_tag解析的 "VTOL" 应该对应枚举 UavType::VTOL_FIXED_WING
				normalized_simple_uav_type_str = "VTOL_FIXED_WING";
			} // 其他如 MULTIROTOR 可能不需要转换，或者也按需添加

			uav_type_ = NSUtils::stringToEnum<UavType>(normalized_simple_uav_type_str, UavType::UNKNOWN);

			LOG_INFO("UAV ({}) 已构造: ID='{}', 完整类型键='{}', 解析出的简短类型='{}', 规范化后类型串='{}', 推断枚举类型: {}, 清除半径: {:.2f}",
					getName().empty() ? uav_type_key : getName(),
					getId(),
					uav_type_key,
					simple_uav_type_str, // 记录原始解析出的字符串
					normalized_simple_uav_type_str, // 记录规范化后的字符串
					NSUtils::enumToString(uav_type_),
					clearance_radius_);

			if (uav_type_ == UavType::UNKNOWN && !uav_type_key.empty() && simple_uav_type_str != "Uav" && simple_uav_type_str != "unknown") {
				// 如果从一个看起来具体的类型键 (例如 "EntityObject.Uav.MyCustomRotor") 中解析出的简短类型 ("MyCustomRotor")
				// 仍然无法转换为已知的 UavType，则记录错误。
				// 排除 simple_uav_type_str == "Uav" 的情况，因为 "Uav" 本身会被正确（或预期地）转为 UNKNOWN
				LOG_ERROR("UAV ID '{}': 无法从完整类型键 '{}' (解析出的简短类型 '{}') 推断出有效的 UAVType枚举值。UAV可能无法正确初始化。",
				          getId(), uav_type_key, simple_uav_type_str);
				// 可以考虑抛出异常，因为这可能是一个严重的配置错误
				// throw DroneException("无效的 UAV 类型键: " + uav_type_key, ErrorCode::ConfigError);
			}

			if (strategy) {
				LOG_WARN("UAV ID '{}': 构造时收到了一个移动策略。UAV通常在 initialize() 中设置自己的特定飞行策略。这个传入的策略将被忽略，除非 initialize() 未能设置一个。", getId());
			}
		}

		// --- Getters ---
		/** @brief 获取无人机类型。*/
		UavType Uav::getType() const {
			return uav_type_;
		}

		/**
		 * @brief 获取当前详细状态 (UAVState)。线程安全。
		 */
		UavState Uav::getUavState() const {
			// 使用 state_mutex_ 保护对可能被其他方法修改的 UAV 特定状态（如载荷）的访问
			// 对于动态数据 (位置、速度等)，假设基类 MovableObject/EntityObject 的更新是线程安全的或可接受轻微延迟
			// **注意:** 如果 UAV 类未来增加了更多需要保护的成员变量，需要在这里加锁
			// std::lock_guard<std::mutex> lock(state_mutex_); // 如果需要保护 UAV 特有成员

			UavState current_uav_state;
			// --- 从基类复制共享状态 ---
			// dynamic_data_ 的访问由基类保证线程安全（通过 notifyUpdate 机制写入，读取通常认为原子或可接受延迟）
			// 直接使用WGS84位置，因为UavState.position是WGS84Point类型
			current_uav_state.position = state_->position;
			current_uav_state.orientation = state_->orientation;
			current_uav_state.velocity = state_->velocity_wgs84;
			current_uav_state.angular_velocity = state_->angular_velocity;
			current_uav_state.acceleration = state_->acceleration;
			current_uav_state.angular_acceleration = state_->angular_acceleration;
			current_uav_state.time_stamp = state_->time_stamp;
			current_uav_state.status = state_->status;
			current_uav_state.capabilities = state_->capabilities;
			// --- 复制 UAV 特定状态 ---
			current_uav_state.mode = getCurrentFlightMode(); // 调用 getter 获取当前模式 (getter 内部处理同步或判断)
			current_uav_state.current_energy = state_->energy_level; // 能量存储在基类状态中

			LOG_TRACE("获取当前 UAV 状态 (ID: {})：t={:.4f}, Pos=({}), Vel=({:.1f},{:.1f},{:.1f}), E={:.1f}",
				getId(), current_uav_state.time_stamp,
				current_uav_state.position.toString(),
				current_uav_state.velocity.x(), current_uav_state.velocity.y(), current_uav_state.velocity.z(),
				current_uav_state.current_energy);
			return current_uav_state; // 返回状态副本
		}

		/**
		 * @brief 获取当前飞行模式。
		 *        **简化实现:** 需要 UAV 内部状态或更复杂的逻辑来判断。
		 */
		FlightMode Uav::getCurrentFlightMode() const {
			// 简化实现：根据类型和速度粗略判断
			switch (uav_type_) {
			case UavType::MULTIROTOR:
				LOG_TRACE("获取飞行模式 (多旋翼)：返回 HOVER");
				return FlightMode::HOVER; // 多旋翼始终是悬停模式
			case UavType::FIXED_WING:
				LOG_TRACE("获取飞行模式 (固定翼)：返回 FIXED_WING");
				return FlightMode::FIXED_WING; // 固定翼始终是固定翼模式
			case UavType::VTOL_FIXED_WING:
				// VTOL 需要更复杂的逻辑，可能依赖于存储的内部模式状态
				// 这里简化为根据速度判断
			{
				LOG_TRACE("获取飞行模式 (VTOL)：基于速度判断...");
				double h_speed = 0.0;
				// 访问速度需要考虑线程安全吗？dynamic_data_由基类管理更新
				// 假设读取是原子的或可接受轻微延迟
				h_speed = state_->velocity_wgs84.head<2>().norm();
				double transition_speed = 15.0; // 默认转换速度
				// 尝试从参数获取转换速度
				// 键名示例: "dynamics.vtol.transitionSpeed"
				auto ts_opt = getEffectiveParam<double>("dynamics.vtol.transitionSpeed");
				if (ts_opt) {
					transition_speed = *ts_opt;
					LOG_TRACE("  使用参数获取的转换速度: {:.1f} m/s", transition_speed);
				}
				else {
					LOG_TRACE("  未找到转换速度参数，使用默认值: {:.1f} m/s", transition_speed);
				}

				// 使用简单的阈值判断（可以加入迟滞避免抖动）
				// 阈值可以设为略低于转换速度，例如 80%
				double hover_threshold = transition_speed * 0.8;
				if (h_speed < hover_threshold) { // 低于阈值一定比例，认为是悬停
					LOG_TRACE("  速度 {:.1f} < {:.1f}，判定为 HOVER", h_speed, hover_threshold);
					return FlightMode::HOVER;
				}
				else { // 高于阈值，认为是固定翼
					LOG_TRACE("  速度 {:.1f} >= {:.1f}，判定为 FIXED_WING", h_speed, hover_threshold);
					return FlightMode::FIXED_WING;
				}
				// 注意：这个简化实现无法表示 TRANSITION 状态，需要更复杂的内部状态机
			}
			default:
				LOG_WARN("获取飞行模式：遇到未知无人机类型 ({})，返回 UNKNOWN", static_cast<int>(uav_type_));
				return FlightMode::UNKNOWN; // 其他未知类型
			}
		}

		/** @brief 获取当前载荷重量 (kg)。线程安全。 */
		double Uav::getPayloadWeight() const {
			std::lock_guard<std::mutex> lock(state_mutex_); // 获取锁保护读取
			return payload_Weight_;
		}

		/**
		 * @brief 设置当前载荷重量。线程安全。
		 */
		bool Uav::setPayloadWeight(double weight) {
			double max_payload = 0.0;
			try {
				// 从参数系统中获取最大载荷限制 (通过层级查找)
				// 键名示例: "physical.max_payload_weight"
				max_payload = getParamOrThrow<double>("physical.max_payload_weight");
			}
			catch (const DroneException& e) {
				LOG_ERROR("UAV [{}] 设置载荷失败：无法获取最大载荷参数 ('physical.max_payload_weight'): {}", getId(), e.what());
				return false; // 获取参数失败
			}

			// 检查输入重量是否在有效范围内 [0, max_payload]
			if (weight < 0.0 || weight > max_payload + Constants::GEOMETRY_EPSILON) { // 加一点容差
				LOG_WARN("UAV [{}] 设置载荷失败：无效载荷重量 {:.2f} kg (允许范围 [0, {:.2f}] kg)。", getId(), weight, max_payload);
				return false; // 重量无效
			}

			std::lock_guard<std::mutex> lock(state_mutex_); // 获取写锁保护修改
			if (std::abs(payload_Weight_ - weight) > Constants::EPSILON) { // 仅当重量实际改变时记录日志
				LOG_INFO("UAV [{}]: 载荷重量设置为 {:.2f} kg (原: {:.2f} kg)。", getId(), weight, payload_Weight_);
				payload_Weight_ = weight;
				// 注意：载荷变化可能影响能量消耗和动力学性能。
				// 相关模型在计算时应通过 owner_ 引用查询当前总质量（空重 + 载荷）。
				// 这里不需要显式更新模型，模型内部应实时获取状态。
			}
			else {
				LOG_TRACE("UAV [{}]: 请求设置的载荷重量 {:.2f} kg 与当前值相同，无需更新。", getId(), weight);
			}
			return true; // 设置成功
		}

		// --- 能力管理方法实现 ---

		/**
		 * @brief 获取无人机的能力列表
		 */
		std::vector<std::string> Uav::getCapabilities() const {
			// 从当前状态中获取能力列表
			std::vector<std::string> capabilities = state_->capabilities;

			LOG_TRACE("UAV [{}]: 获取能力列表，共 {} 项能力", getId(), capabilities.size());
			for (const auto& cap : capabilities) {
				LOG_TRACE("  - 能力: {}", cap);
			}

			return capabilities;
		}

		/**
		 * @brief 检查无人机是否具备指定能力
		 */
		bool Uav::hasCapability(const std::string& capability) const {
			const auto& capabilities = state_->capabilities;

			// 使用 std::find 查找指定能力
			bool has_capability = std::find(capabilities.begin(), capabilities.end(), capability) != capabilities.end();

			LOG_TRACE("UAV [{}]: 检查能力 '{}' - {}", getId(), capability, has_capability ? "具备" : "不具备");

			return has_capability;
		}

		// --- 状态更新 ---
		/**
		 * @brief (内部) 将 UAVState 同步到底层 EntityState。
		 */
		void Uav::syncObjectState(const UavState& uav_state) {
			LOG_TRACE("UAV [{}]: 开始同步 UAVState 到基类 EntityState...", getId());
			EntityState base_state; // 创建临时的基类状态对象
			// 从 UAVState 复制通用字段到 EntityState
			// 注意：uav_state.position 是WGS84坐标，直接复制
			base_state.position = uav_state.position; // 直接复制WGS84坐标
			base_state.orientation = uav_state.orientation;
			base_state.velocity_wgs84 = uav_state.velocity;
			base_state.angular_velocity = uav_state.angular_velocity;
			base_state.acceleration = uav_state.acceleration;
			base_state.angular_acceleration = uav_state.angular_acceleration;
			base_state.energy_level = uav_state.current_energy; // **同步能量**
			base_state.time_stamp = uav_state.time_stamp;
			base_state.status = uav_state.status;
			base_state.capabilities = uav_state.capabilities;

			// 调用 EntityObject 的更新方法，它会处理快照和索引通知
			EntityObject::updateState(base_state); // <--- 修正为调用 EntityObject::updateState
			LOG_TRACE("UAV [{}]: 基类 EntityState 同步完成。", getId());
		}

		/**
		 * @brief 手动更新无人机状态 (使用详细的 UAVState)。
		 */
		void Uav::manualStateUpdate(const UavState& new_uav_state) {
			// 验证输入状态是否有效 (假设 isEmpty 检查 NaN/Inf 等)
			if (new_uav_state.isEmpty()) { // 假设 isEmpty 是 UAVState 的一个方法
				LOG_WARN("UAV [{}] 尝试使用包含无效数据 (NaN/Inf) 的状态进行手动更新，操作已忽略。", getId());
				return;
			}
			LOG_TRACE("UAV [{}] 请求手动更新 UAVState (时间戳={:.4f})...", getId(), new_uav_state.time_stamp);

			// 同步到基类 EntityState 并触发索引更新
			syncObjectState(new_uav_state);

			// 如果有 UAV 特有的状态需要在此处更新（并且这些状态不包含在 EntityState 中）
			// 例如，如果 payload_Weight_ 不是通过参数或命令设置，而是也随 UAVState 更新
			// 则可以在这里处理，并确保线程安全。
			// std::lock_guard<std::mutex> lock(state_mutex_);
			// this->payload_Weight_ = new_uav_state.payload_weight; // 假设 UAVState 有 payload_weight

			LOG_TRACE("UAV [{}] 手动状态更新完成: t={:.2f}, pos=({}), E={:.1f}",
				getId(), state_->time_stamp, state_->position.toString(), state_->energy_level);
		}

		// --- 重写基类方法 ---
		/**
		 * @brief 初始化 UAV。
		 *        由工厂调用，完成 UAV 特定设置。
		 */
		bool Uav::initialize(std::shared_ptr<NSParams::ParamValues> final_params, const nlohmann::json& raw_instance_json_config) {
			if (!final_params) {
				LOG_ERROR("Uav 初始化失败: 传入的参数指针为空。");
				return false;
			}
			LOG_INFO("UAV [{} ID:{}]: 开始执行 initialize(ParamValues&, const json&) (类型: {})", getName(), getId(), NSUtils::enumToString(getType()));

			// 1. 调用基类的 initialize 方法，处理参数赋值、形状创建、添加到环境
			if (!EntityObject::initialize(std::make_shared<NSParams::ParamValues>(*final_params), raw_instance_json_config)) {
				LOG_ERROR("UAV [{} ID:{}]: 调用 EntityObject::initialize 失败。初始化终止。", getName(), getId());
				return false;
			}
			// 到这里，this->params_ 已经包含了最终的参数值

			// 2. 创建动力学模型
			LOG_DEBUG("UAV [{} ID:{}]: 正在创建动力学模型... (类型: {})", getName(), getId(), NSUtils::enumToString(getType()));
			try {
				switch (getType()) {
				case UavType::MULTIROTOR:
					dynamic_model_ = std::make_shared<MultirotorDynamics>(*this); // 传递 this 引用
					break;
				case UavType::FIXED_WING:
					dynamic_model_ = std::make_shared<FixedWingDynamics>(*this);
					break;
				case UavType::VTOL_FIXED_WING:
					dynamic_model_ = std::make_shared<VtolDynamics>(*this);
					break;
				default:
					LOG_ERROR("UAV [{} ID:{}]: 不支持的无人机类型 ({}) 无法创建动力学模型。", getName(), getId(), static_cast<int>(getType()));
					return false;
				}
				LOG_INFO("UAV [{} ID:{}]: 动力学模型 ({}) 创建成功。", getName(), getId(), typeid(*dynamic_model_.get()).name());
			} catch (const std::exception& e) {
				LOG_ERROR("UAV [{} ID:{}]: 创建动力学模型时发生异常: {}", getName(), getId(), e.what());
				return false;
			}

			// 3. 创建能量模型
			LOG_DEBUG("UAV [{} ID:{}]: 正在创建能量模型...", getName(), getId());
			try {
				switch (getType()) {
				case UavType::MULTIROTOR:
					energy_model_ = std::make_shared<MultirotorEnergies>(*this);
					break;
				case UavType::FIXED_WING:
					energy_model_ = std::make_shared<FixedWingEnergies>(*this);
					break;
				case UavType::VTOL_FIXED_WING:
					energy_model_ = std::make_shared<VtolEnergies>(*this);
					break;
				default:
					LOG_ERROR("UAV [{} ID:{}]: 不支持的无人机类型 ({}) 无法创建能量模型。", getName(), getId(), static_cast<int>(getType()));
					return false;
				}
				LOG_INFO("UAV [{} ID:{}]: 能量模型 ({}) 创建成功。", getName(), getId(), typeid(*energy_model_.get()).name());
			} catch (const std::exception& e) {
				LOG_ERROR("UAV [{} ID:{}]: 创建能量模型时发生异常: {}", getName(), getId(), e.what());
				return false;
			}

			// 4. 创建导航器和负载处理器 (如果需要)
			// navigator_ = std::make_shared<Navigator>(*this, env_); // 示例
			// payload_handler_ = std::make_shared<PayloadHandler>(*this); // 示例
			LOG_DEBUG("UAV [{} ID:{}]: 导航器和负载处理器占位符。", getName(), getId());

			// 5. 设置移动策略 (使用 UAV 特定的飞行策略)
			LOG_DEBUG("UAV [{} ID:{}]: 正在设置飞行策略...", getName(), getId());
			if (dynamic_model_) { // 确保动力学模型已创建
				auto flight_strategy = std::make_shared<FlightStrategy>(*this); // 修正：只传递 owner (即 *this)
				setMovementStrategy(flight_strategy);
				LOG_INFO("UAV [{} ID:{}]: FlightStrategy 设置成功。", getName(), getId());
			} else {
				LOG_WARN("UAV [{} ID:{}]: 动力学模型为空，无法设置 FlightStrategy。将使用默认移动策略(如果EntityObject设置了的话)。", getName(), getId());
				// 可以选择设置一个默认的 StillMovementStrategy 或 LinearMovementStrategy
				setMovementStrategy(std::make_shared<LinearMovementStrategy>(*this));
			}

			// 6. 设置初始能量
			try {
				// 键名示例: "energy.initial_energy_level_wh"
				double initial_energy = getParamOrDefault<double>("energy.initial_energy_level_wh",
					getParamOrDefault<double>("energy.max_energy_level_wh", 100.0)); // 默认使用最大能量
				state_->energy_level = initial_energy;
				LOG_INFO("UAV [{} ID:{}]: 初始能量设置为 {:.2f} Wh.", getName(), getId(), initial_energy);
			} catch (const std::exception& e) {
				LOG_ERROR("UAV [{} ID:{}]: 设置初始能量时发生异常: {}", getName(), getId(), e.what());
				// 不认为是致命错误，能量模型可能会有默认值
			}

			// 注意：形状创建已移至 EntityObject::initialize 内部的 createAndSetShapeFromParams()

			LOG_INFO("UAV [{} ID:{}]: 特定于 UAV 的 initialize 完成。", getName(), getId());
			return true;
		}

		// --- 辅助函数实现 (不变) ---
		/** @brief 将 RoutePoint 转换为基础 UAVState。*/
		UavState stateFromRoutePt(const RoutePoint& rp) {
			UavState state;
			state.position = rp.position;         // 复制位置
			state.velocity = rp.velocity;         // 复制速度
			state.orientation = rp.orientation;       // 复制姿态
			state.time_stamp = rp.time_stamp;     // 复制时间戳
			// 其他字段使用默认值或需要额外信息填充
			state.status = "ACTIVE"; // 假设航点上的状态是活动
			// 能量、模式、任务信息等需要根据上下文填充
			// state.current_energy = ...;
			// state.mode = ...;
			LOG_TRACE("从 RoutePoint (t={:.4f}) 创建 UAVState。", rp.time_stamp);
			return state;
		}
		/** @brief 将 UAVState 转换为 RoutePoint。*/
		RoutePoint routePtFromState(const UavState& state) {
			LOG_TRACE("从 UAVState (t={:.4f}) 创建 RoutePoint。", state.time_stamp);
			return RoutePoint(state.position, state.time_stamp, state.velocity, state.orientation);
		}

	} // namespace NSUav
} // namespace NSDrones