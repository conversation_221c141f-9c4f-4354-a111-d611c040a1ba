// include/utils/logging.h
#pragma once
#include <memory>
#include <atomic>
#include <sstream>
// 确保在包含 spdlog 之前定义活动级别宏 (控制编译时日志级别过滤)
#define SPDLOG_ACTIVE_LEVEL SPDLOG_LEVEL_TRACE
#include <spdlog/spdlog.h>
#include <spdlog/fmt/ostr.h>
#include <spdlog/fmt/fmt.h>
#include <spdlog/sinks/stdout_color_sinks.h> // 用于 Windows/Linux 彩色控制台
#include <spdlog/sinks/basic_file_sink.h> // 用于基本文件输出
#include <string>

namespace NSDrones {

	/**
	 * @brief 日志工具类，封装 spdlog。
	 */
	class Logger {
	public:
		/**
		 * @brief 初始化日志系统
		 *
		 * 创建控制台和文件日志输出，设置默认的日志级别和格式。
		 * 应在程序启动时尽早调用。
		 *
		 * @param log_file 日志文件名，默认为 "drones_framework.log"
		 * @param truncate 是否覆盖现有日志文件 (true) 或追加 (false)
		 * @param console_level 控制台日志级别，默认为 "trace"
		 * @param file_level 文件日志级别，默认为 "trace"
		 */
		static void init(const std::string& log_file = "drones_framework.log",
		                bool truncate = true,
		                const std::string& console_level = "trace",
		                const std::string& file_level = "trace");

		/**
		 * @brief 获取全局日志记录器实例
		 * @return spdlog::logger 的共享指针引用
		 * @throws std::runtime_error 如果在 init() 调用之前调用
		 */
		static std::shared_ptr<spdlog::logger>& get_logger();

		/**
		 * @brief 关闭日志系统
		 *
		 * 刷新所有挂起的日志并释放资源。
		 * 通常在程序退出前调用。
		 */
		static void shutdown();

		/**
		 * @brief 设置控制台日志级别
		 *
		 * @param level_str 日志级别字符串 ("trace", "debug", "info", "warn", "error", "critical", "off")
		 * @return 如果级别设置成功返回 true，否则返回 false
		 */
		static bool set_console_level(const std::string& level_str);

		/**
		 * @brief 设置文件日志级别
		 *
		 * @param level_str 日志级别字符串 ("trace", "debug", "info", "warn", "error", "critical", "off")
		 * @return 如果级别设置成功返回 true，否则返回 false
		 */
		static bool set_file_level(const std::string& level_str);

		/**
		 * @brief 获取当前控制台日志级别
		 * @return 当前控制台日志级别字符串
		 */
		static std::string get_console_level();

		/**
		 * @brief 获取当前文件日志级别
		 * @return 当前文件日志级别字符串
		 */
		static std::string get_file_level();

	private:
		// 静态成员，存储日志记录器实例
		static std::shared_ptr<spdlog::logger> logger_instance_;
		static std::shared_ptr<spdlog::sinks::stdout_color_sink_mt> console_sink_;
		static std::shared_ptr<spdlog::sinks::basic_file_sink_mt> file_sink_;

		// 静态变量，用于跟踪日志行号
		static std::atomic<int> log_line_counter_;

	public:
		/**
		 * @brief 获取下一个日志行号（四位整数格式）
		 * @return 格式化的四位整数行号字符串
		 */
		static int get_next_log_line_number();
	};

	// 便捷的日志宏定义 (使用可变参数模板 __VA_ARGS__)
	// 直接使用带命名空间的 Logger::get_logger()
	// 使用 SPDLOG_LOGGER_XXX 宏可以进行编译时级别检查 (基于 SPDLOG_ACTIVE_LEVEL)
	// 每个日志宏都会自动添加四位整数的行号前缀
#define LOG_TRACE(...)    SPDLOG_LOGGER_TRACE(NSDrones::Logger::get_logger(), "[{:04d}] {}", NSDrones::Logger::get_next_log_line_number(), fmt::format(__VA_ARGS__))
#define LOG_DEBUG(...)    SPDLOG_LOGGER_DEBUG(NSDrones::Logger::get_logger(), "[{:04d}] {}", NSDrones::Logger::get_next_log_line_number(), fmt::format(__VA_ARGS__))
#define LOG_INFO(...)     SPDLOG_LOGGER_INFO(NSDrones::Logger::get_logger(), "[{:04d}] {}", NSDrones::Logger::get_next_log_line_number(), fmt::format(__VA_ARGS__))
#define LOG_WARN(...)     SPDLOG_LOGGER_WARN(NSDrones::Logger::get_logger(), "[{:04d}] {}", NSDrones::Logger::get_next_log_line_number(), fmt::format(__VA_ARGS__))
#define LOG_ERROR(...)    SPDLOG_LOGGER_ERROR(NSDrones::Logger::get_logger(), "[{:04d}] {}", NSDrones::Logger::get_next_log_line_number(), fmt::format(__VA_ARGS__))
#define LOG_CRITICAL(...) SPDLOG_LOGGER_CRITICAL(NSDrones::Logger::get_logger(), "[{:04d}] {}", NSDrones::Logger::get_next_log_line_number(), fmt::format(__VA_ARGS__))

// (可选) 添加一个一次性日志宏 (用于只记录一次的警告或信息)
#define LOG_INFO_ONCE(...) \
    { \
        static bool logged_##__LINE__ = false; \
        if (!logged_##__LINE__) { \
            SPDLOG_LOGGER_INFO(NSDrones::Logger::get_logger(), "[{:04d}] {}", NSDrones::Logger::get_next_log_line_number(), fmt::format(__VA_ARGS__)); \
            logged_##__LINE__ = true; \
        } \
    }

#define LOG_WARN_ONCE(...) \
    { \
        static bool logged_##__LINE__ = false; \
        if (!logged_##__LINE__) { \
            SPDLOG_LOGGER_WARN(NSDrones::Logger::get_logger(), "[{:04d}] {}", NSDrones::Logger::get_next_log_line_number(), fmt::format(__VA_ARGS__)); \
            logged_##__LINE__ = true; \
        } \
    }

#define LOG_ERROR_ONCE(...) \
    { \
        static bool logged_##__LINE__ = false; \
        if (!logged_##__LINE__) { \
            SPDLOG_LOGGER_ERROR(NSDrones::Logger::get_logger(), "[{:04d}] {}", NSDrones::Logger::get_next_log_line_number(), fmt::format(__VA_ARGS__)); \
            logged_##__LINE__ = true; \
        } \
    }

} // namespace NSDrones