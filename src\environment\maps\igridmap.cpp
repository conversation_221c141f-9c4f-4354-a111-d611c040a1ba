// src/environment/maps/igridmap.cpp
#include "environment/maps/igridmap.h"
#include "utils/logging.h"
#include "spdlog/fmt/ranges.h"
#include <algorithm>
#include <cctype>

namespace NSDrones {
	namespace NSEnvironment {

		// === 图层检测实现 ===

		bool IGridMap::detectLayers(
			const std::vector<std::string>& available_layers,
			std::shared_ptr<NSParams::ParamValues> global_params,
			std::string& elevation_layer,
			std::string& feature_layer,
			std::string& feature_height_layer) {

			LOG_DEBUG("地图接口: 开始智能检测图层名称，可用图层数量: {}", available_layers.size());

			// 参数验证
			if (!global_params) {
				LOG_ERROR("地图接口: 全局参数对象为空，无法进行图层检测");
				return false;
			}

			if (available_layers.empty()) {
				LOG_WARN("地图接口: 数据源中没有可用的图层");
				return false;
			}

			LOG_DEBUG("地图接口: 可用图层列表: [{}]", fmt::join(available_layers, ", "));

			// 获取图层映射配置（预定义的候选名称列表）
			auto layer_mapping = global_params->getValueOrDefault<std::vector<std::vector<std::string>>>(
				"map.layer_mapping",
				std::vector<std::vector<std::string>>{
					{"elevation", "height", "dem", "dtm", "altitude", "z"},                                    // 高程图层候选名称
					{"feature_type", "landuse", "classification", "class", "type"},                           // 地物类型图层候选名称
					{"feature_height", "building_height", "height_above_ground", "structure_height", "object_height"}  // 地物高度图层候选名称
				}
			);

			// === 1. 检测高程图层（必需） ===
			auto config_elevation = global_params->getValueOrDefault<std::string>("map.layer_config.elevation_layer", "");

			// 优先使用配置指定的图层名称
			if (!config_elevation.empty() &&
				std::find(available_layers.begin(), available_layers.end(), config_elevation) != available_layers.end()) {
				elevation_layer = config_elevation;
				LOG_INFO("地图接口: 使用配置指定的高程图层: '{}'", elevation_layer);
			} else {
				// 配置不存在或无效，使用自动检测
				if (!layer_mapping.empty()) {
					elevation_layer = detectLayerFromCandidates(layer_mapping[0], available_layers);
				}

				if (elevation_layer.empty()) {
					LOG_ERROR("地图接口: 无法找到高程图层，检查数据源是否包含高程数据");
					return false;
				}
				LOG_INFO("地图接口: 自动检测到高程图层: '{}'", elevation_layer);
			}

			// === 2. 检测地物类型图层（可选） ===
			auto config_feature = global_params->getValueOrDefault<std::string>("map.layer_config.feature_layer", "");

			// 优先使用配置指定的图层名称
			if (!config_feature.empty() &&
				std::find(available_layers.begin(), available_layers.end(), config_feature) != available_layers.end()) {
				feature_layer = config_feature;
				LOG_INFO("地图接口: 使用配置指定的地物类型图层: '{}'", feature_layer);
			} else {
				// 配置不存在或无效，使用自动检测
				if (layer_mapping.size() > 1) {
					feature_layer = detectLayerFromCandidates(layer_mapping[1], available_layers);
				}

				if (!feature_layer.empty()) {
					LOG_INFO("地图接口: 自动检测到地物类型图层: '{}'", feature_layer);
				} else {
					LOG_DEBUG("地图接口: 未找到地物类型图层，地物类型查询将不可用");
				}
			}

			// === 3. 检测地物高度图层（可选） ===
			auto config_feature_height = global_params->getValueOrDefault<std::string>("map.layer_config.feature_height_layer", "");

			// 优先使用配置指定的图层名称
			if (!config_feature_height.empty() &&
				std::find(available_layers.begin(), available_layers.end(), config_feature_height) != available_layers.end()) {
				feature_height_layer = config_feature_height;
				LOG_INFO("地图接口: 使用配置指定的地物高度图层: '{}'", feature_height_layer);
			} else {
				// 配置不存在或无效，使用自动检测
				if (layer_mapping.size() > 2) {
					feature_height_layer = detectLayerFromCandidates(layer_mapping[2], available_layers);
				}

				if (!feature_height_layer.empty()) {
					LOG_INFO("地图接口: 自动检测到地物高度图层: '{}'", feature_height_layer);
				} else {
					LOG_DEBUG("地图接口: 未找到地物高度图层，地物高度查询将不可用");
				}
			}

			// === 检测结果汇总 ===
			LOG_INFO("地图接口: 图层检测完成 - 高程图层: '{}', 地物类型图层: '{}', 地物高度图层: '{}'",
				elevation_layer.empty() ? "未找到" : elevation_layer,
				feature_layer.empty() ? "未找到" : feature_layer,
				feature_height_layer.empty() ? "未找到" : feature_height_layer);

			return true;  // 只要找到高程图层就算成功
		}

		std::string IGridMap::detectLayerFromCandidates(
			const std::vector<std::string>& candidates,
			const std::vector<std::string>& available_layers) {

			LOG_TRACE("地图接口: 开始从 {} 个候选名称中匹配图层，可用图层数量: {}",
				candidates.size(), available_layers.size());

			// 遍历候选名称（按优先级顺序）
			for (const auto& candidate : candidates) {
				LOG_TRACE("地图接口: 尝试匹配候选名称: '{}'", candidate);

				// 遍历可用图层进行匹配
				for (const auto& available : available_layers) {
					// === 1. 精确匹配（优先级最高） ===
					if (available == candidate) {
						LOG_DEBUG("地图接口: 精确匹配成功 - 图层: '{}', 候选: '{}'", available, candidate);
						return available;
					}

					// === 2. 包含匹配（不区分大小写） ===
					std::string available_lower = available;
					std::string candidate_lower = candidate;

					// 转换为小写进行比较
					std::transform(available_lower.begin(), available_lower.end(), available_lower.begin(), ::tolower);
					std::transform(candidate_lower.begin(), candidate_lower.end(), candidate_lower.begin(), ::tolower);

					if (available_lower.find(candidate_lower) != std::string::npos) {
						LOG_DEBUG("地图接口: 包含匹配成功 - 图层: '{}', 候选: '{}' (不区分大小写)", available, candidate);
						return available;
					}
				}
			}

			// 未找到匹配的图层
			LOG_DEBUG("地图接口: 未找到匹配的图层，候选名称: [{}], 可用图层: [{}]",
				fmt::join(candidates, ", "), fmt::join(available_layers, ", "));
			return "";
		}



	} // namespace NSEnvironment
} // namespace NSDrones
