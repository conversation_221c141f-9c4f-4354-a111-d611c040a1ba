// src/planning/task_planners/task_planner_surveymultipoints.cpp
#include "planning/task_planners/surveymultipoints_taskplanner.h"
#include "planning/planning_types.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/task_strategies.h"
#include "mission/control_point.h"
#include "uav/uav.h"
#include "environment/environment.h"
#include "utils/coordinate_converter.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"
#include "environment/environment.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include <vector>
#include <map>
#include <string>
#include <algorithm>
#include <utility>
#include <numeric>

namespace NSDrones {
	namespace NSPlanning {

		SurveyMultiPointsTaskPlanner::SurveyMultiPointsTaskPlanner()
			: ITaskPlanner() {}

		bool SurveyMultiPointsTaskPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params,
													  const nlohmann::json& raw_config) {
			LOG_DEBUG("多点巡检任务规划器: 开始初始化");

			if (params) {
				// 加载巡检相关参数
				default_survey_speed_ = params->getValueOrDefault<double>("survey.speed", 8.0);

				LOG_INFO("多点巡检任务规划器: 参数加载完成 - 默认速度:{:.1f}m/s", default_survey_speed_);
			} else {
				LOG_WARN("多点巡检任务规划器: 参数对象为空，使用默认值");
			}

			LOG_DEBUG("多点巡检任务规划器: 初始化完成");
			return true;
		}

		/**
		 * @brief 规划单机多点巡检任务（重构后的接口）
		 */
		SingleTaskPlanningResult SurveyMultiPointsTaskPlanner::planSingleTask(
			const SingleTaskPlanningRequest& request) {

			LOG_INFO("[SurveyMultiPointsTaskPlanner] 开始规划子任务 [{}]，UAV [{}]",
				request.assignment.sub_task_id, request.assignment.assigned_uav_id);

			SingleTaskPlanningResult result;
			result.success = false;
			result.sub_task_id = request.assignment.sub_task_id;
			result.uav_id = request.assignment.assigned_uav_id;

			try {
				// === 第1步：验证输入 ===
				if (!request.uav) {
					result.message = "无人机指针无效";
					LOG_ERROR("[SurveyMultiPointsTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				if (!request.original_task) {
					result.message = "原始任务指针无效";
					LOG_ERROR("[SurveyMultiPointsTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// 验证任务类型
				if (request.original_task->getType() != NSCore::TaskType::SURVEY_MULTIPOINTS) {
					result.message = "任务类型不匹配，期望 SURVEY_MULTIPOINTS，实际 " +
						NSUtils::enumToString(request.original_task->getType());
					LOG_ERROR("[SurveyMultiPointsTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第2步：获取任务参数 ===
				auto params_ptr = request.original_task->getTaskParameters<NSMission::SurveyMultiPointsTaskParams>();
				if (!params_ptr) {
					result.message = "无法获取 SurveyMultiPoints 任务参数";
					LOG_ERROR("[SurveyMultiPointsTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第3步：获取起始状态 ===
				NSUav::UavState start_state;
				if (!getUavStartState(request.uav, start_state)) {
					result.message = "无法获取无人机起始状态";
					LOG_ERROR("[SurveyMultiPointsTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第4步：执行巡检规划 ===
				result = planSurveyTrajectory(request, *params_ptr, start_state);

			} catch (const std::exception& e) {
				result.success = false;
				result.message = "规划异常: " + std::string(e.what());
				LOG_ERROR("[SurveyMultiPointsTaskPlanner] 子任务 [{}] 规划异常: {}", result.sub_task_id, e.what());
			}

			LOG_INFO("[SurveyMultiPointsTaskPlanner] 子任务 [{}] 规划完成，成功: {}",
				result.sub_task_id, result.success);
			return result;
		}

		/**
		 * @brief 检查是否支持指定的子任务类型
		 */
		bool SurveyMultiPointsTaskPlanner::isSubTaskSupported(const SubTaskTarget& sub_target) const {
			return sub_target.task_type == NSCore::TaskType::SURVEY_MULTIPOINTS;
		}

		/**
		 * @brief 规划多点巡检轨迹的核心实现
		 */
		SingleTaskPlanningResult SurveyMultiPointsTaskPlanner::planSurveyTrajectory(
			const SingleTaskPlanningRequest& request,
			const NSMission::SurveyMultiPointsTaskParams& params,
			const NSUav::UavState& start_state) {

			SingleTaskPlanningResult result;
			result.success = false;
			result.sub_task_id = request.assignment.sub_task_id;
			result.uav_id = request.assignment.assigned_uav_id;

			try {
				LOG_DEBUG("[SurveyMultiPointsTaskPlanner] 开始规划巡检轨迹，巡检点数: {}", params.survey_points.size());

				// === 第1步：验证巡检参数 ===
				if (params.survey_points.empty()) {
					result.message = "巡检点列表为空";
					LOG_ERROR("[SurveyMultiPointsTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// 验证每个巡检点的有效性
				for (size_t i = 0; i < params.survey_points.size(); ++i) {
					const auto& cp = params.survey_points[i];
					if (!cp.position.isValid()) {
						result.message = "巡检点#" + std::to_string(i + 1) + "位置无效";
						LOG_ERROR("[SurveyMultiPointsTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
						return result;
					}
				}

				// === 第2步：构建完整几何路径 ===
				std::vector<NSCore::WGS84Point> geometric_path;
				geometric_path.reserve(params.survey_points.size() + 1);

				// 添加起始点
				geometric_path.push_back(start_state.position);

				// 添加所有巡检点
				for (const auto& cp : params.survey_points) {
					geometric_path.push_back(cp.position);
				}

				LOG_DEBUG("[SurveyMultiPointsTaskPlanner] 构建几何路径完成，总点数: {}", geometric_path.size());

				// === 第3步：确定飞行速度 ===
				double flight_speed = default_survey_speed_;

				// 检查UAV的速度限制
				auto dynamics = request.uav->getDynamicsModel();
				if (dynamics) {
					double max_speed = dynamics->getMaxSpeed();
					if (flight_speed > max_speed) {
						double original_speed = flight_speed;
						flight_speed = max_speed;
						LOG_WARN("[SurveyMultiPointsTaskPlanner] 期望速度 {:.2f} 超过UAV最大速度 {:.2f}，调整为最大速度",
							original_speed, max_speed);
					}
				}

				LOG_DEBUG("[SurveyMultiPointsTaskPlanner] 使用飞行速度: {:.2f} m/s", flight_speed);

				// === 第4步：生成轨迹 ===
				Trajectory trajectory;
				if (!smoothAndTimeParameterize(geometric_path, request.uav, start_state,
					flight_speed, trajectory, &result)) {
					result.message = "轨迹生成失败";
					LOG_ERROR("[SurveyMultiPointsTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第5步：应用避障约束（如果有） ===
				if (!request.avoidance_constraints.empty()) {
					LOG_DEBUG("[SurveyMultiPointsTaskPlanner] 应用 {} 个避障约束", request.avoidance_constraints.size());

					if (!applyAvoidanceConstraints(trajectory, request.avoidance_constraints, result)) {
						result.message = "应用避障约束失败";
						LOG_ERROR("[SurveyMultiPointsTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
						return result;
					}
				}

				// === 第6步：验证轨迹 ===
				if (trajectory.empty()) {
					result.message = "生成的轨迹为空";
					LOG_ERROR("[SurveyMultiPointsTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第7步：设置结果 ===
				result.trajectory = std::move(trajectory);
				result.success = true;
				result.message = "多点巡检规划成功";

				LOG_INFO("[SurveyMultiPointsTaskPlanner] 子任务 [{}] 规划成功，轨迹段数: {}，访问 {} 个巡检点",
					result.sub_task_id, result.trajectory.size(), params.survey_points.size());

			} catch (const std::exception& e) {
				result.success = false;
				result.message = "规划异常: " + std::string(e.what());
				LOG_ERROR("[SurveyMultiPointsTaskPlanner] 子任务 [{}] 规划异常: {}", result.sub_task_id, e.what());
			}

			return result;
		}

		/**
		 * @brief 应用避障约束到轨迹
		 */
		bool SurveyMultiPointsTaskPlanner::applyAvoidanceConstraints(
			Trajectory& trajectory,
			const std::vector<AvoidanceConstraint>& constraints,
			SingleTaskPlanningResult& result) {

			if (constraints.empty()) {
				LOG_DEBUG("[SurveyMultiPointsTaskPlanner] 无避障约束需要应用");
				return true;
			}

			LOG_DEBUG("[SurveyMultiPointsTaskPlanner] 开始应用 {} 个避障约束", constraints.size());

			try {
				// 简化实现：检查轨迹是否与约束冲突
				for (const auto& constraint : constraints) {
					LOG_DEBUG("[SurveyMultiPointsTaskPlanner] 检查约束: 类型={}, 来源UAV={}",
						static_cast<int>(constraint.type), constraint.source_uav_id);

					// 这里可以实现具体的约束检查逻辑
					// 当前为简化实现，仅记录约束信息
					if (constraint.type == AvoidanceConstraint::AVOID_TRAJECTORY) {
						LOG_DEBUG("[SurveyMultiPointsTaskPlanner] 应用轨迹避障约束: 来源UAV={}, 安全边距={:.1f}m",
							constraint.source_uav_id, constraint.safety_margin);
					}
					else if (constraint.type == AvoidanceConstraint::AVOID_AREA) {
						LOG_DEBUG("[SurveyMultiPointsTaskPlanner] 应用区域避障约束: 避障区域点数={}, 安全边距={:.1f}m",
							constraint.area_to_avoid.size(), constraint.safety_margin);
					}
					else if (constraint.type == AvoidanceConstraint::AVOID_TIME_WINDOW) {
						LOG_DEBUG("[SurveyMultiPointsTaskPlanner] 应用时间窗口避障约束: 时间范围=[{:.1f}, {:.1f}]",
							constraint.start_time, constraint.end_time);
					}
				}

				LOG_DEBUG("[SurveyMultiPointsTaskPlanner] 避障约束应用完成");
				return true;

			} catch (const std::exception& e) {
				result.warnings.push_back(WarningEvent(WarningType::PLANNING_ERROR,
					"应用避障约束异常: " + std::string(e.what()),
					0.0, {}, result.uav_id));
				LOG_ERROR("[SurveyMultiPointsTaskPlanner] 应用避障约束异常: {}", e.what());
				return false;
			}
		}

	} // namespace NSPlanning
} // namespace NSDrones