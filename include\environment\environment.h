// include/environment/environment.h
#pragma once

#include "core/types.h"
#include "environment/environment_fwd.h"
#include "core/entity_state.h"

#include "environment/coordinate/coordinate_manager.h"
#include "environment/maps/igridmap.h"
#include "environment/indices/ispatial_index.h"
#include "environment/collision/collision_types.h"
#include "environment/storage/object_storage.h"
#include "environment/indices/attribute_index.h"

#include "params/parameters.h"
#include "params/paramregistry.h"

#include "utils/logging.h"
#include "utils/object_id.h"
#include "utils/thread_safe_cache.h"

#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <string>
#include <functional>
#include <optional>
#include <filesystem>
#include <map>
#include <utility>
#include <nlohmann/json.hpp>
#include <grid_map_core/grid_map_core.hpp>

namespace grid_map { class GridMap; }

namespace NSDrones {
	namespace NSCore { class EntityObject; }
	namespace NSUtils { class GeometryManager; }
	namespace NSEnvironment {
		class Obstacle;
		using ObstaclePtr = std::shared_ptr<Obstacle>;
		using ConstObstaclePtr = std::shared_ptr<const Obstacle>;
		class Zone;
		using ZonePtr = std::shared_ptr<Zone>;
		using ConstZonePtr = std::shared_ptr<const Zone>;

		// 碰撞检测相关前向声明
		using ObjectMap = std::unordered_map<ObjectID, std::shared_ptr<EntityObject>>;
		template<typename ObjectMap> class CollisionEngine;

		// 地图相关前向声明
		class TiledGridMap;
		class SingleGridMap;

		// 坐标系统相关前向声明
		class CoordinateManager;
		class TaskSpace;
	}
	namespace NSUav { class Uav; }
	namespace NSAlgorithm {
		// 算法接口前向声明
		class ITaskAllocator;
		class IPathPlanner;
		class ITrajectoryOptimizer;
		class ITrajectoryEvaluator;
	}
}

namespace NSDrones {
	namespace NSEnvironment {
		/**
		 * @struct SpatialQueryResult
		 * @brief 空间查询结果的通用结构体，包含一组对象ID。
		 */
		struct SpatialQueryResult {
			std::vector<ObjectID> object_ids; ///< 查询到的对象ID列表。
		};

		/**
		 * @struct AttributeQueryResult
		 * @brief 属性查询结果的通用结构体，包含一组对象ID。
		 */
		struct AttributeQueryResult {
			std::vector<ObjectID> object_ids; ///< 根据属性查询到的对象ID列表。
		};

		/**
		 * @namespace Attributes
		 * @brief 定义用于属性索引的常用属性键名常量。
		 */
		namespace Attributes {
			// 对象类型属性
			constexpr char TYPE[] = "type";          ///< 对象类型属性键
			constexpr char CATEGORY[] = "category";  ///< 对象类别属性键
			constexpr char STATUS[] = "status";      ///< 对象状态属性键
			constexpr char OWNER[] = "owner";        ///< 对象所有者属性键
			constexpr char TEAM[] = "team";          ///< 对象所属团队属性键
			constexpr char PRIORITY[] = "priority";  ///< 对象优先级属性键

			// 常见对象类型值
			namespace Types {
				constexpr char UAV[] = "UAV";            ///< 无人机
				constexpr char OBSTACLE[] = "OBSTACLE";  ///< 障碍物
				constexpr char ZONE[] = "ZONE";          ///< 区域
				constexpr char TARGET[] = "TARGET";      ///< 目标
				constexpr char SENSOR[] = "SENSOR";      ///< 传感器
			}

			// 常见状态值
			namespace Status {
				constexpr char ACTIVE[] = "ACTIVE";      ///< 活动状态
				constexpr char INACTIVE[] = "INACTIVE";  ///< 非活动状态
				constexpr char DAMAGED[] = "DAMAGED";    ///< 损坏状态
				constexpr char DESTROYED[] = "DESTROYED";///< 销毁状态
			}
		}

		/**
		 * @class Environment
		 * @brief 环境管理器类，负责管理仿真环境中的所有实体对象和状态
		 *
		 * 提供统一的接口来管理、查询和操作环境中的所有对象，包括实体对象、区域、障碍物等。
		 * 负责维护空间索引、属性索引和碰撞检测。
		 *
		 * 采用单例模式，全局唯一实例，支持多个智能指针引用。
		 */
		class Environment : public std::enable_shared_from_this<Environment> {
			// 友元类声明，允许EntityObject访问私有方法
			friend class EntityObject;

		private:
			// 单例模式相关
			static std::shared_ptr<Environment> instance_;
			static std::mutex instance_mutex_;

			std::shared_ptr<IGridMap> mapDataSource_; ///< 指向地图数据源实例的共享指针
			std::shared_ptr<CoordinateManager> coordinate_manager_; ///< 坐标系统管理器
			NSParams::ParamRegistry& param_registry_;       ///< 对参数注册表的引用，用于参数定义和元数据
			std::shared_ptr<NSParams::ParamValues> global_params_; ///< 存储从配置加载的全局参数值（共享指针，内部线程安全）

			// --- 对象存储和索引 ---
			ObjectStorage<std::unordered_map<ObjectID, std::shared_ptr<EntityObject>>> object_storage_; ///< 对象存储管理器
			AttributeIndex attribute_index_; ///< 属性索引管理器
			std::shared_ptr<ISpatialIndex> spatial_index_; ///< 空间索引管理器
			mutable std::shared_mutex objects_mutex_; ///< 用于保护对象存储、属性索引和空间索引并发访问的读写锁

			// --- 碰撞检测 ---
			std::shared_ptr<CollisionEngine<ObjectMap>> collision_engine_; ///< 碰撞引擎



			// --- 算法对象专门指针 ---
			std::shared_ptr<NSAlgorithm::ITaskAllocator> task_allocator_; ///< 任务分配器
			std::shared_ptr<NSAlgorithm::IPathPlanner> path_planner_; ///< 路径规划器
			std::shared_ptr<NSAlgorithm::ITrajectoryOptimizer> trajectory_optimizer_; ///< 轨迹优化器
			std::shared_ptr<NSAlgorithm::ITrajectoryEvaluator> trajectory_evaluator_; ///< 轨迹评估器

			bool is_map_loaded_ = false; ///< 标记地图数据是否已成功加载

			// --- 统一缓存系统 ---
			mutable NSUtils::ObjectIdCache unified_cache_; ///< 统一查询结果缓存
			std::chrono::minutes cache_max_age_{5}; ///< 缓存最大存活时间（默认5分钟）
			size_t cache_max_size_{1000}; ///< 缓存最大条目数（默认1000）

			// --- 私有辅助方法 ---
			/**
			 * @brief 构造函数（私有，通过 createInstance 调用）
			 * @param registry 参数注册表引用
			 */
			explicit Environment(NSParams::ParamRegistry& registry);

			// --- 初始化相关方法 ---
			/**
			 * @brief (内部) 使用 params 模块高级接口加载全局参数
			 * @param config_json 配置JSON（global_parameter_values节点）
			 * @return 加载成功返回true
			 */
			bool loadGlobalParameters(const nlohmann::json& config_json);

			/**
			 * @brief (内部) 配置缓存参数（直接从config_json读取）
			 * @param config_json 配置JSON
			 * @return 配置成功返回true
			 */
			bool preConfigureCacheSettings(const nlohmann::json& config_json);

			/**
			 * @brief (内部) 配置缓存参数（从全局参数读取）
			 * @return 配置成功返回true
			 * @deprecated 使用preConfigureCacheSettings替代
			 */
			bool configureCacheSettings();

			// --- 索引更新相关方法 ---
			/**
			 * @brief (内部) 更新对象的属性索引
			 * @param snapshot 实体状态快照
			 */
			void updateAttributeIndexes(const EntityStateSnapshot& snapshot);

			/**
			 * @brief (内部) 更新对象的关系索引
			 * @param snapshot 实体状态快照
			 */
			void updateRelationshipIndexes(const EntityStateSnapshot& snapshot);

			/**
			 * @brief (内部) 更新对象的空间索引
			 * @param snapshot 实体状态快照
			 */
			void updateSpatialIndexes(const EntityStateSnapshot& snapshot);

			/**
			 * @brief (内部) 通知环境对象状态已更新，用于更新相关索引
			 * @param object_id 对象ID
			 * @param snapshot 实体状态快照
			 */
			void notifyObjectUpdate(const ObjectID& object_id, const EntityStateSnapshot& snapshot);

			/**
			 * @brief (内部) 检查给定点是否与环境中的动态障碍物发生碰撞
			 * @param point 要检查的点 (假定为全局坐标)
			 * @param time_stamp 检查的时间点
			 * @param safety_margin 安全边距
			 * @param ignored_object_ids 忽略的对象ID集合
			 * @return 如果发生碰撞则返回true，否则返回false
			 */
			bool checkCollisionWithDynamicObstacles(const EcefPoint& point,
				Time time_stamp,
				double safety_margin,
				const std::unordered_set<ObjectID>& ignored_object_ids) const;

			/**
			 * @brief (内部) 检查给定线段是否与环境中的动态障碍物发生碰撞
			 * @param p1 线段起点
			 * @param p2 线段终点
			 * @param t1 起点时间
			 * @param t2 终点时间
			 * @param safety_margin 安全边距
			 * @param ignored_object_ids 忽略的对象ID集合
			 * @return 如果发生碰撞则返回true，否则返回false
			 */
			bool checkCollisionWithDynamicObstacles(const EcefPoint& p1,
				const EcefPoint& p2,
				Time t1,
				Time t2,
				double safety_margin,
				const std::unordered_set<ObjectID>& ignored_object_ids) const;

			/**
			 * @brief (内部) 检查是否存在循环引用
			 * @param child 子对象
			 * @param potential_parent 潜在的父对象
			 * @return 如果存在循环引用则返回true，否则返回false
			 */
			bool hasCircularReference(const std::shared_ptr<EntityObject>& child,
				const std::shared_ptr<EntityObject>& potential_parent);

			// 注意：直接的缓存失效方法已被移除，以确保一致使用函数注入模式
			// 所有缓存失效操作应通过ThreadSafeCache的executeUpdate系列方法进行

			/**
			 * @brief (内部) 生成缓存键
			 * @param query_type 查询类型
			 * @param parameters 查询参数
			 * @return 缓存键字符串
			 */
			std::string generateCacheKey(const std::string& query_type,
				const std::vector<std::string>& parameters) const;

			/**
			 * @brief (内部) 生成空间区域的缓存键（本地坐标系）
			 * @param region 空间区域
			 * @return 缓存键字符串
			 */
			std::string generateSpatialCacheKey(const BoundingBox& region) const;

			/**
			 * @brief (内部) 生成WGS84区域的缓存键
			 * @param region WGS84区域
			 * @param filter 可选的查询过滤器
			 * @return 缓存键字符串
			 */
			std::string generateWGS84RegionCacheKey(const WGS84BoundingBox& region,
				const std::optional<SpatialQueryFilter>& filter = std::nullopt) const;

			/**
			 * @brief (内部) 生成WGS84点查询的缓存键
			 * @param point WGS84点
			 * @param radius 查询半径
			 * @param filter 可选的查询过滤器
			 * @return 缓存键字符串
			 */
			std::string generateWGS84PointCacheKey(const WGS84Point& point, double radius,
				const std::optional<SpatialQueryFilter>& filter = std::nullopt) const;

			/**
			 * @brief (内部) 生成ECEF点的缓存键
			 * @param ecef_point ECEF坐标点
			 * @return 缓存键字符串
			 */
			std::string generateEcefPointCacheKey(const EcefPoint& ecef_point) const;

			/**
			 * @brief (内部) 从缓存获取查询结果
			 * @param cache_key 缓存键
			 * @return 缓存的对象ID向量，如果缓存未命中则返回空
			 */
			std::optional<std::vector<ObjectID>> getCachedObjectIds(const std::string& cache_key) const;

			/**
			 * @brief (内部) 将查询结果存入缓存
			 * @param cache_key 缓存键
			 * @param object_ids 对象ID向量
			 * @param query_type 查询类型
			 */
			void setCachedObjectIds(const std::string& cache_key,
				const std::vector<ObjectID>& object_ids,
				const std::string& query_type) const;

			/**
			 * @brief (内部) 验证缓存一致性
			 * @param cache_key 缓存键
			 * @param expected_ids 期望的对象ID向量
			 * @return 缓存是否一致
			 */
			bool validateCacheConsistency(const std::string& cache_key,
				const std::vector<ObjectID>& expected_ids) const;

			/**
			 * @brief (内部) 优化的碰撞检测 - 动态对象与静态对象分离
			 * @param point 检测点
			 * @param time_stamp 时间戳
			 * @param safety_margin 安全边距
			 * @param ignored_object_ids 忽略的对象ID集合
			 * @return 如果发生碰撞则返回true
			 */
			bool checkCollisionOptimized(const WGS84Point& wgs84_point,
				Time time_stamp,
				double safety_margin,
				const std::unordered_set<ObjectID>& ignored_object_ids) const;

			/**
			 * @brief (内部) 查找或匹配TaskSpace
			 * @param origin WGS84原点坐标
			 * @param space_id TaskSpace名称引用，输入期望的ID，输出实际匹配的ID
			 * @return 找到匹配的TaskSpace返回智能指针，否则返回nullptr
			 * @note 此方法只查找已存在的TaskSpace，不创建新的
			 */
			std::shared_ptr<TaskSpace> getOrCreateTaskSpace(const WGS84Point& origin, std::string& space_id);

		public:
			// === 单例模式接口 ===

			/**
			 * @brief 删除拷贝构造和赋值操作
			 */
			Environment(const Environment&) = delete;
			Environment& operator=(const Environment&) = delete;

			/**
			 * @brief 创建全局环境实例（只能调用一次）
			 * @param registry 参数注册表
			 * @return 环境实例的共享指针
			 * @throws std::runtime_error 如果实例已存在
			 */
			static std::shared_ptr<Environment> createInstance(NSParams::ParamRegistry& registry);

			/**
			 * @brief 获取全局环境实例
			 * @return 环境实例的共享指针
			 * @throws std::runtime_error 如果实例不存在
			 */
			static std::shared_ptr<Environment> getInstance();

			/**
			 * @brief 检查全局实例是否存在
			 * @return 如果实例存在返回 true
			 */
			static bool hasInstance();

			/**
			 * @brief 销毁全局实例（主要用于测试和程序退出）
			 */
			static void destroyInstance();

			/**
			 * @brief 获取当前实例的引用计数
			 * @return 引用计数
			 */
			static long getInstanceRefCount();

			/**
			 * @brief 析构函数
			 */
			~Environment() = default;

			/**
			 * @brief 初始化环境
			 * @param config_json 配置JSON
			 * @return 如果成功初始化则返回true，否则返回false
			 */
			bool initialize(const nlohmann::json& config_json);

			// --- 核心组件访问 ---
			/**
			 * @brief 获取地图数据源
			 * @return 地图数据源指针
			 */
			std::shared_ptr<IGridMap> getMapDataSource() const;

			/**
			 * @brief 获取坐标系统管理器
			 * @return 坐标系统管理器指针
			 */
			std::shared_ptr<CoordinateManager> getCoordinateManager() const;

			/**
			 * @brief 获取参数注册表
			 * @return 参数注册表引用
			 */
			NSParams::ParamRegistry& getParamRegistry();

			/**
			 * @brief 获取参数注册表（常量版本）
			 * @return 常量参数注册表引用
			 */
			const NSParams::ParamRegistry& getParamRegistry() const;

			/**
			 * @brief 获取碰撞引擎
			 * @return 碰撞引擎指针
			 */
			std::shared_ptr<CollisionEngine<ObjectMap>> getCollisionEngine() const;



			/**
			 * @brief 设置坐标系统管理器
			 * @param manager 坐标系统管理器指针
			 */
			void setCoordinateManager(std::shared_ptr<CoordinateManager> manager);

			/**
			 * @brief 获取任务空间
			 * @param space_id 空间ID，默认为全局坐标空间
			 * @return 任务空间共享指针，如果不存在则返回nullptr
			 */
			std::shared_ptr<TaskSpace> getTaskSpace(const std::string& space_id = Constants::GLOBAL_TASK_SPACE_ID) const;

			// --- 算法对象管理 ---
			/**
			 * @brief 设置任务分配器
			 * @param allocator 任务分配器指针
			 */
			void setTaskAllocator(std::shared_ptr<NSAlgorithm::ITaskAllocator> allocator);

			/**
			 * @brief 获取任务分配器
			 * @return 任务分配器指针
			 */
			std::shared_ptr<NSAlgorithm::ITaskAllocator> getTaskAllocator() const;

			/**
			 * @brief 设置路径规划器
			 * @param planner 路径规划器指针
			 */
			void setPathPlanner(std::shared_ptr<NSAlgorithm::IPathPlanner> planner);

			/**
			 * @brief 获取路径规划器
			 * @return 路径规划器指针
			 */
			std::shared_ptr<NSAlgorithm::IPathPlanner> getPathPlanner() const;

			/**
			 * @brief 设置轨迹优化器
			 * @param optimizer 轨迹优化器指针
			 */
			void setTrajectoryOptimizer(std::shared_ptr<NSAlgorithm::ITrajectoryOptimizer> optimizer);

			/**
			 * @brief 获取轨迹优化器
			 * @return 轨迹优化器指针
			 */
			std::shared_ptr<NSAlgorithm::ITrajectoryOptimizer> getTrajectoryOptimizer() const;

			/**
			 * @brief 设置轨迹评估器
			 * @param evaluator 轨迹评估器指针
			 */
			void setTrajectoryEvaluator(std::shared_ptr<NSAlgorithm::ITrajectoryEvaluator> evaluator);

			/**
			 * @brief 获取轨迹评估器
			 * @return 轨迹评估器指针
			 */
			std::shared_ptr<NSAlgorithm::ITrajectoryEvaluator> getTrajectoryEvaluator() const;

			/**
			 * @brief 获取对象存储的引用
			 * @return 对象存储的引用
			 */
			const ObjectStorage<std::unordered_map<ObjectID, std::shared_ptr<EntityObject>>>& getObjectStorage() const { return object_storage_; }

			/**
			 * @brief 获取空间索引的引用
			 * @return 空间索引的引用
			 */
			const ISpatialIndex& getSpatialIndex() const {
				if (!spatial_index_) {
					throw std::runtime_error("空间索引管理器未初始化");
				}
				return *spatial_index_;
			}

			// --- 全局参数访问 ---
			/**
			 * @brief 获取全局参数值
			 * @param key 参数键
			 * @return 参数值的可选引用，如果不存在则为空
			 */
			std::optional<NSParams::ParamValue> getGlobalParam(const std::string& key) const;

			/**
			 * @brief 设置全局参数值
			 * @param key 参数键
			 * @param value 参数值
			 * @return 如果设置成功则返回true，否则返回false
			 */
			bool setGlobalParam(const std::string& key, const NSParams::ParamValue& value);

			/**
			 * @brief 获取全局参数值，如果不存在则返回默认值
			 * @tparam T 参数值类型
			 * @param key 参数键
			 * @param default_value 默认值
			 * @return 参数值或默认值
			 */
			template<typename T>
			T getGlobalParamOrDefault(const std::string& key, const T& default_value) const {
				if (global_params_ && global_params_->hasParam(key)) {
					try {
						auto variant_value = global_params_->getValueVariant(key);
						if (variant_value.has_value()) {
							if (const T* val_ptr = std::get_if<T>(&variant_value.value())) {
								return *val_ptr;
							}
						}
						return default_value;
					} catch (const std::exception&) {
						return default_value;
					}
				}
				return default_value;
			}

			/**
			 * @brief 获取全局参数值的共享指针（线程安全）
			 * @return 全局参数值的共享指针
			 */
			std::shared_ptr<NSParams::ParamValues> getGlobalParamValues() const {
				return global_params_;  // ParamValues 内部已线程安全
			}

			/**
			 * @brief 获取全局参数值的引用（向后兼容，但不推荐使用）
			 * @return 全局参数值的const引用
			 * @deprecated 请使用 getGlobalParamValues() 获取共享指针
			 */
			[[deprecated("Use getGlobalParamValues() for thread-safe access")]]
			const NSParams::ParamValues& getGlobalParamValuesRef() const {
				if (!global_params_) {
					throw std::runtime_error("Environment: 全局参数未初始化，无法提供有效的 ParamValues 引用。请先调用 initialize() 方法。");
				}
				return *global_params_;  // ParamValues 内部已线程安全
			}

			// --- 地图加载与管理 ---
			/**
			 * @brief 加载地图数据（从全局参数中读取配置）
			 * @return 如果成功加载则返回true，否则返回false
			 */
			bool loadMapData();

			/**
			 * @brief 地图是否已加载
			 * @return 如果地图已加载则返回true，否则返回false
			 */
			bool isMapLoaded() const { return is_map_loaded_; }

			// --- 对象管理 ---
			/**
			 * @brief 添加实体对象
			 * @tparam T 对象类型
			 * @param obj 对象指针
			 * @return 如果成功添加则返回true，否则返回false
			 */
			template<typename T>
			bool addObject(std::shared_ptr<T> obj);

			/**
			 * @brief 移除对象
			 * @param id 对象ID
			 * @return 如果成功移除则返回true，否则返回false
			 */
			bool removeObject(const ObjectID& id);

			/**
			 * @brief 批量添加对象
			 * @tparam T 对象类型，必须继承自EntityObject
			 * @param objects 对象指针向量
			 * @return 成功添加的对象数量
			 */
			template<typename T>
			size_t addObjects(const std::vector<std::shared_ptr<T>>& objects) {
				static_assert(std::is_base_of_v<EntityObject, T>,
					"T must be derived from EntityObject");

				size_t success_count = 0;
				bool any_success = false;
				std::vector<ObjectID> successfully_added_ids;

				for (const auto& obj : objects) {
					if (!obj) {
						LOG_WARN("环境: 批量添加中遇到空对象指针，跳过");
						continue;
					}

					bool addition_success = true;

					// 添加到对象存储
					try {
						if (object_storage_.addObject(obj)) {
							LOG_DEBUG("环境: 对象 {} 已添加到对象存储", obj->getId());
						} else {
							LOG_WARN("环境: 对象 {} 添加到对象存储失败", obj->getId());
							addition_success = false;
						}
					} catch (const std::exception& e) {
						LOG_ERROR("环境: 添加对象 {} 到对象存储时发生异常: {}", obj->getId(), e.what());
						addition_success = false;
					}

					if (addition_success) {
						// 如果有空间索引，添加到空间索引
						if (spatial_index_) {
							try {
								SpatialObject spatial_obj;
								spatial_obj.id = obj->getId();
								spatial_obj.bounds = obj->getBoundingBox();
								spatial_obj.type = obj->getTypeTag();

								spatial_index_->addObject(spatial_obj);
								LOG_DEBUG("环境: 对象 {} 已添加到空间索引", obj->getId());
							} catch (const std::exception& e) {
								LOG_WARN("环境: 向空间索引添加对象 {} 失败: {}", obj->getId(), e.what());
								addition_success = false;
							}
						}

						// 添加到属性索引
						try {
							attribute_index_.addOrUpdateAttribute(obj->getId(), Attributes::TYPE, obj->getTypeTag());
							LOG_DEBUG("环境: 对象 {} 已添加到属性索引", obj->getId());
						} catch (const std::exception& e) {
							LOG_WARN("环境: 向属性索引添加对象 {} 失败: {}", obj->getId(), e.what());
							addition_success = false;
						}

						if (addition_success) {
							++success_count;
							any_success = true;
							successfully_added_ids.push_back(obj->getId());
							LOG_DEBUG("环境: 对象 {} 完全添加成功", obj->getId());
						} else {
							LOG_WARN("环境: 对象 {} 部分添加失败，可能导致不一致", obj->getId());
						}
					}
				}

				// 只有在有成功添加的对象时才失效缓存
				if (any_success) {
					// 使用函数注入模式处理缓存失效
					unified_cache_.executeUpdateWithFullInvalidation([&successfully_added_ids]() {
						// 这里不需要实际的数据更新，只是批量操作的缓存失效
						return true;
					});
					LOG_DEBUG("环境: 批量添加操作完成，成功添加 {} 个对象", successfully_added_ids.size());
				}

				LOG_INFO("环境: 批量添加 {} 个对象，完全成功 {} 个", objects.size(), success_count);
				return success_count;
			}

			/**
			 * @brief 批量移除对象
			 * @param ids 对象ID向量
			 * @return 成功移除的对象数量
			 */
			size_t removeObjects(const std::vector<ObjectID>& ids);

			/**
			 * @brief 根据ID获取对象
			 * @tparam T 期望的对象类型
			 * @param id 对象ID
			 * @return 对象指针，如果未找到或类型不匹配则返回nullptr
			 */
			template<typename T = EntityObject>
			std::shared_ptr<const T> getObjectById(const ObjectID& id) const {
				return object_storage_.getObject<T>(id);
			}

			/**
			 * @brief 根据ID获取可修改的对象
			 * @tparam T 期望的对象类型
			 * @param id 对象ID
			 * @return 可修改的对象指针，如果未找到或类型不匹配则返回nullptr
			 */
			template<typename T = EntityObject>
			std::shared_ptr<T> getMutableObjectById(const ObjectID& id) {
				return object_storage_.getObject<T>(id);
			}

			/**
			 * @brief 获取所有特定类型的对象
			 * @tparam T 对象类型
			 * @return 对象指针向量
			 */
			template<typename T>
			std::vector<std::shared_ptr<const T>> getAllObjectsByType() const {
				// 生成缓存键（基于类型名）
				std::string type_name = typeid(T).name();
				std::string cache_key = generateCacheKey("objects_by_type", {type_name});

				// 使用函数注入模式：ThreadSafeCache处理所有缓存逻辑
				auto object_ids = unified_cache_.getOrCompute(cache_key, [this]() {
					// 数据提供函数：执行实际查询并提取ObjectID，需要保护数据源访问
					std::shared_lock<std::shared_mutex> lock(objects_mutex_);
					auto objects = object_storage_.getObjectsByType<T>();
					std::vector<ObjectID> object_ids;
					object_ids.reserve(objects.size());
					for (const auto& obj : objects) {
						object_ids.push_back(obj->getId());
					}
					LOG_DEBUG("环境查询: 按类型获取 {} 个对象（已缓存）", object_ids.size());
					return object_ids;
				}, "object");

				// 根据缓存的ObjectID构建结果，添加对象存在性检查
				std::vector<std::shared_ptr<const T>> result;
				result.reserve(object_ids.size());
				{
					std::shared_lock<std::shared_mutex> lock(objects_mutex_);
					for (const auto& id : object_ids) {
						auto obj = this->getObjectById<T>(id);
						if (obj) {
							result.push_back(obj);
						} else {
							// 对象已被删除，记录警告但继续处理其他对象
							LOG_WARN("环境查询: 缓存中的对象ID {} 对应的对象已不存在", id);
						}
					}
				}
				return result;
			}

			/**
			 * @brief 获取所有可移动对象
			 * @return 可移动对象指针向量
			 */
			std::vector<std::shared_ptr<const EntityObject>> getMovableObjects() const;

			/**
			 * @brief 获取所有静态对象
			 * @return 静态对象指针向量
			 */
			std::vector<std::shared_ptr<const EntityObject>> getStaticObjects() const;

			// --- 对象关系管理 ---
			/**
			 * @brief 设置对象的父对象关系
			 * @param child_id 子对象ID
			 * @param new_parent_id 新的父对象ID，如果为 INVALID_OBJECT_ID 则移除父对象关系
			 * @return 如果成功设置则返回true，否则返回false
			 */
			bool setParentRelationship(const ObjectID& child_id, const ObjectID& new_parent_id);

			// --- 属性管理 ---
			/**
			 * @brief 设置对象的属性
			 * @param obj_id 对象ID
			 * @param key 属性键
			 * @param value 属性值
			 * @return 如果成功设置则返回true，否则返回false
			 */
			bool setObjectAttribute(const ObjectID& obj_id, const std::string& key, const std::string& value);

			/**
			 * @brief 批量设置对象的属性
			 * @param obj_id 对象ID
			 * @param attributes 属性键值对映射
			 * @return 如果成功设置则返回true，否则返回false
			 */
			bool setObjectAttributes(const ObjectID& obj_id,
				const std::unordered_map<std::string, std::string>& attributes);

			/**
			 * @brief 移除对象的属性
			 * @param obj_id 对象ID
			 * @param key 属性键
			 * @return 如果成功移除则返回true，否则返回false
			 */
			bool removeObjectAttribute(const ObjectID& obj_id, const std::string& key);

			/**
			 * @brief 获取对象的属性值
			 * @param obj_id 对象ID
			 * @param key 属性键
			 * @return 属性值的可选引用，如果不存在则为空
			 */
			std::optional<std::string> getObjectAttributeValue(const ObjectID& obj_id, const std::string& key) const;

			/**
			 * @brief 获取对象的所有属性
			 * @param obj_id 对象ID
			 * @return 属性键值对映射
			 */
			std::unordered_map<std::string, std::string> getAllObjectAttributes(const ObjectID& obj_id) const;

			/**
			 * @brief 检查对象是否具有特定属性
			 * @param obj_id 对象ID
			 * @param key 属性键
			 * @return 如果对象具有该属性则返回true，否则返回false
			 */
			bool hasObjectAttribute(const ObjectID& obj_id, const std::string& key) const;

			// --- 查询接口 ---
			/**
			 * @brief 根据属性查找对象ID
			 * @param attribute_key 属性键
			 * @param attribute_value 属性值
			 * @return 对象ID向量
			 */
			std::vector<ObjectID> findObjectIdsByAttribute(
				const std::string& attribute_key, const std::string& attribute_value) const;

			/**
			 * @brief 根据属性查找对象
			 * @tparam T 期望的对象类型
			 * @param attribute_key 属性键
			 * @param attribute_value 属性值
			 * @return 对象指针向量
			 */
			template<typename T = EntityObject>
			std::vector<std::shared_ptr<const T>> findObjectsByAttribute(
				const std::string& attribute_key, const std::string& attribute_value) const {

				// 复用已缓存的findObjectIdsByAttribute方法
				auto ids = findObjectIdsByAttribute(attribute_key, attribute_value);
				std::vector<std::shared_ptr<const T>> result;

				// 添加对象存在性检查
				{
					std::shared_lock<std::shared_mutex> lock(objects_mutex_);
					for (const auto& id : ids) {
						auto obj = getObjectById<T>(id);
						if (obj) {
							result.push_back(obj);
						} else {
							// 对象已被删除，记录警告但继续处理其他对象
							LOG_WARN("环境查询: 缓存中的对象ID {} 对应的对象已不存在", id);
						}
					}
				}

				return result;
			}

			/**
			 * @brief 根据多个属性查找对象（逻辑与关系）
			 * @tparam T 期望的对象类型
			 * @param attributes 属性键值对列表
			 * @return 对象指针向量
			 */
			template<typename T = EntityObject>
			std::vector<std::shared_ptr<const T>> findObjectsByAttributes(
				const std::vector<std::pair<std::string, std::string>>& attributes) const {

				// 生成缓存键
				std::vector<std::string> params;
				for (const auto& [key, value] : attributes) {
					params.push_back(key + "=" + value);
				}
				std::string cache_key = generateCacheKey("multi_attr", params);

				// 使用函数注入模式：ThreadSafeCache处理所有缓存逻辑
				auto ids = unified_cache_.getOrCompute(cache_key, [this, &attributes]() {
					// 数据提供函数：执行实际的多属性查询，需要保护数据源访问
					std::shared_lock<std::shared_mutex> lock(objects_mutex_);
					return attribute_index_.findByAttributes(attributes);
				}, "attribute");

				// 根据ObjectID构建结果，添加对象存在性检查
				std::vector<std::shared_ptr<const T>> result;
				{
					std::shared_lock<std::shared_mutex> lock(objects_mutex_);
					for (const auto& id : ids) {
						auto obj = getObjectById<T>(id);
						if (obj) {
							result.push_back(obj);
						} else {
							// 对象已被删除，记录警告但继续处理其他对象
							LOG_WARN("环境查询: 缓存中的对象ID {} 对应的对象已不存在", id);
						}
					}
				}

				return result;
			}

			/**
			 * @brief 使用自定义谓词查找对象
			 * @tparam T 期望的对象类型
			 * @param predicate 判断对象是否匹配的谓词函数
			 * @return 对象指针向量
			 */
			template<typename T = EntityObject>
			std::vector<std::shared_ptr<const T>> findObjectsByPredicate(
				const std::function<bool(const EntityObject&)>& predicate) const {

				std::vector<std::shared_ptr<const T>> result;

				// 保护数据源访问
				std::shared_lock<std::shared_mutex> lock(objects_mutex_);
				auto all_objects = object_storage_.getObjectsByType<EntityObject>();

				for (const auto& obj : all_objects) {
					if (predicate(*obj)) {
						auto typed_obj = std::dynamic_pointer_cast<const T>(obj);
						if (typed_obj) {
							result.push_back(typed_obj);
						}
					}
				}

				return result;
			}

			/**
			 * @brief 根据属性键和值查找对象 (返回AttributeQueryResult，向后兼容)
			 * @param attribute_key 属性键
			 * @param attribute_value 属性值
			 * @return 属性查询结果
			 */
			AttributeQueryResult findObjectsByAttribute(
				const std::string& attribute_key, const std::string& attribute_value) const;

			/**
			 * @brief 使用自定义筛选函数查找对象 (返回AttributeQueryResult，向后兼容)
			 * @param filter_func 判断对象是否匹配的谓词函数
			 * @return 属性查询结果
			 */
			AttributeQueryResult findObjectsByFilter(
				const std::function<bool(const EntityObject&)>& filter_func) const;

			/**
			 * @brief 根据属性获取对象弱指针 (向后兼容)
			 * @param attribute_name 属性键
			 * @param attribute_value 属性值
			 * @return 对象弱指针向量
			 */
			std::vector<std::weak_ptr<const EntityObject>>
				getObjectsByAttribute(const std::string& attribute_name, const std::string& attribute_value) const;

			// --- 区域管理（统一使用ObjectStorage） ---
			/**
			 * @brief 添加区域（委托给统一对象管理）
			 * @param zone 区域指针
			 * @return 如果成功添加则返回true，否则返回false
			 */
			bool addZone(std::shared_ptr<Zone> zone);

			/**
			 * @brief 移除区域（委托给统一对象管理）
			 * @param zone_id 区域ID
			 * @return 如果成功移除则返回true，否则返回false
			 */
			bool removeZone(const ObjectID& zone_id);

			/**
			 * @brief 根据ID获取区域（使用统一对象查询）
			 * @param zone_id 区域ID
			 * @return 区域指针，如果未找到则返回nullptr
			 */
			std::shared_ptr<const Zone> getZoneById(const ObjectID& zone_id) const;

			/**
			 * @brief 获取所有区域（使用统一对象查询）
			 * @return 区域指针向量
			 */
			std::vector<std::shared_ptr<const Zone>> getAllZones() const;

			/**
			 * @brief 根据类型获取区域（使用ObjectStorage的优化查询）
			 * @param type 区域类型
			 * @return 区域指针向量
			 */
			std::vector<std::shared_ptr<const Zone>> getZonesByType(ZoneType type) const;

			// --- 空间查询 ---
			/**
			 * @brief 查询特定区域内的对象（本地坐标系）
			 * @param region 查询区域
			 * @return 空间查询结果
			 */
			SpatialQueryResult findObjectsInRegion(const BoundingBox& region) const;

			/**
			 * @brief 查询特定区域内的对象（WGS84坐标系）
			 * @param region WGS84坐标系查询区域
			 * @param filter 可选的查询过滤器
			 * @return 对象ID列表
			 */
			std::vector<ObjectID> findObjectsInRegion(
				const WGS84BoundingBox& region,
				const std::optional<SpatialQueryFilter>& filter = std::nullopt) const;

			/**
			 * @brief 查询特定点附近的对象（WGS84坐标系）
			 * @param point WGS84坐标系查询点
			 * @param radius 查询半径（米）
			 * @param filter 可选的查询过滤器
			 * @return 对象ID列表
			 */
			std::vector<ObjectID> findObjectsNearPoint(
				const WGS84Point& point, double radius,
				const std::optional<SpatialQueryFilter>& filter = std::nullopt) const;

			/**
			 * @brief 检查两个对象是否可能相交
			 * @param objectId1 第一个对象ID
			 * @param objectId2 第二个对象ID
			 * @return 如果对象的包围盒相交返回true，否则返回false
			 */
			bool objectsIntersect(const ObjectID& objectId1, const ObjectID& objectId2) const;

			/**
			 * @brief 获取对象的WGS84边界盒
			 * @param objectId 对象ID
			 * @return 对象的WGS84边界盒，如果对象不存在则返回std::nullopt
			 */
			std::optional<WGS84BoundingBox> getObjectBounds(const ObjectID& objectId) const;

			/**
			 * @brief 获取空间索引中的所有对象ID
			 * @return 所有对象ID的列表
			 */
			std::vector<ObjectID> getAllSpatialObjectIds() const;

			/**
			 * @brief 配置空间索引的查询缓存
			 * @param enable 是否启用缓存
			 * @param maxSize 最大缓存条目数
			 * @param maxAge 最大缓存生存时间
			 * @return 配置是否成功
			 */
			bool configureSpatialIndexCache(bool enable, size_t maxSize = 100,
				std::chrono::milliseconds maxAge = std::chrono::milliseconds(5000));

			/**
			 * @brief 清除空间索引的查询缓存
			 */
			void clearSpatialIndexCache();

			/**
			 * @brief 获取空间索引的内存使用估算
			 * @return 内存使用量（字节）
			 */
			size_t getSpatialIndexMemoryUsage() const;

			/**
			 * @brief 查询包含特定点的区域
			 * @param point 查询点
			 * @return 区域指针向量
			 */
			std::vector<ConstZonePtr> getZonesContainingPoint(const EcefPoint& ecef_point) const;

			/**
			 * @brief 查询与特定点相交的区域
			 * @param ecef_point 查询点（ECEF坐标）
			 * @return 区域指针向量
			 */
			std::vector<ConstZonePtr> getIntersectingZones(const EcefPoint& ecef_point) const;

			/**
			 * @brief 查询与线段相交的区域
			 * @param ecef_p1 线段起点（ECEF坐标）
			 * @param ecef_p2 线段终点（ECEF坐标）
			 * @return 区域指针向量
			 */
			std::vector<ConstZonePtr> getIntersectingZones(const EcefPoint& ecef_p1, const EcefPoint& ecef_p2) const;

			/**
			 * @brief 查询被违反的区域
			 * @param ecef_point 查询点（ECEF坐标）
			 * @return 区域指针向量
			 */
			std::vector<ConstZonePtr> getViolatedZones(const EcefPoint& ecef_point) const;

			// --- 碰撞检测 ---
			/**
			 * @brief 检查位置是否安全（无碰撞）
			 * @param point 检查点
			 * @param safety_radius 安全半径
			 * @param time_stamp 时间戳
			 * @param ignored_object_ids 忽略的对象ID集合
			 * @return 如果位置安全则返回true，否则返回false
			 */
			bool isPositionSafe(const WGS84Point& wgs84_point, double safety_radius = 0.1,
				Time time_stamp = 0.0,
				const std::unordered_set<ObjectID>& ignored_object_ids = {}) const;

			/**
			 * @brief 检查路径段是否安全（无碰撞）
			 * @param start_pos 起点
			 * @param end_pos 终点
			 * @param safety_radius 安全半径
			 * @param start_time 起点时间
			 * @param end_time 终点时间
			 * @param num_steps 检查步数
			 * @param ignored_object_ids 忽略的对象ID集合
			 * @return 如果路径段安全则返回true，否则返回false
			 */
			bool isPathSegmentSafe(const WGS84Point& start_wgs84, const WGS84Point& end_wgs84,
				double safety_radius = 0.1, Time start_time = 0.0, Time end_time = 0.0,
				int num_steps = 10, const std::unordered_set<ObjectID>& ignored_object_ids = {}) const;

			/**
			 * @brief 检查位置是否有效（向后兼容）
			 * @param point 检查点
			 * @param time_stamp 时间戳
			 * @param safety_margin 安全边距
			 * @param check_threats 是否检查威胁
			 * @param check_no_fly 是否检查禁飞区
			 * @param ignored_object_ids 忽略的对象ID集合
			 * @return 如果位置有效则返回true，否则返回false
			 */
			bool isPositionValid(const EcefPoint& point, Time time_stamp, double safety_margin = 0.0,
				bool check_threats = true, bool check_no_fly = true,
				const std::unordered_set<ObjectID>& ignored_object_ids = {}) const;

			/**
			 * @brief 检查线段是否有效
			 * @param p1 线段起点
			 * @param p2 线段终点
			 * @param t1 起点时间
			 * @param t2 终点时间
			 * @param safety_margin 安全边距
			 * @param check_threats 是否检查威胁
			 * @param check_no_fly 是否检查禁飞区
			 * @param ignored_object_ids 忽略的对象ID集合
			 * @return 如果线段有效则返回true，否则返回false
			 */
			bool isSegmentValid(const EcefPoint& ecef_p1, const EcefPoint& ecef_p2, Time t1, Time t2,
				double safety_margin = 0.0, bool check_threats = true, bool check_no_fly = true,
				const std::unordered_set<ObjectID>& ignored_object_ids = {}) const;

			/**
			 * @brief 检查动态对象与静态对象之间的碰撞
			 * @param safety_margin 安全边距
			 * @param ignored_object_ids 忽略的对象ID集合
			 * @return 如果有碰撞则返回true，否则返回false
			 */
			bool checkDynamicStaticCollisions(double safety_margin = 0.0,
				const std::unordered_set<ObjectID>& ignored_object_ids = {}) const;

			/**
			 * @brief 检查动态对象之间的碰撞
			 * @param safety_margin 安全边距
			 * @param ignored_object_ids 忽略的对象ID集合
			 * @return 如果有碰撞则返回true，否则返回false
			 */
			bool checkDynamicDynamicCollisions(double safety_margin = 0.0,
				const std::unordered_set<ObjectID>& ignored_object_ids = {}) const;

			// --- 地图数据访问 ---
			/**
			 * @brief 获取指定WGS84坐标点的高程值
			 * @param wgs84_point WGS84坐标点
			 * @param layer_name 图层名称
			 * @return 高程值的可选引用，如果不存在则为空
			 */
			std::optional<double> getElevationAtGlobalPoint(
				const WGS84Point& wgs84_point,
				const std::string& layer_name = "elevation") const;

			/**
			 * @brief 获取指定ECEF坐标点的高程值
			 * @param ecef_point ECEF坐标点
			 * @param layer_name 图层名称
			 * @return 高程值的可选引用，如果不存在则为空
			 */
			std::optional<double> getElevationAtGlobalPoint(
				const EcefPoint& ecef_point,
				const std::string& layer_name = "elevation") const;

			/**
			 * @brief 获取地物类型
			 * @param latitude 纬度
			 * @param longitude 经度
			 * @return 地物类型和有效性标志
			 */
			std::pair<FeatureType, bool> getFeatureType(double latitude, double longitude) const;

			/**
			 * @brief 获取地物高度
			 * @param latitude 纬度
			 * @param longitude 经度
			 * @return 地物高度和有效性标志
			 */
			std::pair<double, bool> getFeatureHeight(double latitude, double longitude) const;

			/**
			 * @brief 获取地面高度
			 * @param ecef_point ECEF坐标点
			 * @return 地面高度和有效性标志
			 */
			std::pair<double, bool> getGroundAltitude(const EcefPoint& ecef_point) const;

			/**
			 * @brief 获取指定点的调整后高度（考虑地形和安全高度）
			 * @param ecef_point 查询点（ECEF坐标）
			 * @param height_strategy 高度策略
			 * @param reference_height 参考高度
			 * @return 调整后的高度值
			 */
			std::optional<double> getAdjustedHeightAtPoint(const WGS84Point& ecef_point, AltitudeType height_strategy, double reference_height) const;

		private:
			// --- 内部几何运算接口（使用ECEF坐标） ---
			std::vector<ConstZonePtr> getZonesContainingPointECEF(const EcefPoint& ecef_point) const;
			std::vector<ConstZonePtr> getIntersectingZonesECEF(const EcefPoint& ecef_point) const;
			std::vector<ConstZonePtr> getIntersectingZonesECEF(const EcefPoint& ecef_p1, const EcefPoint& ecef_p2) const;
			std::vector<ConstZonePtr> getViolatedZonesECEF(const EcefPoint& ecef_point) const;
			std::pair<double, bool> getGroundAltitudeECEF(const EcefPoint& ecef_point) const;
		};

	} // namespace NSEnvironment
} // namespace NSDrones

using namespace NSDrones::NSEnvironment;