﻿  task_allocator.cpp
  config.cpp
  main.cpp
  mission.cpp
  task.cpp
  itask_planner.cpp
  mission_planner.cpp
  followpath_taskplanner.cpp
  loiterpoint_taskplanner.cpp
  scanarea_taskplanner.cpp
  surveycylinder_taskplanner.cpp
  surveymultipoints_taskplanner.cpp
  surveysphere_taskplanner.cpp
  fixedwing_dynamics.cpp
  multirotor_dynamics.cpp
  vtol_dynamics.cpp
  fixedwing_energies.cpp
  multirotor_energies.cpp
  vtol_energies.cpp
  flight_strategy.cpp
  正在生成代码...
  正在编译...
  idynamic_model.cpp
  ienergy_model.cpp
  uav.cpp
  uav_config.cpp
  enum_utils.cpp
  file_utils.cpp
  orientation_utils.cpp
  正在生成代码...
    正在创建库 E:/source/dronesplanning/build/Release/drones_planning.lib 和对象 E:/source/dronesplanning/build/Release/drones_planning.exp
main.obj : error LNK2019: 无法解析的外部符号 "public: bool __cdecl NSDrones::NSMission::ControlPoint::isValid(void)const " (?isValid@ControlPoint@NSMission@NSDrones@@QEBA_NXZ)，函数 "public: virtual bool __cdecl NSDrones::NSMission::SurveyCylinderTaskParams::isValid(void)const " (?isValid@SurveyCylinderTaskParams@NSMission@NSDrones@@UEBA_NXZ) 中引用了该符号
main.obj : error LNK2019: 无法解析的外部符号 "public: __cdecl NSDrones::NSMission::Task::Task(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,enum NSDrones::NSCore::TaskType,class std::variant<class std::shared_ptr<class NSDrones::NSMission::PointTarget>,class std::shared_ptr<class NSDrones::NSMission::LineTarget>,class std::shared_ptr<class NSDrones::NSMission::AreaTarget>,class std::shared_ptr<class NSDrones::NSMission::VolumeTarget>,class std::shared_ptr<class NSDrones::NSMission::ObjectTarget> >,class NSDrones::NSMission::CapabilityRequirement,class std::map<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class std::shared_ptr<class NSDrones::NSMission::ITaskStrategy>,struct std::less<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > >,class std::allocator<struct std::pair<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const ,class std::shared_ptr<class NSDrones::NSMission::ITaskStrategy> > > >,class std::shared_ptr<struct NSDrones::NSMission::ITaskParams>,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >)" (??0Task@NSMission@NSDrones@@QEAA@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4TaskType@NSCore@2@V?$variant@V?$shared_ptr@VPointTarget@NSMission@NSDrones@@@std@@V?$shared_ptr@VLineTarget@NSMission@NSDrones@@@2@V?$shared_ptr@VAreaTarget@NSMission@NSDrones@@@2@V?$shared_ptr@VVolumeTarget@NSMission@NSDrones@@@2@V?$shared_ptr@VObjectTarget@NSMission@NSDrones@@@2@@4@VCapabilityRequirement@12@V?$map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$shared_ptr@VITaskStrategy@NSMission@NSDrones@@@2@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$shared_ptr@VITaskStrategy@NSMission@NSDrones@@@2@@std@@@2@@4@V?$shared_ptr@UITaskParams@NSMission@NSDrones@@@4@0@Z)，函数 "class std::shared_ptr<class NSDrones::NSMission::Task> __cdecl std::make_shared<class NSDrones::NSMission::Task,char const (&)[16],enum NSDrones::NSCore::TaskType,class std::shared_ptr<class NSDrones::NSMission::AreaTarget> &,class NSDrones::NSMission::CapabilityRequirement &,class std::map<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class std::shared_ptr<class NSDrones::NSMission::ITaskStrategy>,struct std::less<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > >,class std::allocator<struct std::pair<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const ,class std::shared_ptr<class NSDrones::NSMission::ITaskStrategy> > > > &,class std::shared_ptr<struct NSDrones::NSMission::ScanAreaTaskParams> &,char const (&)[25]>(char const (&)[16],enum NSDrones::NSCore::TaskType &&,class std::shared_ptr<class NSDrones::NSMission::AreaTarget> &,class NSDrones::NSMission::CapabilityRequirement &,class std::map<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class std::shared_ptr<class NSDrones::NSMission::ITaskStrategy>,struct std::less<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > >,class std::allocator<struct std::pair<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const ,class std::shared_ptr<class NSDrones::NSMission::ITaskStrategy> > > > &,class std::shared_ptr<struct NSDrones::NSMission::ScanAreaTaskParams> &,char const (&)[25])" (??$make_shared@VTask@NSMission@NSDrones@@AEAY0BA@$$CBDW4TaskType@NSCore@3@AEAV?$shared_ptr@VAreaTarget@NSMission@NSDrones@@@std@@AEAVCapabilityRequirement@23@AEAV?$map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$shared_ptr@VITaskStrategy@NSMission@NSDrones@@@2@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$shared_ptr@VITaskStrategy@NSMission@NSDrones@@@2@@std@@@2@@7@AEAV?$shared_ptr@UScanAreaTaskParams@NSMission@NSDrones@@@7@AEAY0BJ@$$CBD@std@@YA?AV?$shared_ptr@VTask@NSMission@NSDrones@@@0@AEAY0BA@$$CBD$$QEAW4TaskType@NSCore@NSDrones@@AEAV?$shared_ptr@VAreaTarget@NSMission@NSDrones@@@0@AEAVCapabilityRequirement@NSMission@4@AEAV?$map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$shared_ptr@VITaskStrategy@NSMission@NSDrones@@@2@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$shared_ptr@VITaskStrategy@NSMission@NSDrones@@@2@@std@@@2@@0@AEAV?$shared_ptr@UScanAreaTaskParams@NSMission@NSDrones@@@0@AEAY0BJ@$$CBD@Z) 中引用了该符号
mission.obj : error LNK2001: 无法解析的外部符号 "public: __cdecl NSDrones::NSMission::Task::Task(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,enum NSDrones::NSCore::TaskType,class std::variant<class std::shared_ptr<class NSDrones::NSMission::PointTarget>,class std::shared_ptr<class NSDrones::NSMission::LineTarget>,class std::shared_ptr<class NSDrones::NSMission::AreaTarget>,class std::shared_ptr<class NSDrones::NSMission::VolumeTarget>,class std::shared_ptr<class NSDrones::NSMission::ObjectTarget> >,class NSDrones::NSMission::CapabilityRequirement,class std::map<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class std::shared_ptr<class NSDrones::NSMission::ITaskStrategy>,struct std::less<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > >,class std::allocator<struct std::pair<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const ,class std::shared_ptr<class NSDrones::NSMission::ITaskStrategy> > > >,class std::shared_ptr<struct NSDrones::NSMission::ITaskParams>,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >)" (??0Task@NSMission@NSDrones@@QEAA@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4TaskType@NSCore@2@V?$variant@V?$shared_ptr@VPointTarget@NSMission@NSDrones@@@std@@V?$shared_ptr@VLineTarget@NSMission@NSDrones@@@2@V?$shared_ptr@VAreaTarget@NSMission@NSDrones@@@2@V?$shared_ptr@VVolumeTarget@NSMission@NSDrones@@@2@V?$shared_ptr@VObjectTarget@NSMission@NSDrones@@@2@@4@VCapabilityRequirement@12@V?$map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$shared_ptr@VITaskStrategy@NSMission@NSDrones@@@2@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$shared_ptr@VITaskStrategy@NSMission@NSDrones@@@2@@std@@@2@@4@V?$shared_ptr@UITaskParams@NSMission@NSDrones@@@4@0@Z)
E:\source\dronesplanning\build\Release\drones_planning.exe : fatal error LNK1120: 2 个无法解析的外部命令
