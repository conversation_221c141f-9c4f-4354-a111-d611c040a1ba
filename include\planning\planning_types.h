// include/planning/planning_types.h
#pragma once

#include "core/types.h"
#include "mission/control_point.h"
#include "mission/task.h"
#include "uav/uav_fwd.h"
#include "utils/coordinate_converter.h"
#include "utils/orientation_utils.h"
#include <vector>
#include <string>
#include <memory>
#include <map>
#include <optional>
#include <variant>

namespace NSDrones {
	namespace NSPlanning {
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSMission;

		// === 前向声明 ===
		struct TrajectoryEvaluationMetrics;
		struct CoordinationConstraint;
		class Trajectory;

		// === 统一的轨迹点定义 ===
		/**
		 * @struct TrajectoryPoint
		 * @brief 统一的轨迹点定义
		 *
		 * 设计原则：
		 * - 外部接口统一使用WGS84坐标系
		 * - 内部计算统一使用ECEF坐标系
		 * - 通过CoordinateConverter进行转换
		 */
		struct TrajectoryPoint {
			// === 外部接口数据（WGS84坐标系） ===
			WGS84Point position;                                    ///< WGS84位置坐标
			Time time_stamp = 0.0;                                  ///< 时间戳（秒）
			Vector3D velocity_ned = Vector3D::Zero();               ///< NED坐标系速度向量
			Orientation orientation = Orientation::Identity();       ///< 姿态四元数
			std::vector<PayloadActionCommand> payload_actions;      ///< 载荷动作命令

			// === 内部计算辅助方法 ===
			/**
			 * @brief 获取ECEF坐标（用于内部计算）
			 */
			EcefPoint getEcefPosition() const {
				return CoordinateConverter::wgs84ToECEF(position);
			}

			/**
			 * @brief 获取ECEF速度向量（用于内部计算）
			 */
			Vector3D getEcefVelocity() const {
				return CoordinateConverter::velocityNedToEcef(velocity_ned, position);
			}

			// === 构造函数 ===
			TrajectoryPoint() = default;

			TrajectoryPoint(const WGS84Point& pos,
						   Time time = 0.0,
						   const Vector3D& vel_ned = Vector3D::Zero(),
						   const Orientation& att = Orientation::Identity(),
						   const std::vector<PayloadActionCommand>& actions = {})
				: position(pos), time_stamp(time), velocity_ned(vel_ned),
				  orientation(att), payload_actions(actions) {}

			// === 实用方法 ===
			bool isValid() const {
				return position.isValid() &&
					   std::isfinite(time_stamp) &&
					   time_stamp >= 0.0;
			}

			std::string toString() const {
				return fmt::format("TrajectoryPoint(pos={}, t={:.2f}s)",
								 position.toString(), time_stamp);
			}

			// === 转换方法 ===
			/**
			 * @brief 从ControlPoint创建TrajectoryPoint
			 * @param control_point 控制点
			 * @param time_stamp 时间戳
			 * @param velocity_ned NED速度向量（如果控制点有速度要求，会被覆盖）
			 * @param orientation 姿态（如果控制点有姿态要求，会被覆盖）
			 * @return 轨迹点
			 */
			static TrajectoryPoint fromControlPoint(
				const ControlPoint& control_point,
				Time time_stamp = 0.0,
				const Vector3D& velocity_ned = Vector3D::Zero(),
				const Orientation& orientation = Orientation::Identity()) {

				TrajectoryPoint tp;
				tp.position = control_point.position;
				tp.time_stamp = time_stamp;

				// 应用控制点的速度要求（如果有）
				if (control_point.hasSpeedRequirement()) {
					tp.velocity_ned = control_point.required_speed.value();
				} else {
					tp.velocity_ned = velocity_ned;
				}

				// 应用控制点的姿态要求（如果有）
				if (control_point.hasAttitudeRequirement()) {
					tp.orientation = control_point.required_attitude.value();
				} else if (control_point.hasHeadingRequirement()) {
					// 从航向角创建姿态
					double heading_rad = control_point.required_heading_deg.value() * Constants::DEG_TO_RAD;
					tp.orientation = OrientationUtils::yprToQuaternion(heading_rad * Constants::RAD_TO_DEG, 0.0, 0.0);
				} else {
					tp.orientation = orientation;
				}

				// 复制载荷动作命令
				tp.payload_actions = control_point.action_commands;

				// 如果有载荷动作对象，转换为命令
				if (control_point.action) {
					auto cmd_opt = control_point.action->getCommand();
					if (cmd_opt.has_value()) {
						tp.payload_actions.push_back(cmd_opt.value());
					}
				}

				return tp;
			}

			/**
			 * @brief 验证轨迹点是否满足控制点的约束
			 * @param control_point 控制点约束
			 * @param tolerance_factor 容差因子（默认1.0）
			 * @return 验证结果和错误信息
			 */
			std::pair<bool, std::string> validateAgainstControlPoint(
				const ControlPoint& control_point,
				double tolerance_factor = 1.0) const {

				// 位置检查
				double pos_tolerance = control_point.position_tolerance.value_or(Constants::GEOMETRY_EPSILON) * tolerance_factor;
				double distance = NSUtils::GeometryManager::calculateDistance(position, control_point.position);
				if (distance > pos_tolerance) {
					return {false, fmt::format("位置偏差{:.2f}m超过容差{:.2f}m", distance, pos_tolerance)};
				}

				// 高度检查
				if (control_point.altitude_tolerance.has_value()) {
					double alt_tolerance = control_point.altitude_tolerance.value() * tolerance_factor;
					double alt_diff = std::abs(position.altitude - control_point.required_altitude);
					if (alt_diff > alt_tolerance) {
						return {false, fmt::format("高度偏差{:.2f}m超过容差{:.2f}m", alt_diff, alt_tolerance)};
					}
				}

				// 速度检查
				if (control_point.hasSpeedLimits()) {
					double speed = velocity_ned.norm();
					if (control_point.min_speed.has_value() && speed < control_point.min_speed.value()) {
						return {false, fmt::format("速度{:.2f}m/s低于最小值{:.2f}m/s", speed, control_point.min_speed.value())};
					}
					if (control_point.max_speed.has_value() && speed > control_point.max_speed.value()) {
						return {false, fmt::format("速度{:.2f}m/s超过最大值{:.2f}m/s", speed, control_point.max_speed.value())};
					}
				}

				// 航向检查
				if (control_point.hasHeadingRequirement() && control_point.heading_tolerance_deg.has_value()) {
					Vector3D ypr = OrientationUtils::quaternionToYPR(orientation);
					double current_heading = ypr.x(); // Yaw角度（已经是度数）
					double required_heading = control_point.required_heading_deg.value();
					double heading_tolerance = control_point.heading_tolerance_deg.value() * tolerance_factor;

					double heading_diff = std::abs(current_heading - required_heading);
					// 处理角度环绕
					if (heading_diff > 180.0) heading_diff = 360.0 - heading_diff;

					if (heading_diff > heading_tolerance) {
						return {false, fmt::format("航向偏差{:.1f}°超过容差{:.1f}°", heading_diff, heading_tolerance)};
					}
				}

				return {true, "验证通过"};
			}
		};

		// === 轨迹段和完整轨迹定义 ===
		using TrajectorySegment = std::vector<TrajectoryPoint>;

		/**
		 * @class Trajectory
		 * @brief 完整的无人机轨迹，集成评估结果
		 */
		class Trajectory {
		public:
			// === 构造函数 ===
			Trajectory() = default;
			explicit Trajectory(ObjectID uav_id) : uav_id_(std::move(uav_id)) {}

			// === 基本访问接口 ===
			const ObjectID& getUavId() const { return uav_id_; }
			void setUavId(const ObjectID& id) { uav_id_ = id; }

			const TrajectorySegment& getPoints() const { return points_; }
			TrajectorySegment& getPoints() { return points_; }

			// === 轨迹操作 ===
			void addPoint(const TrajectoryPoint& point) { points_.push_back(point); }
			void addPoints(const TrajectorySegment& segment) {
				points_.insert(points_.end(), segment.begin(), segment.end());
			}
			void clear() { points_.clear(); evaluation_metrics_.reset(); }

			// === 状态查询 ===
			bool isEmpty() const { return points_.empty(); }
			size_t size() const { return points_.size(); }

			// === 统计信息 ===
			double getTotalLength() const;
			Time getTotalTime() const;

			// === 评估结果（集成到轨迹中） ===
			void setEvaluationMetrics(const TrajectoryEvaluationMetrics& metrics);
			const std::optional<TrajectoryEvaluationMetrics>& getEvaluationMetrics() const {
				return evaluation_metrics_;
			}
			bool hasEvaluationMetrics() const {
				return evaluation_metrics_.has_value();
			}

		private:
			ObjectID uav_id_ = INVALID_OBJECT_ID;
			TrajectorySegment points_;
			std::optional<TrajectoryEvaluationMetrics> evaluation_metrics_; ///< 集成评估结果
		};

		// === 子任务目标定义 ===
		/**
		 * @struct SubTaskTarget
		 * @brief 子任务目标，由原始TaskTarget分解而来
		 */
		struct SubTaskTarget {
			ObjectID sub_target_id;                     ///< 子目标ID
			TaskTargetVariant target;                   ///< 具体的子目标（AreaTarget、PointTarget等）
			std::map<std::string, ParamValue> params;  ///< 子任务特定参数

			// 与原始任务的关系
			ObjectID parent_task_id;                    ///< 父任务ID
			NSCore::TaskType task_type;                ///< 明确的任务类型（从父任务继承）
			int sub_task_index;                         ///< 在父任务中的索引

			// 任务上下文信息
			const Task* parent_task_ref = nullptr;     ///< 父任务引用（用于获取策略等）
		};

		// === 子任务分配结果 ===
		/**
		 * @struct SubTaskAssignment
		 * @brief 子任务分配结果（单机单目标）
		 */
		struct SubTaskAssignment {
			ObjectID sub_task_id;                       ///< 子任务ID
			ObjectID assigned_uav_id;                   ///< 分配的无人机ID（单机）
			SubTaskTarget sub_target;                   ///< 分配的子目标
			NSUav::UavState start_state;               ///< UAV起始状态

			// 协调信息
			std::optional<Time> earliest_start_time;    ///< 最早开始时间
			std::optional<Time> latest_finish_time;     ///< 最晚完成时间
			std::vector<ObjectID> coordination_with;    ///< 需要协调的其他子任务ID

			// === 便捷访问方法 ===

			/**
			 * @brief 获取任务类型
			 * @return 明确的任务类型
			 */
			NSCore::TaskType getTaskType() const { return sub_target.task_type; }

			/**
			 * @brief 获取父任务引用
			 * @return 父任务对象指针
			 */
			const Task* getParentTask() const { return sub_target.parent_task_ref; }
		};

		// === 任务分解结果 ===
		/**
		 * @struct TaskDecompositionResult
		 * @brief 任务分解结果
		 */
		struct TaskDecompositionResult {
			bool success = false;
			std::string message = "";
			ObjectID original_task_id;                  ///< 原始任务ID
			std::vector<SubTaskAssignment> sub_assignments; ///< 子任务分配列表

			// 分解统计
			int total_sub_tasks;                        ///< 总子任务数
			int assigned_sub_tasks;                     ///< 已分配子任务数
			std::vector<int> unassigned_sub_task_indices; ///< 未分配的子任务索引
		};

		// === Mission级别分解结果 ===
		/**
		 * @struct MissionDecompositionResult
		 * @brief 整个Mission的分解结果
		 */
		struct MissionDecompositionResult {
			bool success = false;
			std::string message = "";
			std::vector<TaskDecompositionResult> task_decompositions; ///< 各任务的分解结果
			std::vector<ObjectID> unallocated_tasks;    ///< 未分配的任务ID
		};

		// === 路径规划相关数据结构 ===
		/**
		 * @struct PathPlanningRequest
		 * @brief 路径规划请求，统一外部接口使用WGS84
		 */
		struct PathPlanningRequest {
			WGS84Point start;                                       ///< 起始点（WGS84）
			WGS84Point goal;                                        ///< 目标点（WGS84）
			const NSUav::IDynamicModel* dynamics = nullptr;        ///< 动力学模型
			const PathConstraintStrategy* constraints = nullptr;   ///< 路径约束

			// 内部计算辅助方法
			EcefPoint getStartEcef() const { return CoordinateConverter::wgs84ToECEF(start); }
			EcefPoint getGoalEcef() const { return CoordinateConverter::wgs84ToECEF(goal); }
		};

		/**
		 * @struct PathPlanningResult
		 * @brief 路径规划结果，外部接口使用WGS84，内部可转换为ECEF
		 */
		struct PathPlanningResult {
			bool success = false;                                   ///< 规划是否成功
			std::string message = "";                               ///< 结果消息
			std::vector<WGS84Point> waypoints;                     ///< 路径点（WGS84）
			std::vector<WarningEvent> warnings;                    ///< 警告事件

			// 内部计算辅助方法
			std::vector<EcefPoint> getWaypointsEcef() const {
				return CoordinateConverter::batchWgs84ToECEF(waypoints);
			}
		};

		// === 轨迹优化相关数据结构 ===
		/**
		 * @struct TrajectoryOptimizationRequest
		 * @brief 轨迹优化请求
		 */
		struct TrajectoryOptimizationRequest {
			TrajectorySegment initial_trajectory;                  ///< 初始轨迹
			const NSUav::IDynamicModel* dynamics = nullptr;        ///< 动力学模型
			const ITaskStrategyMap* strategies = nullptr;          ///< 任务策略

			// 优化参数
			bool enable_smoothing = true;                          ///< 启用平滑优化
			bool enable_time_optimization = true;                 ///< 启用时间优化
			double max_deviation = 5.0;                           ///< 最大偏差（米）
		};

		/**
		 * @struct TrajectoryOptimizationResult
		 * @brief 轨迹优化结果
		 */
		struct TrajectoryOptimizationResult {
			bool success = false;                                  ///< 优化是否成功
			std::string message = "";                              ///< 结果消息
			TrajectorySegment optimized_trajectory;               ///< 优化后的轨迹
			std::vector<WarningEvent> warnings;                   ///< 警告事件

			// 优化统计信息
			double smoothness_improvement = 0.0;                  ///< 平滑性改进
			double time_reduction = 0.0;                          ///< 时间减少
		};

		// === 轨迹评估相关数据结构 ===
		/**
		 * @struct TrajectoryEvaluationMetrics
		 * @brief 轨迹评估指标，整合了原有的评估功能
		 */
		struct TrajectoryEvaluationMetrics {
			bool is_feasible = false;                              ///< 轨迹是否可行
			std::string message = "";                              ///< 评估消息

			// 性能指标
			std::optional<double> energy_consumption;             ///< 能量消耗（Wh）
			std::optional<Time> flight_time;                      ///< 飞行时间（秒）
			std::optional<Time> endurance_remaining;              ///< 剩余续航时间（秒）
			std::optional<double> risk_score;                     ///< 风险评分（0-1）
			std::optional<double> smoothness_score;               ///< 平滑性评分（0-1）
			std::optional<double> efficiency_score;               ///< 效率评分（0-1）

			// 约束违反信息
			std::vector<std::string> constraint_violations;       ///< 约束违反列表
			std::vector<WarningEvent> warnings;                   ///< 警告事件
		};

		/**
		 * @struct TrajectoryEvaluationRequest
		 * @brief 轨迹评估请求
		 */
		struct TrajectoryEvaluationRequest {
			Trajectory trajectory;                                 ///< 要评估的轨迹
			NSUav::ConstUavPtr uav;                               ///< 无人机对象
			const Mission* mission = nullptr;                     ///< 关联任务

			// 评估选项
			bool evaluate_energy = true;                          ///< 评估能量消耗
			bool evaluate_risk = true;                            ///< 评估风险
			bool evaluate_smoothness = true;                      ///< 评估平滑性
			bool check_constraints = true;                        ///< 检查约束
		};

		// === 避障约束定义 ===
		/**
		 * @brief 避障约束信息
		 */
		struct AvoidanceConstraint {
			enum Type {
				AVOID_TRAJECTORY,    ///< 避开其他UAV的轨迹
				AVOID_AREA,         ///< 避开特定区域
				AVOID_TIME_WINDOW   ///< 避开特定时间窗口
			};

			Type type;                           ///< 约束类型
			ObjectID source_uav_id;             ///< 约束来源的UAV ID
			Trajectory trajectory_to_avoid;     ///< 需要避开的轨迹（AVOID_TRAJECTORY类型）
			std::vector<WGS84Point> area_to_avoid; ///< 需要避开的区域（AVOID_AREA类型）
			Time start_time;                    ///< 约束开始时间
			Time end_time;                      ///< 约束结束时间
			double safety_margin = 20.0;       ///< 安全边距（米）
		};

		// === 单任务规划相关结构 ===
		/**
		 * @struct SingleTaskPlanningRequest
		 * @brief 单机单任务规划请求
		 */
		struct SingleTaskPlanningRequest {
			SubTaskAssignment assignment;               ///< 子任务分配
			NSUav::UavPtr uav;                         ///< 分配的无人机

			// 上下文信息
			const Task* original_task;                 ///< 原始任务（用于获取策略等）
			std::vector<ObjectID> sibling_sub_tasks;   ///< 兄弟子任务ID（用于协调）

			// 约束信息（用于路径重规划）
			std::vector<AvoidanceConstraint> avoidance_constraints; ///< 避障约束列表
		};

		/**
		 * @struct SingleTaskPlanningResult
		 * @brief 单机单任务规划结果
		 */
		struct SingleTaskPlanningResult {
			bool success = false;
			std::string message = "";
			ObjectID sub_task_id;                      ///< 子任务ID
			ObjectID uav_id;                           ///< 无人机ID
			Trajectory trajectory;                     ///< 规划的轨迹
			std::vector<WarningEvent> warnings;       ///< 警告事件

			// 协调信息
			std::vector<CoordinationConstraint> coordination_constraints; ///< 与其他子任务的协调约束
		};

		// === 警告类型枚举 ===
		/**
		 * @enum WarningType
		 * @brief 规划过程中产生的警告类型
		 */
		enum class WarningType {
			// === 通用警告 ===
			UNKNOWN,                        ///< 未知警告类型
			CRITICAL,                       ///< 严重错误
			INTERNAL_ERROR,                 ///< 内部错误

			// === 规划相关警告 ===
			PLANNING_ERROR,                 ///< 规划错误
			PLANNING_FAILURE,               ///< 规划失败
			OPTIMIZATION_FAILED,            ///< 优化失败
			TRAJECTORY_INFEASIBLE,          ///< 轨迹不可行

			// === 分配相关警告 ===
			ALLOCATION_ERROR,               ///< 分配错误
			RESOURCE_UNAVAILABLE,           ///< 资源不可用
			PLANNER_NOT_FOUND,              ///< 找不到规划器

			// === 区域相关警告 ===
			ENTERED_WARNING_ZONE,           ///< 进入警告区域
			LEFT_WARNING_ZONE,              ///< 离开警告区域
			ENTERED_KEEPOUT_ZONE,           ///< 进入禁飞区域
			CROSS_KEEPOUT_BOUNDARY,         ///< 穿越禁飞区边界
			ENTERED_THREAT_ZONE,            ///< 进入威胁区域
			CROSS_THREAT_BOUNDARY,          ///< 穿越威胁区边界

			// === 状态相关警告 ===
			DYNAMICS_VIOLATION,             ///< 动力学约束违反
			PROXIMITY_ALERT,                ///< 接近警报
			INVALID_STATE,                  ///< 无效状态
			ENVIRONMENT_QUERY_FAILURE,      ///< 环境查询失败
			PAYLOAD_ERROR                   ///< 载荷错误
		};

		// === 综合规划结果 ===
		/**
		 * @struct PlanningResult
		 * @brief 简化的规划结果，职责清晰
		 */
		struct PlanningResult {
			// === 基本状态 ===
			bool success = false;                       ///< 整体规划是否成功
			std::string message = "";                   ///< 整体状态消息

			// === 分解结果 ===
			MissionDecompositionResult decomposition;  ///< 任务分解结果

			// === 轨迹结果 ===
			std::vector<SingleTaskPlanningResult> task_results; ///< 各子任务的轨迹规划结果
			std::vector<WarningEvent> global_warnings; ///< 全局警告事件

			// === 便捷访问方法 ===

			/**
			 * @brief 获取指定UAV的完整轨迹（所有任务的轨迹段合并）
			 * @param uav_id 无人机ID
			 * @return 完整轨迹，如果UAV未参与任何任务则返回空轨迹
			 */
			Trajectory getCompleteTrajectoryForUav(const ObjectID& uav_id) const;

			/**
			 * @brief 获取指定UAV在指定任务中的轨迹段
			 * @param uav_id 无人机ID
			 * @param task_id 任务ID
			 * @return 轨迹段，如果不存在则返回nullptr
			 */
			const Trajectory* getTrajectoryForUavInTask(const ObjectID& uav_id, const ObjectID& task_id) const;

			/**
			 * @brief 获取所有UAV的完整轨迹映射
			 * @return UAV ID -> 完整轨迹的映射
			 */
			std::map<ObjectID, Trajectory> getAllCompleteTrajectories() const;

			/**
			 * @brief 检查是否为空
			 */
			bool isEmpty() const { return task_results.empty(); }

			/**
			 * @brief 获取参与的UAV数量
			 */
			size_t getUavCount() const;

			/**
			 * @brief 清空所有数据
			 */
			void clear();
		};

		/**
		 * @struct WarningEvent
		 * @brief 规划过程中产生的警告事件
		 *
		 * 封装了警告事件的完整信息，包括类型、描述、时空信息和关联对象
		 */
		struct WarningEvent {
			// === 基本警告信息 ===
			WarningType wtype = WarningType::UNKNOWN;                       ///< 警告类型
			std::string description = "";                                   ///< 警告的文字描述
			NSCore::Time time_stamp = 0.0;                                 ///< 事件发生时间（近似）
			NSCore::WGS84Point location;                                   ///< 事件发生位置（WGS84坐标）

			// === 关联对象信息 ===
			NSUtils::ObjectID related_uav_id = NSUtils::INVALID_OBJECT_ID;  ///< 关联的无人机ID（如果适用）
			NSUtils::ObjectID related_zone_id = NSUtils::INVALID_OBJECT_ID; ///< 关联的区域ID（如果适用）
			NSUtils::ObjectID related_task_id = NSUtils::INVALID_OBJECT_ID; ///< 关联的任务ID（如果适用）

			// === 构造函数 ===
			/** @brief 默认构造函数 */
			WarningEvent() = default;

			/**
			 * @brief 完整参数构造函数
			 * @param t 警告类型
			 * @param desc 描述信息
			 * @param time 时间戳
			 * @param loc 发生位置
			 * @param uav_id 关联无人机ID
			 * @param zone_id 关联区域ID
			 * @param task_id 关联任务ID
			 */
			WarningEvent(WarningType t,
						 std::string desc,
						 NSCore::Time time = 0.0,
						 const NSCore::WGS84Point& loc = NSCore::WGS84Point(),
						 NSUtils::ObjectID uav_id = NSUtils::INVALID_OBJECT_ID,
						 NSUtils::ObjectID zone_id = NSUtils::INVALID_OBJECT_ID,
						 NSUtils::ObjectID task_id = NSUtils::INVALID_OBJECT_ID)
				: wtype(t), description(std::move(desc)), time_stamp(time), location(loc),
				  related_uav_id(std::move(uav_id)), related_zone_id(std::move(zone_id)), related_task_id(std::move(task_id)) {}
		};

		/**
		 * @enum CalculationFidelityType
		 * @brief 计算精度级别
		 *
		 * 定义了规划计算的精度级别，用于在性能和精度之间进行权衡
		 */
		enum class CalculationFidelityType {
			SIMPLE,                 ///< 简单计算（快速但精度较低）
			DETAILED,               ///< 详细计算（精度高但耗时较长）
			UNKNOWN                 ///< 未知精度级别
		};

		/**
		 * @enum TaskAllocationStrategyType
		 * @brief 任务分配策略类型
		 *
		 * 定义了任务分配器可以使用的不同分配策略
		 */
		enum class TaskAllocationStrategyType {
			CLOSEST_AVAILABLE,      ///< 选择最近的可用无人机
			LEAST_BUSY,             ///< 选择最空闲的无人机
			UNKNOWN                 ///< 未知分配策略
		};

		/**
		 * @enum ScanPatternType
		 * @brief 区域扫描模式类型
		 *
		 * 定义了区域扫描任务可以使用的不同扫描模式
		 */
		enum class ScanPatternType {
			ZIGZAG,                 ///< 之字形扫描
			PARALLEL,               ///< 平行线扫描
			SPIRAL,                 ///< 螺旋扫描
			UNKNOWN                 ///< 未知或未指定扫描模式
		};

		// === 协调约束定义 ===
		/**
		 * @struct CoordinationConstraint
		 * @brief 多机协调约束
		 */
		struct CoordinationConstraint {
			ObjectID constraint_id;                     ///< 约束ID
			std::vector<ObjectID> involved_uavs;        ///< 涉及的无人机ID
			std::string constraint_type;               ///< 约束类型（如"collision_avoidance", "formation"等）
			std::map<std::string, ParamValue> params;  ///< 约束参数
		};

		// === 辅助函数声明 ===
		/**
		 * @brief 时间参数化函数，支持WGS84外部接口和ECEF内部计算
		 */
		bool timeParameterizeConstantSpeed(
			const std::vector<WGS84Point>& path_geometry,
			const NSUav::IDynamicModel* dynamics,
			Time start_time,
			const Vector3D& start_velocity,
			double desired_speed,
			TrajectorySegment& result);

		/**
		 * @brief 合并多个轨迹段为完整轨迹
		 * @param segments 轨迹段列表
		 * @param uav_id 无人机ID
		 * @return 合并后的完整轨迹
		 */
		Trajectory mergeTrajectorySegments(
			const std::vector<TrajectorySegment>& segments,
			const ObjectID& uav_id);

		/**
		 * @brief 验证轨迹的时间一致性
		 * @param trajectory 要验证的轨迹
		 * @return 验证结果和错误信息
		 */
		std::pair<bool, std::string> validateTrajectoryTimeConsistency(
			const Trajectory& trajectory);

	} // namespace NSPlanning
} // namespace NSDrones
