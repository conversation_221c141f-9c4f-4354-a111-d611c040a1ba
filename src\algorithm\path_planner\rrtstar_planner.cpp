// src/algorithm/path_planner/ompl_rrtstar_planner.cpp
#include "algorithm/path_planner/rrtstar_planner.h"
#include "environment/environment.h"
#include "mission/task_strategies.h"
#include "utils/logging.h"
#include "core/types.h"

// OMPL Includes
#include <ompl/base/SpaceInformation.h>
#include <ompl/base/spaces/RealVectorStateSpace.h>
#include <ompl/base/objectives/PathLengthOptimizationObjective.h>
#include <ompl/base/goals/GoalStates.h>
#include <ompl/base/goals/GoalState.h>
#include <ompl/base/PlannerStatus.h>
#include <ompl/geometric/planners/rrt/RRTstar.h>
#include <ompl/geometric/PathGeometric.h>
#include <ompl/geometric/PathSimplifier.h>
#include <ompl/base/PlannerTerminationCondition.h>
#include <ompl/base/ScopedState.h>

#include <vector>
#include <cmath>
#include <memory>
#include <stdexcept>
#include <optional>
#include <utility>
#include <algorithm>
#include <limits>

namespace ob = ompl::base;
namespace og = ompl::geometric;

namespace NSDrones {
	namespace NSAlgorithm {

		// --- 辅助函数：将 OMPL 状态转换为 EcefPoint (移到类外部或保持私有) ---
		// 为了在检查器中方便使用，暂时放在这里，更好的方式可能是传递 lambda 或 OMPLRRTStarPlanner 实例
		// 或者直接在检查器内部实现转换逻辑
		EcefPoint staticOmplStateToEcef(const ob::State* state) {
			const auto* state_r3 = state->as<ob::RealVectorStateSpace::StateType>();
			if (!state_r3) {
				LOG_ERROR("staticOmplStateToEcef: 无法将状态转换为 RealVectorStateSpace::StateType！");
				// 返回 NaN 点表示错误
				return EcefPoint(std::numeric_limits<double>::quiet_NaN(),
					std::numeric_limits<double>::quiet_NaN(),
					std::numeric_limits<double>::quiet_NaN());
			}
			return EcefPoint((*state_r3)[0], (*state_r3)[1], (*state_r3)[2]);
		}


		// --- OMPL 状态有效性检查器实现 ---
		/**
		 * @class OMPLStateValidityChecker
		 * @brief 用于 OMPL 的状态有效性检查器。
		 */
		class OMPLStateValidityChecker : public ob::StateValidityChecker {
		public:
			OMPLStateValidityChecker(const ob::SpaceInformationPtr& si,
				const Environment& environment,
				double safety_margin,
				const NSMission::PathConstraintStrategy* path_constraints = nullptr)
				: ob::StateValidityChecker(si),
				environment_(environment),
				safety_margin_(safety_margin),
				path_constraints_(path_constraints)
			{
				LOG_TRACE("OMPL状态有效性检查器已创建，安全距离: {:.2f}m", safety_margin_);
			}

			bool isValid(const ob::State* state) const override {
				LOG_TRACE("检查 OMPL 状态有效性...");
				const NSMission::PathConstraintStrategy* local_constraints = path_constraints_;

				EcefPoint ecef_point = staticOmplStateToEcef(state);
				if (!std::isfinite(ecef_point.x()) || !std::isfinite(ecef_point.y()) || !std::isfinite(ecef_point.z())) {
					LOG_TRACE("状态包含NaN值，判定为无效");
					return false;
				}
				LOG_TRACE("检查ECEF点: {}", ecef_point.toString());

				bool env_valid = environment_.isPositionValid(ecef_point, 0.0, safety_margin_, false, true);
				LOG_TRACE("环境基本有效性检查结果: {}", env_valid ? "有效" : "无效");
				if (!env_valid) {
					LOG_DEBUG("状态无效详情: 点{} 未通过环境有效性检查", ecef_point.toString());
					return false;
				}

				if (local_constraints) {
					LOG_TRACE("检查路径约束策略...");
					if (local_constraints->min_altitude_agl) {
						auto ground_alt_res = environment_.getGroundAltitude(ecef_point);
						if (!ground_alt_res.second) {
							LOG_WARN("状态有效性检查：无法获取地面高度 {}，无法检查最小AGL约束。", ecef_point.toString());
						}
						else {
							double current_agl = ecef_point.z() - ground_alt_res.first;
							if (current_agl < local_constraints->min_altitude_agl - Constants::GEOMETRY_EPSILON) {
								LOG_TRACE("  失败：当前AGL {:.1f} < 最小AGL约束 {:.1f}", current_agl, local_constraints->min_altitude_agl);
								return false;
							}
							LOG_TRACE("  最小AGL检查通过 (当前 {:.1f} >= 约束 {:.1f})", current_agl, local_constraints->min_altitude_agl);
						}
					}
				}
				else {
					LOG_TRACE("无路径约束策略。");
				}
				LOG_TRACE("状态有效性最终检查结果: 有效");
				return true;
			}

		private:
			const Environment& environment_;
			double safety_margin_;
			const NSMission::PathConstraintStrategy* path_constraints_;
		};

		// --- OMPL 运动有效性检查器实现 ---
		/**
		 * @class OMPLMotionValidator
		 * @brief 用于 OMPL 的运动有效性检查器。
		 */
		class OMPLMotionValidator : public ob::MotionValidator {
		public:
			OMPLMotionValidator(const ob::SpaceInformationPtr& si,
				const Environment& environment,
				double safety_margin,
				const NSMission::PathConstraintStrategy* path_constraints = nullptr)
				: ob::MotionValidator(si),
				environment_(environment),
				safety_margin_(safety_margin),
				path_constraints_(path_constraints)
			{
				LOG_TRACE("OMPL运动有效性检查器已创建，安全距离: {:.2f}m", safety_margin_);
			}

			bool checkMotion(const ob::State* s1, const ob::State* s2) const override {
				LOG_TRACE("检查 OMPL 运动有效性...");
				const Environment& local_env = environment_;
				const NSMission::PathConstraintStrategy* local_constraints = path_constraints_;

				EcefPoint ecef_p1 = staticOmplStateToEcef(s1);
				EcefPoint ecef_p2 = staticOmplStateToEcef(s2);
				if (!std::isfinite(ecef_p1.x()) || !std::isfinite(ecef_p1.y()) || !std::isfinite(ecef_p1.z()) ||
					!std::isfinite(ecef_p2.x()) || !std::isfinite(ecef_p2.y()) || !std::isfinite(ecef_p2.z())) {
					return false;
				}
				LOG_TRACE("检查ECEF线段: {} -> {}", ecef_p1.toString(), ecef_p2.toString());

				double resolution = 1.0;
				if (local_env.getMapDataSource()) {
					auto metadata = local_env.getMapDataSource()->getMetadata();
					// 对于运动检查，使用几何平均分辨率作为步长
					// 这样既不会过度精细（性能问题），也不会过于粗糙（遗漏障碍物）
					double map_res = std::sqrt(metadata.resolution.first * metadata.resolution.second);
					resolution = std::max(Constants::GEOMETRY_EPSILON, map_res);
					if (resolution <= Constants::GEOMETRY_EPSILON) {
						LOG_WARN_ONCE("环境地图分辨率无效 (X:{:.4f}, Y:{:.4f})，运动检查使用默认步长 {}m。",
							metadata.resolution.first, metadata.resolution.second, 1.0);
						resolution = 1.0;
					}
					LOG_TRACE("地图分辨率: X={:.3f}m, Y={:.3f}m，几何平均={:.3f}m，运动检查使用: {:.3f}m",
						metadata.resolution.first, metadata.resolution.second, map_res, resolution);
				}
				LOG_TRACE("使用检查分辨率: {:.3f}m", resolution);

				bool env_valid = local_env.isSegmentValid(ecef_p1, ecef_p2, 0.0, 0.0, safety_margin_, false, true, {});
				LOG_TRACE("环境基本有效性检查结果: {}", env_valid ? "有效" : "无效");
				if (!env_valid) return false;

				if (local_constraints && local_constraints->min_altitude_agl) {
					LOG_TRACE("检查路径 AGL 约束...");
					Vector3D segment_vec = ecef_p2 - ecef_p1;
					double length = segment_vec.norm();
					if (length > resolution) {
						Vector3D direction = segment_vec.normalized();
						int num_steps = static_cast<int>(std::ceil(length / resolution));
						for (int i = 1; i < num_steps; ++i) {
							EcefPoint intermediate_ecef(ecef_p1.toVector3D() + direction * (static_cast<double>(i) * resolution));
							auto ground_alt_res = local_env.getGroundAltitude(intermediate_ecef);
							if (!ground_alt_res.second) {
								LOG_WARN("运动有效性检查：无法获取中间点 {} 地面高度，无法检查最小AGL约束。", intermediate_ecef.toString());
								continue;
							}
							double current_agl = intermediate_ecef.z() - ground_alt_res.first;
							if (current_agl < local_constraints->min_altitude_agl - Constants::GEOMETRY_EPSILON) {
								LOG_TRACE("  失败：中间点AGL {:.1f} < 最小AGL约束 {:.1f}", current_agl, local_constraints->min_altitude_agl);
								return false;
							}
						}
					}
					LOG_TRACE("路径 AGL 约束检查通过。");
				}
				LOG_TRACE("运动有效性最终检查结果: 有效");
				return true;
			}

			bool checkMotion(const ob::State* s1, const ob::State* s2, std::pair<ob::State*, double>& lastValid) const override {
				LOG_TRACE("检查 OMPL 运动并查找最后一个有效点...");
				bool motion_valid = checkMotion(s1, s2);
				if (motion_valid) {
					LOG_TRACE("整个运动有效，返回终点 (t=1.0)。");
					lastValid.first = si_->cloneState(s2);
					lastValid.second = 1.0;
				}
				else {
					LOG_TRACE("运动无效，返回起点 (t=0.0)。(简化实现)");
					lastValid.first = si_->cloneState(s1);
					lastValid.second = 0.0;
				}
				return motion_valid;
			}
		private:
			const Environment& environment_;
			double safety_margin_;
			const NSMission::PathConstraintStrategy* path_constraints_;
		};

		// --- OMPLRRTStarPlanner 类实现 ---

		// 构造函数 (用于两阶段初始化)
		RRTStarPlanner::RRTStarPlanner(ObjectID id,
			const std::string& type_tag,
			const std::string& name,
			const std::string& version)
			: AlgorithmObject(id, type_tag, name, version), IPathPlanner(), options_()
		{
			// 初始化状态空间，边界将在 initialize() 中根据参数设置
			state_space_ = std::make_shared<ob::RealVectorStateSpace>(3);
			// 可以设置一个非常宽松的默认边界，或在 initialize 中强制要求参数提供边界
			ob::RealVectorBounds default_bounds(3);
			default_bounds.setLow(-10000.0); // 示例：非常大的默认边界
			default_bounds.setHigh(10000.0);
			state_space_->as<ob::RealVectorStateSpace>()->setBounds(default_bounds);
			LOG_DEBUG("RRTStarPlanner 已构造，等待 initialize() 调用以配置 Options 和精确边界。");
		}

		// 新增: initialize 方法实现
		bool RRTStarPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) {
			LOG_INFO("RRTStarPlanner::initialize 开始...");
			if (!params) {
				LOG_ERROR("RRTStarPlanner::initialize: 传入的 ParamValues 为空指针。");
				return false;
			}

			// 从 ParamValues 和/或 raw_config 中读取参数来填充 this->options_
			try {
				// 示例: 从 ParamValues 读取参数 (假设参数键与 Options 成员名对应或有映射)
				options_.planning_time_limit = params->getValueOrDefault<double>("planning_time_limit", options_.planning_time_limit);
				options_.rrtstar_range = params->getValueOrDefault<double>("rrtstar_range", options_.rrtstar_range);
				options_.goal_bias = params->getValueOrDefault<double>("goal_bias", options_.goal_bias);
				options_.goal_threshold = params->getValueOrDefault<double>("goal_threshold", options_.goal_threshold);
				options_.safety_margin = params->getValueOrDefault<double>("safety_margin", options_.safety_margin);
				options_.simplify_path = params->getValueOrDefault<bool>("simplify_path", options_.simplify_path);
				options_.simplification_time_limit = params->getValueOrDefault<double>("simplification_time_limit", options_.simplification_time_limit);
				options_.interpolate_path = params->getValueOrDefault<bool>("interpolate_path", options_.interpolate_path);
				options_.interpolation_distance = params->getValueOrDefault<double>("interpolation_distance", options_.interpolation_distance);
				options_.default_speed = params->getValueOrDefault<double>("default_speed", options_.default_speed);

				// 处理可选的边界参数 (bounds)
				// 假设参数 "bounds_low_xyz" 和 "bounds_high_xyz" 是 std::vector<double> 类型
				auto low_bound_vec_opt = params->getValue<std::vector<double>>("bounds_low_xyz");
				auto high_bound_vec_opt = params->getValue<std::vector<double>>("bounds_high_xyz");

				if (low_bound_vec_opt && high_bound_vec_opt) {
					const auto& low_vec = low_bound_vec_opt.value();
					const auto& high_vec = high_bound_vec_opt.value();
					if (low_vec.size() == 3 && high_vec.size() == 3) {
						options_.bounds = std::make_pair(EcefPoint(low_vec[0], low_vec[1], low_vec[2]),
						                               EcefPoint(high_vec[0], high_vec[1], high_vec[2]));
						LOG_DEBUG("RRTStarPlanner::initialize: 从参数加载了自定义边界。");
					} else {
						LOG_WARN("RRTStarPlanner::initialize: 'bounds_low_xyz' 或 'bounds_high_xyz' 参数大小不为3，忽略自定义边界。");
					}
				} else {
					LOG_DEBUG("RRTStarPlanner::initialize: 未在参数中找到自定义边界，将使用环境边界或无限边界。");
					// options_.bounds 保持 std::nullopt
				}

				// 也可以从 raw_config 中读取一些非结构化或特定格式的参数
				// 例如: if (raw_config.contains("some_specific_rrt_config")) { ... }

			} catch (const std::exception& e) {
				LOG_ERROR("RRTStarPlanner::initialize: 解析参数时发生异常: {}", e.what());
				return false;
			}

			// 根据加载的 options_ 设置 OMPL 状态空间边界
			ob::RealVectorBounds ompl_bounds(3);
			auto environment = Environment::getInstance(); // 在更大作用域声明
			if (options_.bounds) {
				const auto& b = options_.bounds.value();
				ompl_bounds.setLow(0, b.first.x()); ompl_bounds.setHigh(0, b.second.x());
				ompl_bounds.setLow(1, b.first.y()); ompl_bounds.setHigh(1, b.second.y());
				ompl_bounds.setLow(2, b.first.z()); ompl_bounds.setHigh(2, b.second.z());
				LOG_INFO("OMPL RRT* (initialize): 使用参数提供的状态空间边界: X=[{:.1f}, {:.1f}], Y=[{:.1f}, {:.1f}], Z=[{:.1f}, {:.1f}]",
					         ompl_bounds.low[0], ompl_bounds.high[0], ompl_bounds.low[1], ompl_bounds.high[1], ompl_bounds.low[2], ompl_bounds.high[2]);
			}
			else {
				if (environment && environment->getMapDataSource()) {
					auto metadata = environment->getMapDataSource()->getMetadata();
					// 从元数据中获取世界坐标边界
					if (metadata.isValid()) {
						// 使用 wgs84_corners 计算世界坐标边界
						// 这里需要将 WGS84 坐标转换为世界坐标
						// 暂时使用一个合理的默认边界，或者可以从其他地方获取
						LOG_INFO("OMPL RRT* (initialize): 从地图元数据获取边界信息");
						// 使用网格尺寸和分辨率计算大致的边界
						double width = metadata.grid_size.first * metadata.resolution.first;
						double height = metadata.grid_size.second * metadata.resolution.second;
						double margin = 100.0; // 添加一些边界缓冲

						ompl_bounds.setLow(0, -width/2 - margin); ompl_bounds.setHigh(0, width/2 + margin);
						ompl_bounds.setLow(1, -height/2 - margin); ompl_bounds.setHigh(1, height/2 + margin);
						ompl_bounds.setLow(2, 0.0); ompl_bounds.setHigh(2, 500.0); // 假设最大高度500m

						LOG_INFO("OMPL RRT* (initialize): 使用地图数据源边界: X=[{:.1f}, {:.1f}], Y=[{:.1f}, {:.1f}], Z=[{:.1f}, {:.1f}]",
						        ompl_bounds.low[0], ompl_bounds.high[0], ompl_bounds.low[1], ompl_bounds.high[1], ompl_bounds.low[2], ompl_bounds.high[2]);
					} else {
						LOG_WARN("OMPL RRT* (initialize): 地图元数据无效，将使用构造时设置的默认宽松边界。");
					}
				} else {
					// 如果既没有参数指定边界，环境边界也无效，则使用构造函数中设置的非常大的默认边界，或报错
					// 这里我们依赖构造函数中已经设置的非常宽松的默认边界，并打印一个警告
					LOG_WARN("OMPL RRT* (initialize): 未从参数或环境获取有效边界，将使用构造时设置的默认宽松边界。");
	                // 或者，如果严格要求边界，可以在这里返回 false
	                // return false;
				}

				// 只有当边界不是默认的宽松边界时才真正设置，或者总是设置
				if (options_.bounds || (environment && environment->getMapDataSource())) {
					state_space_->as<ob::RealVectorStateSpace>()->setBounds(ompl_bounds);
				} // else: 构造函数中设置的默认边界仍然有效
			}

			LOG_INFO("RRTStarPlanner::initialize 完成。配置的选项: time_limit={:.2f}s, range={:.2f}, goal_bias={:.2f}, safety_margin={:.2f}",
			         options_.planning_time_limit, options_.rrtstar_range, options_.goal_bias, options_.safety_margin);
			return true;
		}

		// --- findPath 实现 ---
		bool RRTStarPlanner::findPath(const EcefPoint& start,
			const EcefPoint& goal,
			const NSUav::IDynamicModel* dynamics,
			const NSMission::PathConstraintStrategy* path_constraints,
			std::vector<EcefPoint>& path)
		{
			LOG_INFO("RRTStarPlanner::findPath: 从 {} 到 {}",
				start.toString(), goal.toString());

			if (!state_space_) { // 检查状态空间是否已初始化
				LOG_ERROR("RRTStarPlanner::findPath: StateSpace is null! Did initialize() run?");
				return false;
			}

			// 1. 创建 SpaceInformation
			auto si = std::make_shared<ob::SpaceInformation>(this->state_space_);

			// 2. 设置状态有效性检查器
			auto environment = Environment::getInstance();
			if (!environment) {
				LOG_ERROR("RRTStarPlanner::planPath: Environment 实例不存在");
				return {};
			}
			si->setStateValidityChecker(std::make_shared<OMPLStateValidityChecker>(si, *environment, this->options_.safety_margin, path_constraints));
			si->setMotionValidator(std::make_shared<OMPLMotionValidator>(si, *environment, this->options_.safety_margin, path_constraints));
			si->setup();

			// 3. 创建 ProblemDefinition
			auto pdef = std::make_shared<ob::ProblemDefinition>(si);
			if (!pdef) {
				LOG_ERROR("无法创建 OMPL ProblemDefinition 对象。");
				return false;
			}

			ob::ScopedState<ob::RealVectorStateSpace> start_state(this->state_space_);
			this->ecefToOmplState(start, start_state);

			ob::ScopedState<ob::RealVectorStateSpace> goal_ompl_state(this->state_space_);
			this->ecefToOmplState(goal, goal_ompl_state);

			if (!si->isValid(start_state.get())) {
				LOG_ERROR("提供的起点 {} 在环境中无效。", start.toString());
				LOG_ERROR("可能的原因: 1) 坐标转换错误 2) 地图数据未加载 3) 起点在禁飞区 4) 起点与障碍物冲突");
				LOG_ERROR("建议检查: 1) WGS84坐标配置是否正确 2) 地图数据是否成功加载 3) 起点高度是否合理");
				return false;
			}
			if (!si->isValid(goal_ompl_state.get())) {
				LOG_ERROR("提供的终点 {} 在环境中无效。", goal.toString());
				LOG_ERROR("可能的原因: 1) 坐标转换错误 2) 地图数据未加载 3) 终点在禁飞区 4) 终点与障碍物冲突");
				return false;
			}

			pdef->addStartState(start_state);

			auto ompl_goal = std::make_shared<ob::GoalState>(si);
			ompl_goal->setState(goal_ompl_state);
			ompl_goal->setThreshold(options_.goal_threshold);
			pdef->setGoal(ompl_goal);

			pdef->setOptimizationObjective(std::make_shared<ob::PathLengthOptimizationObjective>(si));

			auto planner = std::make_shared<og::RRTstar>(si);
			if (!planner) {
				LOG_ERROR("无法创建 OMPL RRT* 规划器对象。");
				return false;
			}

			if (options_.rrtstar_range > 0.0) {
				planner->setRange(options_.rrtstar_range);
			}
			planner->setGoalBias(options_.goal_bias);
			planner->setProblemDefinition(pdef);
			planner->setup();

			LOG_INFO("开始 OMPL 规划...");
			ob::PlannerTerminationCondition ptc = ob::timedPlannerTerminationCondition(options_.planning_time_limit);
			ob::PlannerStatus solved_status = planner->solve(ptc);

			if (solved_status == ob::PlannerStatus::EXACT_SOLUTION || solved_status == ob::PlannerStatus::APPROXIMATE_SOLUTION) {
				LOG_INFO("OMPL 规划成功，状态: {}", solved_status.asString());

				ob::PathPtr ompl_path_ptr = pdef->getSolutionPath();
				if (!ompl_path_ptr) {
					LOG_ERROR("规划声称成功，但无法获取解决方案路径！");
					return false;
				}
				auto* geometric_path_ptr = ompl_path_ptr->as<og::PathGeometric>();
				if (!geometric_path_ptr) {
					LOG_ERROR("无法将解决方案路径转换为 PathGeometric！");
					return false;
				}
				og::PathGeometric geometric_path(*geometric_path_ptr);

				if (options_.simplify_path && geometric_path.getStateCount() > 2) {
					LOG_INFO("开始路径简化 (时间限制: {:.2f}s)...", options_.simplification_time_limit);
					og::PathSimplifier simplifier(si);
					ob::PlannerTerminationCondition simplify_ptc = ob::timedPlannerTerminationCondition(options_.simplification_time_limit);
					bool simplified = simplifier.simplify(geometric_path, simplify_ptc);
					if (simplified) {
						LOG_INFO("路径简化成功。");
						if (options_.interpolate_path && options_.interpolation_distance > 0) {
							LOG_INFO("对简化后的路径进行插值 (目标距离: {:.2f}m)...", options_.interpolation_distance);
							unsigned int num_states = static_cast<unsigned int>(geometric_path.length() / options_.interpolation_distance + 1.5);
							geometric_path.interpolate(std::max(2u, num_states));
						}
					}
					else {
						LOG_WARN("路径简化未完成或失败。使用原始路径。");
						if (options_.interpolate_path && options_.interpolation_distance > 0) {
							LOG_INFO("对原始路径进行插值 (目标距离: {:.2f}m)...", options_.interpolation_distance);
							unsigned int num_states = static_cast<unsigned int>(geometric_path.length() / options_.interpolation_distance + 1.5);
							geometric_path.interpolate(std::max(2u, num_states));
						}
					}
				}
				else if (options_.interpolate_path && options_.interpolation_distance > 0 && geometric_path.getStateCount() > 1) {
					LOG_INFO("对原始路径进行插值 (目标距离: {:.2f}m)...", options_.interpolation_distance);
					unsigned int num_states = static_cast<unsigned int>(geometric_path.length() / options_.interpolation_distance + 1.5);
					geometric_path.interpolate(std::max(2u, num_states));
				}

				const auto& states = geometric_path.getStates();
				path.reserve(states.size());
				for (const auto* state_ptr : states) {
					path.push_back(this->omplStateToEcef(state_ptr));
				}

				LOG_INFO("OMPL RRT* 路径查找成功，生成路径包含 {} 个点。", path.size());
				return true;

			}
			else {
				LOG_ERROR("OMPL 规划失败，状态: {}", solved_status.asString());
				return false;
			}
		}

		// --- setParameters 实现 ---
		void RRTStarPlanner::setParameters(const std::map<std::string, double>& parameters) {
			LOG_INFO("OMPL RRT* Planner: 设置参数...");
			bool options_changed = false;
			for (const auto& pair : parameters) {
				const std::string& key = pair.first;
				double value = pair.second;
				LOG_DEBUG("尝试设置参数: {} = {:.3f}", key, value);
				if (key == "planning_time_limit") {
					options_.planning_time_limit = std::max(0.1, value);
					options_changed = true;
				}
				else if (key == "rrtstar_range") {
					options_.rrtstar_range = std::max(0.0, value);
					options_changed = true;
				}
				else if (key == "goal_bias") {
					options_.goal_bias = std::clamp(value, 0.0, 1.0);
					options_changed = true;
				}
				else if (key == "goal_threshold") {
					options_.goal_threshold = std::max(Constants::GEOMETRY_EPSILON, value);
					options_changed = true;
				}
				else if (key == "safety_margin") {
					options_.safety_margin = std::max(0.0, value);
					options_changed = true;
				}
				else if (key == "simplify_path") {
					options_.simplify_path = (value > 0.5);
					options_changed = true;
				}
				else if (key == "simplification_time_limit") {
					options_.simplification_time_limit = std::max(0.01, value);
					options_changed = true;
				}
				else if (key == "interpolate_path") {
					options_.interpolate_path = (value > 0.5);
					options_changed = true;
				}
				else if (key == "interpolation_distance") {
					options_.interpolation_distance = std::max(Constants::GEOMETRY_EPSILON, value);
					options_changed = true;
				}
				else if (key == "default_speed") {
					options_.default_speed = std::max(0.1, value);
					options_changed = true;
				}
				else {
					LOG_WARN("OMPL RRT* Planner: 未知或不支持的参数 '{}'", key);
				}
			}
			if (options_changed) {
				LOG_INFO("OMPL RRT* Planner: 参数已更新。");
			}
		}

		// --- 私有辅助函数实现 ---
		void RRTStarPlanner::ecefToOmplState(const EcefPoint& ecef_point,
			ob::ScopedState<ob::RealVectorStateSpace>& state) const
		{
			state[0] = ecef_point.x();
			state[1] = ecef_point.y();
			state[2] = ecef_point.z();
		}

		EcefPoint RRTStarPlanner::omplStateToEcef(const ob::State* state) const {
			if (!state) {
				LOG_ERROR("omplStateToEcef: 接收到空状态指针！");
				return EcefPoint(std::numeric_limits<double>::quiet_NaN(),
					std::numeric_limits<double>::quiet_NaN(),
					std::numeric_limits<double>::quiet_NaN());
			}
			const auto* state_r3 = state->as<ob::RealVectorStateSpace::StateType>();
			if (!state_r3) {
				LOG_ERROR("omplStateToEcef: 无法将 OMPL 状态转换为 RealVectorStateSpace::StateType！");
				return EcefPoint(std::numeric_limits<double>::quiet_NaN(),
					std::numeric_limits<double>::quiet_NaN(),
					std::numeric_limits<double>::quiet_NaN());
			}
			return EcefPoint((*state_r3)[0], (*state_r3)[1], (*state_r3)[2]);
		}

	} // namespace NSAlgorithm
} // namespace NSDrones