{"description": "路径跟随任务规划器参数定义，用于规划从起点到终点的飞行路径。", "parameters": [{"key": "task.followpath.speed", "type": "double", "name": "跟随速度", "description": "路径跟随时的飞行速度 (米/秒)。", "default": 15.0, "constraints": {"type": "numeric", "min": 1.0, "max": 30.0}, "required": false}, {"key": "task.followpath.altitude", "type": "double", "name": "飞行高度", "description": "路径跟随时的飞行高度 (米)。", "default": 50.0, "constraints": {"type": "numeric", "min": 5.0, "max": 500.0}, "required": false}, {"key": "task.followpath.path_tolerance", "type": "double", "name": "路径容差", "description": "偏离路径的允许距离容差 (米)。", "default": 2.0, "constraints": {"type": "numeric", "min": 0.1, "max": 10.0}, "required": false}, {"key": "task.followpath.waypoint_tolerance", "type": "double", "name": "航点容差", "description": "到达航点的距离容差 (米)。", "default": 1.0, "constraints": {"type": "numeric", "min": 0.1, "max": 5.0}, "required": false}, {"key": "task.followpath.smooth_path", "type": "bool", "name": "平滑路径", "description": "是否对生成的路径进行平滑处理。", "default": true, "required": false}]}