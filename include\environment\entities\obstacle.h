// include/environment/obstacle.h
#pragma once

#include "core/entity_object.h"
#include <string>
#include <memory>

// --- 前向声明 ---
namespace NSDrones {
	namespace NSEnvironment { class Environment; }
	namespace NSCore { class IMovementStrategy; }
}

namespace NSDrones {
	namespace NSEnvironment {
		using namespace ::NSDrones::NSUtils;
		using namespace ::NSDrones::NSCore;

		/**
		 * @class Obstacle
		 * @brief 代表环境中的障碍物，可以是静态的或动态的。
		 *        其动态性由是否关联了 IMovementStrategy 决定。
		 *        继承自 EntityObject，拥有形状和状态。
		 */
		class Obstacle : public EntityObject {
		public:
			/**
			 * @brief 构造函数。
			 * @param id 障碍物的唯一标识符。
			 * @param object_type_key 用于参数系统的对象类型键。
			 * @param name 障碍物名称。
			 * @param initial_state 障碍物的初始状态 (位置、姿态)。
			 * @param strategy (可选) 初始的移动策略。默认为 nullptr (静态)。
			 * @throws DroneException 如果 id 无效。
			 */
			Obstacle(ObjectID id,
				const std::string& object_type_key,
				const std::string& name,
				const EntityState& initial_state,
				std::shared_ptr<IMovementStrategy> strategy = nullptr);

			/** @brief 虚析构函数 */
			~Obstacle() = default;

			// --- 禁止拷贝和移动 ---
			Obstacle(const Obstacle&) = delete;
			Obstacle& operator=(const Obstacle&) = delete;
			Obstacle(Obstacle&&) = delete;
			Obstacle& operator=(Obstacle&&) = delete;

			// --- 重写 EntityObject 方法 ---
			/** @brief 返回 Obstacle 类的固定名称标签。 */
			std::string getClassName() const override { return "EntityObject.Obstacle"; }

			/**
			 * @brief 初始化障碍物对象。
			 *        由工厂函数调用。
			 * @param final_params 最终的参数集。
			 * @param raw_instance_json_config 原始 JSON 配置。
			 * @return 如果成功返回 true。
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> final_params,
				const nlohmann::json& raw_instance_json_config) override;

		};

		// 定义 Obstacle 智能指针别名
		using ObstaclePtr = std::shared_ptr<Obstacle>;
		using ConstObstaclePtr = std::shared_ptr<const Obstacle>;

	} // namespace NSEnvironment
} // namespace NSDrones