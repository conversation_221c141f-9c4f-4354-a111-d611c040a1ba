// include/core/movement_strategy.h
#pragma once

#include "core/types.h"
#include "nlohmann/json.hpp"
#include "utils/logging.h"

// 前向声明
namespace NSDrones {
	namespace NSCore { class EntityObject; }
	namespace NSEnvironment { class CoordinateConverter;}
}

namespace NSDrones {
	namespace NSCore {
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSUtils;

		/**
		 * @class IMovementStrategy
		 * @brief 可移动对象运动策略的接口。
		 *        定义了如何更新对象的状态以及可能的其他移动相关操作。
		 */
		class IMovementStrategy {
		public:
			explicit IMovementStrategy(const EntityObject& owner);

			virtual ~IMovementStrategy() = default;

			virtual bool initialize(const nlohmann::json& params, EntityObject& owner) = 0;
			/**
			 * @brief 更新拥有此策略的对象的动态状态。
			 * @param owner 拥有此移动策略的 EntityObject 对象。
			 * @param dt 时间步长 (秒)。
			 * @note 环境信息通过单例模式获取，无需直接传入。
			 */
			virtual void updateState(EntityObject& owner, Time dt) = 0;

			/**
			 * @brief 预测拥有此策略的对象在 dt 时间后的WGS84位置
			 * @param owner 拥有此移动策略的 EntityObject 对象 (const 引用，因为此方法不应修改对象状态)
			 * @param dt 时间步长 (秒)
			 * @return 预测的未来WGS84坐标位置（人类可理解的地理坐标）
			 * @note 内部使用ECEF坐标系进行精确计算，但返回WGS84坐标便于理解
			 */
			virtual WGS84Point predictWGS84Position(const EntityObject& owner, Time dt) const = 0;

			/**
			 * @brief 预测拥有此策略的对象在 dt 时间后的ECEF位置
			 * @param owner 拥有此移动策略的 EntityObject 对象 (const 引用，因为此方法不应修改对象状态)
			 * @param dt 时间步长 (秒)
			 * @return 预测的未来ECEF坐标位置（用于几何计算）
			 * @note 用于需要高精度几何计算的场景
			 */
			virtual EcefPoint predictEcefPosition(const EntityObject& owner, Time dt) const;

		protected:
			/** @brief 指向拥有此模型的无人机对象 (const 引用)。*/
			const EntityObject& owner_; // 存储 owner 的 const 引用
		};

		/**
		 * @class StillMovementStrategy
		 * @brief 代表对象静止不动。
		 */
		class StillMovementStrategy : public IMovementStrategy {
		public:
			explicit StillMovementStrategy(const EntityObject& owner)
			: IMovementStrategy(owner) {
				LOG_DEBUG("StillMovementStrategy: 已创建。");
			}
			virtual ~StillMovementStrategy() = default;

			bool initialize(const nlohmann::json& params, EntityObject& owner) override;
			void updateState(EntityObject& object, Time dt) override;
			WGS84Point predictWGS84Position(const EntityObject& object, Time dt) const override;
		};

		/**
		 * @class LinearMovementStrategy
		 * @brief 代表对象以恒定速度进行线性运动。
		 */
		class LinearMovementStrategy : public IMovementStrategy {
		private:
			Vector3D velocity_ = Vector3D::Zero(); // 存储自身的速度向量

		public:
			explicit LinearMovementStrategy(const EntityObject& owner)
				: IMovementStrategy(owner){
				LOG_DEBUG("LinearMovementStrategy: 已创建。");
			}
			LinearMovementStrategy(const EntityObject& owner, const Vector3D& velocity) 
				: IMovementStrategy(owner), velocity_(velocity) {
				LOG_DEBUG("LinearMovementStrategy: 已创建，初始速度: ({:.2f}, {:.2f}, {:.2f})", 
					velocity.x(), velocity.y(), velocity.z());
			}
			virtual ~LinearMovementStrategy() = default;

			bool initialize(const nlohmann::json& params, EntityObject& owner) override;
			void updateState(EntityObject& object, Time dt) override;
			WGS84Point predictWGS84Position(const EntityObject& object, Time dt) const override;

			void setVelocity(const Vector3D& vel) { velocity_ = vel; }
			const Vector3D& getVelocity() const { return velocity_; }
		};

		/**
		 * @class AttachedMovementStrategy
		 * @brief 代表对象附着到另一个父对象上，并保持相对偏移。
		 */
		class AttachedMovementStrategy : public IMovementStrategy {
		public:
			explicit AttachedMovementStrategy(const EntityObject& owner)
				: IMovementStrategy(owner) {
				LOG_DEBUG("AttachedMovementStrategy: 已创建。");
			}
			bool initialize(const nlohmann::json& params, EntityObject& owner) override;
			void updateState(EntityObject& object, Time dt) override;
			WGS84Point predictWGS84Position(const EntityObject& object, Time dt) const override;

		private:
			ObjectID target_parent_id_ = INVALID_OBJECT_ID; // 原始的父对象ID，用于附着
			Vector3D offset_ = Vector3D::Zero();          // 相对于父对象锚点的偏移量
			bool use_parent_orientation_ = false;         // 是否使用父对象的姿态来旋转偏移量
			// 新增成员变量
			ObjectID target_object_id_ = INVALID_OBJECT_ID; // 目标对象ID (可以与target_parent_id_不同或相同)
			Vector3D relative_position_ = Vector3D::Zero();   // 相对于target_object_id_的期望相对位置（ECEF坐标系）
		};

	} // namespace NSCore
} // namespace NSDrones 