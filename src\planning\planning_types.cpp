// src/planning/planning_types.cpp
#include "planning/planning_types.h"
#include "utils/logging.h"
#include "utils/geometry_manager.h"
#include "utils/coordinate_converter.h"
#include "uav/idynamic_model.h"
#include <algorithm>
#include <numeric>
#include <set>

namespace NSDrones {
	namespace NSPlanning {

		// === Trajectory 类实现 ===

		double Trajectory::getTotalLength() const {
			if (points_.size() < 2) {
				return 0.0;
			}

			double total_length = 0.0;
			try {
				for (size_t i = 1; i < points_.size(); ++i) {
					double segment_length = GeometryManager::calculate3DDistance(
						points_[i-1].position, points_[i].position);
					total_length += segment_length;
				}
			}
			catch (const std::exception& e) {
				LOG_WARN("轨迹长度计算失败: {}", e.what());
				return 0.0;
			}

			return total_length;
		}

		Time Trajectory::getTotalTime() const {
			if (points_.empty()) {
				return 0.0;
			}

			if (points_.size() == 1) {
				return points_[0].time_stamp;
			}

			// 返回最后一个点的时间戳减去第一个点的时间戳
			return points_.back().time_stamp - points_.front().time_stamp;
		}

		void Trajectory::setEvaluationMetrics(const TrajectoryEvaluationMetrics& metrics) {
			evaluation_metrics_ = metrics;
		}

		// === PlanningResult 类实现 ===

		Trajectory PlanningResult::getCompleteTrajectoryForUav(const ObjectID& uav_id) const {
			Trajectory complete_trajectory(uav_id);

			// 收集该UAV的所有轨迹段
			std::vector<TrajectorySegment> segments;
			for (const auto& task_result : task_results) {
				if (task_result.uav_id == uav_id && task_result.success) {
					segments.push_back(task_result.trajectory.getPoints());
				}
			}

			// 按时间排序并合并
			if (!segments.empty()) {
				complete_trajectory = mergeTrajectorySegments(segments, uav_id);
			}

			return complete_trajectory;
		}

		const Trajectory* PlanningResult::getTrajectoryForUavInTask(
			const ObjectID& uav_id, const ObjectID& task_id) const {

			for (const auto& task_result : task_results) {
				// 检查是否匹配UAV和任务
				if (task_result.uav_id == uav_id &&
					task_result.sub_task_id.find(task_id) != std::string::npos) {
					return &task_result.trajectory;
				}
			}

			return nullptr;
		}

		std::map<ObjectID, Trajectory> PlanningResult::getAllCompleteTrajectories() const {
			std::map<ObjectID, Trajectory> trajectories;

			// 收集所有参与的UAV ID
			std::set<ObjectID> uav_ids;
			for (const auto& task_result : task_results) {
				if (task_result.success) {
					uav_ids.insert(task_result.uav_id);
				}
			}

			// 为每个UAV生成完整轨迹
			for (const auto& uav_id : uav_ids) {
				trajectories[uav_id] = getCompleteTrajectoryForUav(uav_id);
			}

			return trajectories;
		}

		size_t PlanningResult::getUavCount() const {
			std::set<ObjectID> unique_uavs;
			for (const auto& task_result : task_results) {
				if (task_result.success) {
					unique_uavs.insert(task_result.uav_id);
				}
			}
			return unique_uavs.size();
		}

		void PlanningResult::clear() {
			success = false;
			message.clear();
			decomposition = MissionDecompositionResult{};
			task_results.clear();
			global_warnings.clear();
		}

		// === 辅助函数实现 ===

		Trajectory mergeTrajectorySegments(
			const std::vector<TrajectorySegment>& segments,
			const ObjectID& uav_id) {

			Trajectory merged_trajectory(uav_id);

			if (segments.empty()) {
				return merged_trajectory;
			}

			// 收集所有点并按时间排序
			std::vector<TrajectoryPoint> all_points;
			for (const auto& segment : segments) {
				all_points.insert(all_points.end(), segment.begin(), segment.end());
			}

			// 按时间戳排序
			std::sort(all_points.begin(), all_points.end(),
				[](const TrajectoryPoint& a, const TrajectoryPoint& b) {
					return a.time_stamp < b.time_stamp;
				});

			// 添加到合并轨迹中
			for (const auto& point : all_points) {
				merged_trajectory.addPoint(point);
			}

			LOG_DEBUG("轨迹段合并完成，UAV: {}, 总点数: {}", uav_id, all_points.size());
			return merged_trajectory;
		}

		std::pair<bool, std::string> validateTrajectoryTimeConsistency(
			const Trajectory& trajectory) {

			const auto& points = trajectory.getPoints();

			if (points.empty()) {
				return {true, "空轨迹，验证通过"};
			}

			if (points.size() == 1) {
				return {true, "单点轨迹，验证通过"};
			}

			// 检查时间戳单调性
			for (size_t i = 1; i < points.size(); ++i) {
				if (points[i].time_stamp < points[i-1].time_stamp) {
					return {false, fmt::format("时间戳非单调，点 {} 时间 {:.2f} < 点 {} 时间 {:.2f}",
						i, points[i].time_stamp, i-1, points[i-1].time_stamp)};
				}

				// 检查时间间隔合理性（不能为负或过大）
				double time_delta = points[i].time_stamp - points[i-1].time_stamp;
				if (time_delta > 3600.0) { // 超过1小时的间隔可能有问题
					return {false, fmt::format("时间间隔过大，点 {} 到点 {} 间隔 {:.2f}s",
						i-1, i, time_delta)};
				}
			}

			return {true, "时间一致性验证通过"};
		}


		bool timeParameterizeConstantSpeed(
			const std::vector<WGS84Point>& path_geometry,
			const NSUav::IDynamicModel* dynamics,
			Time start_time,
			const Vector3D& start_velocity,
			double desired_speed,
			TrajectorySegment& result) {

			if (path_geometry.size() < 2) {
				LOG_WARN("路径点数量不足，无法进行时间参数化");
				return false;
			}

			if (desired_speed <= 0.0) {
				LOG_WARN("期望速度无效: {}", desired_speed);
				return false;
			}

			result.clear();
			result.reserve(path_geometry.size());

			Time current_time = start_time;
			Vector3D current_velocity = start_velocity;

			// 添加起始点
			result.emplace_back(path_geometry[0], current_time, current_velocity);

			// 为后续点计算时间戳
			for (size_t i = 1; i < path_geometry.size(); ++i) {
				try {
					// 计算段长度
					double segment_length = GeometryManager::calculate3DDistance(
						path_geometry[i-1], path_geometry[i]);

					// 计算时间增量
					double time_delta = segment_length / desired_speed;
					current_time += time_delta;

					// 计算速度方向（简化为指向下一个点）
					Vector3D direction = GeometryManager::calculateDirection(
						path_geometry[i-1], path_geometry[i]);
					current_velocity = direction * desired_speed;

					// 添加轨迹点
					result.emplace_back(path_geometry[i], current_time, current_velocity);
				}
				catch (const std::exception& e) {
					LOG_WARN("时间参数化失败，段 {}: {}", i, e.what());
					return false;
				}
			}

			LOG_DEBUG("时间参数化完成，生成 {} 个轨迹点", result.size());
			return true;
		}

	} // namespace NSPlanning
} // namespace NSDrones