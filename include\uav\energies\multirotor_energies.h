// include/uav/energies/multirotor_energies.h
#pragma once

#include "uav/ienergy_model.h" 
#include "uav/uav.h"
#include <memory>                

namespace NSDrones { namespace NSUav{ class Uav; } }

namespace NSDrones {
	namespace NSUav {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class MultirotorEnergies
		 * @brief 多旋翼无人机的能量消耗模型实现。
		 *        基于简化的功率模型，考虑悬停、垂直和水平运动。
		 *        依赖于 owner_ 引用来访问参数。
		 */
		class MultirotorEnergies : public IEnergyModel {
		public:
			/**
			 * @brief 构造函数。
			 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
			 */
			explicit MultirotorEnergies(const Uav& owner); // 接收 owner 引用

			// --- 禁止拷贝和移动 ---
			MultirotorEnergies(const MultirotorEnergies&) = delete;
			MultirotorEnergies& operator=(const MultirotorEnergies&) = delete;
			MultirotorEnergies(MultirotorEnergies&&) = delete;
			MultirotorEnergies& operator=(MultirotorEnergies&&) = delete;

			/** @brief 虚析构函数 */
			~MultirotorEnergies() override = default;

			// --- 覆写 IEnergyModel 虚函数 ---
			/**
			 * @brief 计算给定状态和时间步长下的能量消耗。
			 *        参数查找基于 "energy.mr.*"。
			 * @param state 无人机当前状态。
			 * @param dt 时间步长 (秒)。
			 * @return 消耗的能量 (单位与参数 "energy.base.max_capacity" 一致, 例如 Wh)。
			 */
			double computeEnergyConsumption(const UavState& state, Time dt) const override;

			/**
			 * @brief 估算在给定状态和当前能量下的剩余续航时间。
			 *        通常基于悬停或低速飞行状态进行估算。
			 * @param current_state 当前无人机状态。
			 * @param current_energy 当前剩余能量。
			 * @return 估算的剩余续航时间 (秒)。
			 */
			Time estimateEnduranceTime(const UavState& current_state, double current_energy) const override;
		};

	} // namespace NSUav
} // namespace NSDrones