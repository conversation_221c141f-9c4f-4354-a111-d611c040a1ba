{"description": "Parameters for Trajectory Optimizer algorithms. This defines common parameters shared by all trajectory optimizer implementations.", "parameters": [{"key": "optimization_timeout_ms", "name": "优化超时时间", "description": "轨迹优化的最大允许时间 (毫秒)。", "type": "int", "default": 5000, "constraints": {"type": "numeric", "min": 500, "max": 30000}, "required": false}, {"key": "convergence_tolerance", "name": "收敛容差", "description": "优化算法的收敛容差。", "type": "double", "default": 1e-06, "constraints": {"type": "numeric", "min": 1e-10, "max": 0.001}, "required": false}, {"key": "enable_collision_avoidance", "name": "启用避障", "description": "是否在轨迹优化中考虑避障约束。", "type": "bool", "default": true, "required": false}]}