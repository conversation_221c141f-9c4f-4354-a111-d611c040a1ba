// include/algorithm/allocator/itask_allocator.h
#pragma once

#include "core/types.h"
#include "mission/mission.h"
#include "uav/uav.h"
#include "planning/planning_types.h"
#include <vector>
#include <map>
#include <memory>
#include <optional>
#include <string>
#include "nlohmann/json.hpp"
#include "utils/logging.h"
#include "uav/uav_fwd.h"
#include "uav/uav_types.h"

namespace NSDrones { namespace NSParams { class ParamValues; } }

namespace NSDrones {
	namespace NSAlgorithm {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSPlanning;

		/**
		 * @class ITaskAllocator
		 * @brief 增强的任务分配器，支持目标分解和UAV分配
		 *
		 * 职责：
		 * 1. 将复杂任务分解为多个子任务目标
		 * 2. 为子任务目标分配合适的无人机
		 * 3. 支持空间分割、点集分组等分解策略
		 */
		class ITaskAllocator {
		public:
			ITaskAllocator() = default;
			virtual ~ITaskAllocator() = default;

			ITaskAllocator(const ITaskAllocator&) = default;
			ITaskAllocator& operator=(const ITaskAllocator&) = default;
			ITaskAllocator(ITaskAllocator&&) = default;
			ITaskAllocator& operator=(ITaskAllocator&&) = default;

		public:
			/**
			 * @brief 分解并分配单个任务
			 * @param task 要分解的任务
			 * @param available_uavs 可用无人机列表
			 * @param current_uav_states 无人机当前状态
			 * @return 任务分解和分配结果
			 */
			virtual TaskDecompositionResult decomposeAndAllocateTask(
				const NSMission::Task& task,
				const std::vector<NSUav::UavPtr>& available_uavs,
				const std::map<ObjectID, NSUav::UavState>& current_uav_states
			) = 0;

			/**
			 * @brief Mission级别的分配（调用多次decomposeAndAllocateTask）
			 * @param mission 任务计划
			 * @param available_uavs 可用无人机
			 * @param current_uav_states 当前状态
			 * @return 整个Mission的分配结果
			 */
			virtual MissionDecompositionResult decomposeAndAllocateMission(
				const NSMission::Mission& mission,
				const std::vector<NSUav::UavPtr>& available_uavs,
				const std::map<ObjectID, NSUav::UavState>& current_uav_states
			) = 0;

		protected:
			/**
			 * @brief 分解TaskTarget为多个子目标
			 * @param task 原始任务
			 * @param available_uav_count 可用无人机数量
			 * @return 子目标列表
			 */
			virtual std::vector<SubTaskTarget> decomposeTaskTarget(
				const NSMission::Task& task,
				int available_uav_count
			) = 0;

			/**
			 * @brief 为子目标分配无人机
			 * @param sub_targets 子目标列表
			 * @param available_uavs 可用无人机
			 * @param current_states 当前状态
			 * @return 分配结果
			 */
			virtual std::vector<SubTaskAssignment> assignUavsToSubTargets(
				const std::vector<SubTaskTarget>& sub_targets,
				const std::vector<NSUav::UavPtr>& available_uavs,
				const std::map<ObjectID, NSUav::UavState>& current_states
			) = 0;
		};

		using ITaskAllocatorPtr = std::shared_ptr<ITaskAllocator>;

	} // namespace NSAlgorithm
} // namespace NSDrones