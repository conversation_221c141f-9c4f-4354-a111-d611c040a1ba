// include/mission/mission.h
#pragma once

#include "core/types.h" 
#include "mission/mission_fwd.h"
#include <vector>
#include <string>
#include <memory>                 
#include <filesystem>             
#include <map>        
#include "mission/task_targets.h"
#include "mission/task_strategies.h" 


namespace NSDrones {
	namespace NSMission {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class Mission
		 * @brief 代表一个完整的任务计划，由一系列有序的任务 (Task) 组成。
		 *        可以包含应用于所有任务的默认策略。
		 */
		class Mission {
		public:
			/**
			 * @brief 构造函数。
			 * @param id 任务计划的唯一标识符。
			 * @param default_strategies (可选) 任务计划级别的默认策略。
			 * @throws DroneException 如果 id 无效。
			 */
			explicit Mission(ObjectID id, ITaskStrategyMap default_strategies = {});

			/** @brief 获取任务计划 ID。 */
			const ObjectID& getId() const;
			/** @brief 获取任务计划级别的默认策略。 */
			const ITaskStrategyMap& getStrategies() const { return strategies_; }
			/** @brief 设置任务计划级别的默认策略。 */
			void setStrategies(ITaskStrategyMap strategies) { strategies_ = std::move(strategies); }

			/** @brief 在任务列表末尾添加一个任务。 */
			void addTask(TaskPtr task);
			/** @brief 在指定索引处插入一个任务。 */
			bool insertTask(size_t index, TaskPtr task);

			/**
			 * @brief 创建任务并自动继承Mission的默认策略
			 * @param id 任务ID
			 * @param type 任务类型
			 * @param target 任务目标
			 * @param requirements 能力需求
			 * @param task_params 任务参数
			 * @param additional_strategies 额外的任务特定策略（会覆盖默认策略中的同名策略）
			 * @param description 任务描述
			 * @return 创建的任务指针
			 */
			TaskPtr createTask(ObjectID id, TaskType type,
							  TaskTargetVariant target,
							  CapabilityRequirement requirements,
							  ITaskParamsPtr task_params,
							  ITaskStrategyMap additional_strategies = {},
							  std::string description = "");
			/** @brief 移除指定索引处的任务。 */
			bool removeTask(size_t index);
			/** @brief 获取任务列表 (const 引用)。 */
			const std::vector<TaskPtr>& getTasks() const;
			/** @brief 获取任务数量。 */
			size_t getTaskCount() const;
			/** @brief 清空所有任务。 */
			void clearTasks();
			/** @brief 检查任务计划是否为空（不包含任何任务）。 */
			bool isEmpty() const;

			/** @brief 通过 ID 获取特定任务。 */
			TaskPtr getTaskById(const ObjectID& task_id) const;

		private:
			ObjectID mission_id_;               // 任务计划 ID
			std::vector<TaskPtr> task_list_;   // 存储任务指针的有序列表
			ITaskStrategyMap strategies_; // 任务计划级别的默认策略

			/**
			 * @brief 合并Mission默认策略和任务特定策略
			 * @param additional_strategies 任务特定的额外策略
			 * @return 合并后的策略映射（任务特定策略会覆盖默认策略中的同名策略）
			 */
			ITaskStrategyMap mergeStrategies(const ITaskStrategyMap& additional_strategies) const;
		};

	} // namespace NSMission
} // namespace NSDrones