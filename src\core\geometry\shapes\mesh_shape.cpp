#include "core/geometry/shapes/mesh_shape.h"
#include "utils/enum_utils.h"
#include <stdexcept>
#include <sstream>
#include <algorithm>
#include <set>
#include <fstream>

namespace NSDrones {
namespace NSCore {

    MeshShape::MeshShape(const std::vector<fcl::Vector3d>& vertices, const std::vector<fcl::Triangle>& triangles)
        : vertices_(vertices), triangles_(triangles), geometry_dirty_(true) {
        validateMesh();
    }

    MeshShape::MeshShape() : geometry_dirty_(true) {
    }

    MeshShape::MeshShape(const MeshShape& other)
        : vertices_(other.vertices_), triangles_(other.triangles_), geometry_dirty_(true) {
    }

    MeshShape& MeshShape::operator=(const MeshShape& other) {
        if (this != &other) {
            vertices_ = other.vertices_;
            triangles_ = other.triangles_;
            markGeometryDirty();
        }
        return *this;
    }

    std::shared_ptr<fcl::CollisionGeometryd> MeshShape::getFCLGeometry() const {
        ensureFCLObject();
        return fcl_mesh_;
    }

    fcl::AABBd MeshShape::getAABB(const fcl::Transform3d& transform) const {
        if (vertices_.empty()) {
            return fcl::AABBd();
        }

        fcl::Vector3d min_pt = transform * vertices_[0];
        fcl::Vector3d max_pt = min_pt;

        for (size_t i = 1; i < vertices_.size(); ++i) {
            fcl::Vector3d transformed_pt = transform * vertices_[i];
            min_pt = min_pt.cwiseMin(transformed_pt);
            max_pt = max_pt.cwiseMax(transformed_pt);
        }

        fcl::AABBd aabb;
        aabb.min_ = min_pt;
        aabb.max_ = max_pt;
        return aabb;
    }

    double MeshShape::getVolume() const {
        if (triangles_.empty()) {
            return 0.0;
        }

        // 使用散度定理计算体积
        double volume = 0.0;
        for (const auto& triangle : triangles_) {
            const fcl::Vector3d& v0 = vertices_[triangle[0]];
            const fcl::Vector3d& v1 = vertices_[triangle[1]];
            const fcl::Vector3d& v2 = vertices_[triangle[2]];

            // 计算三角形对原点的体积贡献
            volume += v0.dot(v1.cross(v2));
        }

        return std::abs(volume) / 6.0;
    }

    double MeshShape::getSurfaceArea() const {
        double area = 0.0;
        for (const auto& triangle : triangles_) {
            area += computeTriangleArea(triangle);
        }
        return area;
    }

    fcl::Vector3d MeshShape::getCentroid() const {
        if (vertices_.empty()) {
            return fcl::Vector3d::Zero();
        }

        fcl::Vector3d centroid = fcl::Vector3d::Zero();
        for (const auto& vertex : vertices_) {
            centroid += vertex;
        }
        return centroid / static_cast<double>(vertices_.size());
    }

    fcl::Matrix3d MeshShape::getInertiaMatrix(double mass) const {
        // 简化的惯性张量计算
        if (vertices_.empty()) {
            return fcl::Matrix3d::Zero();
        }

        fcl::Vector3d centroid = getCentroid();
        fcl::Matrix3d inertia = fcl::Matrix3d::Zero();

        for (const auto& vertex : vertices_) {
            fcl::Vector3d r = vertex - centroid;
            double r_squared = r.squaredNorm();
            
            inertia(0, 0) += r_squared - r.x() * r.x();
            inertia(1, 1) += r_squared - r.y() * r.y();
            inertia(2, 2) += r_squared - r.z() * r.z();
            inertia(0, 1) -= r.x() * r.y();
            inertia(0, 2) -= r.x() * r.z();
            inertia(1, 2) -= r.y() * r.z();
        }

        // 对称化
        inertia(1, 0) = inertia(0, 1);
        inertia(2, 0) = inertia(0, 2);
        inertia(2, 1) = inertia(1, 2);

        return (mass / vertices_.size()) * inertia;
    }

    std::unique_ptr<IShape> MeshShape::clone() const {
        return std::make_unique<MeshShape>(*this);
    }

    nlohmann::json MeshShape::serialize() const {
        nlohmann::json j;
        j["type"] = NSUtils::enumToString(getType());
        
        nlohmann::json vertices_json = nlohmann::json::array();
        for (const auto& vertex : vertices_) {
            vertices_json.push_back({vertex.x(), vertex.y(), vertex.z()});
        }
        j["vertices"] = vertices_json;
        
        nlohmann::json triangles_json = nlohmann::json::array();
        for (const auto& triangle : triangles_) {
            triangles_json.push_back({triangle[0], triangle[1], triangle[2]});
        }
        j["triangles"] = triangles_json;
        
        return j;
    }

    bool MeshShape::deserialize(const nlohmann::json& json) {
        try {
            std::string type_str = json.at("type").get<std::string>();
            auto type_opt = NSUtils::stringToEnum<ShapeType>(type_str, ShapeType::UNKNOWN);
            if (type_opt != ShapeType::MESH) {
                return false;
            }
            
            clear();
            
            if (json.contains("vertices") && json["vertices"].is_array()) {
                for (const auto& vertex_json : json["vertices"]) {
                    if (vertex_json.is_array() && vertex_json.size() == 3) {
                        fcl::Vector3d vertex(
                            vertex_json[0].get<double>(),
                            vertex_json[1].get<double>(),
                            vertex_json[2].get<double>()
                        );
                        vertices_.push_back(vertex);
                    }
                }
            }
            
            if (json.contains("triangles") && json["triangles"].is_array()) {
                for (const auto& triangle_json : json["triangles"]) {
                    if (triangle_json.is_array() && triangle_json.size() == 3) {
                        fcl::Triangle triangle(
                            triangle_json[0].get<size_t>(),
                            triangle_json[1].get<size_t>(),
                            triangle_json[2].get<size_t>()
                        );
                        triangles_.push_back(triangle);
                    }
                }
            }
            
            validateMesh();
            markGeometryDirty();
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    std::string MeshShape::toString() const {
        std::ostringstream oss;
        oss << "MeshShape(vertices=" << vertices_.size() << ", triangles=" << triangles_.size() << ")";
        return oss.str();
    }

    bool MeshShape::containsPoint(const fcl::Vector3d& point) const {
        if (triangles_.empty()) {
            return false;
        }

        // 使用射线投射算法
        fcl::Vector3d ray_direction(1.0, 0.0, 0.0);  // 沿X轴正方向的射线
        int intersection_count = 0;

        for (const auto& triangle : triangles_) {
            const fcl::Vector3d& v0 = vertices_[triangle[0]];
            const fcl::Vector3d& v1 = vertices_[triangle[1]];
            const fcl::Vector3d& v2 = vertices_[triangle[2]];

            // 简化的射线-三角形相交测试
            fcl::Vector3d edge1 = v1 - v0;
            fcl::Vector3d edge2 = v2 - v0;
            fcl::Vector3d h = ray_direction.cross(edge2);
            double a = edge1.dot(h);

            if (std::abs(a) < 1e-10) continue;  // 射线平行于三角形

            double f = 1.0 / a;
            fcl::Vector3d s = point - v0;
            double u = f * s.dot(h);

            if (u < 0.0 || u > 1.0) continue;

            fcl::Vector3d q = s.cross(edge1);
            double v = f * ray_direction.dot(q);

            if (v < 0.0 || u + v > 1.0) continue;

            double t = f * edge2.dot(q);
            if (t > 1e-10) {  // 射线与三角形相交
                intersection_count++;
            }
        }

        return (intersection_count % 2) == 1;  // 奇数次相交表示在内部
    }

    double MeshShape::distanceToPoint(const fcl::Vector3d& point) const {
        if (triangles_.empty()) {
            return std::numeric_limits<double>::infinity();
        }

        double min_distance = std::numeric_limits<double>::max();
        bool inside = containsPoint(point);

        for (const auto& triangle : triangles_) {
            const fcl::Vector3d& v0 = vertices_[triangle[0]];
            const fcl::Vector3d& v1 = vertices_[triangle[1]];
            const fcl::Vector3d& v2 = vertices_[triangle[2]];

            // 计算点到三角形的距离
            fcl::Vector3d normal = computeTriangleNormal(triangle);
            double distance = std::abs(normal.dot(point - v0));
            min_distance = std::min(min_distance, distance);
        }

        return inside ? -min_distance : min_distance;
    }

    double MeshShape::getCharacteristicSize() const {
        if (vertices_.size() < 2) {
            return 0.0;
        }

        fcl::AABBd aabb = getAABB();
        fcl::Vector3d size = aabb.max_ - aabb.min_;
        return size.norm();
    }

    const fcl::Vector3d& MeshShape::getVertex(size_t index) const {
        validateIndex(index, vertices_.size(), "vertex");
        return vertices_[index];
    }

    const fcl::Triangle& MeshShape::getTriangle(size_t index) const {
        validateIndex(index, triangles_.size(), "triangle");
        return triangles_[index];
    }

    size_t MeshShape::addVertex(const fcl::Vector3d& vertex) {
        vertices_.push_back(vertex);
        markGeometryDirty();
        return vertices_.size() - 1;
    }

    void MeshShape::addTriangle(size_t v0, size_t v1, size_t v2) {
        if (v0 >= vertices_.size() || v1 >= vertices_.size() || v2 >= vertices_.size()) {
            throw std::out_of_range("MeshShape: 三角形顶点索引超出范围");
        }
        triangles_.emplace_back(v0, v1, v2);
        markGeometryDirty();
    }

    void MeshShape::addTriangle(const fcl::Triangle& triangle) {
        addTriangle(triangle[0], triangle[1], triangle[2]);
    }

    void MeshShape::setMesh(const std::vector<fcl::Vector3d>& vertices, const std::vector<fcl::Triangle>& triangles) {
        vertices_ = vertices;
        triangles_ = triangles;
        validateMesh();
        markGeometryDirty();
    }

    void MeshShape::clear() {
        vertices_.clear();
        triangles_.clear();
        markGeometryDirty();
    }

    std::vector<fcl::Vector3d> MeshShape::computeNormals() const {
        std::vector<fcl::Vector3d> normals;
        normals.reserve(triangles_.size());
        
        for (const auto& triangle : triangles_) {
            normals.push_back(computeTriangleNormal(triangle));
        }
        
        return normals;
    }

    void MeshShape::ensureFCLObject() const {
        if (!fcl_mesh_ || geometry_dirty_) {
            if (!vertices_.empty() && !triangles_.empty()) {
                fcl_mesh_ = std::make_shared<fcl::BVHModel<fcl::AABBd>>();
                fcl_mesh_->beginModel();
                
                for (const auto& triangle : triangles_) {
                    fcl_mesh_->addTriangle(
                        vertices_[triangle[0]],
                        vertices_[triangle[1]],
                        vertices_[triangle[2]]
                    );
                }
                
                fcl_mesh_->endModel();
                geometry_dirty_ = false;
            }
        }
    }

    void MeshShape::validateMesh() const {
        for (const auto& triangle : triangles_) {
            if (triangle[0] >= vertices_.size() || 
                triangle[1] >= vertices_.size() || 
                triangle[2] >= vertices_.size()) {
                throw std::invalid_argument("MeshShape: 三角形索引超出顶点范围");
            }
        }
    }

    void MeshShape::validateIndex(size_t index, size_t max_size, const std::string& type) const {
        if (index >= max_size) {
            throw std::out_of_range("MeshShape: " + type + "索引超出范围");
        }
    }

    double MeshShape::computeTriangleArea(const fcl::Triangle& triangle) const {
        const fcl::Vector3d& v0 = vertices_[triangle[0]];
        const fcl::Vector3d& v1 = vertices_[triangle[1]];
        const fcl::Vector3d& v2 = vertices_[triangle[2]];

        fcl::Vector3d edge1 = v1 - v0;
        fcl::Vector3d edge2 = v2 - v0;
        return 0.5 * edge1.cross(edge2).norm();
    }

    fcl::Vector3d MeshShape::computeTriangleNormal(const fcl::Triangle& triangle) const {
        const fcl::Vector3d& v0 = vertices_[triangle[0]];
        const fcl::Vector3d& v1 = vertices_[triangle[1]];
        const fcl::Vector3d& v2 = vertices_[triangle[2]];

        fcl::Vector3d edge1 = v1 - v0;
        fcl::Vector3d edge2 = v2 - v0;
        return edge1.cross(edge2).normalized();
    }

} // namespace NSCore
} // namespace NSDrones
