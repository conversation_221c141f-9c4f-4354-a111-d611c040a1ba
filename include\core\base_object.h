// include/core/base_object.h
#pragma once

#include "core/types.h"       
#include <string>
#include <vector>
#include <memory>             
#include <optional>           
#include <stdexcept>          
#include <mutex>              
#include <shared_mutex>  
#include "nlohmann/json.hpp"

// --- 前向声明 ---
namespace NSDrones {
	namespace NSEnvironment { class Environment; }
	namespace NSParams { class ParamValues; class ParamRegistry; }  
}

namespace NSDrones {
	namespace NSCore {
		using namespace ::NSDrones::NSUtils;

		/**
		 * @class BaseObject
		 * @brief 所有模拟实体的基类。
		 *
		 * 管理对象的基本属性 (ID, 类型, 名称)、动态状态（位置、姿态、速度等）、
		 * 几何形状、参数集以及与环境的交互。
		 * 提供参数的层级查找功能。
		 * 派生类需要实现 registerParams 方法来注册自己的参数。
		 * 维护对象在环境索引中的状态，通过 notifyUpdate 通知环境。
		 */
		class BaseObject : public std::enable_shared_from_this<BaseObject> {
			// 授予 Environment 访问权限以调用内部方法（例如设置父子关系、通知）
			friend class NSEnvironment::Environment;

		protected:
			const ObjectID id_;						// 唯一且不可变的对象 ID
			std::string type_tag_;					// 对象细分类型标签
			std::string name_;						// 对象的可读名称 (可变)
			std::shared_ptr<NSParams::ParamValues> params_; // 对象的本地参数集 (共享指针)
			ObjectID parent_id_ = INVALID_OBJECT_ID; // 父对象的 ID
			std::vector<ObjectID> children_ids_;   // 子对象的 ID 列表

			// --- 内部方法 (由 Environment 调用) ---
			/** @brief (内部) 设置父对象 ID，不通知环境，不修改对方。*/
			void internal_setParent(const ObjectID& p_id);
			/** @brief (内部) 添加子对象 ID，不通知环境，不修改对方。*/
			void internal_addChild(const ObjectID& c_id);
			/** @brief (内部) 移除子对象 ID，不通知环境，不修改对方。*/
			void internal_removeChild(const ObjectID& c_id);


		public:
			/**
			 * @brief 构造函数。
			 * @param id 对象的唯一 ID。
			 * @param object_type_key 对象的类型字符串，用于参数系统和工厂。
			 * @param name 对象的可读名称。
			 * @throws DroneException 如果 id 无效。
			 */
			explicit BaseObject(ObjectID id, const std::string& object_type_key, const std::string& name);

			/** @brief 虚析构函数，允许派生类正确清理。*/
			virtual ~BaseObject() = default;

			// --- 环境访问 ---
			/**
			 * @brief 获取环境实例
			 * @return 环境实例的共享指针
			 */
			std::shared_ptr<NSEnvironment::Environment> getEnvironment() const;

			// --- 禁止拷贝和移动 ---
			BaseObject(const BaseObject&) = delete;
			BaseObject& operator=(const BaseObject&) = delete;
			BaseObject(BaseObject&&) = delete;
			BaseObject& operator=(BaseObject&&) = delete;

			// --- 基本信息访问 ---
			/** @brief 获取对象 ID。*/
			const ObjectID& getId() const { return id_; }
			/** @brief 获取对象从配置文件加载的类型键 (例如 "UAV_Multirotor")。用于参数查找和特定配置区分。*/
			const std::string& getTypeTag() const { return type_tag_; }
			/** @brief 获取对象的实际C++类别的通用标签 (例如 "UAV", "Obstacle", "Zone")。由派生类实现。*/
			virtual std::string getClassName() const = 0;
			/** @brief 获取对象名称。*/
			const std::string& getName() const { return name_; }
			/** @brief 设置对象名称。*/
			void setName(std::string name) { name_ = std::move(name); }

			// --- 关系管理 ---
			/**
			 * @brief 请求环境设置此对象的父对象。
			 *        会更新对象内部的 parent_id_ 并通知环境更新关系索引。
			 * @param p_id 新的父对象 ID，或 INVALID_OBJECT_ID 来解除父关系。
			 */
			virtual void setParent(const ObjectID& p_id);
			/** @brief 获取父对象 ID。*/
			const ObjectID& getParentId() const { return parent_id_; }
			/** @brief 获取子对象 ID 列表 (const 引用)。*/
			const std::vector<ObjectID>& getChildrenIds() const { return children_ids_; }

			/**
			 * @brief 对象初始化入口点。
			 *        此方法应由对象的工厂函数或创建逻辑在对象创建后调用。
			 *        它负责使用 Config 系统准备好的、包含默认值和实例特定值的 ParamValues 对象
			 *        来完成对象的最终设置，包括参数赋值、形状创建等。
			 * @param params 由 Config 系统准备好的、包含最终参数值的 ParamValues 对象的共享指针。
			 * @return 如果初始化成功，返回 true。
			 */
			virtual bool initialize(std::shared_ptr<NSParams::ParamValues> final_params, const nlohmann::json& raw_instance_json_config);

			// --- 参数访问 (层级查找) ---
			/** @brief 获取有效参数值 (optional)。*/
			template <typename T>
			std::optional<T> getEffectiveParam(const std::string& key) const;

			/** @brief 获取有效参数值 (找不到或类型不匹配则抛出异常)。*/
			template <typename T>
			T getParamOrThrow(const std::string& key) const;

			/** @brief 获取有效参数值 (找不到则返回默认值)。*/
			template <typename T>
			T getParamOrDefault(const std::string& key, T default_value) const;

			// --- 本地参数修改 ---
			/** @brief 设置本地参数覆盖值。*/
			template <typename T>
			bool setLocalParamOverride(const std::string& key, T value);
			/** @brief 获取本地参数集的 const 引用。*/
			std::shared_ptr<const NSParams::ParamValues> getLocalParams() const { return params_; }

			// --- 方法：从 JSON 应用参数 ---
			/**
			 * @brief 从 JSON 对象加载并应用参数到此对象的本地 ParamValues。
			 *        会根据 ParamRegistry 中的定义进行校验，并覆盖/添加参数。
			 * @param jsonData 包含参数的 JSON 对象 (通常是实例配置文件中的 "parameters" 部分)。
			 * @param registry 用于校验参数的参数注册表。
			 * @return 如果加载和应用成功，则为 true。
			 */
			bool paramsFromJson(const nlohmann::json& jsonData, const NSParams::ParamRegistry& registry);

			// --- 动态数据与关系访问/修改 ---
			// --- 类型转换---
			/** @brief 类型安全的向下转换 (shared_ptr 版本)。*/
			template <typename T>
			std::shared_ptr<T> as() {
				return std::dynamic_pointer_cast<T>(shared_from_this());
			}
			/** @brief 类型安全的向下转换 (const shared_ptr 版本)。*/
			template <typename T>
			std::shared_ptr<const T> as() const {
				return std::dynamic_pointer_cast<const T>(shared_from_this());
			}
		};

		std::vector<WGS84Point> paramToListWGS84Point(const BaseObject* obj, const std::string& key);
		using BaseObjectPtr = std::shared_ptr<BaseObject>;
		using ConstBaseObjectPtr = std::shared_ptr<const BaseObject>;

	} // namespace NSCore
} // namespace NSDrones

// 在文件末尾包含模板实现文件
#include "core/base_object.tpp"

using namespace NSDrones::NSCore;