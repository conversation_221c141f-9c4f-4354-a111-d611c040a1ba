// src/algorithm/path_planner/ipath_planner.cpp
#include "algorithm/path_planner/ipath_planner.h"
#include "environment/environment.h"
#include "uav/uav.h"
#include "utils/logging.h"

namespace NSDrones {
    namespace NSAlgorithm {
        IPathPlanner::IPathPlanner() {
            LOG_DEBUG("IPathPlanner 已构造。");
        }

        bool IPathPlanner::isPathFeasible(const std::vector<EcefPoint>& path) const {
				LOG_TRACE("IPathPlanner 开始默认路径可行性检查 ({} 个点)...", path.size());
				if (path.size() < 2) { LOG_TRACE("路径点数 < 2，视为可行。"); return true; }

				// 检查每个线段是否有效
				for (size_t i = 0; i < path.size() - 1; ++i) {
					const EcefPoint& p1 = path[i];
					const EcefPoint& p2 = path[i + 1];
					// **注意:** 规划阶段通常只检查静态环境，传入 t=0
					auto env = Environment::getInstance();
					if (!env || !env->isSegmentValid(p1, p2, 0.0, 0.0, 0.0, false, true)) { // Use Environment singleton
						LOG_WARN("路径段 {} -> {} 在环境中无效 (碰撞或进入禁飞区)。", i, i + 1);
						return false;
					}
				}
				LOG_TRACE("IPathPlanner 默认路径可行性检查通过。");
				return true;
			}
    } // namespace NSAlgorithm
} // namespace NSDrones
