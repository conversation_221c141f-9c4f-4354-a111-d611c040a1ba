{"global_parameter_definitions": {"type_tag": "global_environment_params", "parameters": [{"key": "default_mission_alt_msl", "name": "默认任务高度", "description": "默认任务飞行高度 (海拔高度，米)。", "type": "double", "default": 50.0}, {"key": "default_mission_speed_mps", "name": "默认任务速度", "description": "默认任务飞行速度 (米/秒)。", "type": "double", "default": 7.0}, {"key": "default_mission_loiter_radius_m", "name": "默认盘旋半径", "description": "默认任务盘旋半径 (米)。", "type": "double", "default": 25.0}, {"key": "default_mission_loiter_time_s", "name": "默认盘旋时间", "description": "默认任务盘旋时间 (秒)。", "type": "double", "default": 30.0}, {"key": "landing_approach_alt_msl", "name": "降落接近高度", "description": "降落任务的接近高度 (海拔高度，米)。", "type": "double", "default": 5.0}, {"key": "wgs84_origin", "name": "WGS84参考原点", "description": "地图服务的WGS84参考原点 [经度, 纬度, 高度]。", "type": "wgs84point", "default": [118.0, 32.0, 0.0]}, {"key": "spatial_index.default_max_objects_per_node", "name": "BVH每个节点最大对象数", "description": "BVH空间索引中每个节点允许的最大对象数量。", "type": "int", "default": 16, "constraints": {"type": "numeric", "min": 1, "max": 100}}, {"key": "spatial_index.default_max_depth", "name": "BVH最大深度", "description": "BVH空间索引的最大树深度。", "type": "int", "default": 32, "constraints": {"type": "numeric", "min": 1, "max": 64}}, {"key": "spatial_index.tile_size", "name": "BVH瓦片大小", "description": "BVH空间索引的瓦片大小 (米)。", "type": "double", "default": 1000.0, "constraints": {"type": "numeric", "min": 100.0, "max": 10000.0}}, {"key": "logging_level", "name": "日志级别", "description": "应用程序的日志记录级别。", "type": "string", "default": "INFO", "enum_type": "NSDrones::NSCore::LogLevel"}, {"key": "logging.console_level", "name": "控制台日志级别", "description": "控制台输出的日志级别。", "type": "string", "default": "trace", "enum_type": "NSDrones::NSCore::LogLevel"}, {"key": "logging.file_level", "name": "文件日志级别", "description": "文件输出的日志级别。", "type": "string", "default": "trace", "enum_type": "NSDrones::NSCore::LogLevel"}, {"key": "environment_cache_max_age_minutes", "name": "Environment缓存最大存活时间", "description": "Environment缓存最大存活时间（分钟）", "type": "int", "default": 5, "constraints": {"type": "numeric", "min": 1, "max": 60}}, {"key": "environment_cache_max_size", "name": "Environment缓存最大条目数", "description": "Environment缓存最大条目数", "type": "int", "default": 1000, "constraints": {"type": "numeric", "min": 100, "max": 10000}}, {"key": "param_registry_cache_max_age_minutes", "name": "ParamRegistry缓存最大存活时间", "description": "ParamRegistry缓存最大存活时间（分钟）", "type": "int", "default": 10, "constraints": {"type": "numeric", "min": 1, "max": 120}}, {"key": "param_registry_cache_max_size", "name": "ParamRegistry缓存最大条目数", "description": "ParamRegistry缓存最大条目数", "type": "int", "default": 500, "constraints": {"type": "numeric", "min": 50, "max": 5000}}, {"key": "map.enabled", "name": "地图功能启用", "description": "是否启用地图数据加载功能。", "type": "bool", "default": true}, {"key": "map.data_directory", "name": "地图数据目录", "description": "地图数据文件所在的目录路径。", "type": "string", "default": "data/terrain"}, {"key": "map.file_path", "name": "地图文件路径", "description": "指定的地图文件路径，为空则在数据目录中自动查找第一个可用文件。", "type": "string", "default": ""}, {"key": "spatial_index.map_bounds_min_x", "name": "地图边界最小X坐标", "description": "地图在局部坐标系统中的最小X坐标值（米）。", "type": "double", "default": -10000.0}, {"key": "spatial_index.map_bounds_min_y", "name": "地图边界最小Y坐标", "description": "地图在局部坐标系统中的最小Y坐标值（米）。", "type": "double", "default": -10000.0}, {"key": "spatial_index.map_bounds_min_z", "name": "地图边界最小Z坐标", "description": "地图在局部坐标系统中的最小Z坐标值（米）。", "type": "double", "default": -1000.0}, {"key": "spatial_index.map_bounds_max_x", "name": "地图边界最大X坐标", "description": "地图在局部坐标系统中的最大X坐标值（米）。", "type": "double", "default": 10000.0}, {"key": "spatial_index.map_bounds_max_y", "name": "地图边界最大Y坐标", "description": "地图在局部坐标系统中的最大Y坐标值（米）。", "type": "double", "default": 10000.0}, {"key": "spatial_index.map_bounds_max_z", "name": "地图边界最大Z坐标", "description": "地图在局部坐标系统中的最大Z坐标值（米）。", "type": "double", "default": 1000.0}, {"key": "map.load_mode", "name": "地图加载模式", "description": "地图数据加载模式：geotiff（直接加载GeoTIFF文件）或 tiff（加载预处理的TIFF文件）。", "type": "string", "default": "geotiff"}, {"key": "map.auto_convert", "name": "自动转换GeoTIFF", "description": "当加载模式为tiff时，是否自动将GeoTIFF文件转换为TIFF文件。", "type": "bool", "default": true}, {"key": "map.layer_config.elevation_layer", "name": "指定高程图层", "description": "指定的高程图层名称，为空则自动检测。", "type": "string", "default": ""}, {"key": "map.layer_config.feature_layer", "name": "指定地物图层", "description": "指定的地物类型图层名称，为空则自动检测。", "type": "string", "default": ""}, {"key": "map.layer_config.feature_height_layer", "name": "指定地物高度图层", "description": "指定的地物高度图层名称，为空则自动检测。", "type": "string", "default": ""}, {"key": "map.layer_mapping", "name": "图层映射配置", "description": "图层名称映射配置，用于自动检测图层。第一行为高程图层候选名称，第二行为地物图层候选名称，第三行为地物高度图层候选名称。", "type": "string[][]", "default": [["elevation", "height", "dem", "dtm", "altitude", "z"], ["feature_type", "landuse", "classification", "class", "type"], ["feature_height", "building_height", "height_above_ground", "structure_height", "object_height"]]}, {"key": "task_spaces.takeoff_area.origin_latitude", "name": "起飞区域基准点纬度", "description": "无人机起飞点附近静态TaskSpace的基准点纬度（度）。", "type": "double", "default": 32.1}, {"key": "task_spaces.takeoff_area.origin_longitude", "name": "起飞区域基准点经度", "description": "无人机起飞点附近静态TaskSpace的基准点经度（度）。", "type": "double", "default": 118.8}, {"key": "task_spaces.takeoff_area.origin_altitude", "name": "起飞区域基准点高度", "description": "无人机起飞点附近静态TaskSpace的基准点高度（米）。", "type": "double", "default": 50.0}, {"key": "task_spaces.takeoff_area.coverage_min_bound", "name": "起飞区域覆盖空间最小边界", "description": "起飞区域TaskSpace的三维覆盖空间最小边界点，格式为[经度,纬度,高度]，定义长方体空间的西南下角。", "type": "double[]", "default": [118.77, 32.09, 0.0]}, {"key": "task_spaces.takeoff_area.coverage_max_bound", "name": "起飞区域覆盖空间最大边界", "description": "起飞区域TaskSpace的三维覆盖空间最大边界点，格式为[经度,纬度,高度]，定义长方体空间的东北上角。", "type": "double[]", "default": [118.83, 32.11, 500.0]}, {"key": "task_spaces.takeoff_area.bvh_max_objects_per_node", "name": "起飞区域BVH每节点最大对象数", "description": "起飞区域TaskSpace的BVH索引每个节点最大对象数。", "type": "int", "default": 16}, {"key": "task_spaces.takeoff_area.bvh_max_depth", "name": "起飞区域BVH最大深度", "description": "起飞区域TaskSpace的BVH索引最大深度。", "type": "int", "default": 32}, {"key": "task_spaces.formation_follow.leader_entity_id", "name": "编队跟随长机实体ID", "description": "伴随长机的动态TaskSpace所跟随的长机实体ID（字符串）。", "type": "string", "default": "leader_1"}, {"key": "task_spaces.formation_follow.coverage_min_bound", "name": "编队跟随覆盖空间最小边界", "description": "编队跟随TaskSpace的三维覆盖空间最小边界点，格式为[经度偏移,纬度偏移,高度偏移]，相对于长机位置的偏移量。", "type": "double[]", "default": [-0.05, -0.05, -200.0]}, {"key": "task_spaces.formation_follow.coverage_max_bound", "name": "编队跟随覆盖空间最大边界", "description": "编队跟随TaskSpace的三维覆盖空间最大边界点，格式为[经度偏移,纬度偏移,高度偏移]，相对于长机位置的偏移量。", "type": "double[]", "default": [0.05, 0.05, 200.0]}, {"key": "task_spaces.formation_follow.bvh_max_objects_per_node", "name": "编队跟随BVH每节点最大对象数", "description": "编队跟随TaskSpace的BVH索引每个节点最大对象数。", "type": "int", "default": 8}, {"key": "task_spaces.formation_follow.bvh_max_depth", "name": "编队跟随BVH最大深度", "description": "编队跟随TaskSpace的BVH索引最大深度。", "type": "int", "default": 24}, {"key": "task_spaces.target_area.origin_latitude", "name": "目标区域基准点纬度", "description": "攻击目标附近任务执行静态TaskSpace的基准点纬度（度）。", "type": "double", "default": 32.0}, {"key": "task_spaces.target_area.origin_longitude", "name": "目标区域基准点经度", "description": "攻击目标附近任务执行静态TaskSpace的基准点经度（度）。", "type": "double", "default": 118.9}, {"key": "task_spaces.target_area.origin_altitude", "name": "目标区域基准点高度", "description": "攻击目标附近任务执行静态TaskSpace的基准点高度（米）。", "type": "double", "default": 100.0}, {"key": "task_spaces.target_area.coverage_min_bound", "name": "目标区域覆盖空间最小边界", "description": "目标区域TaskSpace的三维覆盖空间最小边界点，格式为[经度,纬度,高度]，定义长方体空间的西南下角。", "type": "double[]", "default": [118.88, 31.99, 50.0]}, {"key": "task_spaces.target_area.coverage_max_bound", "name": "目标区域覆盖空间最大边界", "description": "目标区域TaskSpace的三维覆盖空间最大边界点，格式为[经度,纬度,高度]，定义长方体空间的东北上角。", "type": "double[]", "default": [118.92, 32.01, 800.0]}, {"key": "task_spaces.target_area.bvh_max_objects_per_node", "name": "目标区域BVH每节点最大对象数", "description": "目标区域TaskSpace的BVH索引每个节点最大对象数。", "type": "int", "default": 12}, {"key": "task_spaces.target_area.bvh_max_depth", "name": "目标区域BVH最大深度", "description": "目标区域TaskSpace的BVH索引最大深度。", "type": "int", "default": 28}]}, "global_parameter_values": {"default_mission_alt_msl": 75.0, "default_mission_speed_mps": 8.0, "default_mission_loiter_radius_m": 25.0, "default_mission_loiter_time_s": 30.0, "landing_approach_alt_msl": 5.0, "wgs84_origin": [118.795139, 31.921389, 10.0], "spatial_index.default_max_objects_per_node": 16, "spatial_index.default_max_depth": 32, "spatial_index.tile_size": 1000.0, "logging.console_level": "trace", "logging.file_level": "trace", "environment_cache_max_age_minutes": 8, "environment_cache_max_size": 1500, "param_registry_cache_max_age_minutes": 15, "param_registry_cache_max_size": 800, "map.enabled": true, "map.data_directory": "data/terrain", "map.load_mode": "geotiff", "map.auto_convert": true, "map.layer_config.elevation_layer": "", "map.layer_config.feature_layer": "", "map.layer_config.feature_height_layer": "", "map.layer_mapping": [["height", "dem", "dtm", "altitude", "z"], ["landuse", "classification", "class", "type"], ["building_height", "height_above_ground", "structure_height", "object_height"]], "task_spaces.takeoff_area.origin_latitude": 32.1, "task_spaces.takeoff_area.origin_longitude": 118.8, "task_spaces.takeoff_area.origin_altitude": 50.0, "task_spaces.takeoff_area.coverage_min_bound": [118.77, 32.09, 0.0], "task_spaces.takeoff_area.coverage_max_bound": [118.83, 32.11, 500.0], "task_spaces.takeoff_area.bvh_max_objects_per_node": 16, "task_spaces.takeoff_area.bvh_max_depth": 32, "task_spaces.formation_follow.leader_entity_id": "leader_1", "task_spaces.formation_follow.coverage_min_bound": [-0.05, -0.05, -200.0], "task_spaces.formation_follow.coverage_max_bound": [0.05, 0.05, 200.0], "task_spaces.formation_follow.bvh_max_objects_per_node": 8, "task_spaces.formation_follow.bvh_max_depth": 24, "task_spaces.target_area.origin_latitude": 32.0, "task_spaces.target_area.origin_longitude": 118.9, "task_spaces.target_area.origin_altitude": 100.0, "task_spaces.target_area.coverage_min_bound": [118.88, 31.99, 50.0], "task_spaces.target_area.coverage_max_bound": [118.92, 32.01, 800.0], "task_spaces.target_area.bvh_max_objects_per_node": 12, "task_spaces.target_area.bvh_max_depth": 28}}