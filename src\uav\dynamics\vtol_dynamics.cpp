// src/uav/dynamics/vtol_dynamics.cpp
#include "uav/dynamics/vtol_dynamics.h"
#include "uav/dynamics/multirotor_dynamics.h" 
#include "uav/dynamics/fixedwing_dynamics.h"
#include "uav/uav.h"        
#include "utils/logging.h"           
#include <cmath>                    
#include <algorithm>                
#include <memory>                    
#include <stdexcept>                 

namespace NSDrones {
	namespace NSUav {

		/**
		 * @brief VtolDynamics 构造函数。
		 *        内部会创建悬停和固定翼子模型。
		 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
		 * @throws DroneException 如果内部模型创建失败。
		 */
		VtolDynamics::VtolDynamics(const Uav& owner)
			: IDynamicModel(owner) // 调用基类构造函数，传递 owner
		{
			LOG_DEBUG("开始创建 VTOL 内部动力学模型，所有者: {}", owner_.getId());
			// 创建内部模型实例，将 owner 传递给它们的构造函数
			try {
				hover_model_ = std::make_shared<MultirotorDynamics>(owner_); // 传递 owner
				LOG_DEBUG("  悬停动力学模型创建成功。");
			}
			catch (const std::exception& e) {
				throw DroneException("创建 VTOL 内部悬停动力学模型失败: " + std::string(e.what()), ErrorCode::DependencyError);
			}
			try {
				fw_model_ = std::make_shared<FixedWingDynamics>(owner_); // 传递 owner
				LOG_DEBUG("  固定翼动力学模型创建成功。");
			}
			catch (const std::exception& e) {
				throw DroneException("创建 VTOL 内部固定翼动力学模型失败: " + std::string(e.what()), ErrorCode::DependencyError);
			}
			LOG_INFO("VTOL 动力学模型已创建 (所有者 ID: {})。", owner_.getId());
		}

		// --- 内部模型选择辅助函数 ---
		/**
		 * @brief 根据当前状态获取合适的内部模型指针。
		 */
		const IDynamicModel* VtolDynamics::getCurrentModel(const UavState& state) const {
			LOG_TRACE("VTOL 获取当前模型 (所有者 ID: {})：模式={}", owner_.getId(), static_cast<int>(state.mode));
			// 检查内部模型指针是否有效
			if (!hover_model_ || !fw_model_) {
				LOG_ERROR("VTOL 获取当前模型失败：内部模型指针无效 (所有者 ID: {})。", owner_.getId());
				return nullptr; // 返回空指针表示无效
			}

			switch (state.mode) {
			case FlightMode::HOVER:
				LOG_TRACE("  模式为 HOVER，返回悬停模型。");
				return hover_model_.get(); // 返回悬停模型指针
			case FlightMode::FIXED_WING:
				LOG_TRACE("  模式为 FIXED_WING，返回固定翼模型。");
				return fw_model_.get(); // 返回固定翼模型指针
			case FlightMode::TRANSITION:
				// 过渡模式下的行为比较复杂，返回哪个模型的约束取决于具体策略。
				// 策略 1: 基于速度选择 (可能导致约束在转换点跳变)
				// double transSpeed = owner_.getParamOrDefault<double>("dynamics.vtol.transitionSpeed", 15.0);
				// if (state.velocity.norm() < transSpeed * 0.5) return hover_model_.get();
				// else return fw_model_.get();
				// 策略 2: 总是返回固定翼模型（通常约束更严格，更保守）
				LOG_TRACE("  模式为 TRANSITION，返回固定翼模型作为主导约束。");
				return fw_model_.get();
			case FlightMode::UNKNOWN:
			default: // 未知模式
				LOG_WARN("VTOL 模型遇到未知飞行模式 ({})，返回悬停模型作为默认 (所有者 ID: {})。", static_cast<int>(state.mode), owner_.getId());
				return hover_model_.get(); // 默认返回悬停模型
			}
		}

		// --- 覆盖依赖模式的方法 ---
		// 这些方法根据当前状态的飞行模式，从 owner_ 获取相应的参数值返回，
		// 或者委托给对应的内部模型获取。
		// 使用 owner_.getParamOrDefault/Throw 获取参数。
		// 参数键名结构: dynamics.vtol.* (组合/顶层), dynamics.vtol.hover.*, dynamics.vtol.fw.*

		double VtolDynamics::getMaxHorizontalSpeed(const UavState& state) const {
			// 优先根据模式获取特定参数，如果未定义，则使用顶层组合参数
			std::string mode_key_prefix = "dynamics.vtol.";
			if (state.mode == FlightMode::HOVER || state.mode == FlightMode::TRANSITION) {
				mode_key_prefix += "hover.";
			}
			else { // FIXED_WING
				mode_key_prefix += "fw.";
			}
			// 尝试获取特定模式的速度，如果找不到 (返回默认0)，则获取顶层组合速度
			double speed = owner_.getParamOrDefault<double>(mode_key_prefix + "maxHVel", 0.0);
			if (speed <= Constants::EPSILON) {
				speed = owner_.getParamOrDefault<double>("dynamics.vtol.maxHVel", 0.0); // 获取顶层组合参数
			}
			LOG_TRACE("VTOL 最大水平速度 (模式 {}, 所有者 ID: {}): {:.2f} m/s", static_cast<int>(state.mode), owner_.getId(), speed);
			return std::max(0.0, speed); // 确保非负
		}

		double VtolDynamics::getMaxClimbSpeed(const UavState& state) const {
			// 悬停和过渡通常使用悬停模式参数
			std::string mode_key_prefix = "dynamics.vtol.";
			if (state.mode == FlightMode::HOVER || state.mode == FlightMode::TRANSITION) {
				mode_key_prefix += "hover.";
			}
			else { // FIXED_WING
				mode_key_prefix += "fw.";
			}
			// 固定翼使用 maxClimbRate，悬停使用 maxVVelUp
			std::string speed_key = (state.mode == FlightMode::FIXED_WING) ? "maxClimbRate" : "maxVVelUp";
			double speed = owner_.getParamOrDefault<double>(mode_key_prefix + speed_key, 0.0);
			if (speed <= Constants::EPSILON) {
				speed = owner_.getParamOrDefault<double>("dynamics.vtol.maxVVelUp", 0.0); // 顶层组合参数
			}
			LOG_TRACE("VTOL 最大爬升速度 (模式 {}, 所有者 ID: {}): {:.2f} m/s", static_cast<int>(state.mode), owner_.getId(), speed);
			return std::max(0.0, speed); // 确保非负
		}

		double VtolDynamics::getMaxDescendSpeed(const UavState& state) const {
			std::string mode_key_prefix = "dynamics.vtol.";
			if (state.mode == FlightMode::HOVER || state.mode == FlightMode::TRANSITION) {
				mode_key_prefix += "hover.";
			}
			else { // FIXED_WING
				mode_key_prefix += "fw.";
			}
			// 固定翼使用 maxSinkRate，悬停使用 maxVVelDown
			std::string speed_key = (state.mode == FlightMode::FIXED_WING) ? "maxSinkRate" : "maxVVelDown";
			double speed = owner_.getParamOrDefault<double>(mode_key_prefix + speed_key, 0.0);
			if (speed <= Constants::EPSILON) {
				speed = owner_.getParamOrDefault<double>("dynamics.vtol.maxVVelDown", 0.0); // 顶层组合参数
			}
			LOG_TRACE("VTOL 最大下降速度 (模式 {}, 所有者 ID: {}): {:.2f} m/s", static_cast<int>(state.mode), owner_.getId(), speed);
			return std::max(0.0, speed); // 确保非负 (下降速度也表示为正值)
		}

		double VtolDynamics::getMaxHorizontalAcceleration(const UavState& state) const {
			// 通常使用组合参数，因为过渡状态难以确定特定模式
			double acc = owner_.getParamOrDefault<double>("dynamics.vtol.maxHAcc", 0.0);
			LOG_TRACE("VTOL 最大水平加速度 (模式 {}, 所有者 ID: {}): {:.2f} m/s^2 (使用组合值)", static_cast<int>(state.mode), owner_.getId(), acc);
			return std::max(0.0, acc); // 确保非负
		}

		double VtolDynamics::getMaxVerticalAcceleration(const UavState& state) const {
			// 通常使用组合参数
			double acc = owner_.getParamOrDefault<double>("dynamics.vtol.maxVAcc", 0.0);
			LOG_TRACE("VTOL 最大垂直加速度 (模式 {}, 所有者 ID: {}): {:.2f} m/s^2 (使用组合值)", static_cast<int>(state.mode), owner_.getId(), acc);
			return std::max(0.0, acc); // 确保非负
		}

		double VtolDynamics::getMaxHorizontalDeceleration(const UavState& state) const {
			// 通常使用组合参数
			double hacc = getMaxHorizontalAcceleration(state); // 获取对应的组合 HAcc
			double decel = owner_.getParamOrDefault<double>("dynamics.vtol.maxHDecel", hacc); // 默认等于 HAcc
			LOG_TRACE("VTOL 最大水平减速度 (模式 {}, 所有者 ID: {}): {:.2f} m/s^2 (使用组合值)", static_cast<int>(state.mode), owner_.getId(), decel);
			return std::max(0.0, decel); // 确保非负
		}

		double VtolDynamics::getMaxVerticalDeceleration(const UavState& state) const {
			// 通常使用组合参数
			double vacc = getMaxVerticalAcceleration(state); // 获取对应的组合 VAcc
			double decel = owner_.getParamOrDefault<double>("dynamics.vtol.maxVDecel", vacc); // 默认等于 VAcc
			LOG_TRACE("VTOL 最大垂直减速度 (模式 {}, 所有者 ID: {}): {:.2f} m/s^2 (使用组合值)", static_cast<int>(state.mode), owner_.getId(), decel);
			return std::max(0.0, decel); // 确保非负
		}

		double VtolDynamics::getMaxAltitude(const UavState& state) const {
			// 通常使用顶层组合参数
			return owner_.getParamOrDefault<double>("dynamics.vtol.maxAlt", Constants::INF);
		}

		double VtolDynamics::getMinOperationalSpeed(const UavState& state) const {
			// VTOL 可以悬停，最小运行速度为 0
			// 可以选择性地让固定翼模式有最小速度，但 getCurrentModel 会处理
			return 0.0;
		}

		// --- 以下方法委托给当前模式的内部模型 ---
		double VtolDynamics::getMaxTurnRate(const UavState& state) const {
			const IDynamicModel* model = getCurrentModel(state); // 获取当前模型
			double rate = model ? model->getMaxTurnRate(state) : 0.0; // 委托计算
			LOG_TRACE("VTOL 最大转弯率 (模式 {}, 所有者 ID: {}): {:.3f} rad/s", static_cast<int>(state.mode), owner_.getId(), rate);
			return rate;
		}

		double VtolDynamics::getMinTurnRadius(const UavState& state) const {
			const IDynamicModel* model = getCurrentModel(state);
			// 如果模型无效，返回无穷大表示无法转弯
			double radius = model ? model->getMinTurnRadius(state) : Constants::INF;
			LOG_TRACE("VTOL 最小转弯半径 (模式 {}, 所有者 ID: {}): {:.2f} m", static_cast<int>(state.mode), owner_.getId(), radius);
			return radius;
		}

		double VtolDynamics::getMaxBankAngle(const UavState& state) const {
			const IDynamicModel* model = getCurrentModel(state);
			double angle = model ? model->getMaxBankAngle(state) : 0.0; // 获取倾斜角或姿态角限制
			LOG_TRACE("VTOL 最大倾斜/姿态角 (模式 {}, 所有者 ID: {}): {:.2f} 度", static_cast<int>(state.mode), owner_.getId(), angle * Constants::RAD_TO_DEG);
			return angle;
		}

		// --- isStateTransitionFeasible 实现 ---
		/**
		 * @brief 检查 VTOL 状态转换是否可行，考虑模式转换约束。
		 */
		bool VtolDynamics::isStateTransitionFeasible(const UavState& current, const UavState& next, Time dt) const {
			LOG_TRACE("检查 VTOL 状态转换可行性 (所有者 ID: {})：模式 {} -> {}, dt={:.4f}s",
				owner_.getId(), static_cast<int>(current.mode), static_cast<int>(next.mode), dt);

			// 检查时间步长
			if (dt <= Constants::TIME_EPSILON) {
				// 如果时间步长无效，只有当位置没有改变时转换才可行
				bool position_changed = (current.position - next.position).norm() > Constants::GEOMETRY_EPSILON;
				if (position_changed) {
					LOG_TRACE("  失败：时间步长 <= 0 ({:.4f}) 但位置改变。", dt);
					return false;
				}
				else {
					LOG_TRACE("  通过：时间步长 <= 0 且位置未变。");
					return true;
				}
			}

			// --- 1. 基本限制检查 (高度, 速度) ---
			// 使用下一状态对应的模式限制
			if (!checkAltitudeLimit(next.position, getMaxAltitude(next))) {
				LOG_TRACE("  失败：下一状态高度 ({:.1f}) 超出最大限制 ({:.1f})。", next.position.altitude, getMaxAltitude(next));
				return false;
			}
			if (!checkSpeedLimit(next.velocity,
				getMaxHorizontalSpeed(next),
				getMaxClimbSpeed(next),
				getMaxDescendSpeed(next),
				getMinOperationalSpeed(next))) { // VTOL 最小速度为 0
				LOG_TRACE("  失败：下一状态速度限制检查失败。");
				return false;
			}
			// 检查当前速度是否满足当前模式的最低速度要求
			// 对于 VTOL，hover/transition 模式下最小速度为 0，fw 模式下有失速速度
			double current_min_op_speed = 0.0;
			if (current.mode == FlightMode::FIXED_WING && fw_model_) { // 仅当是固定翼模式且模型有效时检查
				current_min_op_speed = fw_model_->getMinOperationalSpeed(current);
			}
			if (current.velocity.norm() > Constants::VELOCITY_EPSILON &&
				current.velocity.norm() < current_min_op_speed - Constants::VELOCITY_EPSILON) {
				LOG_TRACE("  失败：当前速度 ({:.2f}) 低于当前模式 ({}) 最小运行速度 ({:.2f})。",
					current.velocity.norm(), static_cast<int>(current.mode), current_min_op_speed);
				return false;
			}
			LOG_TRACE("  基本限制检查（高度、速度）通过。");


			// --- 2. 加速度/减速度限制 ---
			// 使用顶层的组合限制（通常更严格或代表平均能力）
			Vector3D requiredAcc = (next.velocity - current.velocity) / dt; // 计算所需平均加速度
			double hAccMag = requiredAcc.head<2>().norm(); // 水平加速度大小
			double vAcc = requiredAcc.z();                 // 垂直加速度
			double hSpeedChange = next.velocity.head<2>().norm() - current.velocity.head<2>().norm(); // 水平速度变化量

			double maxHAcc = getMaxHorizontalAcceleration(current); // 获取组合限制
			double maxVAcc = getMaxVerticalAcceleration(current);
			double maxHDecel = getMaxHorizontalDeceleration(current);
			double maxVDecel = getMaxVerticalDeceleration(current);

			if (hSpeedChange > Constants::VELOCITY_EPSILON) { // 水平加速
				if (hAccMag > maxHAcc + Constants::VELOCITY_EPSILON) {
					LOG_TRACE("  失败：水平加速度检查失败 (需要 {:.2f} > 限制 {:.2f})。", hAccMag, maxHAcc);
					return false;
				}
			}
			else if (hSpeedChange < -Constants::VELOCITY_EPSILON) { // 水平减速
				if (hAccMag > maxHDecel + Constants::VELOCITY_EPSILON) { // 检查加速度模长是否超过减速限制
					LOG_TRACE("  失败：水平减速度检查失败 (加速度模 {:.2f} > 限制 {:.2f})。", hAccMag, maxHDecel);
					return false;
				}
			} // 水平速度不变则跳过

			if (vAcc > Constants::EPSILON) { // 垂直向上加速或向下减速
				if (vAcc > maxVAcc + Constants::VELOCITY_EPSILON) {
					LOG_TRACE("  失败：垂直向上加速度/向下减速检查失败 (需要 {:.2f} > 限制 {:.2f})。", vAcc, maxVAcc);
					return false;
				}
			}
			else if (vAcc < -Constants::EPSILON) { // 垂直向下加速或向上减速
				if (std::abs(vAcc) > maxVDecel + Constants::VELOCITY_EPSILON) { // 检查绝对值是否超过减速度限制
					LOG_TRACE("  失败：垂直向下加速度/向上减速检查失败 (需要 {:.2f} > 限制 {:.2f})。", std::abs(vAcc), maxVDecel);
					return false;
				}
			} // 垂直加速度接近零则跳过
			LOG_TRACE("  加速度/减速度限制检查通过。");

			// --- 3. 转弯约束 ---
			// 使用当前状态对应模式的限制进行检查
			double current_max_turn_rate = getMaxTurnRate(current);
			double current_min_turn_radius = getMinTurnRadius(current);
			if (!checkTurnLimit(current.velocity, next.velocity, dt, current_max_turn_rate, current_min_turn_radius)) {
				// checkTurnConstraint 内部会打印失败信息
				return false;
			}
			LOG_TRACE("  转弯约束检查通过。");

			// --- 4. 模式转换约束 ---
			if (current.mode != next.mode) {
				LOG_INFO("  检测到模式转换: {} -> {} (所有者 ID: {})", static_cast<int>(current.mode), static_cast<int>(next.mode), owner_.getId());
				// 从 owner_ 获取转换速度阈值
				double transSpeed = owner_.getParamOrDefault<double>("dynamics.vtol.transitionSpeed", 15.0);

				// 检查是否是允许的转换路径 (不允许直接 HOVER <-> FIXED_WING)
				bool valid_path =
					(current.mode == FlightMode::HOVER && next.mode == FlightMode::TRANSITION) ||
					(current.mode == FlightMode::TRANSITION && next.mode == FlightMode::HOVER) ||
					(current.mode == FlightMode::TRANSITION && next.mode == FlightMode::FIXED_WING) ||
					(current.mode == FlightMode::FIXED_WING && next.mode == FlightMode::TRANSITION);
				if (!valid_path) {
					LOG_TRACE("  失败：不允许的模式转换路径 {} -> {}", static_cast<int>(current.mode), static_cast<int>(next.mode));
					return false;
				}

				// 检查转换时的速度约束
				double nextHSpeed = next.velocity.head<2>().norm(); // 下一状态的水平速度
				// 进入固定翼模式时，速度必须高于（或等于）转换速度
				if (next.mode == FlightMode::FIXED_WING && nextHSpeed < transSpeed - Constants::VELOCITY_EPSILON) {
					LOG_TRACE("  失败：进入固定翼模式速度 ({:.2f} m/s) 低于转换速度 ({:.2f} m/s)。", nextHSpeed, transSpeed);
					return false;
				}
				// 进入悬停模式时，速度必须低于（或等于）转换速度
				if (next.mode == FlightMode::HOVER && nextHSpeed > transSpeed + Constants::VELOCITY_EPSILON) {
					LOG_TRACE("  失败：进入悬停模式速度 ({:.2f} m/s) 高于转换速度 ({:.2f} m/s)。", nextHSpeed, transSpeed);
					return false;
				}
				// 过渡模式 (TRANSITION) 的速度可以在阈值附近，暂不设严格限制
				// 但可以检查是否低于固定翼失速速度（如果模型有效）
				if (next.mode == FlightMode::TRANSITION && fw_model_) {
					double fw_stall_speed = fw_model_->getMinOperationalSpeed(next);
					if (nextHSpeed < fw_stall_speed - Constants::VELOCITY_EPSILON) {
						LOG_TRACE("  警告（非失败）：过渡模式速度 ({:.2f} m/s) 低于固定翼失速速度 ({:.2f} m/s)。", nextHSpeed, fw_stall_speed);
					}
				}
				LOG_TRACE("  模式转换路径和速度检查通过。");
			}
			else {
				LOG_TRACE("  模式未转换。");
			}

			LOG_TRACE("VTOL 状态转换可行性检查通过 (所有者 ID: {})。", owner_.getId());
			return true; // 所有检查通过
		}

		// --- 力学计算实现 (委托或混合) ---
		/** @brief 计算 VTOL 升力。*/
		Vector3D VtolDynamics::computeLiftForce(const UavState& state, double air_density) const {
			LOG_TRACE("计算 VTOL 升力 (所有者 ID: {})：模式={}", owner_.getId(), static_cast<int>(state.mode));
			// 检查内部模型指针
			if (!hover_model_ || !fw_model_) {
				LOG_ERROR("计算 VTOL 升力失败：内部模型无效 (所有者 ID: {})。", owner_.getId());
				return Vector3D::Zero();
			}

			if (state.mode == FlightMode::TRANSITION) {
				// 简化：过渡时升力由悬停系统和机翼共同提供，难以精确计算。
				// 策略：使用悬停模型的升力估算，因为它是主要的垂直支撑力。
				LOG_TRACE("  过渡模式，使用悬停模型计算升力。");
				return hover_model_->computeLiftForce(state, air_density);
			}

			const IDynamicModel* model = getCurrentModel(state); // 获取当前模式的模型
			if (!model) return Vector3D::Zero(); // 如果模型无效

			Vector3D lift = model->computeLiftForce(state, air_density); // 委托计算
			LOG_TRACE("  委托给 {} 模型，升力=({:.2f}, {:.2f}, {:.2f})",
				(state.mode == FlightMode::HOVER ? "悬停" : "固定翼"), lift.x(), lift.y(), lift.z());
			return lift;
		}

		/** @brief 计算 VTOL 阻力。*/
		Vector3D VtolDynamics::computeDragForce(const UavState& state, double air_density) const {
			LOG_TRACE("计算 VTOL 阻力 (所有者 ID: {})：模式={}", owner_.getId(), static_cast<int>(state.mode));
			if (!hover_model_ || !fw_model_) {
				LOG_ERROR("计算 VTOL 阻力失败：内部模型无效 (所有者 ID: {})。", owner_.getId());
				return Vector3D::Zero();
			}

			if (state.mode == FlightMode::TRANSITION) {
				// 混合阻力：基于速度插值悬停和固定翼阻力
				LOG_TRACE("  计算过渡模式混合阻力...");
				Vector3D hover_drag = hover_model_->computeDragForce(state, air_density);
				Vector3D fw_drag = fw_model_->computeDragForce(state, air_density);
				// 获取转换速度阈值
				double trans_speed = owner_.getParamOrDefault<double>("dynamics.vtol.transitionSpeed", 15.0);
				double h_speed = state.velocity.head<2>().norm(); // 当前水平速度

				// 计算混合因子 (0=纯悬停, 1=纯固定翼)
				// 确保 trans_speed > 0 避免除零
				double blend_factor = (trans_speed < Constants::EPSILON) ?
					(h_speed > Constants::EPSILON ? 1.0 : 0.0) : // 如果转换速度为0，则速度大于0即为固定翼模式
					std::clamp(h_speed / trans_speed, 0.0, 1.0); // 线性插值因子

				Vector3D drag = (1.0 - blend_factor) * hover_drag + blend_factor * fw_drag; // 线性插值混合
				LOG_TRACE("    混合因子={:.3f}, 悬停阻力=({:.2f},{:.2f},{:.2f}), 固定翼阻力=({:.2f},{:.2f},{:.2f})",
					blend_factor, hover_drag.x(), hover_drag.y(), hover_drag.z(),
					fw_drag.x(), fw_drag.y(), fw_drag.z());
				LOG_TRACE("    混合阻力=({:.2f},{:.2f},{:.2f})", drag.x(), drag.y(), drag.z());
				return drag;
			}

			const IDynamicModel* model = getCurrentModel(state); // 获取当前模式模型
			if (!model) return Vector3D::Zero(); // 模型无效

			Vector3D drag = model->computeDragForce(state, air_density); // 委托计算
			LOG_TRACE("  委托给 {} 模型，阻力=({:.2f},{:.2f},{:.2f})",
				(state.mode == FlightMode::HOVER ? "悬停" : "固定翼"), drag.x(), drag.y(), drag.z());
			return drag;
		}

		/** @brief 计算 VTOL 推力。*/
		Vector3D VtolDynamics::computeThrustForce(const UavState& state) const {
			LOG_TRACE("计算 VTOL 推力 (所有者 ID: {})：模式={}", owner_.getId(), static_cast<int>(state.mode));
			if (!hover_model_ || !fw_model_) {
				LOG_ERROR("计算 VTOL 推力失败：内部模型无效 (所有者 ID: {})。", owner_.getId());
				return Vector3D::Zero();
			}

			if (state.mode == FlightMode::TRANSITION) {
				// 混合推力：同样可以插值，但推力方向在过渡时复杂。
				// 简化：仍然使用线性插值，但这可能不完全准确反映物理过程。
				LOG_TRACE("  计算过渡模式混合推力 (简化插值)...");
				Vector3D hover_thrust = hover_model_->computeThrustForce(state); // 悬停推力（通常垂直）
				Vector3D fw_thrust = fw_model_->computeThrustForce(state);     // 固定翼推力（通常向前）
				double trans_speed = owner_.getParamOrDefault<double>("dynamics.vtol.transitionSpeed", 15.0);
				double h_speed = state.velocity.head<2>().norm();
				double blend_factor = (trans_speed < Constants::EPSILON) ?
					(h_speed > Constants::EPSILON ? 1.0 : 0.0) :
					std::clamp(h_speed / trans_speed, 0.0, 1.0);

				Vector3D thrust = (1.0 - blend_factor) * hover_thrust + blend_factor * fw_thrust; // 线性插值
				LOG_TRACE("    混合因子={:.3f}, 悬停推力=({:.2f},{:.2f},{:.2f}), 固定翼推力=({:.2f},{:.2f},{:.2f})",
					blend_factor, hover_thrust.x(), hover_thrust.y(), hover_thrust.z(),
					fw_thrust.x(), fw_thrust.y(), fw_thrust.z());
				LOG_TRACE("    混合推力=({:.2f},{:.2f},{:.2f})", thrust.x(), thrust.y(), thrust.z());
				return thrust;
			}

			const IDynamicModel* model = getCurrentModel(state); // 获取当前模式模型
			if (!model) return Vector3D::Zero(); // 模型无效

			Vector3D thrust = model->computeThrustForce(state); // 委托计算
			LOG_TRACE("  委托给 {} 模型，推力=({:.2f},{:.2f},{:.2f})",
				(state.mode == FlightMode::HOVER ? "悬停" : "固定翼"), thrust.x(), thrust.y(), thrust.z());
			return thrust;
		}

	} // namespace NSUav
} // namespace NSDrones