{"description": "Parameters for BSpline Trajectory Optimizer", "parameters": [{"key": "order", "type": "int", "description": "B-spline order.", "default": 3, "constraints": {"type": "numeric", "min": 2, "max": 5}}, {"key": "output_points", "type": "int", "description": "Number of points in the output optimized trajectory.", "default": 50, "constraints": {"type": "numeric", "min": 10}}, {"key": "max_iterations", "type": "int", "description": "Maximum number of optimization iterations.", "default": 100, "constraints": {"type": "numeric", "min": 10}}, {"key": "learning_rate", "type": "double", "description": "Learning rate for the optimization algorithm.", "default": 0.01, "constraints": {"type": "numeric", "min": 1e-06, "max": 1.0}}, {"key": "tolerance", "type": "double", "description": "Convergence tolerance for optimization.", "default": 0.0001, "constraints": {"type": "numeric", "min": 1e-08}}, {"key": "collision_weight", "type": "double", "description": "Weight for collision avoidance cost.", "default": 100.0, "constraints": {"type": "numeric", "min": 0.0}}, {"key": "safe_distance", "type": "double", "description": "Safe distance from obstacles (m).", "default": 1.0, "constraints": {"type": "numeric", "min": 0.1}}, {"key": "sample_points", "type": "int", "description": "Number of points to sample along the trajectory for checks.", "default": 30, "constraints": {"type": "numeric", "min": 5}}, {"key": "cost_weights.energy", "type": "double", "description": "Weight for energy minimization cost.", "default": 1.0, "constraints": {"type": "numeric", "min": 0.0, "max": 1.0}}]}