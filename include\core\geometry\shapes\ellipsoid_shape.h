#pragma once

#include "core/geometry/ishape.h"
#include <fcl/geometry/shape/ellipsoid.h>

namespace NSDrones {
namespace NSCore {

    /**
     * @brief 椭球体形状
     * 
     * 表示一个以局部坐标系原点为中心的椭球体。
     * 椭球体的三个半轴分别沿X、Y、Z轴方向。
     */
    class EllipsoidShape : public IShape {
    private:
        double radius_x_;  // X轴半径
        double radius_y_;  // Y轴半径
        double radius_z_;  // Z轴半径
        
        mutable std::shared_ptr<fcl::Ellipsoidd> fcl_ellipsoid_;  // FCL几何对象（延迟创建）

    public:
        /**
         * @brief 构造函数
         * @param radius_x X轴半径
         * @param radius_y Y轴半径
         * @param radius_z Z轴半径
         */
        EllipsoidShape(double radius_x, double radius_y, double radius_z);

        /**
         * @brief 默认构造函数（单位椭球）
         */
        EllipsoidShape();

        /**
         * @brief 拷贝构造函数
         */
        EllipsoidShape(const EllipsoidShape& other);

        /**
         * @brief 赋值操作符
         */
        EllipsoidShape& operator=(const EllipsoidShape& other);

        // IShape接口实现
        ShapeType getType() const override { return ShapeType::ELLIPSOID; }
        int getDimension() const override { return 3; }  // 3D实体
        std::shared_ptr<fcl::CollisionGeometryd> getFCLGeometry() const override;
        fcl::AABBd getAABB(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const override;
        double getVolume() const override;
        double getSurfaceArea() const override;
        fcl::Vector3d getCentroid() const override;
        fcl::Matrix3d getInertiaMatrix(double mass) const override;
        std::unique_ptr<IShape> clone() const override;
        nlohmann::json serialize() const override;
        bool deserialize(const nlohmann::json& json) override;
        std::string toString() const override;
        bool containsPoint(const fcl::Vector3d& point) const override;
        double distanceToPoint(const fcl::Vector3d& point) const override;
        double getCharacteristicSize() const override;

        // EllipsoidShape特有方法
        /**
         * @brief 获取X轴半径
         */
        double getRadiusX() const { return radius_x_; }

        /**
         * @brief 获取Y轴半径
         */
        double getRadiusY() const { return radius_y_; }

        /**
         * @brief 获取Z轴半径
         */
        double getRadiusZ() const { return radius_z_; }

        /**
         * @brief 获取半径向量
         */
        fcl::Vector3d getRadii() const { return fcl::Vector3d(radius_x_, radius_y_, radius_z_); }

        /**
         * @brief 设置半径
         * @param radius_x X轴半径
         * @param radius_y Y轴半径
         * @param radius_z Z轴半径
         */
        void setRadii(double radius_x, double radius_y, double radius_z);

        /**
         * @brief 设置半径
         * @param radii 半径向量
         */
        void setRadii(const fcl::Vector3d& radii);

        /**
         * @brief 检查是否为球体
         * @param tolerance 容差
         * @return 是否为球体
         */
        bool isSphere(double tolerance = 1e-6) const;

        /**
         * @brief 计算椭球面上的点
         * @param theta 极角（0到π）
         * @param phi 方位角（0到2π）
         * @param transform 变换矩阵
         * @return 椭球面上的点
         */
        fcl::Vector3d getPointOnEllipsoid(double theta, double phi,
                                         const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 生成椭球面上的均匀采样点
         * @param num_points 点的数量
         * @param transform 变换矩阵
         * @return 点列表
         */
        std::vector<fcl::Vector3d> generateUniformPoints(size_t num_points,
                                                         const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 计算椭球面法向量（在给定点处）
         * @param point 椭球面上的点
         * @param transform 变换矩阵
         * @return 法向量
         */
        fcl::Vector3d getSurfaceNormal(const fcl::Vector3d& point,
                                      const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 获取最大半径
         */
        double getMaxRadius() const;

        /**
         * @brief 获取最小半径
         */
        double getMinRadius() const;

        /**
         * @brief 获取偏心率（扁率）
         */
        double getEccentricity() const;

        /**
         * @brief 创建单位椭球
         * @return EllipsoidShape实例
         */
        static std::unique_ptr<EllipsoidShape> createUnitEllipsoid();

        /**
         * @brief 创建球体（所有半径相等）
         * @param radius 半径
         * @return EllipsoidShape实例
         */
        static std::unique_ptr<EllipsoidShape> createSphere(double radius);

        /**
         * @brief 创建扁椭球（两个半径相等）
         * @param equatorial_radius 赤道半径
         * @param polar_radius 极半径
         * @return EllipsoidShape实例
         */
        static std::unique_ptr<EllipsoidShape> createOblate(double equatorial_radius, double polar_radius);

        /**
         * @brief 创建长椭球（一个轴较长）
         * @param major_radius 长轴半径
         * @param minor_radius 短轴半径
         * @return EllipsoidShape实例
         */
        static std::unique_ptr<EllipsoidShape> createProlate(double major_radius, double minor_radius);

    private:
        /**
         * @brief 确保FCL对象已创建
         */
        void ensureFCLObject() const;

        /**
         * @brief 验证半径参数
         */
        void validateRadii() const;
    };

} // namespace NSCore
} // namespace NSDrones
