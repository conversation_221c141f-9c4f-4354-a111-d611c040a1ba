// src/utils/file_utils.cpp
#include "utils/file_utils.h"
#include "params/param_json.h"
#include "utils/logging.h"
#include "core/types.h"
#include <fstream>
#include <sstream>
#include <system_error>
#include <algorithm>

#ifdef _WIN32
#include <windows.h>
#include <vector>    // for vector buffer
#elif defined(__APPLE__)
#include <unistd.h>  // for readlink
#include <limits.h> // for PATH_MAX
#include <mach-o/dyld.h>  // for _NSGetExecutablePath
#else
#include <unistd.h>  // for readlink
#include <limits.h> // for PATH_MAX
#endif

#include <nlohmann/json.hpp>

namespace NSDrones {
	namespace NSUtils {
		namespace fs = std::filesystem; // 文件系统命名空间别名
		using json = nlohmann::json;    // JSON 类型别名
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSParams;

		/**
		 * @brief 检查文件是否存在且是常规文件。
		 */
		bool isFileExists(const fs::path& filepath) {
			std::error_code ec; // 用于接收可能的错误码
			bool exists = fs::is_regular_file(filepath, ec); // 检查是否为常规文件
			if (ec) { // 如果检查过程发生错误
				LOG_ERROR("检查文件 {} 状态时出错: {}", filepath.generic_string(), ec.message());
				return false; // 操作出错，返回 false
			}
			LOG_DEBUG("文件 {} 是否存在且为常规文件: {}", filepath.generic_string(), exists);
			return exists; // 返回检查结果
		}

		/**
		 * @brief 检查目录是否存在。
		 */
		bool isDirExists(const fs::path& dirpath) {
			std::error_code ec; // 用于接收可能的错误码
			bool exists = fs::is_directory(dirpath, ec); // 检查是否为目录
			if (ec) { // 如果检查过程发生错误
				LOG_ERROR("检查目录 {} 状态时出错: {}", dirpath.generic_string(), ec.message());
				return false; // 操作出错，返回 false
			}
			LOG_DEBUG("目录 {} 是否存在: {}", dirpath.generic_string(), exists);
			return exists; // 返回检查结果
		}

		/**
		 * @brief 读取文件内容到字符串。
		 */
		std::optional<std::string> readFileToString(const fs::path& filepath) {
			LOG_DEBUG("尝试读取文件: {}", filepath.generic_string());
			std::ifstream ifs(filepath, std::ios::in | std::ios::binary); // 以二进制模式读取以避免行尾转换问题
			if (!ifs.is_open() || !ifs.good()) { // 检查文件是否成功打开
				LOG_ERROR("无法打开文件进行读取: {} (原因: {})", filepath.generic_string(), strerror(errno)); // 使用 strerror 获取系统错误信息
				return std::nullopt; // 返回空 optional 表示失败
			}

			// 获取文件大小
			ifs.seekg(0, std::ios::end);
			std::streamsize size = ifs.tellg();
			ifs.seekg(0, std::ios::beg);

			// 检查文件大小是否有效
			if (size <= 0) { // 文件为空或大小无效
				if (size == 0) {
					LOG_DEBUG("文件为空: {}", filepath.generic_string());
					return ""; // 文件为空，返回空字符串
				}
				else {
					LOG_ERROR("获取文件大小失败或文件大小无效 ({}) for file: {}", size, filepath.generic_string());
					return std::nullopt; // 文件大小无效，返回 nullopt
				}
			}

			// 读取内容
			std::string content(static_cast<size_t>(size), '\0'); // 预分配字符串大小，并用空字符填充
			if (!ifs.read(content.data(), size)) { // 使用 .data() 获取非 const 指针
				LOG_ERROR("读取文件内容失败: {} (读取了 {} 字节，预期 {})", filepath.generic_string(), ifs.gcount(), size);
				return std::nullopt; // 读取失败
			}
			LOG_INFO("成功读取文件: {}, 大小: {} 字节", filepath.generic_string(), size);
			return content; // 返回包含文件内容的字符串
		}

		/**
		 * @brief 安全地拼接路径组件。
		 */
		fs::path joinPaths(const fs::path& base, const fs::path& part) {
			fs::path result = base;
			return result / part; // 使用 / 运算符安全地连接路径
		}

		/**
		 * @brief 获取文件名（不含扩展名）。
		 * @param filepath 文件路径。
		 * @return 不含扩展名的文件名。
		 */
		std::string getFileNameWithoutExtension(const fs::path& filepath) {
			// 获取文件名（含扩展名）
			std::string filename = filepath.filename().string();

			// 查找最后一个点的位置（扩展名的开始位置）
			size_t lastDot = filename.find_last_of(".");

			// 如果找到点，则返回点之前的部分；否则返回整个文件名
			return (lastDot != std::string::npos) ? filename.substr(0, lastDot) : filename;
		}

		// --- 路径解析函数实现 ---

		fs::path getExePath() {
			LOG_TRACE("尝试获取可执行文件路径...");
#ifdef _WIN32
			std::vector<wchar_t> buffer(MAX_PATH + 1, L'\\0'); // 额外空间并初始化
			DWORD pathLen = 0;
			const int max_attempts = 5; // 防止无限循环
			for (int attempt = 0; attempt < max_attempts; ++attempt) {
				pathLen = GetModuleFileNameW(NULL, buffer.data(), static_cast<DWORD>(buffer.size()));
				if (pathLen == 0) {
					LOG_ERROR("Windows API GetModuleFileNameW 失败，错误码: {}", GetLastError());
					return fs::path();
				}
				if (pathLen < buffer.size()) { // 成功，路径在缓冲区内
					fs::path exe_path(buffer.data()); // buffer.data() 是 wchar_t*
					LOG_DEBUG("成功获取 Windows 可执行文件路径: {}", exe_path.generic_string());
					return exe_path;
				}
				// 缓冲区不足，尝试扩大
				if (buffer.size() >= (MAX_PATH * 16)) { // 设置一个合理的上限
					LOG_ERROR("GetModuleFileNameW 缓冲区大小达到上限 ({}) 仍不足，放弃获取路径。", buffer.size());
					return fs::path();
				}
				buffer.resize(buffer.size() * 2);
				LOG_TRACE("GetModuleFileNameW 缓冲区不足，调整大小为 {} 并重试...", buffer.size());
			}
			LOG_ERROR("GetModuleFileNameW 多次尝试后仍无法获取完整路径。");
			return fs::path();
#else // 假定为类 Unix 系统 (Linux, macOS 等)
			char result[PATH_MAX];
			ssize_t count = readlink("/proc/self/exe", result, PATH_MAX);
			if (count != -1) {
				fs::path exe_path(std::string(result, static_cast<size_t>(count)));
				LOG_DEBUG("成功通过 readlink /proc/self/exe 获取可执行文件路径: {}", exe_path.generic_string());
				return exe_path;
			}
			LOG_ERROR("readlink /proc/self/exe 失败或不受支持。错误: {}", strerror(errno));

			// macOS 回退逻辑
			#ifdef __APPLE__
			char path_buffer[PATH_MAX];
			uint32_t size = sizeof(path_buffer);
			if (_NSGetExecutablePath(path_buffer, &size) == 0) {
				LOG_DEBUG("macOS: 使用 _NSGetExecutablePath 获取可执行文件路径: {}", path_buffer);
				return fs::path(path_buffer);
			} else {
				LOG_ERROR("macOS: _NSGetExecutablePath 失败，缓冲区大小不足");
			}
			#endif

			return fs::path();
#endif
		}

		fs::path getExeDir() {
			LOG_TRACE("尝试获取可执行文件所在目录...");
			fs::path exe_path = getExePath();
			if (!exe_path.empty() && exe_path.has_parent_path()) {
				fs::path exe_dir = exe_path.parent_path();
				LOG_DEBUG("可执行文件所在目录: {}", exe_dir.generic_string());
				return exe_dir;
			}
			LOG_WARN("无法确定可执行文件目录 (可执行文件路径为空或无父路径)。");
			return fs::path();
		}

		fs::path getDataDirectory() {
			LOG_TRACE("尝试获取应用程序的 data 目录路径...");
			fs::path exe_dir = getExeDir();
			if (!exe_dir.empty() && exe_dir.has_parent_path()) {
				fs::path data_dir = exe_dir / "data";
				LOG_DEBUG("应用程序的 data 目录: {}", data_dir.generic_string());
				return data_dir;
			}
			LOG_WARN("无法确定应用程序的 data 目录 (可执行文件路径为空或无父路径)。");
			return fs::path();
		}

		fs::path getDataPath() {
			LOG_TRACE("尝试获取应用程序的数据根路径...");
			// 使用启发式方法查找数据根路径
			fs::path exe_dir = getExeDir();
			if (!exe_dir.empty()) {
				// 首先检查可执行文件目录是否包含 'data' 子目录
				fs::path data_dir = exe_dir / "data";
				if (fs::exists(data_dir) && fs::is_directory(data_dir)) {
					LOG_DEBUG("找到数据根路径: {}", exe_dir.generic_string());
					return exe_dir;
				}

				// 如果可执行文件目录没有 'data' 子目录，检查上级目录
				if (exe_dir.has_parent_path()) {
					fs::path parent_dir = exe_dir.parent_path();
					fs::path parent_data_dir = parent_dir / "data";
					if (fs::exists(parent_data_dir) && fs::is_directory(parent_data_dir)) {
						LOG_DEBUG("在上级目录找到数据根路径: {}", parent_dir.generic_string());
						return parent_dir;
					}
				}

				// 如果都没找到，返回可执行文件目录作为备选
				LOG_DEBUG("使用可执行文件目录作为数据根路径: {}", exe_dir.generic_string());
				return exe_dir;
			}

			// 最后的备选方案：使用当前工作目录
			fs::path current_dir = fs::current_path();
			LOG_WARN("无法确定数据根路径，使用当前工作目录: {}", current_dir.generic_string());
			return current_dir;
		}

		fs::path resolvePath(const fs::path& base_path, const fs::path& relative_or_absolute_path) {
			LOG_TRACE("解析路径: '{}' (相对于基础路径 '{}')...", \
				relative_or_absolute_path.generic_string(), base_path.generic_string());

			if (relative_or_absolute_path.empty()) {
				LOG_WARN("尝试解析空的路径，返回空路径。");
				return {};
			}

			// 基础路径有效性检查
			if (base_path.empty()) {
				if (relative_or_absolute_path.is_absolute()) {
					LOG_DEBUG("基础路径为空，但目标路径 '{}' 是绝对路径，尝试直接规范化。", relative_or_absolute_path.generic_string());
					// 允许继续处理绝对路径
				}
				else {
					LOG_ERROR("无法解析相对路径 '{}'，因为提供的基础路径为空。", relative_or_absolute_path.generic_string());
					return {}; // 无法解析相对路径
				}
			}

			try {
				fs::path final_path;
				if (relative_or_absolute_path.is_absolute()) {
					LOG_TRACE("路径 '{}' 已是绝对路径，直接规范化。", relative_or_absolute_path.generic_string());
					final_path = fs::weakly_canonical(relative_or_absolute_path);
				}
				else {
					// 再次检查 base_path，以防上面的逻辑允许空 base_path 通过（虽然不应该）
					if (base_path.empty()) {
						LOG_ERROR("逻辑错误：尝试解析相对路径 '{}' 时基础路径为空。", relative_or_absolute_path.generic_string());
						return {};
					}
					// 使用 C++17 文件系统的路径拼接操作符
					fs::path combined_path = base_path / relative_or_absolute_path;
					LOG_TRACE("组合路径: {}", combined_path.generic_string());
					// 使用 weakly_canonical 进行规范化，它不会因为路径不存在而失败
					final_path = fs::weakly_canonical(combined_path);
				}

				// 统一路径分隔符为 '/' (可选，但有助于跨平台一致性)
				// 使用 generic_string() 而不是 string() 来确保跨平台一致性
				std::string path_str = final_path.generic_string();
				fs::path result_path(path_str);

				LOG_DEBUG("成功解析路径 '{}' (基于 '{}') 为: {}", \
					relative_or_absolute_path.generic_string(), base_path.generic_string(), result_path.generic_string());
				return result_path;

			}
			catch (const fs::filesystem_error& e) {
				LOG_WARN("解析或规范化路径 '{}' (基于 '{}') 时出错: {}。返回未规范化的原始组合路径作为回退（如果可能）。", \
					relative_or_absolute_path.generic_string(), base_path.generic_string(), e.what());
				// 尝试返回一个尽可能有用的路径，即使规范化失败
				if (relative_or_absolute_path.is_absolute()) return relative_or_absolute_path;
				if (!base_path.empty()) return base_path / relative_or_absolute_path;
				return {}; // 最差情况
			}
		}

		/**
		 * @brief 从文件加载 JSON 对象。
		 * @param json_file_path JSON 文件的路径。
		 * @return 解析后的 nlohmann::json 对象。
		 * @throws DroneException 如果文件不存在、无法读取或 JSON 解析失败。
		 */
		json loadJsonFile(const fs::path& json_file_path) {
			LOG_DEBUG("尝试从文件加载 JSON: {}", json_file_path.generic_string());

			// 1. 读取文件内容到字符串
			auto file_content_opt = readFileToString(json_file_path);
			if (!file_content_opt) {
				std::string error_msg = "无法读取 JSON 文件内容: " + json_file_path.generic_string();
				LOG_ERROR(error_msg);
				throw DroneException(error_msg, ErrorCode::IoError);
			}
			const std::string& file_content = *file_content_opt;
			if (file_content.empty() && isFileExists(json_file_path)) { // 文件存在但为空
				LOG_WARN("JSON 文件为空: {}。将返回空的 JSON 对象。", json_file_path.generic_string());
				return json::object();
			}
			if (file_content.empty() && !isFileExists(json_file_path)) { // 文件不存在导致内容为空
				std::string error_msg = "JSON 文件不存在或无法访问: " + json_file_path.generic_string();
				LOG_ERROR(error_msg);
				throw DroneException(error_msg, ErrorCode::NotFound);
			}

			// 2. 解析 JSON 字符串
			try {
				json parsed_json = json::parse(file_content);
				LOG_INFO("成功从文件加载并解析 JSON: {}", json_file_path.generic_string());
				return parsed_json;
			}
			catch (const json::parse_error& e) {
				std::string error_msg = "解析 JSON 文件 '" + json_file_path.generic_string() + "' 失败: " + e.what() + " (在字节偏移 " + std::to_string(e.byte) + ")";
				LOG_ERROR(error_msg);
				throw DroneException(error_msg, ErrorCode::ExecutionFailed);
			}
			catch (const std::exception& e) { // 其他可能的异常，例如 json::type_error
				std::string error_msg = "处理 JSON 文件 '" + json_file_path.generic_string() + "' 时发生非解析类型的JSON相关错误: " + e.what();
				LOG_ERROR(error_msg);
				throw DroneException(error_msg, ErrorCode::ResourceUnavailable);
			}
		}

		bool findFilesInDirectory(
			const fs::path& directory_path,
			const std::vector<std::string>& extensions,
			bool recursive,
			std::vector<fs::path>& out_file_paths)
		{
			// 手动构建扩展名字符串以用于日志记录
			std::string extensions_str_for_log;
			if (extensions.empty()) {
				extensions_str_for_log = "any"; // 或者 "N/A", ""
			}
			else {
				for (size_t i = 0; i < extensions.size(); ++i) {
					if (i > 0) {
						extensions_str_for_log += ", ";
					}
					extensions_str_for_log += extensions[i];
				}
			}
			LOG_TRACE("尝试在目录 '{}' 中查找文件 (扩展名: [{}], 递归: {})...",
				directory_path.generic_string(), extensions_str_for_log, (recursive ? "true" : "false"));

			if (!fs::exists(directory_path) || !fs::is_directory(directory_path)) {
				LOG_ERROR("目录 '{}' 不存在或不是一个有效的目录。", directory_path.generic_string());
				return false;
			}

			out_file_paths.clear(); // 清空输出向量

			try {
				if (recursive) {
					for (const auto& entry : fs::recursive_directory_iterator(directory_path)) {
						if (entry.is_regular_file()) {
							if (extensions.empty()) { // 如果没有指定扩展名，则添加所有文件
								out_file_paths.push_back(entry.path());
							} else {
								for (const auto& ext : extensions) {
									// 确保扩展名比较不区分大小写，或者根据需要调整
									std::string file_ext_str = entry.path().extension().string();
									std::transform(file_ext_str.begin(), file_ext_str.end(), file_ext_str.begin(), ::tolower);
									std::string conf_ext_str = ext;
									std::transform(conf_ext_str.begin(), conf_ext_str.end(), conf_ext_str.begin(), ::tolower);
									if (file_ext_str == conf_ext_str) {
										out_file_paths.push_back(entry.path());
										break; // 找到匹配的扩展名，处理下一个文件
									}
								}
							}
						}
					}
				} else {
					for (const auto& entry : fs::directory_iterator(directory_path)) {
						if (entry.is_regular_file()) {
							if (extensions.empty()) {
								out_file_paths.push_back(entry.path());
							} else {
								for (const auto& ext : extensions) {
									std::string file_ext_str = entry.path().extension().string();
									std::transform(file_ext_str.begin(), file_ext_str.end(), file_ext_str.begin(), ::tolower);
									std::string conf_ext_str = ext;
									std::transform(conf_ext_str.begin(), conf_ext_str.end(), conf_ext_str.begin(), ::tolower);
									if (file_ext_str == conf_ext_str) {
										out_file_paths.push_back(entry.path());
										break;
									}
								}
							}
						}
					}
				}
				LOG_INFO("在目录 '{}' 中找到 {} 个匹配的文件。", directory_path.generic_string(), out_file_paths.size());
				return true;
			}
			catch (const fs::filesystem_error& e) {
				LOG_ERROR("遍历目录 '{}' 时发生文件系统错误: {}", directory_path.generic_string(), e.what());
				return false;
			}
			catch (const std::exception& e) {
				LOG_ERROR("查找文件时发生未知异常: {}", e.what());
				return false;
			}
		}

	} // namespace NSUtils
} // namespace NSDrones