﻿  trajectory_optimizer.cpp
  config.cpp
  main.cpp
  itask_planner.cpp
  mission_planner.cpp
  planning_types.cpp
  surveysphere_taskplanner.cpp
  fixedwing_dynamics.cpp
  uav_config.cpp
  orientation_utils.cpp
  正在生成代码...
ompl.lib(RealVectorBounds.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(RealVectorBounds.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(StateSpace.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(StateSpace.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(SpaceInformation.cpp.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(SpaceInformation.cpp.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(RealVectorStateSpace.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(RealVectorStateSpace.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(ProblemDefinition.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(ProblemDefinition.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(PathLengthOptimizationObjective.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(PathLengthOptimizationObjective.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(GoalRegion.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(GoalRegion.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(GoalState.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(GoalState.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(PlannerStatus.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(PlannerStatus.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(PlannerTerminationCondition.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(PlannerTerminationCondition.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(PathGeometric.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(PathGeometric.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(RRTstar.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(RRTstar.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(PathSimplifier.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(PathSimplifier.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(RandomNumbers.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(RandomNumbers.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(StateSampler.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(StateSampler.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(Console.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(Console.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(String.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(String.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(GenericParam.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(GenericParam.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(ProjectionEvaluator.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(ProjectionEvaluator.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(DiscreteMotionValidator.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(DiscreteMotionValidator.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(UniformValidStateSampler.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(UniformValidStateSampler.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(DubinsStateSpace.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(DubinsStateSpace.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(OwenStateSpace.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(OwenStateSpace.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(VanaStateSpace.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(VanaStateSpace.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(VanaOwenStateSpace.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(VanaOwenStateSpace.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(ReedsSheppStateSpace.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(ReedsSheppStateSpace.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(ConstrainedStateSpace.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(ConstrainedStateSpace.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(RealVectorStateProjections.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(RealVectorStateProjections.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(Cost.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(Cost.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(SpaceInformation.cpp.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(SpaceInformation.cpp.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(PathControl.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(PathControl.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(OptimizationObjective.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(OptimizationObjective.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(PathLengthDirectInfSampler.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(PathLengthDirectInfSampler.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(Goal.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(Goal.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(ValidStateSampler.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(ValidStateSampler.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(PlannerData.cpp.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(PlannerData.cpp.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(Planner.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(Planner.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(RejectionInfSampler.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(RejectionInfSampler.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(OrderedInfSampler.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(OrderedInfSampler.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(SelfConfig.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(SelfConfig.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(GeometricEquations.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(GeometricEquations.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(ProlateHyperspheroid.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(ProlateHyperspheroid.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(SO2StateSpace.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(SO2StateSpace.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(SE2StateSpace.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(SE2StateSpace.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(Constraint.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(Constraint.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(WrapperStateSpace.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(WrapperStateSpace.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(SimpleDirectedControlSampler.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(SimpleDirectedControlSampler.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(ControlSpace.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(ControlSpace.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(InformedStateSampler.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(InformedStateSampler.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(StateStorage.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(StateStorage.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(RRTConnect.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(RRTConnect.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(RRT.cpp.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(RRT.cpp.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(LBKPIECE1.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(LBKPIECE1.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(KPIECE1.cpp.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(KPIECE1.cpp.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(RRT.cpp.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(RRT.cpp.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(KPIECE1.cpp.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(KPIECE1.cpp.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(ControlSampler.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(ControlSampler.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
ompl.lib(PrecomputedStateSampler.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
ompl.lib(PrecomputedStateSampler.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
tinysplinecxx.lib(tinysplinecxx.obj) : error LNK2038: 检测到“_ITERATOR_DEBUG_LEVEL”的不匹配项: 值“0”不匹配值“2”(task_allocator.obj 中)
tinysplinecxx.lib(tinysplinecxx.obj) : error LNK2038: 检测到“RuntimeLibrary”的不匹配项: 值“MD_DynamicRelease”不匹配值“MDd_DynamicDebug”(task_allocator.obj 中)
    正在创建库 E:/source/dronesplanning/build/Debug/drones_planning.lib 和对象 E:/source/dronesplanning/build/Debug/drones_planning.exp
LINK : warning LNK4098: 默认库“MSVCRT”与其他库的使用冲突；请使用 /NODEFAULTLIB:library
mission.obj : error LNK2019: 无法解析的外部符号 "public: __cdecl NSDrones::NSMission::Task::Task(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,enum NSDrones::NSCore::TaskType,class std::variant<class std::shared_ptr<class NSDrones::NSMission::PointTarget>,class std::shared_ptr<class NSDrones::NSMission::LineTarget>,class std::shared_ptr<class NSDrones::NSMission::AreaTarget>,class std::shared_ptr<class NSDrones::NSMission::VolumeTarget>,class std::shared_ptr<class NSDrones::NSMission::ObjectTarget> >,class NSDrones::NSMission::CapabilityRequirement,class std::map<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class std::shared_ptr<class NSDrones::NSMission::ITaskStrategy>,struct std::less<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > >,class std::allocator<struct std::pair<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const ,class std::shared_ptr<class NSDrones::NSMission::ITaskStrategy> > > >,class std::shared_ptr<struct NSDrones::NSMission::ITaskParams>,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >)" (??0Task@NSMission@NSDrones@@QEAA@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4TaskType@NSCore@2@V?$variant@V?$shared_ptr@VPointTarget@NSMission@NSDrones@@@std@@V?$shared_ptr@VLineTarget@NSMission@NSDrones@@@2@V?$shared_ptr@VAreaTarget@NSMission@NSDrones@@@2@V?$shared_ptr@VVolumeTarget@NSMission@NSDrones@@@2@V?$shared_ptr@VObjectTarget@NSMission@NSDrones@@@2@@4@VCapabilityRequirement@12@V?$map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$shared_ptr@VITaskStrategy@NSMission@NSDrones@@@2@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$shared_ptr@VITaskStrategy@NSMission@NSDrones@@@2@@std@@@2@@4@V?$shared_ptr@UITaskParams@NSMission@NSDrones@@@4@0@Z)，函数 "void __cdecl std::_Construct_in_place<class NSDrones::NSMission::Task,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,enum NSDrones::NSCore::TaskType &,class std::variant<class std::shared_ptr<class NSDrones::NSMission::PointTarget>,class std::shared_ptr<class NSDrones::NSMission::LineTarget>,class std::shared_ptr<class NSDrones::NSMission::AreaTarget>,class std::shared_ptr<class NSDrones::NSMission::VolumeTarget>,class std::shared_ptr<class NSDrones::NSMission::ObjectTarget> >,class NSDrones::NSMission::CapabilityRequirement,class std::map<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class std::shared_ptr<class NSDrones::NSMission::ITaskStrategy>,struct std::less<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > >,class std::allocator<struct std::pair<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const ,class std::shared_ptr<class NSDrones::NSMission::ITaskStrategy> > > >,class std::shared_ptr<struct NSDrones::NSMission::ITaskParams>,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > >(class NSDrones::NSMission::Task &,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > &&,enum NSDrones::NSCore::TaskType &,class std::variant<class std::shared_ptr<class NSDrones::NSMission::PointTarget>,class std::shared_ptr<class NSDrones::NSMission::LineTarget>,class std::shared_ptr<class NSDrones::NSMission::AreaTarget>,class std::shared_ptr<class NSDrones::NSMission::VolumeTarget>,class std::shared_ptr<class NSDrones::NSMission::ObjectTarget> > &&,class NSDrones::NSMission::CapabilityRequirement &&,class std::map<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class std::shared_ptr<class NSDrones::NSMission::ITaskStrategy>,struct std::less<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > >,class std::allocator<struct std::pair<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const ,class std::shared_ptr<class NSDrones::NSMission::ITaskStrategy> > > > &&,class std::shared_ptr<struct NSDrones::NSMission::ITaskParams> &&,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > &&)" (??$_Construct_in_place@VTask@NSMission@NSDrones@@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAW4TaskType@NSCore@3@V?$variant@V?$shared_ptr@VPointTarget@NSMission@NSDrones@@@std@@V?$shared_ptr@VLineTarget@NSMission@NSDrones@@@2@V?$shared_ptr@VAreaTarget@NSMission@NSDrones@@@2@V?$shared_ptr@VVolumeTarget@NSMission@NSDrones@@@2@V?$shared_ptr@VObjectTarget@NSMission@NSDrones@@@2@@5@VCapabilityRequirement@23@V?$map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$shared_ptr@VITaskStrategy@NSMission@NSDrones@@@2@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$shared_ptr@VITaskStrategy@NSMission@NSDrones@@@2@@std@@@2@@5@V?$shared_ptr@UITaskParams@NSMission@NSDrones@@@5@V45@@std@@YAXAEAVTask@NSMission@NSDrones@@$$QEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@AEAW4TaskType@NSCore@3@$$QEAV?$variant@V?$shared_ptr@VPointTarget@NSMission@NSDrones@@@std@@V?$shared_ptr@VLineTarget@NSMission@NSDrones@@@2@V?$shared_ptr@VAreaTarget@NSMission@NSDrones@@@2@V?$shared_ptr@VVolumeTarget@NSMission@NSDrones@@@2@V?$shared_ptr@VObjectTarget@NSMission@NSDrones@@@2@@0@$$QEAVCapabilityRequirement@23@$$QEAV?$map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$shared_ptr@VITaskStrategy@NSMission@NSDrones@@@2@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$shared_ptr@VITaskStrategy@NSMission@NSDrones@@@2@@std@@@2@@0@$$QEAV?$shared_ptr@UITaskParams@NSMission@NSDrones@@@0@1@Z) 中引用了该符号
E:\source\dronesplanning\build\Debug\drones_planning.exe : fatal error LNK1120: 1 个无法解析的外部命令
