﻿  Building Custom Rule E:/source/dronesplanning/CMakeLists.txt
  task_allocator.cpp
E:\source\dronesplanning\include\planning/planning_types.h(117,36): error C2039: "fromEulerAngles": 不是 "Eigen::Quaternion<double,0>" 的成员
C:\vcpkg\installed\x64-windows\include\Eigen\src/Geometry/Quaternion.h(366,9): message : 参见“Eigen::Quaternion<double,0>”的声明
E:\source\dronesplanning\include\planning/planning_types.h(117,1): error C3861: “fromEulerAngles”: 找不到标识符
E:\source\dronesplanning\include\planning/planning_types.h(175,43): error C2039: "toEulerAngles": 不是 "Eigen::Quaternion<double,0>" 的成员
C:\vcpkg\installed\x64-windows\include\Eigen\src/Geometry/Quaternion.h(366,9): message : 参见“Eigen::Quaternion<double,0>”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(133,33): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_trivially_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(133,19): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_trivially_destructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::is_trivially_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::is_trivially_move_assignable<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(141,22): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_move_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(214,26): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
E:\source\dronesplanning\include\planning/planning_types.h(239,47): message : 查看对正在编译的 类 模板 实例化“std::optional<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(60,19): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_constructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(60,5): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::negation<std::conjunction<std::is_trivially_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> > > >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(97,9): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_move<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(39,19): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_constructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(39,5): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_copy_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::negation<std::conjunction<std::is_trivially_copy_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> > > >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(61,42): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_copy<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\type_traits(808,28): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_trivially_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(70,29): message : 查看指向正在编译的变量模板“const bool is_trivially_destructible_v<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(82): message : 查看对正在编译的 类 模板 实例化“std::_Optional_construct_base<_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(120): message : 查看对正在编译的 类 模板 实例化“std::_Deleted_copy_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(214): message : 查看对正在编译的 类 模板 实例化“std::_Deleted_move_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(101,29): error C2079: “std::_Optional_destruct_base<_Ty,false>::_Value”使用未定义的 struct“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(139): message : 查看对正在编译的 类 模板 实例化“std::_Optional_destruct_base<_Ty,false>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\type_traits(740,28): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(221,39): message : 查看指向正在编译的变量模板“const bool is_destructible_v<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(221,62): error C2338: static_assert failed: 'T in optional<T> must meet the Cpp17Destructible requirements (N4828 [optional.optional]/3).'
E:\source\dronesplanning\include\planning/planning_types.h(345,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(345,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(345,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(345,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(345,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(377,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(377,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(403,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(403,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(469,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(469,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(469,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(469,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(469,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(528,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(528,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\algorithm/allocator/task_allocator.h(58,25): error C3646: “allocate”: 未知重写说明符
E:\source\dronesplanning\include\algorithm/allocator/task_allocator.h(58,33): error C2059: 语法错误:“(”
E:\source\dronesplanning\include\algorithm/allocator/task_allocator.h(61,72): error C2238: 意外的标记位于“;”之前
E:\source\dronesplanning\src\algorithm\allocator\task_allocator.cpp(55,39): error C2039: "allocate": 不是 "NSDrones::NSAlgorithm::TaskAllocator" 的成员
E:\source\dronesplanning\include\algorithm/allocator/task_allocator.h(32,9): message : 参见“NSDrones::NSAlgorithm::TaskAllocator”的声明
E:\source\dronesplanning\src\algorithm\allocator\task_allocator.cpp(55,47): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\src\algorithm\allocator\task_allocator.cpp(55,47): error C2146: 语法错误: 缺少“;”(在标识符“allocate”的前面)
E:\source\dronesplanning\src\algorithm\allocator\task_allocator.cpp(59,3): error C2143: 语法错误: 缺少“;”(在“{”的前面)
E:\source\dronesplanning\src\algorithm\allocator\task_allocator.cpp(59,3): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
  energy_evaluator.cpp
E:\source\dronesplanning\include\planning/planning_types.h(117,36): error C2039: "fromEulerAngles": 不是 "Eigen::Quaternion<double,0>" 的成员
C:\vcpkg\installed\x64-windows\include\Eigen\src/Geometry/Quaternion.h(366,9): message : 参见“Eigen::Quaternion<double,0>”的声明
E:\source\dronesplanning\include\planning/planning_types.h(117,1): error C3861: “fromEulerAngles”: 找不到标识符
E:\source\dronesplanning\include\planning/planning_types.h(175,43): error C2039: "toEulerAngles": 不是 "Eigen::Quaternion<double,0>" 的成员
C:\vcpkg\installed\x64-windows\include\Eigen\src/Geometry/Quaternion.h(366,9): message : 参见“Eigen::Quaternion<double,0>”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(133,33): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_trivially_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(133,19): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_trivially_destructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::is_trivially_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::is_trivially_move_assignable<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(141,22): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_move_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(214,26): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
E:\source\dronesplanning\include\planning/planning_types.h(239,47): message : 查看对正在编译的 类 模板 实例化“std::optional<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(60,19): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_constructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(60,5): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::negation<std::conjunction<std::is_trivially_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> > > >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(97,9): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_move<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(39,19): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_constructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(39,5): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_copy_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::negation<std::conjunction<std::is_trivially_copy_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> > > >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(61,42): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_copy<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\type_traits(808,28): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_trivially_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(70,29): message : 查看指向正在编译的变量模板“const bool is_trivially_destructible_v<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(82): message : 查看对正在编译的 类 模板 实例化“std::_Optional_construct_base<_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(120): message : 查看对正在编译的 类 模板 实例化“std::_Deleted_copy_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(214): message : 查看对正在编译的 类 模板 实例化“std::_Deleted_move_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(101,29): error C2079: “std::_Optional_destruct_base<_Ty,false>::_Value”使用未定义的 struct“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(139): message : 查看对正在编译的 类 模板 实例化“std::_Optional_destruct_base<_Ty,false>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\type_traits(740,28): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(221,39): message : 查看指向正在编译的变量模板“const bool is_destructible_v<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(221,62): error C2338: static_assert failed: 'T in optional<T> must meet the Cpp17Destructible requirements (N4828 [optional.optional]/3).'
E:\source\dronesplanning\include\planning/planning_types.h(270,20): error C2079: “NSDrones::NSPlanning::SubTaskAssignment::start_state”使用未定义的 struct“NSDrones::NSUav::UavState”
E:\source\dronesplanning\include\planning/planning_types.h(345,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(345,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(345,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(345,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(345,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(377,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(377,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(403,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(403,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(413,17): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/planning_types.h(413,17): error C2143: 语法错误: 缺少“;”(在“*”的前面)
E:\source\dronesplanning\include\planning/planning_types.h(413,36): error C2238: 意外的标记位于“;”之前
E:\source\dronesplanning\include\planning/planning_types.h(469,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(469,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(469,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(469,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(469,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(528,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(528,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(51,19): error C3646: “evaluate”: 未知重写说明符
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(51,27): error C2059: 语法错误:“(”
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(55,20): error C2238: 意外的标记位于“;”之前
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(58,22): error C2065: “TrajectoryCost”: 未声明的标识符
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(58,9): error C2923: "std::map": "TrajectoryCost" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(58,22): message : 参见“TrajectoryCost”的声明
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(58,9): error C2976: “std::map'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\map(73,11): message : 参见“std::map”的声明
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(60,19): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(60,19): error C2143: 语法错误: 缺少“,”(在“&”的前面)
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(62,20): error C2955: “std::map”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\map(73,11): message : 参见“std::map”的声明
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(58,38): error C3668: “NSDrones::NSAlgorithm::EnergyEvaluator::evaluate”: 包含重写说明符“override”的方法没有重写任何基类方法
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(116,35): error C2143: 语法错误: 缺少“;”(在“NSDrones::NSAlgorithm::EnergyEvaluator::evaluate”的前面)
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(116,43): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(120,3): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(116,35): error C2511: “int NSDrones::NSAlgorithm::EnergyEvaluator::evaluate(NSDrones::NSUav::ConstUavPtr,const NSDrones::NSPlanning::Trajectory &,const NSDrones::NSMission::Mission *) const”:“NSDrones::NSAlgorithm::EnergyEvaluator”中没有找到重载的成员函数
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(25,9): message : 参见“NSDrones::NSAlgorithm::EnergyEvaluator”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(122,19): error C2146: 语法错误: 缺少“;”(在标识符“cost_result”的前面)
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(122,19): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(123,4): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(127,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(128,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(129,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(129,5): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(130,12): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(134,4): error C2352: “NSDrones::NSCore::BaseObject::getName”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(89,23): message : 参见“NSDrones::NSCore::BaseObject::getName”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(134,4): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(145,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(146,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(147,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(147,5): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(148,12): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(158,1): error C2671: “NSDrones::NSAlgorithm::EnergyEvaluator::evaluate”: 静态成员函数没有“this”指针
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(161,19): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(199,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(163,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(165,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(167,6): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(171,6): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(172,6): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(172,6): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(174,12): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(187,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(194,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(195,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(195,5): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(196,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(197,5): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(199,12): error C2065: “cost_result”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(204,45): error C2676: 二进制“[”:“const NSDrones::NSPlanning::Trajectory”不定义该运算符或到预定义运算符可接收的类型的转换
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(204,25): error C2530: “segment”: 必须初始化引用
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(205,33): error C3536: “segment”: 初始化之前无法使用
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(205,24): error C2530: “states”: 必须初始化引用
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(208): error C3536: “states”: 初始化之前无法使用
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(209,6): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(213,5): error C2352: “NSDrones::NSCore::BaseObject::getId”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\include\core/base_object.h(83,20): message : 参见“NSDrones::NSCore::BaseObject::getId”的声明
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(220,40): error C2109: 下标要求数组或指针类型
E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp(220,40): fatal  error C1003: 错误计数超过 100；正在停止编译
  ipath_planner.cpp
E:\source\dronesplanning\include\planning/planning_types.h(117,36): error C2039: "fromEulerAngles": 不是 "Eigen::Quaternion<double,0>" 的成员
C:\vcpkg\installed\x64-windows\include\Eigen\src/Geometry/Quaternion.h(366,9): message : 参见“Eigen::Quaternion<double,0>”的声明
E:\source\dronesplanning\include\planning/planning_types.h(117,1): error C3861: “fromEulerAngles”: 找不到标识符
E:\source\dronesplanning\include\planning/planning_types.h(175,43): error C2039: "toEulerAngles": 不是 "Eigen::Quaternion<double,0>" 的成员
C:\vcpkg\installed\x64-windows\include\Eigen\src/Geometry/Quaternion.h(366,9): message : 参见“Eigen::Quaternion<double,0>”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(133,33): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_trivially_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(133,19): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_trivially_destructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::is_trivially_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::is_trivially_move_assignable<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(141,22): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_move_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(214,26): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
E:\source\dronesplanning\include\planning/planning_types.h(239,47): message : 查看对正在编译的 类 模板 实例化“std::optional<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(60,19): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_constructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(60,5): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::negation<std::conjunction<std::is_trivially_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> > > >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(97,9): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_move<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(39,19): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_constructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(39,5): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_copy_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::negation<std::conjunction<std::is_trivially_copy_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> > > >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(61,42): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_copy<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\type_traits(808,28): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_trivially_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(70,29): message : 查看指向正在编译的变量模板“const bool is_trivially_destructible_v<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(82): message : 查看对正在编译的 类 模板 实例化“std::_Optional_construct_base<_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(120): message : 查看对正在编译的 类 模板 实例化“std::_Deleted_copy_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(214): message : 查看对正在编译的 类 模板 实例化“std::_Deleted_move_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(101,29): error C2079: “std::_Optional_destruct_base<_Ty,false>::_Value”使用未定义的 struct“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(139): message : 查看对正在编译的 类 模板 实例化“std::_Optional_destruct_base<_Ty,false>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\type_traits(740,28): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(221,39): message : 查看指向正在编译的变量模板“const bool is_destructible_v<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(221,62): error C2338: static_assert failed: 'T in optional<T> must meet the Cpp17Destructible requirements (N4828 [optional.optional]/3).'
E:\source\dronesplanning\include\planning/planning_types.h(345,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(345,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(345,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(345,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(345,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(377,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(377,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(403,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(403,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(413,17): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/planning_types.h(413,17): error C2143: 语法错误: 缺少“;”(在“*”的前面)
E:\source\dronesplanning\include\planning/planning_types.h(413,36): error C2238: 意外的标记位于“;”之前
E:\source\dronesplanning\include\planning/planning_types.h(469,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(469,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(469,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(469,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(469,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(528,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(528,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\src\algorithm\path_planner\ipath_planner.cpp(13,28): error C2511: “bool NSDrones::NSAlgorithm::IPathPlanner::isPathFeasible(const std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> &) const”:“NSDrones::NSAlgorithm::IPathPlanner”中没有找到重载的成员函数
E:\source\dronesplanning\include\environment/environment.h(66,9): message : 参见“NSDrones::NSAlgorithm::IPathPlanner”的声明
  rrtstar_planner.cpp
E:\source\dronesplanning\include\planning/planning_types.h(117,36): error C2039: "fromEulerAngles": 不是 "Eigen::Quaternion<double,0>" 的成员
C:\vcpkg\installed\x64-windows\include\Eigen\src/Geometry/Quaternion.h(366,9): message : 参见“Eigen::Quaternion<double,0>”的声明
E:\source\dronesplanning\include\planning/planning_types.h(117,1): error C3861: “fromEulerAngles”: 找不到标识符
E:\source\dronesplanning\include\planning/planning_types.h(175,43): error C2039: "toEulerAngles": 不是 "Eigen::Quaternion<double,0>" 的成员
C:\vcpkg\installed\x64-windows\include\Eigen\src/Geometry/Quaternion.h(366,9): message : 参见“Eigen::Quaternion<double,0>”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(133,33): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_trivially_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(133,19): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_trivially_destructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::is_trivially_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::is_trivially_move_assignable<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(141,22): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_move_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(214,26): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
E:\source\dronesplanning\include\planning/planning_types.h(239,47): message : 查看对正在编译的 类 模板 实例化“std::optional<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(60,19): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_constructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(60,5): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::negation<std::conjunction<std::is_trivially_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> > > >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(97,9): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_move<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(39,19): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_constructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(39,5): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_copy_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::negation<std::conjunction<std::is_trivially_copy_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> > > >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(61,42): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_copy<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\type_traits(808,28): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_trivially_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(70,29): message : 查看指向正在编译的变量模板“const bool is_trivially_destructible_v<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(82): message : 查看对正在编译的 类 模板 实例化“std::_Optional_construct_base<_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(120): message : 查看对正在编译的 类 模板 实例化“std::_Deleted_copy_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(214): message : 查看对正在编译的 类 模板 实例化“std::_Deleted_move_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(101,29): error C2079: “std::_Optional_destruct_base<_Ty,false>::_Value”使用未定义的 struct“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(139): message : 查看对正在编译的 类 模板 实例化“std::_Optional_destruct_base<_Ty,false>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\type_traits(740,28): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(221,39): message : 查看指向正在编译的变量模板“const bool is_destructible_v<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(221,62): error C2338: static_assert failed: 'T in optional<T> must meet the Cpp17Destructible requirements (N4828 [optional.optional]/3).'
E:\source\dronesplanning\include\planning/planning_types.h(345,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(345,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(345,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(345,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(345,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(377,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(377,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(403,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(403,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(413,17): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/planning_types.h(413,17): error C2143: 语法错误: 缺少“;”(在“*”的前面)
E:\source\dronesplanning\include\planning/planning_types.h(413,36): error C2238: 意外的标记位于“;”之前
E:\source\dronesplanning\include\planning/planning_types.h(469,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(469,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(469,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(469,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(469,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(528,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(528,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
  trajectory_optimizer.cpp
E:\source\dronesplanning\include\planning/planning_types.h(117,36): error C2039: "fromEulerAngles": 不是 "Eigen::Quaternion<double,0>" 的成员
C:\vcpkg\installed\x64-windows\include\Eigen\src/Geometry/Quaternion.h(366,9): message : 参见“Eigen::Quaternion<double,0>”的声明
E:\source\dronesplanning\include\planning/planning_types.h(117,1): error C3861: “fromEulerAngles”: 找不到标识符
E:\source\dronesplanning\include\planning/planning_types.h(175,43): error C2039: "toEulerAngles": 不是 "Eigen::Quaternion<double,0>" 的成员
C:\vcpkg\installed\x64-windows\include\Eigen\src/Geometry/Quaternion.h(366,9): message : 参见“Eigen::Quaternion<double,0>”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(133,33): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_trivially_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(133,19): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_trivially_destructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::is_trivially_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::is_trivially_move_assignable<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(141,22): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_move_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(214,26): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
E:\source\dronesplanning\include\planning/planning_types.h(239,47): message : 查看对正在编译的 类 模板 实例化“std::optional<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(60,19): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_constructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(60,5): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::negation<std::conjunction<std::is_trivially_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> > > >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(97,9): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_move<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(39,19): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_constructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(39,5): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_copy_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::negation<std::conjunction<std::is_trivially_copy_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> > > >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(61,42): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_copy<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\type_traits(808,28): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_trivially_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(70,29): message : 查看指向正在编译的变量模板“const bool is_trivially_destructible_v<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(82): message : 查看对正在编译的 类 模板 实例化“std::_Optional_construct_base<_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(120): message : 查看对正在编译的 类 模板 实例化“std::_Deleted_copy_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(214): message : 查看对正在编译的 类 模板 实例化“std::_Deleted_move_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(101,29): error C2079: “std::_Optional_destruct_base<_Ty,false>::_Value”使用未定义的 struct“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(139): message : 查看对正在编译的 类 模板 实例化“std::_Optional_destruct_base<_Ty,false>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\type_traits(740,28): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(221,39): message : 查看指向正在编译的变量模板“const bool is_destructible_v<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(221,62): error C2338: static_assert failed: 'T in optional<T> must meet the Cpp17Destructible requirements (N4828 [optional.optional]/3).'
E:\source\dronesplanning\include\planning/planning_types.h(270,20): error C2079: “NSDrones::NSPlanning::SubTaskAssignment::start_state”使用未定义的 struct“NSDrones::NSUav::UavState”
E:\source\dronesplanning\include\planning/planning_types.h(345,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(345,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(345,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(345,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(345,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(377,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(377,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(403,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(403,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(413,17): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/planning_types.h(413,17): error C2143: 语法错误: 缺少“;”(在“*”的前面)
E:\source\dronesplanning\include\planning/planning_types.h(413,36): error C2238: 意外的标记位于“;”之前
E:\source\dronesplanning\include\planning/planning_types.h(469,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(469,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(469,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(469,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(469,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(528,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(528,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(879,4): error C2065: “iter”: 未声明的标识符
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(952,20): error C2039: "allFinite": 不是 "NSDrones::NSCore::EcefPoint" 的成员
E:\source\dronesplanning\include\core/types.h(387,9): message : 参见“NSDrones::NSCore::EcefPoint”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(969,34): error C2039: "fromEulerAngles": 不是 "Eigen::Quaternion<double,0>" 的成员
C:\vcpkg\installed\x64-windows\include\Eigen\src/Geometry/Quaternion.h(366,9): message : 参见“Eigen::Quaternion<double,0>”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(969,49): error C3861: “fromEulerAngles”: 找不到标识符
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1202,15): error C2039: "start": 不是 "NSDrones::NSUtils::Stopwatch" 的成员
E:\source\dronesplanning\include\utils/stopwatch.h(13,9): message : 参见“NSDrones::NSUtils::Stopwatch”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1210,26): error C2039: "reserve": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(199,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1214,23): error C2039: "size": 不是 "NSDrones::NSPlanning::TrajectoryPoint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(37,10): message : 参见“NSDrones::NSPlanning::TrajectoryPoint”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1216,28): error C2039: "push_back": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(199,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1221,44): error C2664: “NSDrones::NSPlanning::TrajectorySegment NSDrones::NSAlgorithm::TrajectoryOptimizer::optimizeTrajectorySegmentInternal(const NSDrones::NSPlanning::TrajectorySegment &,const NSDrones::NSUav::IDynamicModel &,const NSDrones::NSMission::ITaskStrategyMap &) const”: 无法将参数 1 从“const NSDrones::NSPlanning::TrajectoryPoint”转换为“const NSDrones::NSPlanning::TrajectorySegment &”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1222,7): message : 原因如下: 无法从“const NSDrones::NSPlanning::TrajectoryPoint”转换为“const NSDrones::NSPlanning::TrajectorySegment”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1222,7): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(675,42): message : 参见“NSDrones::NSAlgorithm::TrajectoryOptimizer::optimizeTrajectorySegmentInternal”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1221,44): message : 尝试匹配参数列表“(const NSDrones::NSPlanning::TrajectoryPoint, const NSDrones::NSUav::IDynamicModel, NSDrones::NSMission::ITaskStrategyMap)”时
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1226,28): error C2039: "push_back": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(199,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1228,28): error C2039: "push_back": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(199,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1232,15): error C2039: "stop": 不是 "NSDrones::NSUtils::Stopwatch" 的成员
E:\source\dronesplanning\include\utils/stopwatch.h(13,9): message : 参见“NSDrones::NSUtils::Stopwatch”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1235,30): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(199,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1240,12): error C2679: 二元“=”: 没有找到接受“NSDrones::NSPlanning::Trajectory”类型的右操作数的运算符(或没有可接受的转换)
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(1566,26): message : 可能是“std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>> &std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>>::operator =(std::initializer_list<_Ty>)”
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryPoint
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(1545,26): message : 或    “std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>> &std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>>::operator =(const std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>> &)”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(798,26): message : 或    “std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>> &std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>>::operator =(std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>> &&) noexcept(<expr>)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1240,12): message : 尝试匹配参数列表“(NSDrones::NSPlanning::TrajectorySegment, NSDrones::NSPlanning::Trajectory)”时
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1263,19): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(199,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): error C2672: “begin”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(609,23): message : 可能是“const _Ty *std::begin(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : “const _Ty *std::begin(const std::valarray<_Ty> &)”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(604,17): message : 或    “_Ty *std::begin(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : “_Ty *std::begin(std::valarray<_Ty> &)”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1589,27): message : 或    “_Ty *std::begin(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : “_Ty *std::begin(_Ty (&)[_Size]) noexcept”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1571,30): message : 或    “unknown-type std::begin(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : 未能使函数模板“unknown-type std::begin(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : 用下列模板参数:
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : “_Container=NSDrones::NSPlanning::Trajectory”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1565,30): message : 或    “unknown-type std::begin(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : 未能使函数模板“unknown-type std::begin(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : 用下列模板参数:
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : “_Container=const NSDrones::NSPlanning::Trajectory”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(55,35): message : 或    “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): error C2672: “end”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(619,23): message : 可能是“const _Ty *std::end(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : “const _Ty *std::end(const std::valarray<_Ty> &)”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(614,17): message : 或    “_Ty *std::end(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : “_Ty *std::end(std::valarray<_Ty> &)”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1594,27): message : 或    “_Ty *std::end(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : “_Ty *std::end(_Ty (&)[_Size]) noexcept”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1583,30): message : 或    “unknown-type std::end(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : 未能使函数模板“unknown-type std::end(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : 用下列模板参数:
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : “_Container=NSDrones::NSPlanning::Trajectory”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1577,30): message : 或    “unknown-type std::end(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : 未能使函数模板“unknown-type std::end(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : 用下列模板参数:
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : “_Container=const NSDrones::NSPlanning::Trajectory”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(60,35): message : 或    “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,37): message : “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,49): error C3536: “<begin>$L0”: 初始化之前无法使用
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,49): error C3536: “<end>$L0”: 初始化之前无法使用
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1271,47): error C2100: 非法的间接寻址
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1272,11): error C2664: “bool NSDrones::NSAlgorithm::TrajectoryOptimizer::isTrajectorySegmentFeasible(const NSDrones::NSPlanning::TrajectorySegment &,const NSDrones::NSUav::IDynamicModel &,const NSDrones::NSMission::ITaskStrategyMap &) const”: 无法将参数 1 从“const int”转换为“const NSDrones::NSPlanning::TrajectorySegment &”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1272,39): message : 原因如下: 无法从“const int”转换为“const NSDrones::NSPlanning::TrajectorySegment”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1272,39): message : class“std::vector<NSDrones::NSPlanning::TrajectoryPoint,std::allocator<NSDrones::NSPlanning::TrajectoryPoint>>”的构造函数声明为“explicit”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1037,29): message : 参见“NSDrones::NSAlgorithm::TrajectoryOptimizer::isTrajectorySegmentFeasible”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1272,11): message : 尝试匹配参数列表“(const int, const NSDrones::NSUav::IDynamicModel, NSDrones::NSMission::ITaskStrategyMap)”时
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1287,29): error C2084: 函数“void NSDrones::NSAlgorithm::TrajectoryOptimizer::setParameters(const std::map<std::string,double,std::less<std::string>,std::allocator<std::pair<const std::string,double>>> &)”已有主体
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1111,29): message : 参见“setParameters”的前一个定义
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1406,29): error C2039: "checkCollision": 不是 "NSDrones::NSAlgorithm::TrajectoryOptimizer" 的成员
E:\source\dronesplanning\include\algorithm/trajectory_optimizer/trajectory_optimizer.h(32,9): message : 参见“NSDrones::NSAlgorithm::TrajectoryOptimizer”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1406,80): error C2270: “checkCollision”: 非成员函数上不允许修饰符
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1409,19): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(199,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): error C2672: “begin”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(609,23): message : 可能是“const _Ty *std::begin(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : “const _Ty *std::begin(const std::valarray<_Ty> &)”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(604,17): message : 或    “_Ty *std::begin(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : “_Ty *std::begin(std::valarray<_Ty> &)”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1589,27): message : 或    “_Ty *std::begin(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : “_Ty *std::begin(_Ty (&)[_Size]) noexcept”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1571,30): message : 或    “unknown-type std::begin(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : 未能使函数模板“unknown-type std::begin(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : 用下列模板参数:
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : “_Container=NSDrones::NSPlanning::Trajectory”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1565,30): message : 或    “unknown-type std::begin(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : 未能使函数模板“unknown-type std::begin(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : 用下列模板参数:
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : “_Container=const NSDrones::NSPlanning::Trajectory”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(55,35): message : 或    “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): error C2672: “end”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(619,23): message : 可能是“const _Ty *std::end(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : “const _Ty *std::end(const std::valarray<_Ty> &)”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(614,17): message : 或    “_Ty *std::end(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : “_Ty *std::end(std::valarray<_Ty> &)”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1594,27): message : 或    “_Ty *std::end(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : “_Ty *std::end(_Ty (&)[_Size]) noexcept”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1583,30): message : 或    “unknown-type std::end(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : 未能使函数模板“unknown-type std::end(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : 用下列模板参数:
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : “_Container=NSDrones::NSPlanning::Trajectory”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1577,30): message : 或    “unknown-type std::end(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : 未能使函数模板“unknown-type std::end(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : 用下列模板参数:
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : “_Container=const NSDrones::NSPlanning::Trajectory”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(60,35): message : 或    “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,37): message : “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”: 无法从“const NSDrones::NSPlanning::Trajectory”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,49): error C3536: “<begin>$L0”: 初始化之前无法使用
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,49): error C3536: “<end>$L0”: 初始化之前无法使用
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1416,47): error C2100: 非法的间接寻址
E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp(1417,11): error C3861: “checkTrajectorySegmentCollision”: 找不到标识符
  config.cpp
E:\source\dronesplanning\include\planning/planning_types.h(117,36): error C2039: "fromEulerAngles": 不是 "Eigen::Quaternion<double,0>" 的成员
C:\vcpkg\installed\x64-windows\include\Eigen\src/Geometry/Quaternion.h(366,9): message : 参见“Eigen::Quaternion<double,0>”的声明
E:\source\dronesplanning\include\planning/planning_types.h(117,1): error C3861: “fromEulerAngles”: 找不到标识符
E:\source\dronesplanning\include\planning/planning_types.h(175,43): error C2039: "toEulerAngles": 不是 "Eigen::Quaternion<double,0>" 的成员
C:\vcpkg\installed\x64-windows\include\Eigen\src/Geometry/Quaternion.h(366,9): message : 参见“Eigen::Quaternion<double,0>”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(133,33): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_trivially_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(133,19): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_trivially_destructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::is_trivially_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::is_trivially_move_assignable<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(141,22): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_move_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(214,26): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
E:\source\dronesplanning\include\planning/planning_types.h(239,47): message : 查看对正在编译的 类 模板 实例化“std::optional<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(60,19): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_constructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(60,5): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::negation<std::conjunction<std::is_trivially_move_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> > > >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(97,9): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_move<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(39,19): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_constructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(39,5): message : 查看指向正在编译的变量模板“const bool conjunction_v<std::is_copy_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>,std::negation<std::conjunction<std::is_trivially_copy_constructible<NSDrones::NSPlanning::TrajectoryEvaluationMetrics> > > >”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(61,42): message : 查看对正在编译的 别名 模板 实例化“std::_SMF_control_copy<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\type_traits(808,28): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_trivially_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(70,29): message : 查看指向正在编译的变量模板“const bool is_trivially_destructible_v<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(82): message : 查看对正在编译的 类 模板 实例化“std::_Optional_construct_base<_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xsmf_control.h(120): message : 查看对正在编译的 类 模板 实例化“std::_Deleted_copy_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(214): message : 查看对正在编译的 类 模板 实例化“std::_Deleted_move_assign<std::_Optional_construct_base<_Ty>,_Ty>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(101,29): error C2079: “std::_Optional_destruct_base<_Ty,false>::_Value”使用未定义的 struct“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(139): message : 查看对正在编译的 类 模板 实例化“std::_Optional_destruct_base<_Ty,false>”的引用
          with
          [
              _Ty=NSDrones::NSPlanning::TrajectoryEvaluationMetrics
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\type_traits(740,28): error C2139: “NSDrones::NSPlanning::TrajectoryEvaluationMetrics”: 未定义的类不允许作为编译器内部类型特征“__is_destructible”的参数
E:\source\dronesplanning\include\planning/planning_types.h(23,10): message : 参见“NSDrones::NSPlanning::TrajectoryEvaluationMetrics”的声明
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(221,39): message : 查看指向正在编译的变量模板“const bool is_destructible_v<NSDrones::NSPlanning::TrajectoryEvaluationMetrics>”的引用
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\optional(221,62): error C2338: static_assert failed: 'T in optional<T> must meet the Cpp17Destructible requirements (N4828 [optional.optional]/3).'
E:\source\dronesplanning\include\planning/planning_types.h(345,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(345,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(345,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(345,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(345,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(377,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(377,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(377,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(403,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(403,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(403,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(413,17): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/planning_types.h(413,17): error C2143: 语法错误: 缺少“;”(在“*”的前面)
E:\source\dronesplanning\include\planning/planning_types.h(413,36): error C2238: 意外的标记位于“;”之前
E:\source\dronesplanning\include\planning/planning_types.h(469,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(469,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(469,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(469,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(469,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,16): error C2065: “WarningEvent”: 未声明的标识符
E:\source\dronesplanning\include\planning/planning_types.h(528,9): error C2923: "std::vector": "WarningEvent" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/planning_types.h(528,16): message : 参见“WarningEvent”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,9): error C2976: “std::vector'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\planning/planning_types.h(528,30): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(494,14): message : 参见“std::vector”的声明
E:\source\dronesplanning\include\algorithm/allocator/task_allocator.h(58,25): error C3646: “allocate”: 未知重写说明符
E:\source\dronesplanning\include\algorithm/allocator/task_allocator.h(58,33): error C2059: 语法错误:“(”
E:\source\dronesplanning\include\algorithm/allocator/task_allocator.h(61,72): error C2238: 意外的标记位于“;”之前
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(51,19): error C3646: “evaluate”: 未知重写说明符
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(51,27): error C2059: 语法错误:“(”
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(55,20): error C2238: 意外的标记位于“;”之前
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(58,22): error C2065: “TrajectoryCost”: 未声明的标识符
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(58,9): error C2923: "std::map": "TrajectoryCost" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(58,22): message : 参见“TrajectoryCost”的声明
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(58,9): error C2976: “std::map'”: 模板 参数太少
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\map(73,11): message : 参见“std::map”的声明
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(60,19): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(60,19): error C2143: 语法错误: 缺少“,”(在“&”的前面)
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(62,20): error C2955: “std::map”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\map(73,11): message : 参见“std::map”的声明
E:\source\dronesplanning\include\algorithm/evaluator/energy_evaluator.h(58,38): error C3668: “NSDrones::NSAlgorithm::EnergyEvaluator::evaluate”: 包含重写说明符“override”的方法没有重写任何基类方法
E:\source\dronesplanning\include\planning/task_planners/followpath_taskplanner.h(29,9): error C3668: “NSDrones::NSPlanning::FollowPathTaskPlanner::isTaskTypeSupported”: 包含重写说明符“override”的方法没有重写任何基类方法
E:\source\dronesplanning\include\planning/task_planners/loiterpoint_taskplanner.h(127,36): error C2535: “std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>> NSDrones::NSPlanning::LoiterPointTaskPlanner::generateLoiterWaypoints(const NSDrones::NSCore::WGS84Point &,double,int,bool) const”: 已经定义或声明成员函数
E:\source\dronesplanning\include\planning/task_planners/loiterpoint_taskplanner.h(113,36): message : 参见“NSDrones::NSPlanning::LoiterPointTaskPlanner::generateLoiterWaypoints”的声明
E:\source\dronesplanning\include\planning/task_planners/loiterpoint_taskplanner.h(56,9): error C3668: “NSDrones::NSPlanning::LoiterPointTaskPlanner::isTaskTypeSupported”: 包含重写说明符“override”的方法没有重写任何基类方法
E:\source\dronesplanning\include\planning/task_planners/scanarea_taskplanner.h(54,9): error C3668: “NSDrones::NSPlanning::ScanAreaTaskPlanner::isTaskTypeSupported”: 包含重写说明符“override”的方法没有重写任何基类方法
E:\source\dronesplanning\include\planning/task_planners/surveymultipoints_taskplanner.h(49,9): error C3668: “NSDrones::NSPlanning::SurveyMultiPointsTaskPlanner::isTaskTypeSupported”: 包含重写说明符“override”的方法没有重写任何基类方法
E:\source\dronesplanning\include\planning/task_planners/surveysphere_taskplanner.h(26,9): error C3668: “NSDrones::NSPlanning::SurveySphereTaskPlanner::isTaskTypeSupported”: 包含重写说明符“override”的方法没有重写任何基类方法
E:\source\dronesplanning\include\planning/task_planners/surveycylinder_taskplanner.h(26,9): error C3668: “NSDrones::NSPlanning::SurveyCylinderTaskPlanner::isTaskTypeSupported”: 包含重写说明符“override”的方法没有重写任何基类方法
  base_object.cpp
  entity_object.cpp
  entity_state.cpp
  ishape.cpp
  shape_factory.cpp
  box_shape.cpp
  capsule_shape.cpp
  compound_shape.cpp
