#include "core/geometry/shapes/compound_shape.h"
#include "utils/enum_utils.h"
#include <stdexcept>
#include <sstream>
#include <algorithm>

namespace NSDrones{
namespace NSCore {

    CompoundShape::CompoundShape() : aabb_dirty_(true) {
    }

    CompoundShape::CompoundShape(const CompoundShape& other) : aabb_dirty_(true) {
        components_.reserve(other.components_.size());
        for (const auto& component : other.components_) {
            components_.emplace_back(component);
        }
    }

    CompoundShape& CompoundShape::operator=(const CompoundShape& other) {
        if (this != &other) {
            components_.clear();
            components_.reserve(other.components_.size());
            for (const auto& component : other.components_) {
                components_.emplace_back(component);
            }
            markAABBDirty();
        }
        return *this;
    }

    std::shared_ptr<fcl::CollisionGeometryd> CompoundShape::getFCLGeometry() const {
        // 复合形状不能直接转换为单个FCL几何对象
        // 需要在碰撞检测时分别处理每个子形状
        throw std::runtime_error("CompoundShape: 复合形状不支持直接获取FCL几何对象，请使用子形状进行碰撞检测");
    }

    fcl::AABBd CompoundShape::getAABB(const fcl::Transform3d& transform) const {
        if (components_.empty()) {
            return fcl::AABBd();
        }

        if (aabb_dirty_) {
            updateCachedAABB();
        }

        // 应用变换到缓存的AABB
        fcl::AABBd transformed_aabb;
        fcl::Vector3d min_pt = transform * fcl::Vector3d(cached_aabb_.min_);
        fcl::Vector3d max_pt = transform * fcl::Vector3d(cached_aabb_.max_);
        
        transformed_aabb.min_ = min_pt.cwiseMin(max_pt);
        transformed_aabb.max_ = min_pt.cwiseMax(max_pt);
        
        return transformed_aabb;
    }

    double CompoundShape::getVolume() const {
        double total_volume = 0.0;
        for (const auto& component : components_) {
            total_volume += component.shape->getVolume();
        }
        return total_volume;
    }

    double CompoundShape::getSurfaceArea() const {
        // 注意：这是一个近似值，因为子形状可能重叠
        double total_area = 0.0;
        for (const auto& component : components_) {
            total_area += component.shape->getSurfaceArea();
        }
        return total_area;
    }

    fcl::Vector3d CompoundShape::getCentroid() const {
        if (components_.empty()) {
            return fcl::Vector3d::Zero();
        }

        fcl::Vector3d weighted_centroid = fcl::Vector3d::Zero();
        double total_volume = 0.0;

        for (const auto& component : components_) {
            double volume = component.shape->getVolume();
            fcl::Vector3d local_centroid = component.shape->getCentroid();
            fcl::Vector3d global_centroid = component.transform * local_centroid;
            
            weighted_centroid += volume * global_centroid;
            total_volume += volume;
        }

        if (total_volume > 1e-10) {
            return weighted_centroid / total_volume;
        }
        return fcl::Vector3d::Zero();
    }

    fcl::Matrix3d CompoundShape::getInertiaMatrix(double mass) const {
        if (components_.empty()) {
            return fcl::Matrix3d::Zero();
        }

        fcl::Matrix3d total_inertia = fcl::Matrix3d::Zero();
        double total_volume = getVolume();
        fcl::Vector3d compound_centroid = getCentroid();

        for (const auto& component : components_) {
            double component_volume = component.shape->getVolume();
            double component_mass = mass * (component_volume / total_volume);
            
            // 获取子形状相对于自身质心的惯性张量
            fcl::Matrix3d local_inertia = component.shape->getInertiaMatrix(component_mass);
            
            // 计算子形状质心在复合形状坐标系中的位置
            fcl::Vector3d local_centroid = component.shape->getCentroid();
            fcl::Vector3d global_centroid = component.transform * local_centroid;
            fcl::Vector3d offset = global_centroid - compound_centroid;
            
            // 使用平行轴定理转换惯性张量
            fcl::Matrix3d offset_inertia = fcl::Matrix3d::Zero();
            double offset_sq = offset.squaredNorm();
            offset_inertia.diagonal().setConstant(component_mass * offset_sq);
            offset_inertia -= component_mass * (offset * offset.transpose());
            
            // 旋转局部惯性张量到全局坐标系
            fcl::Matrix3d rotation = component.transform.rotation();
            fcl::Matrix3d rotated_inertia = rotation * local_inertia * rotation.transpose();
            
            total_inertia += rotated_inertia + offset_inertia;
        }

        return total_inertia;
    }

    std::unique_ptr<IShape> CompoundShape::clone() const {
        return std::make_unique<CompoundShape>(*this);
    }

    nlohmann::json CompoundShape::serialize() const {
        nlohmann::json j;
        j["type"] = NSUtils::enumToString(getType());
        
        nlohmann::json components_json = nlohmann::json::array();
        for (const auto& component : components_) {
            nlohmann::json comp_json;
            comp_json["name"] = component.name;
            comp_json["shape"] = component.shape->serialize();
            
            // 序列化变换矩阵
            const auto& t = component.transform.translation();
            const auto& r = component.transform.rotation();
            comp_json["transform"]["translation"] = {t.x(), t.y(), t.z()};
            comp_json["transform"]["rotation"] = {
                {r(0,0), r(0,1), r(0,2)},
                {r(1,0), r(1,1), r(1,2)},
                {r(2,0), r(2,1), r(2,2)}
            };
            
            components_json.push_back(comp_json);
        }
        j["components"] = components_json;
        
        return j;
    }

    bool CompoundShape::deserialize(const nlohmann::json& json) {
        try {
            std::string type_str = json.at("type").get<std::string>();
            auto type_opt = NSUtils::stringToEnum<ShapeType>(type_str, ShapeType::UNKNOWN);
            if (type_opt != ShapeType::COMPOUND) {
                return false;
            }
            
            clear();
            
            if (json.contains("components") && json["components"].is_array()) {
                for (const auto& comp_json : json["components"]) {
                    // 反序列化子形状
                    auto shape = IShape::createFromJson(comp_json["shape"]);
                    if (!shape) {
                        continue;
                    }
                    
                    // 反序列化变换
                    fcl::Transform3d transform = fcl::Transform3d::Identity();
                    if (comp_json.contains("transform")) {
                        const auto& t_json = comp_json["transform"]["translation"];
                        transform.translation() = fcl::Vector3d(
                            t_json[0].get<double>(),
                            t_json[1].get<double>(),
                            t_json[2].get<double>()
                        );
                        
                        const auto& r_json = comp_json["transform"]["rotation"];
                        fcl::Matrix3d rotation;
                        for (int i = 0; i < 3; ++i) {
                            for (int j = 0; j < 3; ++j) {
                                rotation(i, j) = r_json[i][j].get<double>();
                            }
                        }
                        transform.linear() = rotation;
                    }
                    
                    std::string name = comp_json.value("name", "");
                    addComponent(std::move(shape), transform, name);
                }
            }
            
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    std::string CompoundShape::toString() const {
        std::ostringstream oss;
        oss << "CompoundShape(components=" << components_.size() << ")";
        return oss.str();
    }

    bool CompoundShape::containsPoint(const fcl::Vector3d& point) const {
        for (const auto& component : components_) {
            fcl::Vector3d local_point = component.transform.inverse() * point;
            if (component.shape->containsPoint(local_point)) {
                return true;
            }
        }
        return false;
    }

    double CompoundShape::distanceToPoint(const fcl::Vector3d& point) const {
        if (components_.empty()) {
            return std::numeric_limits<double>::infinity();
        }

        double min_distance = std::numeric_limits<double>::infinity();
        for (const auto& component : components_) {
            fcl::Vector3d local_point = component.transform.inverse() * point;
            double distance = component.shape->distanceToPoint(local_point);
            min_distance = std::min(min_distance, distance);
        }
        
        return min_distance;
    }

    double CompoundShape::getCharacteristicSize() const {
        if (components_.empty()) {
            return 0.0;
        }

        fcl::AABBd aabb = getAABB();
        fcl::Vector3d size = aabb.max_ - aabb.min_;
        return size.norm();
    }

    void CompoundShape::addComponent(std::unique_ptr<IShape> shape, 
                                   const fcl::Transform3d& transform,
                                   const std::string& name) {
        if (!shape) {
            throw std::invalid_argument("CompoundShape: 不能添加空的形状");
        }
        
        components_.emplace_back(std::move(shape), transform, name);
        markAABBDirty();
    }

    void CompoundShape::removeComponent(size_t index) {
        validateIndex(index);
        components_.erase(components_.begin() + index);
        markAABBDirty();
    }

    void CompoundShape::removeComponent(const std::string& name) {
        auto it = std::find_if(components_.begin(), components_.end(),
            [&name](const ShapeComponent& comp) { return comp.name == name; });
        
        if (it != components_.end()) {
            components_.erase(it);
            markAABBDirty();
        }
    }

    void CompoundShape::clear() {
        components_.clear();
        markAABBDirty();
    }

    const ShapeComponent& CompoundShape::getComponent(size_t index) const {
        validateIndex(index);
        return components_[index];
    }

    const ShapeComponent* CompoundShape::getComponent(const std::string& name) const {
        auto it = std::find_if(components_.begin(), components_.end(),
            [&name](const ShapeComponent& comp) { return comp.name == name; });
        
        return (it != components_.end()) ? &(*it) : nullptr;
    }

    void CompoundShape::updateComponentTransform(size_t index, const fcl::Transform3d& transform) {
        validateIndex(index);
        components_[index].transform = transform;
        markAABBDirty();
    }

    void CompoundShape::updateComponentTransform(const std::string& name, const fcl::Transform3d& transform) {
        auto it = std::find_if(components_.begin(), components_.end(),
            [&name](const ShapeComponent& comp) { return comp.name == name; });
        
        if (it != components_.end()) {
            it->transform = transform;
            markAABBDirty();
        }
    }

    std::vector<fcl::AABBd> CompoundShape::getComponentAABBs(const fcl::Transform3d& parent_transform) const {
        std::vector<fcl::AABBd> aabbs;
        aabbs.reserve(components_.size());
        
        for (const auto& component : components_) {
            fcl::Transform3d combined_transform = parent_transform * component.transform;
            aabbs.push_back(component.shape->getAABB(combined_transform));
        }
        
        return aabbs;
    }

    void CompoundShape::optimize() {
        // 简单的优化：移除空的或无效的组件
        components_.erase(
            std::remove_if(components_.begin(), components_.end(),
                [](const ShapeComponent& comp) {
                    return !comp.shape || comp.shape->getVolume() <= 0.0;
                }),
            components_.end()
        );
        markAABBDirty();
    }

    std::unique_ptr<CompoundShape> CompoundShape::createSimple(
        std::unique_ptr<IShape> shape1, const fcl::Transform3d& transform1,
        std::unique_ptr<IShape> shape2, const fcl::Transform3d& transform2) {
        
        auto compound = std::make_unique<CompoundShape>();
        compound->addComponent(std::move(shape1), transform1, "component1");
        compound->addComponent(std::move(shape2), transform2, "component2");
        return compound;
    }

    void CompoundShape::updateCachedAABB() const {
        if (components_.empty()) {
            cached_aabb_ = fcl::AABBd();
            aabb_dirty_ = false;
            return;
        }

        // 计算第一个组件的AABB
        auto aabb = components_[0].shape->getAABB(components_[0].transform);
        cached_aabb_ = aabb;

        // 合并其他组件的AABB
        for (size_t i = 1; i < components_.size(); ++i) {
            aabb = components_[i].shape->getAABB(components_[i].transform);
            cached_aabb_ += aabb;
        }

        aabb_dirty_ = false;
    }

    void CompoundShape::validateIndex(size_t index) const {
        if (index >= components_.size()) {
            throw std::out_of_range("CompoundShape: 组件索引超出范围");
        }
    }

} // namespace NSCore
} // namespace NSDrones