{"description": "任务空间基类定义", "parameters": [{"key": "coverage_min_bound", "name": "覆盖空间最小边界", "description": "TaskSpace的三维覆盖空间最小边界点，格式为[经度,纬度,高度]", "type": "wgs84point", "default": [118.77, 31.99, 0.0]}, {"key": "coverage_max_bound", "name": "覆盖空间最大边界", "description": "TaskSpace的三维覆盖空间最大边界点，格式为[经度,纬度,高度]", "type": "wgs84point", "default": [118.83, 32.01, 500.0]}, {"key": "bvh_max_objects_per_node", "name": "BVH每节点最大对象数", "description": "BVH索引每个节点最大对象数", "type": "int", "default": 16}, {"key": "bvh_max_depth", "name": "BVH最大深度", "description": "BVH索引最大深度", "type": "int", "default": 32}, {"key": "bvh_enabled", "name": "BVH索引启用", "description": "是否启用BVH索引", "type": "bool", "default": true}]}