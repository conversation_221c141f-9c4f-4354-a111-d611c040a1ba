Performing C++ SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: E:/source/dronesplanning/build/CMakeFiles/CMakeScratch/TryCompile-7d7k5t

Run Build Command(s):C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_5dd1b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:m && MSBuild version 17.5.1+f6fdcf537 for .NET Framework



  用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.35.32216.1 版

  src.cxx

  版权所有(C) Microsoft Corporation。保留所有权利。

  cl /c /I"C:\vcpkg\installed\x64-windows\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\"Debug\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_5dd1b.dir\Debug\\" /Fd"cmTC_5dd1b.dir\Debug\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "E:\source\dronesplanning\build\CMakeFiles\CMakeScratch\TryCompile-7d7k5t\src.cxx"

E:\source\dronesplanning\build\CMakeFiles\CMakeScratch\TryCompile-7d7k5t\src.cxx(1,10): fatal  error C1083: 无法打开包括文件: “pthread.h”: No such file or directory [E:\source\dronesplanning\build\CMakeFiles\CMakeScratch\TryCompile-7d7k5t\cmTC_5dd1b.vcxproj]



Source file was:
#include <pthread.h>

static void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_cancel(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}


Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: E:/source/dronesplanning/build/CMakeFiles/CMakeScratch/TryCompile-p6p0cg

Run Build Command(s):C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_7f50b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:m && MSBuild version 17.5.1+f6fdcf537 for .NET Framework



  用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.35.32216.1 版

  版权所有(C) Microsoft Corporation。保留所有权利。

  CheckFunctionExists.cxx

  cl /c /I"C:\vcpkg\installed\x64-windows\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\"Debug\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_7f50b.dir\Debug\\" /Fd"cmTC_7f50b.dir\Debug\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "E:\source\dronesplanning\build\CMakeFiles\CMakeScratch\TryCompile-p6p0cg\CheckFunctionExists.cxx"

LINK : fatal error LNK1104: 无法打开文件“pthreads.lib” [E:\source\dronesplanning\build\CMakeFiles\CMakeScratch\TryCompile-p6p0cg\cmTC_7f50b.vcxproj]




Determining if the function pthread_create exists in the pthread failed with the following output:
Change Dir: E:/source/dronesplanning/build/CMakeFiles/CMakeScratch/TryCompile-1o1ut8

Run Build Command(s):C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_1d535.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:m && MSBuild version 17.5.1+f6fdcf537 for .NET Framework



  用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.35.32216.1 版

  版权所有(C) Microsoft Corporation。保留所有权利。

  CheckFunctionExists.cxx

  cl /c /I"C:\vcpkg\installed\x64-windows\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\"Debug\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_1d535.dir\Debug\\" /Fd"cmTC_1d535.dir\Debug\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "E:\source\dronesplanning\build\CMakeFiles\CMakeScratch\TryCompile-1o1ut8\CheckFunctionExists.cxx"

LINK : fatal error LNK1104: 无法打开文件“pthread.lib” [E:\source\dronesplanning\build\CMakeFiles\CMakeScratch\TryCompile-1o1ut8\cmTC_1d535.vcxproj]




