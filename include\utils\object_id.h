// include/utils/object_id.h
#pragma once

#include <string>
#include <optional>

namespace NSDrones {
	namespace NSUtils { 

		using ObjectID = std::string;
		const ObjectID INVALID_OBJECT_ID = ""; 
		/**
		 * @brief 生成一个新的、唯一的对象 ID (基于 UUID v4)。
		 * @return 新的对象 ID 字符串。如果生成失败，返回 INVALID_OBJECT_ID ("")。
		 */
		ObjectID generateObjectID(); // 只保留声明

		/**
		 * @brief 检查给定的 ID 是否有效。
		 *        有效的 ID 是非空的字符串。
		 * @param id 要检查的对象 ID。
		 * @return 如果 ID 不是空字符串，返回 true。
		 */
		inline bool isValidObjectID(const ObjectID& id) {
			return !id.empty();
		}

		// 辅助函数，用于日志记录时优雅地处理 ObjectID 和 std::optional<ObjectID>
		inline std::string toString(const std::optional<ObjectID>& id_opt) {
			if (id_opt && isValidObjectID(*id_opt)) {
				return *id_opt;
			}
			return "None";
		}
		inline std::string toString(const ObjectID& id) {
			if (isValidObjectID(id)) {
				return id;
			}
			return "None";
		}

	} // namespace NSUtils
} // namespace NSDrones

using namespace NSDrones::NSUtils;