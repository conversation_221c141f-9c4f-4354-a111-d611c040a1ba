// include/uav/idynamic_model.h
#pragma once

#include "core/types.h"      
#include "uav/uav_types.h"    
#include <memory>               
#include <limits>              
#include <cmath>                
#include <algorithm>           

namespace NSDrones { namespace NSUav { class Uav; } }

namespace NSDrones {
	namespace NSUav {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class IDynamicModel
		 * @brief 无人机动力学约束和行为的抽象基类接口。
		 *
		 * 定义特定类型无人机的速度、加速度、转弯率等限制，以及力的计算。
		 * 规划器使用这些信息来生成可行的轨迹。
		 * 模型实例由 UAV 对象持有，并通过构造时传入的 UAV 引用访问参数。
		 */
		class IDynamicModel {
		public:
			/**
			 * @brief 构造函数。
			 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
			 *             用于访问参数和当前状态（如果需要）。
			 */
			explicit IDynamicModel(const Uav& owner); 

			/** @brief 虚析构函数。*/
			virtual ~IDynamicModel() = default;

			// --- 禁止拷贝和移动 ---
			IDynamicModel(const IDynamicModel&) = delete;
			IDynamicModel& operator=(const IDynamicModel&) = delete;
			IDynamicModel(IDynamicModel&&) = delete;
			IDynamicModel& operator=(IDynamicModel&&) = delete;

			/** @brief 获取此模型代表的无人机类型 (纯虚函数)。*/
			virtual UavType getType() const = 0;

			// --- 核心约束接口 ---
			/** @brief 获取最大水平飞行速度 (m/s)。*/
			virtual double getMaxHorizontalSpeed(const UavState& state) const;
			/** @brief 获取最大爬升速度 (m/s, 正值向上)。*/
			virtual double getMaxClimbSpeed(const UavState& state) const;
			/** @brief 获取最大下降速度 (m/s, 正值向下)。*/
			virtual double getMaxDescendSpeed(const UavState& state) const;
			/** @brief 获取最大水平加速度 (m/s^2)。*/
			virtual double getMaxHorizontalAcceleration(const UavState& state) const;
			/** @brief 获取最大垂直加速度 (m/s^2, 向上)。*/
			virtual double getMaxVerticalAcceleration(const UavState& state) const;
			/** @brief 获取最大水平减速度 (m/s^2, 绝对值)。*/
			virtual double getMaxHorizontalDeceleration(const UavState& state) const;
			/** @brief 获取最大垂直减速度 (m/s^2, 绝对值，用于停止运动)。*/
			virtual double getMaxVerticalDeceleration(const UavState& state) const;
			/** @brief 获取最大运行高度 (米，相对于参考平面)。*/
			virtual double getMaxAltitude(const UavState& state) const;
			/** @brief 获取最小运行速度 (m/s)，例如失速速度。*/
			virtual double getMinOperationalSpeed(const UavState& state) const;

			// --- 以下方法通常依赖于具体模型，设为纯虚函数 ---
			/** @brief 获取最大转弯率 (弧度/秒)。子类必须实现。*/
			virtual double getMaxTurnRate(const UavState& state) const = 0;
			/** @brief 获取给定状态下的最小转弯半径 (米)。子类必须实现。*/
			virtual double getMinTurnRadius(const UavState& state) const = 0;
			/** @brief 获取最大允许的倾斜角 (或姿态角，弧度)。子类必须实现。*/
			virtual double getMaxBankAngle(const UavState& state) const = 0;

			/**
			 * @brief (纯虚函数) 检查两个状态之间的转换在给定的时间步长内是否动态可行。
			 *        这是规划器检查轨迹段可行性的核心方法。
			 * @param current 当前状态。
			 * @param next 下一个期望状态。
			 * @param dt 时间步长 (秒)。
			 * @return 如果转换可行，返回 true。
			 */
			virtual bool isStateTransitionFeasible(const UavState& current, const UavState& next, Time dt) const = 0;

			// --- 辅助估算方法 ---
			/**
			 * @brief 估算在两个速度值之间改变所需的时间 (秒)。
			 *        使用水平加速/减速限制进行估算。
			 * @param state 当前状态 (用于获取限制)。
			 * @param currentSpeed 当前速度大小。
			 * @param targetSpeed 目标速度大小。
			 * @return 估算的时间 (秒)，如果无法加速/减速则返回无穷大。
			 */
			virtual Time estimateSpeedChangeTime(const UavState& state, double currentSpeed, double targetSpeed) const;
			/**
			 * @brief 估算从当前速度向量转向下一个速度向量所需的时间 (秒)。
			 *        使用最大转弯率进行估算。
			 * @param state 当前状态 (用于获取限制)。
			 * @param currentVelocity 当前速度向量。
			 * @param nextVelocity 目标速度向量。
			 * @return 估算的时间 (秒)，如果无法转弯则返回无穷大，如果无需转弯则返回 0。
			 */
			virtual Time estimateTurnTime(const UavState& state, const Vector3D& currentVelocity, const Vector3D& nextVelocity) const;

			// --- 速度约束校验方法 ---
			/**
			 * @brief 综合速度校验函数 - 校验并调整速度，确保在安全范围内
			 * @param state 当前状态（用于获取动态限制）
			 * @param desired_speed 期望的速度值 (m/s)
			 * @param adjusted_speed 输出参数：调整后的速度 (m/s)
			 * @param warning_message 输出参数：如果有调整，返回警告信息
			 * @return true表示速度在安全范围内或已成功调整，false表示无法满足约束
			 *
			 * @note 这个函数主要用于路径规划中的水平飞行速度校验，
			 *       会同时考虑最大水平速度和最小运行速度约束
			 */
			virtual bool validateAndAdjustSpeed(const UavState& state,
				double desired_speed,
				double& adjusted_speed,
				std::string& warning_message) const;

			/**
			 * @brief 三维速度向量校验函数 - 校验并调整三维速度向量
			 * @param state 当前状态（用于获取动态限制）
			 * @param desired_velocity 期望的三维速度向量 (m/s)
			 * @param adjusted_velocity 输出参数：调整后的速度向量 (m/s)
			 * @param warning_message 输出参数：如果有调整，返回警告信息
			 * @return true表示速度在安全范围内或已成功调整，false表示无法满足约束
			 *
			 * @note 这个函数会分别校验水平和垂直速度分量，确保都在安全范围内
			 */
			virtual bool validateAndAdjustVelocity(const UavState& state,
				const Vector3D& desired_velocity,
				Vector3D& adjusted_velocity,
				std::string& warning_message) const;

			// --- 力学计算接口 (用于更详细的仿真或能量模型) ---
			/**
			 * @brief (纯虚函数) 计算给定状态下的升力向量。
			 * @param state 无人机状态。
			 * @param air_density 空气密度 (kg/m^3)。
			 * @return 升力向量 (牛顿, 世界坐标系)。子类必须实现。
			 */
			virtual Vector3D computeLiftForce(const UavState& state, double air_density) const = 0;
			/**
			 * @brief (纯虚函数) 计算给定状态下的阻力向量。
			 * @param state 无人机状态。
			 * @param air_density 空气密度 (kg/m^3)。
			 * @return 阻力向量 (牛顿, 世界坐标系, 通常与速度方向相反)。子类必须实现。
			 */
			virtual Vector3D computeDragForce(const UavState& state, double air_density) const = 0;
			/**
			 * @brief (纯虚函数) 计算给定状态下所需的推力向量（或可用推力）。
			 * @param state 无人机状态。
			 * @return 推力向量 (牛顿, 世界坐标系或机体坐标系，取决于实现)。子类必须实现。
			 */
			virtual Vector3D computeThrustForce(const UavState& state) const = 0;

			// --- 静态辅助函数  ---
			/** @brief 静态辅助函数：检查速度向量是否在给定的限制范围内。*/
			static bool checkSpeedLimit(const Vector3D& velocity, double maxHVel, double maxVVelUp, double maxVVelDown, double minOpSpeed = 0.0);
			/** @brief 静态辅助函数：检查位置是否低于最大高度限制。*/
			static bool checkAltitudeLimit(const WGS84Point& position, double maxAlt);
			/** @brief 静态辅助函数：检查从 v_current 到 v_next 的转弯是否满足最大转弯率和最小半径约束。*/
			static bool checkTurnLimit(const Vector3D& v_current, const Vector3D& v_next, Time dt, double maxTurnRate, double minTurnRadius);

		protected:
			/** @brief 指向拥有此模型的无人机对象 (const 引用)。*/
			const Uav& owner_; // 存储 owner 的 const 引用
		};

		// 定义动力学模型接口的智能指针类型 
		using IDynamicModelPtr = std::shared_ptr<IDynamicModel>;
		using ConstIDynamicModelPtr = std::shared_ptr<const IDynamicModel>;

	} // namespace NSUav
} // namespace NSDrones