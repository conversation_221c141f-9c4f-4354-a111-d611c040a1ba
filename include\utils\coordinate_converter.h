// include/utils/coordinate_converter.h
#pragma once

#include "core/types.h"
#include <GeographicLib/Geocentric.hpp>
#include <vector>

namespace NSDrones {
	namespace NSUtils {

		/**
		 * @class CoordinateConverter
		 * @brief 全局坐标系转换工具类
		 *
		 * 提供WGS84和ECEF坐标系之间的转换功能，不依赖于任何特定的TaskSpace或局部坐标系。
		 * 这是一个静态工具类，所有方法都是线程安全的。
		 */
		class CoordinateConverter {
		public:
			/**
			 * @brief WGS84坐标转换为ECEF坐标
			 * @param wgs84_pos WGS84坐标点
			 * @return ECEF坐标点
			 */
			static EcefPoint wgs84ToECEF(const WGS84Point& wgs84_pos);

			/**
			 * @brief ECEF坐标转换为WGS84坐标
			 * @param ecef_pos ECEF坐标点
			 * @return WGS84坐标点
			 */
			static WGS84Point ecefToWGS84(const EcefPoint& ecef_pos);

			/**
			 * @brief 批量WGS84坐标转换为ECEF坐标
			 * @param wgs84_points WGS84坐标点列表
			 * @return ECEF坐标点列表
			 */
			static std::vector<EcefPoint> batchWgs84ToECEF(const std::vector<WGS84Point>& wgs84_points);

			/**
			 * @brief 批量ECEF坐标转换为WGS84坐标
			 * @param ecef_points ECEF坐标点列表
			 * @return WGS84坐标点列表
			 */
			static std::vector<WGS84Point> batchEcefToWGS84(const std::vector<EcefPoint>& ecef_points);

			/**
			 * @brief 计算两个ECEF坐标点之间的距离
			 * @param ecef_pos1 第一个ECEF坐标点
			 * @param ecef_pos2 第二个ECEF坐标点
			 * @return 距离（米）
			 */
			static double distanceECEF(const EcefPoint& ecef_pos1, const EcefPoint& ecef_pos2);

			/**
			 * @brief 计算ECEF坐标系中的速度向量
			 * @param ecef_pos1 起始ECEF坐标点
			 * @param ecef_pos2 结束ECEF坐标点
			 * @param time_delta 时间间隔（秒）
			 * @return ECEF坐标系中的速度向量（米/秒）
			 */
			static Vector3D velocityECEF(const EcefPoint& ecef_pos1, const EcefPoint& ecef_pos2, double time_delta);

			/**
			 * @brief 计算ECEF坐标系中的加速度向量
			 * @param ecef_vel1 起始ECEF速度向量
			 * @param ecef_vel2 结束ECEF速度向量
			 * @param time_delta 时间间隔（秒）
			 * @return ECEF坐标系中的加速度向量（米/秒²）
			 */
			static Vector3D accelerationECEF(const Vector3D& ecef_vel1, const Vector3D& ecef_vel2, double time_delta);

			/**
			 * @brief 在ECEF坐标系中进行位置预测
			 * @param ecef_pos 当前ECEF坐标点
			 * @param ecef_velocity ECEF速度向量
			 * @param time_delta 预测时间间隔（秒）
			 * @return 预测的ECEF坐标点
			 */
			static EcefPoint predictPositionECEF(const EcefPoint& ecef_pos, const Vector3D& ecef_velocity, double time_delta);

			/**
			 * @brief 在ECEF坐标系中进行位置预测（包含加速度）
			 * @param ecef_pos 当前ECEF坐标点
			 * @param ecef_velocity ECEF速度向量
			 * @param ecef_acceleration ECEF加速度向量
			 * @param time_delta 预测时间间隔（秒）
			 * @return 预测的ECEF坐标点
			 */
			static EcefPoint predictPositionECEF(const EcefPoint& ecef_pos, const Vector3D& ecef_velocity, const Vector3D& ecef_acceleration, double time_delta);

		private:
			// 私有构造函数，防止实例化
			CoordinateConverter() = delete;
			~CoordinateConverter() = delete;
			CoordinateConverter(const CoordinateConverter&) = delete;
			CoordinateConverter& operator=(const CoordinateConverter&) = delete;

			/**
			 * @brief 获取GeographicLib的Geocentric对象（线程安全的单例）
			 * @return Geocentric对象引用
			 */
			static const GeographicLib::Geocentric& getGeocentric();
		};

	} // namespace NSUtils
} // namespace NSDrones
