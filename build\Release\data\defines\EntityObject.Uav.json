{"description": "Common parameters for all UAV types, inheriting from EntityObject.", "parameters": [{"key": "physical.empty_weight", "type": "double", "description": "空重 (不含载荷) (kg)", "default": 2.0, "constraints": {"type": "numeric", "min": 0.1, "max": 100.0}}, {"key": "physical.max_payload_weight", "type": "double", "description": "最大载荷能力 (kg)", "default": 1.0, "constraints": {"type": "numeric", "min": 0.0, "max": 20.0}}, {"key": "dynamics.base.maxHVel", "type": "double", "description": "最大水平速度 (m/s)", "default": 15.0, "constraints": {"type": "numeric", "min": 1.0, "max": 80.0}}, {"key": "dynamics.base.maxHAcc", "type": "double", "description": "最大水平加速度 (m/s^2)", "default": 5.0, "constraints": {"type": "numeric", "min": 0.5, "max": 20.0}}, {"key": "dynamics.base.maxVVelUp", "type": "double", "description": "最大上升速度 (m/s)", "default": 4.0, "constraints": {"type": "numeric", "min": 0.5, "max": 15.0}}, {"key": "dynamics.base.maxVVelDown", "type": "double", "description": "最大下降速度 (m/s)", "default": 3.0, "constraints": {"type": "numeric", "min": 0.5, "max": 10.0}}, {"key": "dynamics.base.maxVAcc", "type": "double", "description": "最大垂直加速度 (m/s^2)", "default": 2.0, "constraints": {"type": "numeric", "min": 0.5, "max": 10.0}}, {"key": "dynamics.base.maxAlt", "type": "double", "description": "最大飞行高度 (AGL 或 MSL 取决于约定) (m)", "default": 1000.0, "constraints": {"type": "numeric", "min": 10.0}}, {"key": "dynamics.base.maxHDecel", "type": "double", "description": "最大水平减速度 (m/s^2, 通常等于最大水平加速度)", "default": 5.0, "constraints": {"type": "numeric", "min": 0.5, "max": 20.0}}, {"key": "dynamics.base.maxVDecel", "type": "double", "description": "最大垂直减速度 (m/s^2, 通常等于最大垂直加速度)", "default": 2.0, "constraints": {"type": "numeric", "min": 0.5, "max": 10.0}}, {"key": "dynamics.base.minOpSpeed", "type": "double", "description": "最小运行速度 (m/s, 多旋翼通常为0)", "default": 0.0, "constraints": {"type": "numeric", "min": 0.0}}, {"key": "dynamics.base.max_yaw_rate_dps", "type": "double", "description": "最大偏航角速度 (deg/s)", "default": 90.0, "constraints": {"type": "numeric", "min": 10.0, "max": 360.0}}, {"key": "energy.model_type", "type": "string", "enum_type": "NSDrones::NSCore::EnergyModelType", "description": "使用的能量消耗模型类型。实际合法值由 enum_type 定义的C++枚举确定。", "default": "SIMPLE", "required": false}, {"key": "energy.initial_battery_level_wh", "type": "double", "description": "初始电池电量 (Wh)", "default": 200.0, "constraints": {"type": "numeric", "min": 0.0, "max": 2000.0}}, {"key": "energy.base.max_capacity", "type": "double", "description": "电池总容量 (Wh)", "default": 200.0, "constraints": {"type": "numeric", "min": 10.0, "max": 2000.0}}, {"key": "energy.base.min_safe_fraction", "type": "double", "description": "最低安全电量比例 (0-1)", "default": 0.1, "constraints": {"type": "numeric", "min": 0.0, "max": 1.0}}, {"key": "energy.base.charging_efficiency", "type": "double", "description": "充电效率 (0-1)", "default": 0.9, "constraints": {"type": "numeric", "min": 0.01, "max": 1.0}}, {"key": "energy.base.discharging_efficiency", "type": "double", "description": "放电效率 (0-1)", "default": 0.95, "constraints": {"type": "numeric", "min": 0.01, "max": 1.0}}, {"key": "energy.base.baseline_power", "type": "double", "description": "基础系统/航电功耗 (W)", "default": 30.0, "constraints": {"type": "numeric", "min": 5.0, "max": 200.0}}, {"key": "payload.supported_types", "type": "string[]", "default": ["camera_rgb", "generic_sensor"], "description": "支持的载荷类型标识符列表。"}, {"key": "payloads", "type": "string[]", "default": [], "description": "实例实际挂载的载荷ID列表。"}, {"key": "flight_controller.type", "type": "string", "enum_type": "NSDrones::NSCore::FlightControllerType", "description": "飞控类型或使用的主要算法。实际合法值由 enum_type 定义的C++枚举确定。", "default": "PID", "required": false}, {"key": "aero.wing_span_m", "type": "double", "description": "翼展 (m)", "default": 1.5, "constraints": {"type": "numeric", "min": 0.2, "max": 10.0}, "required": false}, {"key": "physics.mass_kg", "type": "double", "description": "总质量 (kg)。如果提供，则可能覆盖 empty_weight + payload_weight 的计算值，或作为主要质量来源。", "default": 2.5, "constraints": {"type": "numeric", "min": 0.1, "max": 150.0}, "required": false}, {"key": "limits.max_speed_mps", "type": "double", "description": "最大综合飞行速度限制 (m/s)。", "default": 15.0, "constraints": {"type": "numeric", "min": 1.0, "max": 80.0}, "required": false}]}