﻿  itask_planner.cpp
  mission_planner.cpp
E:\source\dronesplanning\src\planning\mission_planner.cpp(838,31): error C2039: "AvoidanceConstraint": 不是 "NSDrones::NSPlanning::MissionPlanner" 的成员
E:\source\dronesplanning\include\planning/mission_planner.h(56,9): message : 参见“NSDrones::NSPlanning::MissionPlanner”的声明
E:\source\dronesplanning\src\planning\mission_planner.cpp(1226,23): error C2512: “NSDrones::NSMission::Mission”: 没有合适的默认构造函数可用
E:\source\dronesplanning\include\algorithm/allocator/task_allocator.h(18,30): message : 参见“NSDrones::NSMission::Mission”的声明
  planning_types.cpp
E:\source\dronesplanning\src\planning\planning_types.cpp(230,44): error C2039: "calculateDirection": 不是 "NSDrones::NSUtils::GeometryManager" 的成员
E:\source\dronesplanning\include\utils/geometry_manager.h(32,9): message : 参见“NSDrones::NSUtils::GeometryManager”的声明
E:\source\dronesplanning\src\planning\planning_types.cpp(230,62): error C3861: “calculateDirection”: 找不到标识符
  followpath_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(175,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(207,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(248,6): error C2039: "zone_id": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(253,29): error C2653: “AvoidanceConstraintType”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(253,54): error C2065: “ALTITUDE_SEPARATION”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(254,7): error C2039: "min_altitude": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(254,7): error C2039: "max_altitude": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(257,34): error C2653: “AvoidanceConstraintType”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(257,59): error C2065: “SPATIAL_AVOIDANCE”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(258,7): error C2039: "avoidance_radius": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
  loiterpoint_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(173,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(220,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(261,6): error C2039: "zone_id": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(266,29): error C2653: “AvoidanceConstraintType”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(266,54): error C2065: “ALTITUDE_SEPARATION”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(267,7): error C2039: "min_altitude": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(267,7): error C2039: "max_altitude": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(270,34): error C2653: “AvoidanceConstraintType”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(270,59): error C2065: “SPATIAL_AVOIDANCE”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(271,7): error C2039: "avoidance_radius": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
  scanarea_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(209,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(241,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
  surveycylinder_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(259,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(291,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
  surveymultipoints_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(166,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(198,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
  surveysphere_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(263,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(290,23): error C2676: 二进制“[”:“NSDrones::NSPlanning::Trajectory”不定义该运算符或到预定义运算符可接收的类型的转换
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(307,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
  fixedwing_dynamics.cpp
  enum_utils.cpp
  orientation_utils.cpp
  正在生成代码...
