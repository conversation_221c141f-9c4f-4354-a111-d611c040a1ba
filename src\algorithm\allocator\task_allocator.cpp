// src/algorithm/allocator/task_allocator.cpp
#include "algorithm/allocator/task_allocator.h"
#include "environment/environment.h"
#include "mission/mission.h"
#include "mission/task.h"
#include "mission/capability_requirement.h"
#include "uav/uav.h"
#include "utils/logging.h"
#include "utils/enum_utils.h"
#include "params/paramregistry.h"
#include <stdexcept>
#include <set>

namespace NSDrones {
	namespace NSAlgorithm {

		TaskAllocator::TaskAllocator(ObjectID id,
			const std::string& type_tag,
			const std::string& name,
			const std::string& version)
			: AlgorithmObject(id, type_tag, name, version),
			ITaskAllocator() // ITaskAllocator 默认构造
		{
			LOG_INFO("TaskAllocator [{}] (ID: {}) constructed. Version: {}. Associated with Environment.",
				getName(), getId(), getVersion());
		}

		bool TaskAllocator::initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) {
			LOG_DEBUG("TaskAllocator [{}] (ID: {}): Initializing...", getName(), getId());
			if (!AlgorithmObject::initialize(params, raw_config)) {
				LOG_ERROR("TaskAllocator [{}] (ID: {}): Base initialization failed.", getName(), getId());
				return false;
			}
			// TaskAllocator 特定的参数可以在这里加载，如果存在的话
			// example: some_specific_param_ = getParamOrDefault<double>("my_allocator_param", 0.5);
			LOG_INFO("TaskAllocator [{}] (ID: {}) initialized successfully.", getName(), getId());
			return true;
		}

		/**
		 * @brief Mission级别的分配实现
		 */
		MissionDecompositionResult TaskAllocator::decomposeAndAllocateMission(
			const NSMission::Mission& mission,
			const std::vector<NSUav::UavPtr>& available_uavs,
			const std::map<ObjectID, NSUav::UavState>& current_uav_states)
		{
			LOG_INFO("任务分配器 [{}] (ID: {}): 开始为任务计划 [{}] 分配无人机。可用无人机数量: {}",
				getName(), getId(), mission.getId(), available_uavs.size());

			MissionDecompositionResult result;
			result.success = true; // 初始假设成功

			const auto& tasks = mission.getTasks();
			if (tasks.empty()) {
				result.message = "No tasks in the mission to allocate.";
				LOG_INFO("TaskAllocator [{}] (ID: {}): Mission [{}] has no tasks.", getName(), getId(), mission.getId());
				return result;
			}

			if (available_uavs.empty()) {
				result.success = false;
				result.message = "没有可用的无人机进行分配。";
				LOG_WARN("{}", result.message);
				// 将所有任务标记为未分配
				for (const auto& task_ptr : mission.getTasks()) {
					if (task_ptr) {
						result.unallocated_tasks.push_back(task_ptr->getId());
					}
				}
				return result;
			}

			// 遍历任务列表，为每个任务调用decomposeAndAllocateTask
			for (const auto& task_ptr : tasks) {
				if (!task_ptr) {
					LOG_WARN("任务列表中遇到空指针，跳过此任务。");
					continue;
				}

				TaskDecompositionResult task_result = decomposeAndAllocateTask(*task_ptr, available_uavs, current_uav_states);
				result.task_decompositions.push_back(task_result);

				if (!task_result.success) {
					result.success = false;
					result.unallocated_tasks.push_back(task_ptr->getId());
					LOG_WARN("任务 {} 分解和分配失败: {}", task_ptr->getId(), task_result.message);
				} else {
					LOG_INFO("任务 {} 分解和分配成功，生成 {} 个子任务分配",
						task_ptr->getId(), task_result.sub_assignments.size());
				}
			}

			if (result.success) {
				result.message = "所有任务分解和分配成功";
				LOG_INFO("Mission [{}] 所有任务分解和分配完成", mission.getId());
			} else {
				result.message = "部分任务分解和分配失败";
				LOG_WARN("Mission [{}] 有 {} 个任务未能成功分配", mission.getId(), result.unallocated_tasks.size());
			}

			return result;
		}

		/**
		 * @brief 单个任务的分解和分配实现
		 */
		TaskDecompositionResult TaskAllocator::decomposeAndAllocateTask(
			const NSMission::Task& task,
			const std::vector<NSUav::UavPtr>& available_uavs,
			const std::map<ObjectID, NSUav::UavState>& current_uav_states)
		{
			LOG_DEBUG("开始分解和分配任务 {} (类型: {})", task.getId(), NSUtils::enumToString(task.getType()));

			TaskDecompositionResult result;
			result.success = false;
			result.original_task_id = task.getId();

			try {
				// 第1步：分解任务为子目标
				std::vector<SubTaskTarget> sub_targets = decomposeTaskTarget(task, available_uavs.size());
				result.total_sub_tasks = static_cast<int>(sub_targets.size());

				if (sub_targets.empty()) {
					result.message = "任务分解失败，未生成子目标";
					LOG_WARN("任务 {} 分解失败", task.getId());
					return result;
				}

				LOG_DEBUG("任务 {} 分解为 {} 个子目标", task.getId(), sub_targets.size());

				// 第2步：为子目标分配无人机
				std::vector<SubTaskAssignment> assignments = assignUavsToSubTargets(sub_targets, available_uavs, current_uav_states);
				result.sub_assignments = assignments;
				result.assigned_sub_tasks = static_cast<int>(assignments.size());

				// 第3步：检查分配结果
				if (assignments.size() == sub_targets.size()) {
					result.success = true;
					result.message = "任务分解和分配成功";
					LOG_INFO("任务 {} 成功分配所有 {} 个子任务", task.getId(), assignments.size());
				} else {
					result.success = false;
					result.message = fmt::format("部分子任务分配失败: {}/{}", assignments.size(), sub_targets.size());

					// 记录未分配的子任务索引
					for (size_t i = assignments.size(); i < sub_targets.size(); ++i) {
						result.unassigned_sub_task_indices.push_back(static_cast<int>(i));
					}

					LOG_WARN("任务 {} 仅成功分配 {}/{} 个子任务", task.getId(), assignments.size(), sub_targets.size());
				}

			} catch (const std::exception& e) {
				result.success = false;
				result.message = "任务分解和分配异常: " + std::string(e.what());
				LOG_ERROR("任务 {} 分解和分配异常: {}", task.getId(), e.what());
			}

			return result;
		}

		/**
		 * @brief 分解TaskTarget为多个子目标的简单实现
		 */
		std::vector<SubTaskTarget> TaskAllocator::decomposeTaskTarget(
			const NSMission::Task& task,
			int available_uav_count)
		{
			LOG_DEBUG("分解任务目标，任务类型: {}, 可用UAV数量: {}",
				NSUtils::enumToString(task.getType()), available_uav_count);

			std::vector<SubTaskTarget> sub_targets;

			// 简单实现：为每个任务创建一个子目标（不进行复杂分解）
			SubTaskTarget sub_target;
			sub_target.sub_target_id = task.getId() + "_sub_0";
			sub_target.target = task.getTarget(); // 直接使用原始目标
			sub_target.parent_task_id = task.getId();
			sub_target.task_type = task.getType();
			sub_target.sub_task_index = 0;
			sub_target.parent_task_ref = &task;

			// 复制任务参数
			// 注意：这里需要根据实际的Task类接口来获取参数
			// sub_target.params = task.getParameters(); // 如果Task有这个方法

			sub_targets.push_back(sub_target);

			LOG_DEBUG("任务 {} 分解为 {} 个子目标", task.getId(), sub_targets.size());
			return sub_targets;
		}

		/**
		 * @brief 为子目标分配无人机的实现
		 */
		std::vector<SubTaskAssignment> TaskAllocator::assignUavsToSubTargets(
			const std::vector<SubTaskTarget>& sub_targets,
			const std::vector<NSUav::UavPtr>& available_uavs,
			const std::map<ObjectID, NSUav::UavState>& current_states)
		{
			LOG_DEBUG("开始为 {} 个子目标分配无人机，可用UAV数量: {}", sub_targets.size(), available_uavs.size());

			std::vector<SubTaskAssignment> assignments;
			std::set<ObjectID> used_uavs; // 跟踪已使用的UAV

			for (const auto& sub_target : sub_targets) {
				LOG_DEBUG("为子目标 {} 分配无人机", sub_target.sub_target_id);

				// 查找合适的UAV
				NSUav::UavPtr selected_uav = nullptr;
				for (const auto& uav_ptr : available_uavs) {
					if (!uav_ptr || used_uavs.count(uav_ptr->getId())) {
						continue; // 跳过空指针或已使用的UAV
					}

					// 这里可以添加更复杂的匹配逻辑
					// 目前简单选择第一个可用的UAV
					selected_uav = uav_ptr;
					break;
				}

				if (selected_uav) {
					SubTaskAssignment assignment;
					assignment.sub_task_id = sub_target.sub_target_id;
					assignment.assigned_uav_id = selected_uav->getId();
					assignment.sub_target = sub_target;

					// 设置起始状态
					auto state_it = current_states.find(selected_uav->getId());
					if (state_it != current_states.end()) {
						assignment.start_state = state_it->second;
					} else {
						// 使用UAV的当前状态
						assignment.start_state = selected_uav->getCurrentState();
					}

					assignments.push_back(assignment);
					used_uavs.insert(selected_uav->getId());

					LOG_DEBUG("子目标 {} 分配给UAV {}", sub_target.sub_target_id, selected_uav->getId());
				} else {
					LOG_WARN("无法为子目标 {} 找到合适的UAV", sub_target.sub_target_id);
					break; // 无法继续分配
				}
			}

			LOG_DEBUG("成功分配 {}/{} 个子目标", assignments.size(), sub_targets.size());
			return assignments;
		}

		// 实现内部辅助函数 checkUavCapabilities
		bool TaskAllocator::checkUavCapabilities(const NSUav::UavPtr& uav, const NSMission::CapabilityRequirement& req) const {
			if (!uav) return false;
			LOG_TRACE("  检查无人机 {} 的能力是否满足需求...", uav->getId());

			// 1. 检查无人机类型（支持类型兼容性）
			if (req.required_uav_type.has_value()) {
				UavType required_type = req.required_uav_type.value();
				UavType uav_type = uav->getType();

				bool type_compatible = false;

				// 精确匹配
				if (uav_type == required_type) {
					type_compatible = true;
				}
				// VTOL兼容性：VTOL可以执行MULTIROTOR任务（悬停模式）
				else if (required_type == UavType::MULTIROTOR && uav_type == UavType::VTOL_FIXED_WING) {
					type_compatible = true;
					LOG_TRACE("    类型兼容: VTOL无人机可以执行MULTIROTOR任务（悬停模式）。");
				}

				if (!type_compatible) {
					LOG_TRACE("    类型不匹配: 需要 {}, 实际 {}。",
						NSUtils::enumToString(required_type), NSUtils::enumToString(uav_type));
					return false;
				}
				LOG_TRACE("    类型匹配/兼容: {}。", NSUtils::enumToString(uav_type));
			}
			else {
				LOG_TRACE("    无特定类型要求。");
			}

			// 2. 检查必需的载荷/能力
			// **注意:** UAV 类需要提供 getCapabilities() 方法
			const auto& uav_caps = uav->getCapabilities(); // 假设 UAV 有 getCapabilities()
			const auto& required_payloads = req.required_payloads;
			if (!required_payloads.empty()) {
				LOG_TRACE("    检查必需载荷/能力 ({} 项)...", required_payloads.size());
				for (const auto& required_cap : required_payloads) {
					// 使用 uav->hasCapability() 检查
					if (!uav->hasCapability(required_cap)) { // 假设 UAV 有 hasCapability()
						LOG_TRACE("      缺少必需能力: {}。", required_cap);
						return false;
					}
					LOG_TRACE("      满足必需能力: {}。", required_cap);
				}
				LOG_TRACE("    所有必需能力均满足。");
			}
			else {
				LOG_TRACE("    无必需载荷/能力要求。");
			}

			// 3. 检查最小载荷能力 (如果任务有要求)
			if (req.min_payload_capacity.has_value()) {
				double required_capacity = req.min_payload_capacity.value();
				// 假设 UAV 有 getParamOrDefault<double>("payload.max_weight", 0.0)
				double uav_capacity = uav->getParamOrDefault<double>("payload.max_weight", 0.0);
				LOG_TRACE("    检查最小载荷能力: 需要 >= {:.2f} kg, 无人机最大载荷 {:.2f} kg", required_capacity, uav_capacity);
				if (uav_capacity < required_capacity - Constants::EPSILON) { // 考虑浮点误差
					LOG_TRACE("      载荷能力不足。");
					return false;
				}
				LOG_TRACE("    载荷能力满足。");
			}

			// 4. 检查续航时间 (如果任务有要求)
			// **注意:** 简单分配器通常不检查这个，或者需要能量模型和状态
			// if (req.min_endurance_time.has_value()) { ... }

			// 5. 检查航程 (如果任务有要求)
			// **注意:** 简单分配器通常不检查这个
			// if (req.min_range.has_value()) { ... }

			LOG_TRACE("  无人机 {} 满足所有检查的基本能力要求。", uav->getId());
			return true; // 如果所有检查都通过
		}

	} // namespace NSAlgorithm
} // namespace NSDrones