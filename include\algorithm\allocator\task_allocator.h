// include/planning/allocator/task_allocator.h
#pragma once

#include "algorithm/algorithm_object.h"
#include "algorithm/allocator/itask_allocator.h"
#include "core/types.h"
#include "uav/uav_fwd.h"
#include "uav/uav_types.h"
#include "params/parameters.h"
#include <nlohmann/json.hpp>

#include <vector>
#include <map>
#include <string>
#include <memory>

namespace NSDrones {
	namespace NSMission { class Mission; struct CapabilityRequirement; }
}

namespace NSDrones {
	namespace NSAlgorithm {

		/**
		 * @class TaskAllocator
		 * @brief 一个简单的任务分配器实现。
		 *
		 * 按顺序处理任务，为每个任务选择第一个满足类型和数量要求的可用无人机。
		 * 不进行优化，不考虑复杂约束（如路径、时间、能量）。
		 * 支持编队分配。
		 */
		class TaskAllocator : public ITaskAllocator, public AlgorithmObject  {
		public:
			/**
			 * @brief 构造函数。
			 * @param id 对象ID。
			 * @param type_tag 类型标签 (例如 "AlgorithmObject.TaskAllocator.Simple")。
			 * @param name 算法实例的名称 (可选)。
			 * @param version 算法版本号 (可选)。
			 */
			TaskAllocator(ObjectID id,
						  const std::string& type_tag,
						  const std::string& name = "AlgorithmObject.TaskAllocator",
						  const std::string& version = "1.0.0.0");

			~TaskAllocator() override = default;

			// Keep copy/move constructors/assignments if needed, or delete them
			// Depending on whether TaskAllocator state (like stored env) should be copied/moved
			TaskAllocator(const TaskAllocator&) = default; 
			TaskAllocator& operator=(const TaskAllocator&) = default; 
			TaskAllocator(TaskAllocator&&) = default; 
			TaskAllocator& operator=(TaskAllocator&&) = default;

			/**
			 * @brief 分解并分配单个任务
			 */
			TaskDecompositionResult decomposeAndAllocateTask(
				const NSMission::Task& task,
				const std::vector<NSUav::UavPtr>& available_uavs,
				const std::map<ObjectID, NSUav::UavState>& current_uav_states) override;

			/**
			 * @brief Mission级别的分配
			 */
			MissionDecompositionResult decomposeAndAllocateMission(
				const NSMission::Mission& mission,
				const std::vector<NSUav::UavPtr>& available_uavs,
				const std::map<ObjectID, NSUav::UavState>& current_uav_states) override;

			/**
			 * @brief 初始化简单任务分配器。
			 * @param params 包含配置参数的 ParamValues 对象 (当前 TaskAllocator 可能不使用)。
			 * @param raw_config 实例的原始JSON配置 (当前 TaskAllocator 可能不使用)。
			 * @return 总是返回 true，因为当前 TaskAllocator 没有特定配置。
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) override;

		protected:
			/**
			 * @brief 分解TaskTarget为多个子目标
			 */
			std::vector<SubTaskTarget> decomposeTaskTarget(
				const NSMission::Task& task,
				int available_uav_count) override;

			/**
			 * @brief 为子目标分配无人机
			 */
			std::vector<SubTaskAssignment> assignUavsToSubTargets(
				const std::vector<SubTaskTarget>& sub_targets,
				const std::vector<NSUav::UavPtr>& available_uavs,
				const std::map<ObjectID, NSUav::UavState>& current_states) override;

		private:
			/**
			 * @brief (内部辅助) 检查无人机是否满足任务的基本能力要求。
			 *        **注意：** 这里只进行基本类型和载荷匹配，不考虑续航等性能。
			 * @param uav 无人机指针。
			 * @param req 能力需求。
			 * @return 如果满足返回 true。
			 */
			bool checkUavCapabilities(const NSUav::UavPtr& uav, const NSMission::CapabilityRequirement& req) const;

		};

	} // namespace NSAlgorithm
} // namespace NSDrones