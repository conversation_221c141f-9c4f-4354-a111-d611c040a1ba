// src/planning/itask_planner.cpp
#include "planning/itask_planner.h"
#include "environment/entities/zone.h"
#include "environment/environment.h"
#include "environment/collision/collision_engine.h"
#include "mission/mission.h"
#include "mission/task.h"
#include "mission/control_point.h"
#include "mission/task_strategies.h"
#include "planning/planning_types.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "algorithm/evaluator/itrajectory_evaluator.h"
#include "uav/uav.h"
#include "uav/idynamic_model.h"
#include "utils/logging.h"
#include "utils/geometry_manager.h"
#include "utils/coordinate_converter.h"
#include "utils/object_id.h"
#include "core/types.h"
#include <utility>
#include <cmath>
#include <stdexcept>
#include <map>
#include <vector>
#include <optional>
#include <algorithm>
#include <unordered_map>

namespace NSDrones {
	namespace NSPlanning {

		// === 构造函数实现 ===

		ITaskPlanner::ITaskPlanner() {
			LOG_DEBUG("任务规划器: 基类初始化完成");
		}

		// === 环境和算法组件访问方法实现 ===

		std::shared_ptr<NSEnvironment::Environment> ITaskPlanner::getEnvironment() const {
			return NSEnvironment::Environment::getInstance();
		}

		IPathPlannerPtr ITaskPlanner::getPathPlanner() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getPathPlanner();
		}

		ITrajectoryOptimizerPtr ITaskPlanner::getTrajectoryOptimizer() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getTrajectoryOptimizer();
		}

		ITrajectoryEvaluatorPtr ITaskPlanner::getTrajectoryEvaluator() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getTrajectoryEvaluator();
		}

		// === 通用任务规划辅助方法实现 ===



		std::vector<NSCore::WGS84Point> ITaskPlanner::planPathToTarget(
			const NSUav::UavState& start_state,
			const NSCore::WGS84Point& target_wgs84,
			const NSUav::UavPtr& uav,
			const SubTaskTarget& sub_target,
			SingleTaskPlanningResult& result) const {

			std::vector<NSCore::WGS84Point> path;

			auto path_planner = getPathPlanner();
			if (!path_planner) {
				LOG_ERROR("任务规划器: 路径规划器未初始化");
				return path;
			}

			const NSUav::IDynamicModel* dynamics = uav->getDynamicsModel().get();
			if (!dynamics) {
				LOG_ERROR("任务规划器: 无人机[{}]缺少动力学模型", uav->getId());
				return path;
			}

			// 坐标转换
			NSCore::EcefPoint start_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(start_state.position);
			NSCore::EcefPoint target_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(target_wgs84);

			LOG_DEBUG("任务规划器: 规划路径从{}到{}", start_state.position.toString(), target_wgs84.toString());

			// 调用路径规划器
			std::vector<NSCore::EcefPoint> ecef_path;
			bool path_found = path_planner->findPath(start_ecef, target_ecef, dynamics, nullptr, ecef_path);

			if (!path_found || ecef_path.size() < 2) {
				std::string error_msg = "无法规划从" + start_state.position.toString() +
									   "到" + target_wgs84.toString() + "的路径";
				result.warnings.push_back(WarningEvent(WarningType::PLANNING_FAILURE, error_msg,
											  start_state.time_stamp, start_state.position,
											  uav->getId(), NSUtils::INVALID_OBJECT_ID, sub_target.sub_task_id));
				LOG_ERROR("任务规划器: 子任务[{}] {}", sub_target.sub_task_id, error_msg);
				return path;
			}

			// 转换ECEF路径为WGS84路径
			path.reserve(ecef_path.size());
			for (const auto& ecef_point : ecef_path) {
				path.push_back(NSUtils::CoordinateConverter::ecefToWGS84(ecef_point));
			}

			LOG_DEBUG("任务规划器: 成功规划路径，包含{}个点", path.size());
			return path;
		}

		// === 辅助方法实现 ===

		TrajectorySegment ITaskPlanner::generateLinearSegment(
			const TrajectoryPoint& start_point,
			const NSCore::WGS84Point& end_wgs84_pos,
			double speed,
			const NSUav::IDynamicModel& dynamics,
			const NSUtils::ObjectID& uav_id) const
		{
			TrajectorySegment segment;

			// 速度验证
			if (speed <= NSCore::Constants::VELOCITY_EPSILON) {
				LOG_WARN("[任务规划器] 生成线性轨迹段失败，速度({:.3f})无效", speed);
				return segment;
			}

			// 使用GeometryManager计算距离
			double distance = NSEnvironment::GeometryManager::calculateDistance(
				start_point.position, end_wgs84_pos);

			if (distance < NSCore::Constants::GEOMETRY_EPSILON) {
				LOG_WARN("[任务规划器] 起点和终点距离过近({:.3e}m)，只包含起点", distance);
				segment.push_back(start_point);
				return segment;
			}

			// 计算飞行时间和方向
			double duration = distance / speed;
			double bearing_deg = NSEnvironment::GeometryManager::calculateBearing(
				start_point.position, end_wgs84_pos);
			double bearing_rad = bearing_deg * NSCore::Constants::DEG_TO_RAD;

			// 添加起点
			segment.push_back(start_point);

			// 创建终点轨迹点
			TrajectoryPoint end_point;
			end_point.position = end_wgs84_pos;
			end_point.time_stamp = start_point.time_stamp + duration;
			end_point.velocity_ned = NSCore::Vector3D(
				std::cos(bearing_rad) * speed,
				std::sin(bearing_rad) * speed,
				0.0
			);

			// 设置姿态
			if (end_point.velocity_ned.squaredNorm() > NSCore::Constants::VELOCITY_EPSILON * NSCore::Constants::VELOCITY_EPSILON) {
				end_point.orientation = NSCore::Orientation::FromTwoVectors(
					NSCore::Vector3D::UnitX(), end_point.velocity_ned.normalized());
			}
			else {
				end_point.orientation = start_point.orientation;
			}

			// 继承载荷动作
			end_point.payload_actions = start_point.payload_actions;

			segment.push_back(end_point);

			LOG_DEBUG("[任务规划器] 成功生成线性轨迹段，无人机{}，{}个轨迹点，耗时{:.2f}s",
				uav_id, segment.size(), duration);

			return segment;
		}




		/**
		 * @brief 平滑几何路径并进行时间参数化。
		 * @param geometric_path 输入几何路径。
		 * @param uav 无人机指针。
		 * @param start_state 起始状态。
		 * @param desired_speed 期望速度。
		 * @param optimized_segment 输出优化后的轨迹段。
		 * @param result_ptr (可选) 用于添加告警。
		 * @param strategies (可选) 应用于此轨迹段的策略。
		 * @return 如果成功生成有效轨迹返回 true。
		 */
		bool ITaskPlanner::smoothAndTimeParameterize(const std::vector<WGS84Point>& geometric_path,
			const NSUav::UavPtr& uav,
			const NSUav::UavState& start_state,
			double desired_speed,
			Trajectory& optimized_trajectory,
			SingleTaskPlanningResult* result_ptr,
			const NSMission::ITaskStrategyMap& strategies)
		{
			LOG_DEBUG("[任务规划器] 开始为UAV{}平滑和时间参数化包含{}个点的几何路径，期望速度{:.2f}m/s",
				uav->getId(), geometric_path.size(), desired_speed);

			// === 输入验证 ===
			if (!uav) {
				LOG_ERROR("[任务规划器] 轨迹平滑和时间参数化失败：UAV指针无效");
				if (result_ptr) {
					result_ptr->warnings.push_back(WarningEvent(WarningType::CRITICAL,
						"内部错误：UAV指针无效", start_state.time_stamp));
				}
				return false;
			}

			if (geometric_path.size() < 2) {
				LOG_WARN("[任务规划器] 几何路径点数({})不足，无法处理", geometric_path.size());
				if (result_ptr) {
					result_ptr->warnings.push_back(WarningEvent(WarningType::PLANNING_ERROR,
						"几何路径点数不足", start_state.time_stamp, {}, uav->getId()));
				}
				return false;
			}

			if (desired_speed <= Constants::VELOCITY_EPSILON) {
				LOG_ERROR("[任务规划器] 期望速度({:.3f})无效", desired_speed);
				if (result_ptr) {
					result_ptr->warnings.push_back(WarningEvent(WarningType::PLANNING_ERROR,
						"期望速度无效", start_state.time_stamp, {}, uav->getId()));
				}
				return false;
			}

			const auto dynamic_model = uav->getDynamicsModel();
			if (!dynamic_model) {
				LOG_ERROR("[任务规划器] 无法获取UAV{}的动力学模型", uav->getId());
				if (result_ptr) {
					result_ptr->warnings.push_back(WarningEvent(WarningType::CRITICAL,
						"无法获取动力学模型", start_state.time_stamp, {}, uav->getId()));
				}
				return false;
			}

			try {
				// === 第1步：创建初始轨迹段 ===
				TrajectorySegment initial_segment;
				initial_segment.reserve(geometric_path.size());

				NSCore::Time current_time = start_state.time_stamp;
				NSCore::Vector3D current_velocity = start_state.velocity;

				// 生成轨迹点
				for (size_t i = 0; i < geometric_path.size(); ++i) {
					TrajectoryPoint trajectory_point;
					trajectory_point.position = geometric_path[i];
					trajectory_point.time_stamp = current_time;
					trajectory_point.velocity_ned = current_velocity;
					trajectory_point.orientation = start_state.orientation;

					initial_segment.push_back(trajectory_point);

					// 计算到下一个点的时间和速度
					if (i < geometric_path.size() - 1) {
						double distance = NSEnvironment::GeometryManager::calculateDistance(
							geometric_path[i], geometric_path[i + 1]);
						double time_increment = distance / desired_speed;
						current_time += time_increment;

						// 计算速度方向
						double bearing = NSEnvironment::GeometryManager::calculateBearing(
							geometric_path[i], geometric_path[i + 1]);
						double bearing_rad = bearing * NSCore::Constants::DEG_TO_RAD;
						current_velocity = NSCore::Vector3D(
							std::cos(bearing_rad) * desired_speed,
							std::sin(bearing_rad) * desired_speed,
							0.0
						);
					}
				}

				LOG_DEBUG("[任务规划器] 初始轨迹段生成完成，包含{}个点", initial_segment.size());

				// === 第2步：轨迹优化（如果可用） ===
				TrajectorySegment optimized_segment = initial_segment;
				auto trajectory_optimizer = getTrajectoryOptimizer();
				if (trajectory_optimizer) {
					LOG_DEBUG("[任务规划器] 尝试优化轨迹段...");

					TrajectoryOptimizationRequest opt_request;
					opt_request.initial_trajectory = initial_segment;
					opt_request.dynamics = dynamic_model.get();
					opt_request.strategies = &strategies;

					TrajectoryOptimizationResult opt_result = trajectory_optimizer->optimize(opt_request);

					if (opt_result.success && !opt_result.optimized_trajectory.empty()) {
						optimized_segment = opt_result.optimized_trajectory;
						LOG_INFO("[任务规划器] 轨迹优化成功，优化后包含{}个点", optimized_segment.size());
					} else {
						LOG_WARN("[任务规划器] 轨迹优化失败：{}，使用原始轨迹", opt_result.message);
						if (result_ptr) {
							result_ptr->warnings.push_back(WarningEvent(WarningType::OPTIMIZATION_FAILED,
								"轨迹优化失败: " + opt_result.message, start_state.time_stamp, {}, uav->getId()));
						}
					}
				}

				// === 第3步：设置输出轨迹 ===
				optimized_trajectory.setPoints(optimized_segment);

				double total_duration = optimized_segment.empty() ? 0.0 :
					optimized_segment.back().time_stamp - optimized_segment.front().time_stamp;

				LOG_INFO("[任务规划器] UAV{}轨迹平滑和时间参数化成功，生成{}个轨迹点，耗时{:.2f}s",
					uav->getId(), optimized_segment.size(), total_duration);
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("[任务规划器] 轨迹平滑和时间参数化异常: {}", e.what());
				if (result_ptr) {
					result_ptr->warnings.push_back(WarningEvent(WarningType::PLANNING_ERROR,
						"轨迹生成异常: " + std::string(e.what()), start_state.time_stamp, {}, uav->getId()));
				}
				return false;
			}
		}
	} // namespace NSPlanning
} // namespace NSDrones