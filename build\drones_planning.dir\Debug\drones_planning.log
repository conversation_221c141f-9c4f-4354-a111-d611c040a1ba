﻿  task_allocator.cpp
  energy_evaluator.cpp
  ipath_planner.cpp
  rrtstar_planner.cpp
  trajectory_optimizer.cpp
  config.cpp
  environment.cpp
  main.cpp
  itask_planner.cpp
  mission_planner.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(275,56): error C2280: “NSDrones::NSMission::Task::Task(const NSDrones::NSMission::Task &)”: 尝试引用已删除的函数
E:\source\dronesplanning\include\mission/task.h(55,4): message : 参见“NSDrones::NSMission::Task::Task”的声明
E:\source\dronesplanning\include\mission/task.h(55,4): message : “NSDrones::NSMission::Task::Task(const NSDrones::NSMission::Task &)”: 已隐式删除函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1983): message : 查看对正在编译的函数 模板 实例化“void std::_Construct_in_place<_Ty,const NSDrones::NSMission::Task&>(_Ty &,const NSDrones::NSMission::Task &) noexcept(false)”的引用
          with
          [
              _Ty=NSDrones::NSMission::Task
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(2663): message : 查看对正在编译的函数 模板 实例化“std::_Ref_count_obj2<_Ty>::_Ref_count_obj2<const NSDrones::NSMission::Task&>(const NSDrones::NSMission::Task &)”的引用
          with
          [
              _Ty=NSDrones::NSMission::Task
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(2664,20): message : 查看对正在编译的函数 模板 实例化“std::_Ref_count_obj2<_Ty>::_Ref_count_obj2<const NSDrones::NSMission::Task&>(const NSDrones::NSMission::Task &)”的引用
          with
          [
              _Ty=NSDrones::NSMission::Task
          ]
E:\source\dronesplanning\src\planning\mission_planner.cpp(1227,63): message : 查看对正在编译的函数 模板 实例化“std::shared_ptr<NSDrones::NSMission::Task> std::make_shared<NSDrones::NSMission::Task,const NSDrones::NSMission::Task&>(const NSDrones::NSMission::Task &)”的引用
  planning_types.cpp
  followpath_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(185,60): error C2838: “SPEED_ADJUSTED”: 成员声明中的限定名称非法
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(185,74): error C2065: “SPEED_ADJUSTED”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(265,6): error C2039: "zone_id": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(270,29): error C2653: “AvoidanceConstraintType”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(270,54): error C2065: “ALTITUDE_SEPARATION”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(271,7): error C2039: "min_altitude": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(271,7): error C2039: "max_altitude": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(274,34): error C2653: “AvoidanceConstraintType”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(274,59): error C2065: “SPATIAL_AVOIDANCE”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(275,7): error C2039: "avoidance_radius": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
  loiterpoint_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(173,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(220,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(261,6): error C2039: "zone_id": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(266,29): error C2653: “AvoidanceConstraintType”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(266,54): error C2065: “ALTITUDE_SEPARATION”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(267,7): error C2039: "min_altitude": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(267,7): error C2039: "max_altitude": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(270,34): error C2653: “AvoidanceConstraintType”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(270,59): error C2065: “SPATIAL_AVOIDANCE”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(271,7): error C2039: "avoidance_radius": 不是 "NSDrones::NSPlanning::AvoidanceConstraint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(517,10): message : 参见“NSDrones::NSPlanning::AvoidanceConstraint”的声明
  scanarea_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(209,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(241,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
  surveycylinder_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(259,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(291,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
  surveymultipoints_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(166,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(198,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
  surveysphere_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(263,35): error C2039: "getMaxSpeed": 不是 "NSDrones::NSUav::IDynamicModel" 的成员
E:\source\dronesplanning\include\uav/idynamic_model.h(26,9): message : 参见“NSDrones::NSUav::IDynamicModel”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(290,23): error C2676: 二进制“[”:“NSDrones::NSPlanning::Trajectory”不定义该运算符或到预定义运算符可接收的类型的转换
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(307,20): error C2039: "empty": 不是 "NSDrones::NSPlanning::Trajectory" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(309,9): message : 参见“NSDrones::NSPlanning::Trajectory”的声明
  fixedwing_dynamics.cpp
  multirotor_dynamics.cpp
  vtol_dynamics.cpp
  正在生成代码...
  正在编译...
  fixedwing_energies.cpp
  multirotor_energies.cpp
  vtol_energies.cpp
  flight_strategy.cpp
  idynamic_model.cpp
  ienergy_model.cpp
  uav.cpp
  uav_config.cpp
  orientation_utils.cpp
  正在生成代码...
