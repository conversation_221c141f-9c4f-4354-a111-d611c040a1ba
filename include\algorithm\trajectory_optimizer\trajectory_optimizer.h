// include/algorithm/trajectory_optimizer/trajectory_optimizer.h
#pragma once

#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "algorithm/algorithm_object.h"
#include "core/types.h"
#include "environment/environment_fwd.h"
#include "mission/task_strategies.h" 
#include "planning/planning_types.h" 
#include <map>
#include <string>
#include <memory>
#include <vector>
#include <functional> 
#include <filesystem> 

// --- Tinyspline ---
#include <tinysplinecxx.h>

namespace tinyspline { class BSpline; }
namespace NSDrones { namespace NSUav { class IDynamicModel; } }

namespace NSDrones {
	namespace NSAlgorithm {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @brief B样条轨迹优化器。
		 *        继承自 AlgorithmObject 和 ITrajectoryOptimizer。
		 */
		class TrajectoryOptimizer : public ITrajectoryOptimizer, public AlgorithmObject {
		public:
			/**
			 * @struct Options
			 * @brief B 样条优化器的配置选项。
			 */
			struct Options {
				// --- 样条基本参数 ---
				int bspline_degree = 3;             // B 样条阶数
				int output_path_points = 50;      // 输出路径的点数

				// --- 成本函数权重 ---
				double smoothness_weight_pos = 0.1;  // 平滑度: 控制点距离 (可选)
				double smoothness_weight_vel = 1.0;  // 平滑度: 速度平方积分 (近似加速度)
				double smoothness_weight_acc = 2.0;  // 平滑度: 加速度平方积分 (近似Jerk)
				double smoothness_weight_jerk = 1.0; // 平滑度: Jerk平方积分 (近似Snap)
				double collision_weight = 1000.0;   // 碰撞成本权重 (通常需要较高权重)
				double feasibility_weight_vel = 50.0; // 可行性: 速度限制
				double feasibility_weight_acc = 50.0; // 可行性: 加速度限制
				double feasibility_weight_turn = 30.0;// 可行性: 转弯率限制
				// double time_weight = 0.0;        // 可选: 时间成本权重

				// --- 约束和检查参数 ---
				double collision_safety_margin = 1.0; // 碰撞检查安全距离 (米)
				double min_trajectory_duration = 0.1; // 最小轨迹持续时间 (秒)
				int smoothness_integration_samples = 20; // 平滑度成本积分采样点数
				int collision_check_samples = 30;     // 碰撞成本采样点数
				int feasibility_check_samples = 25;   // 可行性成本采样点数

				// --- 优化器参数 ---
				int max_iterations = 50;         // 最大优化迭代次数
				double cost_tolerance = 1e-5;      // 成本函数收敛容差
				double optimizer_learning_rate = 0.01; // 梯度下降学习率
				double gradient_epsilon = 1e-5;      // 数值梯度计算步长
			};

			/**
			 * @brief 构造函数。
			 * @param id 对象ID。
			 * @param type_tag 类型标签。
			 * @param env_ref 环境对象的引用。
			 * @param name (可选) 优化器实例名称。
			 * @param version (可选) 优化器版本。
			 */
			explicit TrajectoryOptimizer(ObjectID id,
			                           const std::string& type_tag,
			                           const std::string& name = "BSplineTrajectoryOptimizer_Default",
			                           const std::string& version = "1.0.0.bspline");

			/** @brief 析构函数。*/
			~TrajectoryOptimizer() override = default;

			/**
			 * @brief 优化轨迹（重构后的统一接口）
			 * @param request 轨迹优化请求
			 * @return 轨迹优化结果
			 */
			TrajectoryOptimizationResult optimize(const TrajectoryOptimizationRequest& request) override;

			/**
			 * @brief 检查轨迹可行性（重构后的统一接口）
			 * @param trajectory 轨迹
			 * @param dynamics 动力学模型
			 * @return 可行性检查结果
			 */
			bool isFeasible(const Trajectory& trajectory, const NSUav::IDynamicModel& dynamics) const override;

			/**
			 * @brief 设置优化器参数（重构后的统一接口）
			 * @param parameters 参数映射
			 */
			void setParameters(const std::map<std::string, double>& parameters) override;

			/**
			 * @brief 初始化轨迹优化器。
			 * @param params 包含配置参数的 ParamValues 对象。
			 * @param raw_config 实例的原始JSON配置。
			 * @return 如果初始化成功则返回 true，否则返回 false。
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) override;

		private:
			Options options_; ///< 存储配置选项

			/**
			 * @brief 内部轨迹优化实现
			 * @param initial_trajectory 初始轨迹
			 * @param dynamics 动力学模型
			 * @param strategies 任务策略
			 * @return 优化后的轨迹
			 */
			Trajectory optimizeTrajectoryInternal(
				const Trajectory& initial_trajectory,
				const NSUav::IDynamicModel& dynamics,
				const ITaskStrategyMap* strategies) const;

			/**
			 * @brief 检查轨迹碰撞
			 * @param trajectory 要检查的轨迹
			 * @return 是否存在碰撞
			 */
			bool checkCollision(const Trajectory& trajectory) const;
		};

	} // namespace NSAlgorithm
} // namespace NSDrones