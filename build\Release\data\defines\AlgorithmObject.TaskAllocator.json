{"description": "Parameters for Task Allocator algorithms. This defines common parameters shared by all task allocator implementations.", "parameters": [{"key": "allocation_strategy", "name": "分配策略", "description": "任务分配策略类型 (例如 'simple', 'optimized', 'greedy')。", "type": "string", "default": "simple", "required": false}, {"key": "max_allocation_time_ms", "name": "最大分配时间", "description": "任务分配的最大允许时间 (毫秒)。", "type": "int", "default": 5000, "constraints": {"type": "numeric", "min": 100, "max": 60000}, "required": false}, {"key": "enable_reallocation", "name": "启用重新分配", "description": "是否允许在执行过程中重新分配任务。", "type": "bool", "default": false, "required": false}]}