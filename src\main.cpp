// src/main.cpp
#include "drones.h"
#include "config.h"

#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/fmt/ranges.h>
#include <Windows.h>

// C++ 标准库
#include <iostream>
#include <vector>
#include <memory>
#include <cmath>
#include <filesystem>
#include <sstream>
#include <iomanip>
#include <exception>
#include <limits>
#include <chrono>

// 新增：包含所有任务规划器的参数头文件
#include "mission/task_targets.h"
#include "planning/task_planners/followpath_taskplanner.h"
#include "planning/task_planners/loiterpoint_taskplanner.h"
#include "planning/task_planners/scanarea_taskplanner.h"
#include "planning/task_planners/surveycylinder_taskplanner.h"
#include "planning/task_planners/surveymultipoints_taskplanner.h"
#include "planning/task_planners/surveysphere_taskplanner.h"

// 地图相关头文件
#include "environment/maps/single_gridmap.h"

// Windows 特定 (用于设置控制台输出编码)
#ifdef _WIN32
// 在这里声明原始代码页变量
UINT original_codepage = 0;
UINT original_input_codepage = 0;

// 全局清理函数
void cleanup_log_on_exit() {
	if (original_codepage != 0) {
		SetConsoleOutputCP(original_codepage);
	}
	if (original_input_codepage != 0) {
		SetConsoleCP(original_input_codepage);
	}
	NSDrones::Logger::shutdown();
	// 在atexit处理程序中直接使用std::cout可能不可靠
}
#else
// 非Windows平台的atexit处理
void cleanup_log_on_exit() {
	if (spdlog::default_logger()) { // 检查默认记录器是否存在
		spdlog::default_logger_raw()->flush();
	}
	NSDrones::Logger::shutdown();
}
#endif

// 使用 using 简化命名空间，提高代码可读性
using namespace NSDrones;
using namespace NSDrones::NSUtils;
using namespace NSDrones::NSCore;
using namespace NSDrones::NSEnvironment;
using namespace NSDrones::NSUav;
using namespace NSDrones::NSMission;
using namespace NSDrones::NSPlanning;
using namespace NSDrones::NSParams;

// 使用 JSON 别名
using json = nlohmann::json;
namespace fs = std::filesystem; // 添加文件系统命名空间别名

// --- 辅助函数 ---
namespace { // 匿名命名空间

	/**
	 * @brief 以编程方式创建一个包含示例任务的 Mission 对象。
	 * @param mission_id 任务计划的 ID。
	 * @param target_uav 目标无人机的共享指针。
	 * @param config_ptr 配置对象的常量共享指针，用于创建规划器实例。
	 * @param env 对环境对象的引用，用于获取全局参数。
	 * @return 创建的 Mission 对象。
	 * @throws std::runtime_error 如果 target_uav 或 config_ptr 无效。
	 */
	Mission createProgrammaticMission(const std::string& mission_id,
		UavPtr target_uav,
		std::shared_ptr<const Config> config_ptr) {
		LOG_INFO("辅助函数: 开始以编程方式创建任务计划 '{}', 目标无人机: '{}'", mission_id, target_uav ? target_uav->getId() : "无效");
		if (!target_uav) {
			LOG_ERROR("辅助函数: 创建任务计划 '{}' 失败，目标无人机指针无效！", mission_id);
			throw std::runtime_error("无效的目标无人机指针提供给 createProgrammaticMission");
		}
		if (!config_ptr) {
			LOG_ERROR("辅助函数: 创建任务计划 '{}' 失败，配置指针 (config_ptr) 无效！", mission_id);
			throw std::runtime_error("无效的配置指针提供给 createProgrammaticMission");
		}

		// --- 任务规划器将在 MissionPlanner 创建后注册 ---


		Mission mission(mission_id);

		// --- 通用配置 ---
		CapabilityRequirement single_mr_req;
		single_mr_req.required_uav_type = UavType::MULTIROTOR;
		single_mr_req.min_required_count = 1;
		single_mr_req.max_required_count = 1;
		TaskTargetVariant target_uav_variant = std::make_shared<ObjectTarget>(target_uav->getId());

		// 从环境中获取默认任务参数
		auto env = Environment::getInstance();
		double default_alt_msl = env->getGlobalParamOrDefault<double>("default_mission_alt_msl", 60.0);
		double default_speed_mps = env->getGlobalParamOrDefault<double>("default_mission_speed_mps", 5.0);
		double default_loiter_radius_m = env->getGlobalParamOrDefault<double>("default_mission_loiter_radius_m", 25.0);
		double default_loiter_time_s = env->getGlobalParamOrDefault<double>("default_mission_loiter_time_s", 30.0);

		LOG_DEBUG("辅助函数: 使用全局/默认任务参数: 高度={}m MSL, 速度={}m/s, 盘旋半径={}m, 盘旋时间={}s",
			default_alt_msl, default_speed_mps, default_loiter_radius_m, default_loiter_time_s);

		// --- 任务定义 ---

		// 任务 1: 起飞 (可以使用简单的 FOLLOW_PATH 到达起飞高度)
		// 使用配置的 WGS84 原点作为任务起点，而不是依赖可能错误的 UAV 位置
		auto global_params = env->getGlobalParamValues();
		WGS84Point current_wgs84_pos(118.80, 32.10, 10.0); // 默认值 (经度, 纬度, 高度)

		if (global_params && global_params->hasParam("wgs84_origin")) {
			try {
				// 直接读取 WGS84Point 类型
				// 使用三参数版本的getValueOrDefault，它会自动从ParamRegistry获取参数定义中的缺省值
				WGS84Point backup_default(118.0, 32.0, 0.0); // 备用缺省值 (经度, 纬度, 高度)
				current_wgs84_pos = global_params->getValueOrDefault<WGS84Point>("wgs84_origin", backup_default, &NSParams::ParamRegistry::getInstance());
				LOG_INFO("辅助函数 [{}]: 使用配置的 WGS84 原点作为任务起点: {}",
					mission_id, current_wgs84_pos.toString());
			} catch (const std::exception& e) {
				LOG_WARN("辅助函数 [{}]: 读取 WGS84 原点失败，使用默认值: {}", mission_id, e.what());
			}
		} else {
			LOG_INFO("辅助函数 [{}]: 使用默认 WGS84 原点作为任务起点: {}",
				mission_id, current_wgs84_pos.toString());
		}

		// 起飞任务：从当前位置垂直上升到指定高度，添加小的水平偏移以确保路径规划器能够工作
		WGS84Point takeoff_wgs84_pos = current_wgs84_pos;
		takeoff_wgs84_pos.latitude += 0.00001;  // 向北偏移约1米，确保起点和终点不完全相同
		takeoff_wgs84_pos.longitude += 0.00001; // 向东偏移约1米
		takeoff_wgs84_pos.altitude = default_alt_msl;

		// 直接使用 WGS84 坐标创建 ControlPoint
		ControlPoint takeoff_cp(takeoff_wgs84_pos, ControlPointType::WAYPOINT_MUST_PASS, takeoff_wgs84_pos.altitude, AltitudeType::ABSOLUTE_ALTITUDE);
		auto takeoff_params = std::make_shared<FollowPathTaskParams>(ControlPointList{ takeoff_cp }, 1, false);
		ITaskStrategyMap takeoff_strategies;
		takeoff_strategies["Speed"] = std::make_shared<SpeedStrategy>(default_speed_mps * 0.5);
		auto task_takeoff = std::make_shared<Task>("Task_Takeoff_1", TaskType::FOLLOW_PATH, target_uav_variant,
			single_mr_req, takeoff_strategies, takeoff_params, "起飞至指定高度");
		mission.addTask(task_takeoff);
		LOG_INFO("任务 [Task_Takeoff_1] (FOLLOW_PATH - 模拟起飞) 添加成功。目标高度: {:.1f}m MSL", takeoff_wgs84_pos.altitude);

		// 任务 2: 前往第一个航点
		// 使用相对于起飞点的偏移创建 WGS84 坐标（简化演示）
		WGS84Point waypoint1_wgs84 = current_wgs84_pos;
		waypoint1_wgs84.latitude += 0.001;   // 向北约100米
		waypoint1_wgs84.longitude += 0.001;  // 向东约100米
		waypoint1_wgs84.altitude = default_alt_msl;

		ControlPoint wp1_cp(waypoint1_wgs84, ControlPointType::WAYPOINT_MUST_PASS, default_alt_msl, AltitudeType::ABSOLUTE_ALTITUDE);
		auto goto_wp1_params = std::make_shared<FollowPathTaskParams>(ControlPointList{ wp1_cp }, 1, false);
		ITaskStrategyMap goto_wp1_strategies;
		goto_wp1_strategies["Speed"] = std::make_shared<SpeedStrategy>(default_speed_mps);
		auto task_goto_wp1 = std::make_shared<Task>("Task_GoToWP1_2", TaskType::FOLLOW_PATH, std::make_shared<PointTarget>(waypoint1_wgs84),
			single_mr_req, goto_wp1_strategies, goto_wp1_params, "前往航点1");
		mission.addTask(task_goto_wp1);
		LOG_INFO("任务 [Task_GoToWP1_2] (FOLLOW_PATH) 添加成功。目标点: ({})", waypoint1_wgs84.toString());

		// 任务 3: 在航点 1 盘旋
		ControlPoint loiter_cp(waypoint1_wgs84, ControlPointType::WAYPOINT_CONSTRAINT, default_alt_msl, AltitudeType::ABSOLUTE_ALTITUDE);
		auto loiter_params = std::make_shared<LoiterPointTaskParams>(loiter_cp, default_loiter_radius_m, default_loiter_time_s, true);
		ITaskStrategyMap loiter_strategies;
		loiter_strategies["Speed"] = std::make_shared<SpeedStrategy>(default_speed_mps * 0.8);
		auto task_loiter = std::make_shared<Task>("Task_LoiterWP1_3", TaskType::LOITER_POINT, std::make_shared<PointTarget>(waypoint1_wgs84),
			single_mr_req, loiter_strategies, loiter_params, "在航点1盘旋");
		mission.addTask(task_loiter);
		LOG_INFO("任务 [Task_LoiterWP1_3] (LOITER_POINT) 添加成功。中心: ({}), 半径: {:.1f}m, 时长: {:.1f}s",
			waypoint1_wgs84.toString(), default_loiter_radius_m, default_loiter_time_s);

		// 任务 4: 区域扫描 (SCAN_AREA)
		double scan_altitude = default_alt_msl + 5.0;
		// 创建扫描区域的 WGS84 坐标顶点（相对于起飞点的偏移）
		std::vector<WGS84Point> area_vertices_wgs84;
		area_vertices_wgs84.emplace_back(current_wgs84_pos.latitude + 0.0005, current_wgs84_pos.longitude + 0.0005, scan_altitude);  // 东北角
		area_vertices_wgs84.emplace_back(current_wgs84_pos.latitude + 0.0015, current_wgs84_pos.longitude + 0.0005, scan_altitude);  // 东南角
		area_vertices_wgs84.emplace_back(current_wgs84_pos.latitude + 0.0015, current_wgs84_pos.longitude + 0.0015, scan_altitude);  // 西南角
		area_vertices_wgs84.emplace_back(current_wgs84_pos.latitude + 0.0005, current_wgs84_pos.longitude + 0.0015, scan_altitude);  // 西北角

		double scan_strip_width = 10.0;
		double scan_overlap = 0.1;
		double scan_angle_deg = 45.0;
		auto scan_params = std::make_shared<ScanAreaTaskParams>(area_vertices_wgs84, scan_strip_width, scan_overlap, scan_angle_deg, std::nullopt);
		ITaskStrategyMap scan_strategies;
		scan_strategies["Speed"] = std::make_shared<SpeedStrategy>(default_speed_mps * 0.9);
		auto area_target = std::make_shared<AreaTarget>(area_vertices_wgs84);
		auto task_scan = std::make_shared<Task>("Task_ScanArea_4", TaskType::SCAN_AREA, area_target,
			single_mr_req, scan_strategies, scan_params, "扫描指定矩形区域");
		mission.addTask(task_scan);
		LOG_INFO("任务 [Task_ScanArea_4] (SCAN_AREA) 添加成功。区域顶点数: {}, 带宽: {:.1f}m, 重叠: {:.1f}, 角度: {:.1f}deg",
			area_vertices_wgs84.size(), scan_strip_width, scan_overlap, scan_angle_deg);

		// 任务 5: 圆柱勘测 (SURVEY_CYLINDER)
		WGS84Point cylinder_center_wgs84 = current_wgs84_pos;
		cylinder_center_wgs84.latitude -= 0.0005;  // 向南偏移
		cylinder_center_wgs84.altitude = default_alt_msl - 20.0;

		double cylinder_radius = 30.0;
		double cylinder_height = 40.0;
		double line_spacing = 5.0;  // 扫描线行间距（米）
		std::optional<double> start_altitude = std::nullopt;  // 起始扫描绝对高度
		ControlPoint cylinder_center_cp(cylinder_center_wgs84, ControlPointType::WAYPOINT_CONSTRAINT, cylinder_center_wgs84.altitude, AltitudeType::ABSOLUTE_ALTITUDE);
		auto survey_cyl_params = std::make_shared<SurveyCylinderTaskParams>(cylinder_center_cp, cylinder_radius, cylinder_height, line_spacing, start_altitude, true);
		ITaskStrategyMap survey_cyl_strategies;
		survey_cyl_strategies["Speed"] = std::make_shared<SpeedStrategy>(default_speed_mps * 0.7);
		auto task_survey_cyl = std::make_shared<Task>("Task_SurveyCylinder_5", TaskType::SURVEY_CYLINDER, std::make_shared<PointTarget>(cylinder_center_wgs84),
			single_mr_req, survey_cyl_strategies, survey_cyl_params, "勘测圆柱区域");
		mission.addTask(task_survey_cyl);
		LOG_INFO("任务 [Task_SurveyCylinder_5] (SURVEY_CYLINDER) 添加成功。中心: ({}), R={:.1f}, H={:.1f}",
			cylinder_center_wgs84.toString(), cylinder_radius, cylinder_height);

		// 任务 6: 多点勘测 (SURVEY_MULTI_POINTS)
		ControlPointList survey_points;
		// 创建多个勘测点的 WGS84 坐标
		WGS84Point survey_point1 = current_wgs84_pos;
		survey_point1.latitude -= 0.0005;
		survey_point1.longitude += 0.0005;
		survey_point1.altitude = default_alt_msl;
		survey_points.emplace_back(survey_point1, ControlPointType::WAYPOINT_MUST_PASS);

		WGS84Point survey_point2 = current_wgs84_pos;
		survey_point2.latitude -= 0.0007;
		survey_point2.longitude += 0.0007;
		survey_point2.altitude = default_alt_msl + 10.0;
		survey_points.emplace_back(survey_point2, ControlPointType::WAYPOINT_MUST_PASS);

		WGS84Point survey_point3 = current_wgs84_pos;
		survey_point3.latitude -= 0.0005;
		survey_point3.longitude += 0.0009;
		survey_point3.altitude = default_alt_msl;
		survey_points.emplace_back(survey_point3, ControlPointType::WAYPOINT_MUST_PASS);

		double survey_dwell_time = 5.0;
		auto survey_multi_params = std::make_shared<SurveyMultiPointsTaskParams>(survey_points);
		ITaskStrategyMap survey_multi_strategies;
		survey_multi_strategies["Speed"] = std::make_shared<SpeedStrategy>(default_speed_mps);
		std::vector<WGS84Point> target_survey_positions;
		target_survey_positions.reserve(survey_points.size());
		for (const auto& cp : survey_points) {
			target_survey_positions.push_back(cp.position);
		}
		auto survey_target = std::make_shared<LineTarget>(target_survey_positions);
		auto task_survey_multi = std::make_shared<Task>("Task_SurveyMultiPoints_6", TaskType::SURVEY_MULTIPOINTS, survey_target,
			single_mr_req, survey_multi_strategies, survey_multi_params, "勘测多个离散点");
		mission.addTask(task_survey_multi);
		LOG_INFO("任务 [Task_SurveyMultiPoints_6] (SURVEY_MULTI_POINTS) 添加成功。勘测点数: {}", survey_points.size());

		// 任务 7: 球面勘测 (SURVEY_SPHERE)
		WGS84Point sphere_center_wgs84 = current_wgs84_pos;
		sphere_center_wgs84.longitude -= 0.0005;  // 向西偏移
		sphere_center_wgs84.altitude = default_alt_msl;

		double sphere_radius = 20.0;
		int sphere_rings = 4; // 水平环数
		int sphere_points_per_ring = 6; // 每环点数

		// 1. 创建 ControlPoint
		ControlPoint sphere_center_cp(sphere_center_wgs84, ControlPointType::WAYPOINT_CONSTRAINT, default_alt_msl, AltitudeType::ABSOLUTE_ALTITUDE);

		// 2. 生成拍摄角度列表 (std::vector<AnglePair>)
		std::vector<AnglePair> photo_angles;
		photo_angles.reserve(sphere_rings * sphere_points_per_ring); // 预分配

		// 简化生成逻辑：均匀分布环和点 (不考虑极点)
		double elevation_step_deg = 180.0 / (sphere_rings + 1); // 将 [-90, 90] 分为 N+1 段
		for (int i = 1; i <= sphere_rings; ++i) { // 从第一个环开始，避开极点
			double elevation_deg = 90.0 - i * elevation_step_deg; // 从上往下
			double azimuth_step_deg = 360.0 / sphere_points_per_ring;
			for (int j = 0; j < sphere_points_per_ring; ++j) {
				double azimuth_deg = j * azimuth_step_deg;
				photo_angles.push_back({ azimuth_deg, elevation_deg });
			}
		}

		// 3. 创建 SurveySphereTaskParams
		auto survey_sphere_params = std::make_shared<SurveySphereTaskParams>(sphere_center_cp, sphere_radius, photo_angles);

		ITaskStrategyMap survey_sphere_strategies;
		survey_sphere_strategies["Speed"] = std::make_shared<SpeedStrategy>(default_speed_mps * 0.8);
		// Task 目标使用 WGS84 坐标
		auto task_survey_sphere = std::make_shared<Task>("Task_SurveySphere_7", TaskType::SURVEY_SPHERE, std::make_shared<PointTarget>(sphere_center_wgs84),
			single_mr_req, survey_sphere_strategies, survey_sphere_params, "勘测球面区域");
		mission.addTask(task_survey_sphere);
		LOG_INFO("任务 [Task_SurveySphere_7] (SURVEY_SPHERE) 添加成功。中心: ({}), R={:.1f}, 计划点数: {}",
			sphere_center_wgs84.toString(), sphere_radius, photo_angles.size());

		// 任务 8: 前往第二个航点 (原任务4)
		WGS84Point waypoint2_wgs84 = current_wgs84_pos;
		waypoint2_wgs84.latitude += 0.0005;   // 向北偏移
		waypoint2_wgs84.longitude -= 0.001;   // 向西偏移
		waypoint2_wgs84.altitude = default_alt_msl + 10.0;

		ControlPoint wp2_cp(waypoint2_wgs84, ControlPointType::WAYPOINT_MUST_PASS, waypoint2_wgs84.altitude, AltitudeType::ABSOLUTE_ALTITUDE);
		auto goto_wp2_params = std::make_shared<FollowPathTaskParams>(ControlPointList{ wp2_cp }, 1, false);
		ITaskStrategyMap goto_wp2_strategies;
		goto_wp2_strategies["Speed"] = std::make_shared<SpeedStrategy>(default_speed_mps);
		auto task_goto_wp2 = std::make_shared<Task>("Task_GoToWP2_8", TaskType::FOLLOW_PATH, std::make_shared<PointTarget>(waypoint2_wgs84),
			single_mr_req, goto_wp2_strategies, goto_wp2_params, "前往航点2");
		mission.addTask(task_goto_wp2);
		LOG_INFO("任务 [Task_GoToWP2_8] (FOLLOW_PATH) 添加成功。目标点: ({})", waypoint2_wgs84.toString());

		// 任务 9: 返航 (原任务5)
		// 返回到起飞点的 WGS84 坐标
		WGS84Point return_wgs84_pos = current_wgs84_pos;  // 使用起飞点的经纬度
		return_wgs84_pos.altitude = default_alt_msl;

		ControlPoint return_cp(return_wgs84_pos, ControlPointType::WAYPOINT_MUST_PASS, return_wgs84_pos.altitude, AltitudeType::ABSOLUTE_ALTITUDE);
		auto return_params = std::make_shared<FollowPathTaskParams>(ControlPointList{ return_cp }, 1, false);
		ITaskStrategyMap return_strategies;
		return_strategies["Speed"] = std::make_shared<SpeedStrategy>(default_speed_mps);
		auto task_return = std::make_shared<Task>("Task_Return_9", TaskType::FOLLOW_PATH, target_uav_variant,
			single_mr_req, return_strategies, return_params, "返航至起飞点上空");
		mission.addTask(task_return);
		LOG_INFO("任务 [Task_Return_9] (FOLLOW_PATH - 模拟返航) 添加成功。目标点: ({})", return_wgs84_pos.toString());

		// 任务 10: 降落 (原任务6)
		// 降落到起飞点的 WGS84 坐标
		WGS84Point land_wgs84_pos = current_wgs84_pos;  // 使用起飞点的经纬度
		land_wgs84_pos.altitude = env->getGlobalParamOrDefault<double>("landing_approach_alt_msl", 5.0);

		ControlPoint land_cp1(land_wgs84_pos, ControlPointType::WAYPOINT_MUST_PASS, land_wgs84_pos.altitude, AltitudeType::ABSOLUTE_ALTITUDE);
		auto land_params = std::make_shared<FollowPathTaskParams>(ControlPointList{ land_cp1 }, 1, false);
		ITaskStrategyMap land_strategies;
		land_strategies["Speed"] = std::make_shared<SpeedStrategy>(default_speed_mps * 0.3);
		auto task_land = std::make_shared<Task>("Task_Land_8", TaskType::FOLLOW_PATH, std::make_shared<ObjectTarget>(target_uav->getId()),
			single_mr_req, land_strategies, land_params, "返回基地并降落");
		mission.addTask(task_land);
		LOG_INFO("任务 [Task_Land_8] (FOLLOW_PATH - 模拟降落) 添加成功。目标点: ({})", land_wgs84_pos.toString());

		LOG_INFO("辅助函数: 任务计划 '{}' 以编程方式创建完成，共 {} 个任务。", mission_id, mission.getTasks().size());
		return mission;
	}

	/**
	 * @brief SingleGridMap 测试函数
	 * @return 测试是否成功
	 */
	bool testSingleGridMap() {
		LOG_INFO("=== SingleGridMap 测试开始 ===");

		try {
			// 1. 测试文件路径（使用terrain子目录）
			fs::path data_dir = NSUtils::getExeDir() / "data";
			fs::path terrain_dir = data_dir / "terrain";
			fs::path geotiff_file = terrain_dir / "nanjing.tif";
			fs::path cache_file = terrain_dir / "nanjing.png";

			LOG_INFO("测试文件路径:");
			LOG_INFO("  原始 GeoTIFF: {}", geotiff_file.string());
			LOG_INFO("  缓存 png: {}", cache_file.string());

			// 检查原始文件是否存在
			if (!fs::exists(geotiff_file)) {
				LOG_WARN("原始 GeoTIFF 文件不存在: {}", geotiff_file.string());
				LOG_WARN("跳过 SingleGridMap 测试");
				return false;
			}

			// 2. 创建 SingleGridMap 实例
			auto single_grid_map = std::make_shared<SingleGridMap>();

			// 3. 初始化（传入全局参数）
			auto environment = Environment::getInstance();
			auto global_params = environment->getGlobalParamValues();
			if (!single_grid_map->initialize(global_params)) {
				LOG_ERROR("SingleGridMap 初始化失败");
				return false;
			}

			LOG_INFO("SingleGridMap 初始化成功");

			// 4. 测试从 GeoTIFF 加载
			LOG_INFO("开始从 GeoTIFF 文件加载地图数据...");
			auto start_time = std::chrono::high_resolution_clock::now();

			// 使用公共方法加载
			bool load_success = single_grid_map->loadFromPng(geotiff_file.generic_string());

			auto end_time = std::chrono::high_resolution_clock::now();
			auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

			if (!load_success) {
				LOG_ERROR("GeoTIFF 加载失败");
				return false;
			}

			LOG_INFO("GeoTIFF 加载成功，耗时: {}ms", duration.count());

			// 5. 显示地图信息
			auto metadata = single_grid_map->getMetadata();
			LOG_INFO("地图信息:");
			LOG_INFO("  地图ID: {}", metadata.map_id);
			LOG_INFO("  地图名称: {}", metadata.map_name);
			LOG_INFO("  分辨率: ({:.3f}, {:.3f})m/像素", metadata.resolution.first, metadata.resolution.second);
			LOG_INFO("  网格尺寸: {}x{}", metadata.grid_size.first, metadata.grid_size.second);
			// MapMetadata 中没有 reference_origin 字段，跳过这个信息

			if (!std::isnan(metadata.min_elevation_point.altitude)) {
				LOG_INFO("  最低点: {}",
					metadata.min_elevation_point.toString());
			}

			if (!std::isnan(metadata.max_elevation_point.altitude)) {
				LOG_INFO("  最高点: {}",
					metadata.max_elevation_point.toString());
			}

			// 构建图层字符串
			std::string layers_str;
			for (size_t i = 0; i < metadata.available_layers.size(); ++i) {
				if (i > 0) layers_str += ", ";
				layers_str += metadata.available_layers[i];
			}
			LOG_INFO("  可用图层: [{}]", layers_str);

			// 6. 测试保存为多波段 TIFF
			LOG_INFO("开始保存为多波段 TIFF 缓存文件...");
			start_time = std::chrono::high_resolution_clock::now();

			bool save_success = single_grid_map->saveToPng(cache_file.string());

			end_time = std::chrono::high_resolution_clock::now();
			duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

			if (!save_success) {
				LOG_ERROR("多波段 TIFF 保存失败");
				return false;
			}

			LOG_INFO("多波段 TIFF 保存成功，耗时: {}ms", duration.count());

			// 检查文件大小
			if (fs::exists(cache_file)) {
				auto file_size = fs::file_size(cache_file);
				LOG_INFO("缓存文件大小: {:.2f} MB", file_size / (1024.0 * 1024.0));
			}

			// 7. 测试快速加载
			LOG_INFO("测试从缓存文件快速加载...");
			auto fast_grid_map = std::make_shared<SingleGridMap>();

			if (!fast_grid_map->initialize(global_params)) {
				LOG_ERROR("快速加载测试: SingleGridMap 初始化失败");
				return false;
			}

			start_time = std::chrono::high_resolution_clock::now();

			// 使用静态工厂方法加载 TIFF
			auto fast_loaded_map = std::make_shared<SingleGridMap>();
			fast_loaded_map->loadFromPng(cache_file.string());
			bool fast_load_success = (fast_loaded_map != nullptr);
			if (fast_load_success) {
				fast_grid_map = fast_loaded_map; // 替换实例
			}

			end_time = std::chrono::high_resolution_clock::now();
			duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

			if (!fast_load_success) {
				LOG_ERROR("缓存文件快速加载失败");
				return false;
			}

			LOG_INFO("缓存文件快速加载成功，耗时: {}ms", duration.count());

			// 验证数据一致性
			auto fast_metadata = fast_grid_map->getMetadata();
			bool metadata_match = (
				fast_metadata.map_id == metadata.map_id &&
				fast_metadata.resolution == metadata.resolution &&
				fast_metadata.grid_size == metadata.grid_size
			);

			if (metadata_match) {
				LOG_INFO("数据一致性验证通过");
			} else {
				LOG_WARN("数据一致性验证失败");
			}

			// 8. 测试高程查询
			LOG_INFO("测试高程查询功能...");

			// 查询几个测试点的高程
			std::vector<std::pair<double, double>> test_points = {
				{118.7, 32.0},   // 南京市中心附近
				{118.8, 32.1},   // 另一个测试点
				{118.6, 31.9}    // 第三个测试点
			};

			for (size_t i = 0; i < test_points.size(); ++i) {
				double lat = test_points[i].first;
				double lon = test_points[i].second;

				try {
					auto elevation_opt = fast_grid_map->getElevation(lat, lon);
					double elevation = elevation_opt.value_or(std::numeric_limits<double>::quiet_NaN());
					if (!std::isnan(elevation)) {
						LOG_INFO("  测试点 {}: ({:.6f}, {:.6f}) -> 高程: {:.2f}m",
							i + 1, lat, lon, elevation);
					} else {
						LOG_INFO("  测试点 {}: ({:.6f}, {:.6f}) -> 无高程数据",
							i + 1, lat, lon);
					}
				} catch (const std::exception& e) {
					LOG_WARN("  测试点 {}: 查询高程时发生异常: {}", i + 1, e.what());
				}
			}

			LOG_INFO("=== SingleGridMap 测试成功完成 ===");
			return true;

		} catch (const std::exception& e) {
			LOG_ERROR("SingleGridMap 测试过程中发生异常: {}", e.what());
			LOG_INFO("=== SingleGridMap 测试失败结束 ===");
			return false;
		}
	}

} // 匿名命名空间结束


// --- 主函数 ---
int main(int argc, char* argv[]) {
#ifdef _WIN32
	original_codepage = GetConsoleOutputCP();
	original_input_codepage = GetConsoleCP();
	SetConsoleOutputCP(CP_UTF8);
	SetConsoleCP(CP_UTF8);
	std::atexit(cleanup_log_on_exit);
#else
	std::atexit(cleanup_log_on_exit);
#endif

	try {
		NSDrones::Logger::init("logs/drones_planning_log.txt", true);
		LOG_INFO("当前工作目录: {}", std::filesystem::current_path().string());
		LOG_INFO("系统日志通过 NSDrones::Logger::init() 初始化成功。日志级别 Console: TRACE, File: TRACE");
		if (spdlog::default_logger()) {
			spdlog::flush_on(spdlog::level::err);
			LOG_TRACE("Main: spdlog::flush_on(spdlog::level::err) 已设置。");
		}
		else {
			LOG_WARN("Main: 默认spdlog记录器未设置，无法配置 flush_on(err)。");
		}
	}
	catch (const std::exception& ex) {
		std::cerr << "通过 NSDrones::Logger::init() 初始化日志失败: " << ex.what() << std::endl;
		cleanup_log_on_exit();
		return 1;
	}

	try { // 主要业务逻辑的 try 块
		// 1. 配置文件路径
		fs::path config_file_path = NSUtils::getExeDir() / "data" / "configfile.json";
		LOG_INFO("加载配置文件路径: {}", config_file_path.string());

		// 2. 创建 ParamRegistry 单例引用
		auto& param_registry_ref = NSDrones::NSParams::ParamRegistry::getInstance();
		LOG_INFO("ParamRegistry 单例已获取 (main)。");

		// 3. 创建 Config 实例
		std::shared_ptr<NSDrones::Config> config_ptr;
		config_ptr = std::make_shared<NSDrones::Config>(config_file_path.string());
		LOG_INFO("Main: Config 实例已创建。");

		// 4. 初始化 Config 系统
		if (!config_ptr->initialize()) {
			LOG_CRITICAL("Main: Config 初始化失败 (Config::initialize() 返回 false)。程序退出。详情请查看 Config 日志。");
			throw std::runtime_error("Config 初始化失败");
		}
		LOG_INFO("Main: Config 初始化成功。");

		// 5. 从 Config 获取 Environment 实例
		std::shared_ptr<Environment> environment = config_ptr->getEnvironment();
		if (!environment) {
			LOG_CRITICAL("Main: 从 Config 获取 Environment 实例失败 (返回 nullptr)。程序退出。");
			throw std::runtime_error("获取 Environment 实例失败");
		}
		LOG_INFO("Main: Environment 实例已从 Config 获取。");

		// 6. 根据配置文件更新日志级别
		LOG_INFO("Main: 开始从全局参数读取并应用日志级别设置...");

		std::string console_log_level_str = "info";  // 默认值
		std::string file_log_level_str = "debug";    // 默认值

		auto global_params_ptr = environment->getGlobalParamValues();
		if (global_params_ptr && global_params_ptr->size() > 0) {
			// 尝试读取控制台日志级别
			try {
				auto console_level_opt = global_params_ptr->getValue<std::string>("logging.console_level");
				if (console_level_opt.has_value()) {
					console_log_level_str = console_level_opt.value();
					LOG_DEBUG("Main: 从配置读取控制台日志级别: {}", console_log_level_str);
				} else {
					LOG_WARN("Main: 配置中未找到控制台日志级别，使用默认值: {}", console_log_level_str);
				}
			}
			catch (const std::exception& e) {
				LOG_WARN("Main: 读取控制台日志级别时发生异常，使用默认值 '{}': {}", console_log_level_str, e.what());
			}

			// 尝试读取文件日志级别
			try {
				auto file_level_opt = global_params_ptr->getValue<std::string>("logging.file_level");
				if (file_level_opt.has_value()) {
					file_log_level_str = file_level_opt.value();
					LOG_DEBUG("Main: 从配置读取文件日志级别: {}", file_log_level_str);
				} else {
					LOG_WARN("Main: 配置中未找到文件日志级别，使用默认值: {}", file_log_level_str);
				}
			}
			catch (const std::exception& e) {
				LOG_WARN("Main: 读取文件日志级别时发生异常，使用默认值 '{}': {}", file_log_level_str, e.what());
			}

			// 转换为小写以匹配spdlog的级别字符串格式
			std::string console_level_lower = console_log_level_str;
			std::string file_level_lower = file_log_level_str;
			std::transform(console_level_lower.begin(), console_level_lower.end(),
			              console_level_lower.begin(), ::tolower);
			std::transform(file_level_lower.begin(), file_level_lower.end(),
			              file_level_lower.begin(), ::tolower);

			// 立即更新日志级别
			LOG_INFO("Main: 准备更新日志级别 - 控制台: {} -> {}, 文件: {} -> {}",
			         console_log_level_str, console_level_lower, file_log_level_str, file_level_lower);

			bool update_success = NSDrones::Logger::set_console_level(console_level_lower);
			update_success = NSDrones::Logger::set_file_level(file_log_level_str);
			if (update_success) {
				LOG_INFO("Main: 日志级别已根据配置文件成功更新！");
				LOG_INFO("Main: 当前实际日志级别 - 控制台: {}, 文件: {}",
				         NSDrones::Logger::get_console_level(), NSDrones::Logger::get_file_level());
			} else {
				LOG_ERROR("Main: 日志级别更新失败，继续使用初始级别");
			}
		} else {
			LOG_WARN("Main: 全局参数为空，继续使用初始日志级别。控制台: {}, 文件: {}",
			         console_log_level_str, file_log_level_str);
		}

		// 7. 批量加载并创建对象实例（分别处理实体对象和算法对象）
		LOG_INFO("Main: 准备从预加载的配置创建并添加对象实例到 Environment...");
		auto instance_names = config_ptr->getPreloadedInstanceNames();
		LOG_INFO("Main: 获取到 {} 个预加载的实例名称。", instance_names.size());

		for (const auto& name : instance_names) {
			LOG_DEBUG("Main: 尝试创建实例 '{}'", name);
			bool instance_processed = false;

			// 获取实例的type_tag来确定正确的类型
			std::string type_tag = config_ptr->getInstanceTypeTag(name);
			if (type_tag.empty()) {
				LOG_WARN("Main: 无法获取实例 '{}' 的type_tag，跳过创建。", name);
				continue;
			}

			LOG_DEBUG("Main: 实例 '{}' 的type_tag为 '{}'", name, type_tag);

			// 根据type_tag确定对象类型并创建
			if (type_tag.find("EntityObject.") == 0) {
				// 实体对象
				auto entity_object = config_ptr->createObjectInstance<EntityObject>(name);
				if (entity_object) {
					environment->addObject(entity_object);
					LOG_DEBUG("Main: 实体对象实例 '{}' 已创建并添加到环境。", name);
					instance_processed = true;
				}
			} else if (type_tag.find("TaskSpace.") == 0) {
				// TaskSpace对象 - 通过Config创建，自动添加到CoordinateManager
				auto taskspace_object = config_ptr->createTaskSpaceInstance(name);
				if (taskspace_object) {
					LOG_DEBUG("Main: TaskSpace对象实例 '{}' 已创建并添加到CoordinateManager。", name);
					instance_processed = true;
				}
			} else if (type_tag.find("AlgorithmObject.TaskAllocator.") == 0) {
				// 任务分配器
				auto task_allocator = config_ptr->createObjectInstance<NSDrones::NSAlgorithm::ITaskAllocator>(name);
				if (task_allocator) {
					environment->setTaskAllocator(task_allocator);
					LOG_DEBUG("Main: 任务分配器实例 '{}' 已创建并设置到环境。", name);
					instance_processed = true;
				}
			}
			else if (type_tag.find("AlgorithmObject.PathPlanner.") == 0) {
				// 路径规划器
				auto path_planner = config_ptr->createObjectInstance<NSDrones::NSAlgorithm::IPathPlanner>(name);
				if (path_planner) {
					environment->setPathPlanner(path_planner);
					LOG_DEBUG("Main: 路径规划器实例 '{}' 已创建并设置到环境。", name);
					instance_processed = true;
				}
			}
			else if (type_tag.find("AlgorithmObject.TrajectoryOptimizer.") == 0) {
				// 轨迹优化器
				auto trajectory_optimizer = config_ptr->createObjectInstance<NSDrones::NSAlgorithm::ITrajectoryOptimizer>(name);
				if (trajectory_optimizer) {
					environment->setTrajectoryOptimizer(trajectory_optimizer);
					LOG_DEBUG("Main: 轨迹优化器实例 '{}' 已创建并设置到环境。", name);
					instance_processed = true;
				}
			}
			else if (type_tag.find("AlgorithmObject.TrajectoryEvaluator.") == 0) {
				// 轨迹评估器
				auto trajectory_evaluator = config_ptr->createObjectInstance<NSDrones::NSAlgorithm::ITrajectoryEvaluator>(name);
				if (trajectory_evaluator) {
					environment->setTrajectoryEvaluator(trajectory_evaluator);
					LOG_DEBUG("Main: 轨迹评估器实例 '{}' 已创建并设置到环境。", name);
					instance_processed = true;
				}
			}
			else if (type_tag.find("TaskPlanner.") == 0) {
				// 任务规划器不需要添加到环境，它们将在 MissionPlanner 创建后单独注册
				LOG_DEBUG("Main: 实例 '{}' (type_tag: '{}') 是任务规划器，将在 MissionPlanner 创建后注册。", name, type_tag);
				instance_processed = true;
			}

			if (!instance_processed) {
				LOG_WARN("Main: 实例 '{}' (type_tag: '{}') 无法识别为任何已知的对象类型，未添加到环境。", name, type_tag);
			}
		}
		LOG_INFO("Main: 所有预加载的对象实例已尝试创建并添加到环境。");

		// 8. SingleGridMap 测试用例（已禁用，避免重复加载地图数据）
		// bool grid_map_test_success = testSingleGridMap();
		// if (grid_map_test_success) {
		//     LOG_INFO("SingleGridMap 测试完成并成功");
		// } else {
		//     LOG_WARN("SingleGridMap 测试完成但有失败项");
		// }
		LOG_INFO("SingleGridMap 测试已跳过（避免重复加载地图数据）");

		// 9. 创建碰撞引擎（使用环境中已创建的CollisionEngine）
		auto collision_engine_ptr = environment->getCollisionEngine();
		if (collision_engine_ptr) {
			LOG_INFO("碰撞引擎 (CollisionEngine) 已从环境获取。");
		}
		else {
			LOG_ERROR("环境中的碰撞引擎未初始化。");
			throw std::runtime_error("环境中的碰撞引擎未初始化");
		}

		// 11. 从 Environment 中获取已设置的算法对象实例
		LOG_INFO("Main: 准备从 Environment 获取算法对象实例...");

		// 获取任务分配器
		auto task_allocator = environment->getTaskAllocator();
		if (!task_allocator) {
			LOG_CRITICAL("Main: 无法从 Environment 获取任务分配器。程序将退出。");
			throw std::runtime_error("任务分配器获取失败");
		}
		LOG_INFO("Main: 任务分配器已成功从 Environment 获取。");

		// 获取路径规划器
		auto path_planner = environment->getPathPlanner();
		if (!path_planner) {
			LOG_CRITICAL("无法从 Environment 获取路径规划器。程序将退出。");
			throw std::runtime_error("路径规划器获取失败");
		}
		LOG_INFO("路径规划器已成功从 Environment 获取。");

		// 获取轨迹优化器
		auto trajectory_optimizer = environment->getTrajectoryOptimizer();
		if (!trajectory_optimizer) {
			LOG_WARN("无法从 Environment 获取轨迹优化器。将使用 nullptr 继续。");
		}
		else {
			LOG_INFO("轨迹优化器已成功从 Environment 获取。");
		}

		// 获取轨迹评估器
		auto trajectory_evaluator = environment->getTrajectoryEvaluator();
		if (!trajectory_evaluator) {
			LOG_WARN("无法从 Environment 获取轨迹评估器。将使用 nullptr 继续。");
		}
		else {
			LOG_INFO("轨迹评估器已成功从 Environment 获取。");
		}

		// 12. 创建 MissionPlanner
		auto mission_planner = std::make_shared<NSDrones::NSPlanning::MissionPlanner>();
		LOG_INFO("任务规划器 (MissionPlanner) 创建成功。");

		// 12.5. 创建并注册任务规划器到 MissionPlanner
		LOG_INFO("Main: 开始创建并注册任务规划器到 MissionPlanner...");

		// 定义任务规划器实例 ID 和对应的任务类型映射
		const std::vector<std::pair<std::string, TaskType>> task_planner_mappings = {
			{"followpath_planner", TaskType::FOLLOW_PATH},
			{"loiterpoint_planner", TaskType::LOITER_POINT},
			{"scanarea_planner", TaskType::SCAN_AREA},
			{"surveycylinder_planner", TaskType::SURVEY_CYLINDER},
			{"surveymultipoints_planner", TaskType::SURVEY_MULTIPOINTS},
			{"surveysphere_planner", TaskType::SURVEY_SPHERE}
		};

		int registered_count = 0;
		for (const auto& mapping : task_planner_mappings) {
			const std::string& instance_id = mapping.first;
			TaskType task_type = mapping.second;

			LOG_DEBUG("Main: 尝试创建并注册任务规划器，实例 ID: '{}', 任务类型: {}", instance_id, NSUtils::enumToString(task_type));
			try {
				auto planner_instance = config_ptr->createObjectInstance<ITaskPlanner>(instance_id);
				if (planner_instance) {
					mission_planner->registerTaskPlanner(task_type, planner_instance);
					LOG_INFO("Main: 成功注册任务规划器: '{}' -> {}", instance_id, NSUtils::enumToString(task_type));
					registered_count++;
				}
				else {
					LOG_ERROR("Main: 创建任务规划器实例 '{}' 失败 (Config 返回 nullptr)。", instance_id);
				}
			}
			catch (const std::exception& e) {
				LOG_ERROR("Main: 创建任务规划器实例 '{}' 时捕获到异常: {}", instance_id, e.what());
			}
			catch (...) {
				LOG_ERROR("Main: 创建任务规划器实例 '{}' 时捕获到未知异常。", instance_id);
			}
		}
		LOG_INFO("Main: 任务规划器注册完成，成功注册 {} 个任务规划器。", registered_count);

		// 13. 将Environment中的UAV对象添加到MissionPlanner的可用UAV列表中
		LOG_INFO("Main: 开始将Environment中的UAV对象添加到MissionPlanner...");
		auto all_uavs = environment->findObjectsByAttribute<NSDrones::NSUav::Uav>("type", "EntityObject.Uav.Multirotor");
		auto fixedwing_uavs = environment->findObjectsByAttribute<NSDrones::NSUav::Uav>("type", "EntityObject.Uav.FixedWing");
		auto vtol_uavs = environment->findObjectsByAttribute<NSDrones::NSUav::Uav>("type", "EntityObject.Uav.VTOL");

		// 合并所有类型的UAV
		all_uavs.insert(all_uavs.end(), fixedwing_uavs.begin(), fixedwing_uavs.end());
		all_uavs.insert(all_uavs.end(), vtol_uavs.begin(), vtol_uavs.end());

		for (const auto& uav : all_uavs) {
			if (uav) {
				// 需要转换为非const指针，因为MissionPlanner需要非const UAV指针
				auto non_const_uav = std::const_pointer_cast<NSDrones::NSUav::Uav>(uav);
				mission_planner->addAvailableUAV(non_const_uav);
				LOG_INFO("Main: UAV '{}' 已添加到MissionPlanner的可用UAV列表。", uav->getId());
			}
		}
		LOG_INFO("Main: 共添加了 {} 个UAV到MissionPlanner。", all_uavs.size());

		// 14. 自动选择首个UAV作为任务目标对象
		std::string target_uav_id;
		std::shared_ptr<NSDrones::NSUav::Uav> target_uav_obj = nullptr;

		// 直接从我们刚才收集的UAV列表中选择第一个
		if (!all_uavs.empty()) {
			target_uav_obj = std::const_pointer_cast<NSDrones::NSUav::Uav>(all_uavs[0]);
			target_uav_id = target_uav_obj->getId();
			LOG_INFO("自动选择UAV '{}' 作为任务目标UAV。", target_uav_id);
		} else {
			LOG_ERROR("未找到可用的UAV实例，无法创建任务。");
			throw std::runtime_error("未找到可用UAV");
		}

		// 15. 创建任务（以编程方式创建示例任务）
		Mission mission("DefaultMission");
		// 直接使用我们已经获取的UAV对象，无需重新创建
		if (!target_uav_obj) { // 确保UAV对象有效
			LOG_ERROR("目标UAV对象为空，无法创建任务。");
			throw std::runtime_error("目标UAV对象为空");
		}
		mission = createProgrammaticMission("ProgrammaticTestMission01", target_uav_obj, config_ptr);
		LOG_INFO("示例任务 '{}' 创建完成，包含 {} 个任务。", mission.getId(), mission.getTasks().size());

		// 16. 调用 MissionPlanner 进行任务规划
		PlanningResult planning_result;
		if (mission_planner && !mission.getTasks().empty()) {
			LOG_INFO("开始使用 MissionPlanner 规划任务 '{}'...", mission.getId());
			planning_result = mission_planner->planMission(mission);
			LOG_INFO("MissionPlanner 已完成任务 '{}' 的规划。", mission.getId());

			if (planning_result.success) {
				LOG_INFO("任务规划成功！");
				const auto all_trajectories = planning_result.getAllCompleteTrajectories();
				LOG_INFO("共规划了 {} 条轨迹。", all_trajectories.size());
				for (const auto& trajectory_pair : all_trajectories) {
					const ObjectID& uav_id = trajectory_pair.first;
					const Trajectory& trajectory = trajectory_pair.second;
					LOG_INFO("轨迹 UAV ID: {}", uav_id);
					LOG_INFO("  轨迹点数量: {}", trajectory.getPoints().size());
					LOG_INFO("  总长度: {:.2f} 米", trajectory.getTotalLength());
					LOG_INFO("  预计总时间: {:.2f} 秒", trajectory.getTotalTime());
					int point_idx = 0;
					for (const auto& point : trajectory.getPoints()) {
						LOG_DEBUG("    轨迹点 {}: 位置 {}, 时间 {:.2f}s, 速度 {:.2f}m/s",
							point_idx++,
							point.position.toString(),
							point.time_stamp,
							point.velocity_ned.norm()
						);
					}
				}
			}
			else {
				LOG_ERROR("任务规划失败。");
			}

			const auto& warnings = planning_result.global_warnings;
			if (!warnings.empty()) {
				LOG_WARN("任务规划期间产生 {} 条告警信息:", warnings.size());
				for (const auto& warning : warnings) {
					LOG_WARN("- 告警类型: {} (代码: {}), 描述: {}",
						NSUtils::enumToString<WarningType>(warning.wtype),
						static_cast<int>(warning.wtype),
						warning.description);
					if (NSDrones::NSUtils::isValidObjectID(warning.related_uav_id)) {
						LOG_WARN("  相关对象 ID: {}", warning.related_uav_id);
					}
					if (NSDrones::NSUtils::isValidObjectID(warning.related_task_id)) {
						LOG_WARN("  相关任务 ID: {}", warning.related_task_id);
					}
					if (NSDrones::NSUtils::isValidObjectID(warning.related_zone_id)) {
						LOG_WARN("  相关区域 ID: {}", warning.related_zone_id);
					}
				}
			}
			else {
				LOG_INFO("任务规划期间无告警信息。");
			}
		}
		else {
			if (!mission_planner) {
				LOG_ERROR("MissionPlanner 未初始化，无法执行任务规划。");
			}
			if (mission.getTasks().empty()) {
				LOG_WARN("任务列表为空，不执行规划。");
			}
		}

		LOG_INFO("Main: 程序业务逻辑正常完成。");
		cleanup_log_on_exit();
		return 0;

	}
	catch (const std::exception& e) {
		LOG_CRITICAL("Main: 捕获到未处理的 std::exception: {}。程序将终止执行。", e.what());
		cleanup_log_on_exit();
		return 1;
	}
	catch (...) {
		LOG_CRITICAL("Main: 捕获到未知的异常类型。程序将终止执行。");
		cleanup_log_on_exit();
		return 1;
	}
}
