// include/utils/file_utils.h
#pragma once

#include <string>
#include <filesystem> 
#include <optional>
#include "core/types.h" 
#include <nlohmann/json.hpp>

namespace NSDrones {
	namespace NSUtils {
		namespace fs = std::filesystem; // 文件系统命名空间别名

		/**
		 * @brief 从文件加载 JSON 对象。
		 * @param json_file_path JSON 文件的路径。
		 * @return 解析后的 nlohmann::json 对象。
		 * @throws DroneException 如果文件不存在、无法读取或 JSON 解析失败。
		 */
		nlohmann::json loadJsonFile(const fs::path& json_file_path);

		/**
		 * @brief 检查文件是否存在且是常规文件。
		 * @param filepath 文件路径。
		 * @return 如果文件存在且是常规文件，返回 true。
		 */
		bool isFileExists(const fs::path& filepath);

		/**
		 * @brief 检查目录是否存在。
		 * @param dirpath 目录路径。
		 * @return 如果目录存在，返回 true。
		 */
		bool isDirExists(const fs::path& dirpath);

		/**
		 * @brief 读取文件内容到字符串。
		 * @param filepath 文件路径。
		 * @return 包含文件内容的 optional<string>，如果读取失败则返回 nullopt。
		 */
		std::optional<std::string> readFileToString(const fs::path& filepath);

		/**
		 * @brief 获取文件名（不含扩展名）。
		 * @param filepath 文件路径。
		 * @return 不含扩展名的文件名。
		 */
		std::string getFileNameWithoutExtension(const fs::path& filepath);

		/**
		 * @brief 安全地拼接路径组件。
		 * @param base 基础路径。
		 * @param part 要附加的部分。
		 * @return 拼接后的路径对象。
		 */
		fs::path joinPaths(const fs::path& base, const fs::path& part);

		/**
		 * @brief 获取当前可执行文件所在的目录。
		 * @return 可执行文件所在目录的路径，如果失败则返回空路径。
		 */
		fs::path getExePath();

		/**
		 * @brief 获取当前可执行文件所在的目录。
		 * @return 可执行文件所在目录的路径，如果失败则返回空路径。
		 */
		fs::path getExeDir();

		/**
		 * @brief 尝试查找应用程序的基础数据路径（通常是包含 'data' 目录的父目录或项目根目录）。
		 *
		 * 使用启发式方法查找，例如检查可执行文件目录的上级目录是否包含 'data' 子目录，
		 * 或者直接返回可执行文件目录或当前工作目录作为备选。
		 * 这个函数提供一个起点，实际的基础路径可能需要结合配置文件进一步确定。
		 *
		 * @return 推测的基础数据路径，如果无法确定则返回当前工作目录。
		 */
		fs::path getDataPath();

		/**
		 * @brief 解析相对于指定基础路径的文件或目录路径。
		 *
		 * 如果 relative_or_absolute_path 是绝对路径，则直接规范化并返回。
		 * 否则，将其与 base_path 组合，然后规范化。
		 *
		 * @param base_path 用于解析相对路径的基础绝对路径 (不应为空)。
		 * @param relative_or_absolute_path 要解析的路径（可以是相对的或绝对的）。
		 * @return 计算并规范化后的绝对路径。如果 base_path 无效或解析失败，可能返回空路径。
		 */
		fs::path resolvePath(const fs::path& base_path, const fs::path& relative_or_absolute_path);

		/**
		 * @brief 在指定目录中查找具有特定扩展名的文件。
		 *
		 * @param directory_path 要搜索的目录路径。
		 * @param extensions 要查找的文件扩展名列表 (例如 {".txt", ".log"})。扩展名应包含点。
		 * @param recursive 如果为 true，则递归搜索子目录。
		 * @param out_file_paths (输出参数) 找到的文件路径将被添加到此向量中。
		 * @return 如果搜索操作成功（即使没有找到文件），则返回 true。
		 *         如果目录不存在或不是目录，或者发生其他文件系统错误，则返回 false。
		 */
		bool findFilesInDirectory(
			const fs::path& directory_path,
			const std::vector<std::string>& extensions,
			bool recursive,
			std::vector<fs::path>& out_file_paths);

	} // namespace NSUtils
} // namespace NSDrones