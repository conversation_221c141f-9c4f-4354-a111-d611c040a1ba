// include/environment/storage/object_storage.tpp
#pragma once

// 首先包含基础类型定义
#include "core/entity_object.h"
#include "utils/logging.h"

// 然后包含object_storage.h
#include "environment/storage/object_storage.h"

// 最后包含派生类定义
#include "environment/entities/zone.h"
#include "environment/entities/obstacle.h"
#include "uav/uav.h"

namespace NSDrones {
	namespace NSEnvironment {
		using namespace ::NSDrones::NSCore;
		using namespace ::NSDrones::NSUtils;

		// ObjectTraits特化的实现
		inline bool ObjectTraits<EntityObject>::isMovable(const EntityObject& obj) {
			return obj.getMovementStrategy() != nullptr;
		}

		inline bool ObjectTraits<Zone>::isMovable(const Zone& obj) {
			return obj.getMovementStrategy() != nullptr;
		}

		inline ZoneType ObjectTraits<Zone>::getZoneType(const Zone& obj) {
			return obj.getType();
		}

		inline bool ObjectTraits<Obstacle>::isMovable(const Obstacle& obj) {
			return obj.getMovementStrategy() != nullptr;
		}

		inline bool ObjectTraits<NSUav::Uav>::isMovable(const NSUav::Uav& obj) {
			return obj.getMovementStrategy() != nullptr;
		}

		// ObjectStorage模板方法的实现
		template<typename ObjectContainer>
		template<typename T>
		bool ObjectStorage<ObjectContainer>::addObject(std::shared_ptr<T> obj) {
			if (!obj) {
				LOG_ERROR("对象存储: 尝试添加空对象指针");
				return false;
			}

			const ObjectID& id = obj->getId();

			// 添加到主存储
			{
				std::unique_lock lock(objects_mutex_);

				// 检查对象是否已存在
				if (objects_.find(id) != objects_.end()) {
					LOG_WARN("对象存储: 对象ID {} 已存在，将被替换", id);
				}

				objects_[id] = obj;
			}

			// 更新类型索引
			{
				std::unique_lock lock(type_index_mutex_);
				type_index_[ObjectTraits<T>::type_name].insert(id);
			}

			// 更新动静态索引
			{
				std::unique_lock lock(mobility_index_mutex_);

				if (ObjectTraits<T>::isMovable(*obj)) {
					static_object_ids_.erase(id);
					movable_object_ids_.insert(id);
				}
				else {
					movable_object_ids_.erase(id);
					static_object_ids_.insert(id);
				}
			}

			// 更新特定类型索引
			updateSpecialTypeIndices(obj);

			LOG_INFO("对象存储: 成功添加类型为 '{}' 的对象 '{}' (ID: {})",
				ObjectTraits<T>::type_name, obj->getName(), id);

			return true;
		}

		template<typename ObjectContainer>
		bool ObjectStorage<ObjectContainer>::removeObject(const ObjectID& id) {
			std::shared_ptr<EntityObject> obj;

			// 从主存储获取对象
			{
				std::unique_lock lock(objects_mutex_);
				auto it = objects_.find(id);
				if (it == objects_.end()) {
					LOG_WARN("对象存储: 尝试删除不存在的对象ID {}", id);
					return false;
				}

				obj = it->second;
				objects_.erase(it);
			}

			// 从各索引中移除
			{
				std::unique_lock type_lock(type_index_mutex_);
				for (auto& [type, ids] : type_index_) {
					ids.erase(id);
				}
			}

			{
				std::unique_lock mobility_lock(mobility_index_mutex_);
				movable_object_ids_.erase(id);
				static_object_ids_.erase(id);
			}

			{
				std::unique_lock special_lock(special_type_mutex_);
				zone_ids_.erase(id);

				for (auto& [type, ids] : zone_type_index_) {
					ids.erase(id);
				}

				obstacle_ids_.erase(id);
			}

			LOG_INFO("对象存储: 成功移除对象 '{}' (ID: {})", obj->getName(), id);

			return true;
		}

		template<typename ObjectContainer>
		template<typename T>
		std::shared_ptr<T> ObjectStorage<ObjectContainer>::getObject(const ObjectID& id) const {
			std::shared_lock lock(objects_mutex_);

			auto it = objects_.find(id);
			if (it == objects_.end()) {
				return nullptr;
			}

			if constexpr (std::is_same_v<T, EntityObject>) {
				return it->second;
			}
			else {
				return std::dynamic_pointer_cast<T>(it->second);
			}
		}

		template<typename ObjectContainer>
		template<typename T>
		std::vector<std::shared_ptr<T>> ObjectStorage<ObjectContainer>::getObjectsByType() const {
			std::vector<std::shared_ptr<T>> result;
			std::unordered_set<ObjectID> ids;

			// 获取类型索引中的ID
			{
				std::shared_lock lock(type_index_mutex_);
				auto it = type_index_.find(ObjectTraits<T>::type_name);
				if (it != type_index_.end()) {
					ids = it->second; // 复制ID集
				}
			}

			// 将ID转换为对象指针
			if (!ids.empty()) {
				result.reserve(ids.size());

				std::shared_lock lock(objects_mutex_);
				for (const auto& id : ids) {
					auto it = objects_.find(id);
					if (it != objects_.end()) {
						if constexpr (std::is_same_v<T, EntityObject>) {
							result.push_back(it->second);
						}
						else if constexpr (std::is_base_of_v<EntityObject, T>) {
							auto typed_obj = std::dynamic_pointer_cast<T>(it->second);
							if (typed_obj) {
								result.push_back(typed_obj);
							}
						}
						else {
							auto typed_obj = std::dynamic_pointer_cast<T>(it->second);
							if (typed_obj) {
								result.push_back(typed_obj);
							}
						}
					}
				}
			}

			return result;
		}

		template<typename ObjectContainer>
		std::vector<std::shared_ptr<EntityObject>> ObjectStorage<ObjectContainer>::getMovableObjects() const {
			return getObjectsByIds(movable_object_ids_);
		}

		template<typename ObjectContainer>
		std::vector<std::shared_ptr<EntityObject>> ObjectStorage<ObjectContainer>::getStaticObjects() const {
			return getObjectsByIds(static_object_ids_);
		}

		template<typename ObjectContainer>
		std::vector<std::shared_ptr<Zone>> ObjectStorage<ObjectContainer>::getZones() const {
			std::vector<std::shared_ptr<Zone>> result;
			std::unordered_set<ObjectID> ids;

			{
				std::shared_lock lock(special_type_mutex_);
				ids = zone_ids_; // 复制ID集
			}

			if (!ids.empty()) {
				result.reserve(ids.size());

				std::shared_lock lock(objects_mutex_);
				for (const auto& id : ids) {
					auto it = objects_.find(id);
					if (it != objects_.end()) {
						auto zone = std::dynamic_pointer_cast<Zone>(it->second);
						if (zone) {
							result.push_back(zone);
						}
					}
				}
			}

			return result;
		}

		template<typename ObjectContainer>
		std::vector<std::shared_ptr<Zone>> ObjectStorage<ObjectContainer>::getZonesByType(ZoneType type) const {
			std::vector<std::shared_ptr<Zone>> result;
			std::unordered_set<ObjectID> ids;

			{
				std::shared_lock lock(special_type_mutex_);
				auto it = zone_type_index_.find(type);
				if (it != zone_type_index_.end()) {
					ids = it->second; // 复制ID集
				}
			}

			if (!ids.empty()) {
				result.reserve(ids.size());

				std::shared_lock lock(objects_mutex_);
				for (const auto& id : ids) {
					auto it = objects_.find(id);
					if (it != objects_.end()) {
						auto zone = std::dynamic_pointer_cast<Zone>(it->second);
						if (zone) {
							result.push_back(zone);
						}
					}
				}
			}

			return result;
		}

		template<typename ObjectContainer>
		std::vector<std::shared_ptr<Obstacle>> ObjectStorage<ObjectContainer>::getObstacles() const {
			std::vector<std::shared_ptr<Obstacle>> result;
			std::unordered_set<ObjectID> ids;

			{
				std::shared_lock lock(special_type_mutex_);
				ids = obstacle_ids_; // 复制ID集
			}

			if (!ids.empty()) {
				result.reserve(ids.size());

				std::shared_lock lock(objects_mutex_);
				for (const auto& id : ids) {
					auto it = objects_.find(id);
					if (it != objects_.end()) {
						auto obstacle = std::dynamic_pointer_cast<Obstacle>(it->second);
						if (obstacle) {
							result.push_back(obstacle);
						}
					}
				}
			}

			return result;
		}

		template<typename ObjectContainer>
		size_t ObjectStorage<ObjectContainer>::getObjectCount() const {
			std::shared_lock lock(objects_mutex_);
			return objects_.size();
		}

		template<typename ObjectContainer>
		bool ObjectStorage<ObjectContainer>::isObjectDynamic(const ObjectID& id) const {
			std::shared_lock lock(mobility_index_mutex_);
			return movable_object_ids_.find(id) != movable_object_ids_.end();
		}

		template<typename ObjectContainer>
		std::vector<std::shared_ptr<EntityObject>> ObjectStorage<ObjectContainer>::getAllObjects() const {
			std::vector<std::shared_ptr<EntityObject>> result;

			std::shared_lock lock(objects_mutex_);
			result.reserve(objects_.size());

			for (const auto& [id, obj] : objects_) {
				if (obj) {
					result.push_back(obj);
				}
			}

			return result;
		}

		template<typename ObjectContainer>
		size_t ObjectStorage<ObjectContainer>::getObjectCountByType(const std::string& type_name) const {
			std::shared_lock lock(type_index_mutex_);
			auto it = type_index_.find(type_name);
			if (it != type_index_.end()) {
				return it->second.size();
			}
			return 0;
		}

		template<typename ObjectContainer>
		void ObjectStorage<ObjectContainer>::clear() {
			{
				std::unique_lock obj_lock(objects_mutex_);
				objects_.clear();
			}

			{
				std::unique_lock type_lock(type_index_mutex_);
				type_index_.clear();
			}

			{
				std::unique_lock mobility_lock(mobility_index_mutex_);
				movable_object_ids_.clear();
				static_object_ids_.clear();
			}

			{
				std::unique_lock special_lock(special_type_mutex_);
				zone_ids_.clear();
				zone_type_index_.clear();
				obstacle_ids_.clear();
			}

			LOG_INFO("对象存储: 已清空所有对象和索引");
		}

		template<typename ObjectContainer>
		std::vector<std::shared_ptr<EntityObject>> ObjectStorage<ObjectContainer>::getObjectsByIds(const std::unordered_set<ObjectID>& ids) const {
			std::vector<std::shared_ptr<EntityObject>> result;

			if (ids.empty()) {
				return result;
			}

			result.reserve(ids.size());

			std::shared_lock lock(objects_mutex_);
			for (const auto& id : ids) {
				auto it = objects_.find(id);
				if (it != objects_.end()) {
					result.push_back(it->second);
				}
			}

			return result;
		}

		template<typename ObjectContainer>
		template<typename T>
		void ObjectStorage<ObjectContainer>::updateSpecialTypeIndices(std::shared_ptr<T> obj) {
			if (!obj) return;

			const ObjectID& id = obj->getId();

			if constexpr (std::is_same_v<T, Zone> || std::is_base_of_v<Zone, T>) {
				std::unique_lock lock(special_type_mutex_);
				zone_ids_.insert(id);

				auto zone = std::static_pointer_cast<Zone>(obj);
				zone_type_index_[zone->getType()].insert(id);
				return;
			}
			else if constexpr (std::is_same_v<T, Obstacle> || std::is_base_of_v<Obstacle, T>) {
				std::unique_lock lock(special_type_mutex_);
				obstacle_ids_.insert(id);
				return;
			}
			else if constexpr (std::is_same_v<T, EntityObject> || std::is_base_of_v<EntityObject, T>) {
				// 尝试转换为特殊类型并更新相应索引
				auto zone = std::dynamic_pointer_cast<Zone>(obj);
				if (zone) {
					std::unique_lock lock(special_type_mutex_);
					zone_ids_.insert(id);
					zone_type_index_[zone->getType()].insert(id);
					return;
				}

				auto obstacle = std::dynamic_pointer_cast<Obstacle>(obj);
				if (obstacle) {
					std::unique_lock lock(special_type_mutex_);
					obstacle_ids_.insert(id);
					return;
				}
			}
		}

	// 显式实例化模板类
	template class ObjectStorage<std::unordered_map<NSCore::ObjectID, std::shared_ptr<NSCore::EntityObject>>>;

	// 显式实例化常用的模板方法
	template std::shared_ptr<NSCore::BaseObject> ObjectStorage<std::unordered_map<NSCore::ObjectID, std::shared_ptr<NSCore::EntityObject>>>::getObject<NSCore::BaseObject>(const NSCore::ObjectID& id) const;
	template std::shared_ptr<NSCore::EntityObject> ObjectStorage<std::unordered_map<NSCore::ObjectID, std::shared_ptr<NSCore::EntityObject>>>::getObject<NSCore::EntityObject>(const NSCore::ObjectID& id) const;
	template std::shared_ptr<NSUav::Uav> ObjectStorage<std::unordered_map<NSCore::ObjectID, std::shared_ptr<NSCore::EntityObject>>>::getObject<NSUav::Uav>(const NSCore::ObjectID& id) const;
	template bool ObjectStorage<std::unordered_map<NSCore::ObjectID, std::shared_ptr<NSCore::EntityObject>>>::addObject<NSCore::EntityObject>(std::shared_ptr<NSCore::EntityObject> obj);
	template bool ObjectStorage<std::unordered_map<NSCore::ObjectID, std::shared_ptr<NSCore::EntityObject>>>::addObject<Zone>(std::shared_ptr<Zone> obj);
	template bool ObjectStorage<std::unordered_map<NSCore::ObjectID, std::shared_ptr<NSCore::EntityObject>>>::addObject<Obstacle>(std::shared_ptr<Obstacle> obj);
	template bool ObjectStorage<std::unordered_map<NSCore::ObjectID, std::shared_ptr<NSCore::EntityObject>>>::addObject<NSUav::Uav>(std::shared_ptr<NSUav::Uav> obj);

	} // namespace NSEnvironment
} // namespace NSDrones

