// src/planning/task_planners/task_planner_loiterpoint.cpp
#include "planning/task_planners/loiterpoint_taskplanner.h"
#include "planning/planning_types.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/task_strategies.h"
#include "mission/control_point.h"
#include "uav/uav.h"
#include "environment/environment.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"
#include "utils/coordinate_converter.h"
#include <GeographicLib/Geodesic.hpp>
#include <cmath>
#include <vector>
#include <map>
#include <string>
#include <memory>
#include <algorithm>
#include <numeric>
#include <utility>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace NSDrones {
	namespace NSPlanning {

		LoiterPointTaskPlanner::LoiterPointTaskPlanner()
			: ITaskPlanner() {}

		bool LoiterPointTaskPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params,
												const nlohmann::json& raw_config) {
			LOG_DEBUG("盘旋任务规划器: 开始初始化");

			if (params) {
				// 加载盘旋相关参数
				default_points_per_circle_ = params->getValueOrDefault<int>("loiter.points_per_circle", 36);
				min_loiter_radius_ = params->getValueOrDefault<double>("loiter.min_radius", 10.0);

				LOG_INFO("盘旋任务规划器: 参数加载完成 - 每圈航点数:{}, 最小半径:{:.1f}m",
						default_points_per_circle_, min_loiter_radius_);
			} else {
				LOG_WARN("盘旋任务规划器: 参数对象为空，使用默认值");
			}

			LOG_DEBUG("盘旋任务规划器: 初始化完成");
			return true;
		}

		/**
		 * @brief 规划单机定点盘旋任务（重构后的接口）
		 */
		SingleTaskPlanningResult LoiterPointTaskPlanner::planSingleTask(
			const SingleTaskPlanningRequest& request) {

			LOG_INFO("[LoiterPointTaskPlanner] 开始规划子任务 [{}]，UAV [{}]",
				request.assignment.sub_task_id, request.assignment.assigned_uav_id);

			SingleTaskPlanningResult result;
			result.success = false;
			result.sub_task_id = request.assignment.sub_task_id;
			result.uav_id = request.assignment.assigned_uav_id;

			try {
				// === 第1步：验证输入（使用公共方法） ===
				if (!validateBasicTaskRequest(request, result, NSCore::TaskType::LOITER_POINT)) {
					return result;
				}

				// === 第2步：获取任务参数 ===
				auto params_ptr = request.original_task->getTaskParameters<NSMission::LoiterPointTaskParams>();
				if (!params_ptr) {
					result.message = "无法获取 LoiterPoint 任务参数";
					LOG_ERROR("[LoiterPointTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第3步：获取起始状态 ===
				NSUav::UavState start_state;
				if (!getUavStartState(request.uav, start_state)) {
					result.message = "无法获取无人机起始状态";
					LOG_ERROR("[LoiterPointTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第4步：执行盘旋规划 ===
				result = planLoiterTrajectory(request, *params_ptr, start_state);

			} catch (const std::exception& e) {
				result.success = false;
				result.message = "规划异常: " + std::string(e.what());
				LOG_ERROR("[LoiterPointTaskPlanner] 子任务 [{}] 规划异常: {}", result.sub_task_id, e.what());
			}

			LOG_INFO("[LoiterPointTaskPlanner] 子任务 [{}] 规划完成，成功: {}",
				result.sub_task_id, result.success);
			return result;
		}

		/**
		 * @brief 规划盘旋轨迹的核心实现
		 */
		SingleTaskPlanningResult LoiterPointTaskPlanner::planLoiterTrajectory(
			const SingleTaskPlanningRequest& request,
			const NSMission::LoiterPointTaskParams& params,
			const NSUav::UavState& start_state) {

			SingleTaskPlanningResult result;
			result.success = false;
			result.sub_task_id = request.assignment.sub_task_id;
			result.uav_id = request.assignment.assigned_uav_id;

			try {
				LOG_DEBUG("[LoiterPointTaskPlanner] 开始规划盘旋轨迹，半径: {:.1f}m，持续时间: {:.1f}s",
					params.radius, params.duration_seconds);

				// === 第1步：验证参数 ===
				if (params.radius < min_loiter_radius_) {
					result.message = "盘旋半径(" + std::to_string(params.radius) +
						")小于最小值(" + std::to_string(min_loiter_radius_) + ")";
					LOG_ERROR("[LoiterPointTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				if (params.duration_seconds < 0.0) {
					result.message = "盘旋持续时间不能为负值";
					LOG_ERROR("[LoiterPointTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第2步：获取盘旋中心位置 ===
				NSCore::WGS84Point loiter_center = params.center_point.position;
				double radius = std::max(params.radius, min_loiter_radius_);
				double duration = std::max(params.duration_seconds, 0.0);
				bool clockwise = params.clockwise;

				LOG_DEBUG("[LoiterPointTaskPlanner] 盘旋参数 - 中心:{}, 半径:{:.1f}m, 持续时间:{:.1f}s, 方向:{}",
					loiter_center.toString(), radius, duration, clockwise ? "顺时针" : "逆时针");

				// === 第3步：生成盘旋路径点 ===
				std::vector<NSCore::WGS84Point> loiter_waypoints = generateLoiterWaypoints(
					loiter_center, radius, default_points_per_circle_, clockwise);

				if (loiter_waypoints.empty()) {
					result.message = "盘旋路径点生成失败";
					LOG_ERROR("[LoiterPointTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第4步：计算飞行参数 ===
				double desired_speed = 8.0; // 默认盘旋速度

				// 校验和调整飞行速度（使用公共方法）
				double adjusted_speed;
				if (!validateAndAdjustFlightSpeed(request.uav, start_state, desired_speed, adjusted_speed, result)) {
					return result;
				}
				desired_speed = std::min(adjusted_speed, adjusted_speed * 0.7); // 盘旋时使用较低速度

				// 计算需要的圈数
				double circumference = NSCore::Constants::TWO_PI * radius;
				double time_per_loop = circumference / std::max(desired_speed, NSCore::Constants::VELOCITY_EPSILON);
				int num_loops = std::max(1, static_cast<int>(std::ceil(duration / time_per_loop)));

				LOG_DEBUG("[LoiterPointTaskPlanner] 飞行参数 - 速度:{:.1f}m/s, 每圈时间:{:.1f}s, 圈数:{}",
					desired_speed, time_per_loop, num_loops);

				// === 第5步：构建完整几何路径 ===
				std::vector<NSCore::WGS84Point> geometric_path;
				geometric_path.reserve(1 + loiter_waypoints.size() * num_loops);

				// 添加起始点
				geometric_path.push_back(start_state.position);

				// 添加多圈盘旋路径
				for (int i = 0; i < num_loops; ++i) {
					geometric_path.insert(geometric_path.end(), loiter_waypoints.begin(), loiter_waypoints.end());
				}

				LOG_DEBUG("[LoiterPointTaskPlanner] 构建几何路径完成，总点数: {}", geometric_path.size());

				// === 第6步：生成轨迹 ===
				Trajectory trajectory;
				if (!smoothAndTimeParameterize(geometric_path, request.uav, start_state,
					desired_speed, trajectory, &result)) {
					result.message = "轨迹生成失败";
					LOG_ERROR("[LoiterPointTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第7步：应用避障约束（使用公共方法） ===
				if (!applyAvoidanceConstraints(trajectory, request.avoidance_constraints, result)) {
					result.message = "应用避障约束失败";
					LOG_ERROR("[LoiterPointTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第8步：验证轨迹（使用公共方法） ===
				if (!validateGeneratedTrajectory(trajectory, result)) {
					return result;
				}

				// === 第9步：设置结果 ===
				result.trajectory = std::move(trajectory);
				result.success = true;
				result.message = "盘旋规划成功";

				LOG_INFO("[LoiterPointTaskPlanner] 子任务 [{}] 规划成功，轨迹段数: {}，盘旋圈数: {}",
					result.sub_task_id, result.trajectory.size(), num_loops);

			} catch (const std::exception& e) {
				result.success = false;
				result.message = "规划异常: " + std::string(e.what());
				LOG_ERROR("[LoiterPointTaskPlanner] 子任务 [{}] 规划异常: {}", result.sub_task_id, e.what());
			}

			return result;
		}



		/**
		 * @brief 生成盘旋路径点
		 */
		std::vector<NSCore::WGS84Point> LoiterPointTaskPlanner::generateLoiterWaypoints(
			const NSCore::WGS84Point& center,
			double radius,
			int num_points,
			bool clockwise) const {

			LOG_DEBUG("[LoiterPointTaskPlanner] 生成盘旋路径点 - 中心:{}, 半径:{:.1f}m, 点数:{}, 方向:{}",
				center.toString(), radius, num_points, clockwise ? "顺时针" : "逆时针");

			std::vector<NSCore::WGS84Point> waypoints;

			try {
				// 参数验证
				if (radius <= 0.0) {
					LOG_ERROR("[LoiterPointTaskPlanner] 无效的盘旋半径: {:.1f}m", radius);
					return waypoints;
				}

				if (num_points < 3) {
					LOG_ERROR("[LoiterPointTaskPlanner] 盘旋点数过少: {}, 至少需要3个点", num_points);
					return waypoints;
				}

				// 确保点数合理
				num_points = std::max(3, std::min(num_points, 360)); // 限制在3-360之间

				// 计算角度步长
				double angle_step = 2.0 * M_PI / num_points;
				if (!clockwise) {
					angle_step = -angle_step; // 逆时针方向
				}

				// 生成圆形路径点
				for (int i = 0; i < num_points; ++i) {
					double angle = i * angle_step;

					// 计算相对于中心的偏移量（米）
					double offset_east = radius * std::cos(angle);   // 东向偏移
					double offset_north = radius * std::sin(angle);  // 北向偏移

					// 使用地球半径近似计算经纬度偏移
					const double EARTH_RADIUS = 6378137.0; // WGS84椭球长半轴（米）

					// 纬度偏移（北向）
					double lat_offset = offset_north / EARTH_RADIUS * (180.0 / M_PI);

					// 经度偏移（东向，需要考虑纬度的余弦修正）
					double cos_lat = std::cos(center.latitude * M_PI / 180.0);
					double lon_offset = offset_east / (EARTH_RADIUS * cos_lat) * (180.0 / M_PI);

					// 计算新的经纬度
					NSCore::WGS84Point waypoint;
					waypoint.latitude = center.latitude + lat_offset;
					waypoint.longitude = center.longitude + lon_offset;
					waypoint.altitude = center.altitude; // 保持相同高度

					waypoints.push_back(waypoint);

					LOG_TRACE("[LoiterPointTaskPlanner] 生成路径点 {}: 角度={:.1f}°, 位置={}",
						i, angle * 180.0 / M_PI, waypoint.toString());
				}

				// 添加第一个点作为闭合点（确保形成完整的圆）
				if (!waypoints.empty()) {
					waypoints.push_back(waypoints[0]);
				}

				LOG_INFO("[LoiterPointTaskPlanner] 成功生成 {} 个盘旋路径点（包含闭合点）", waypoints.size());

			} catch (const std::exception& e) {
				LOG_ERROR("[LoiterPointTaskPlanner] 生成盘旋路径点异常: {}", e.what());
				waypoints.clear();
			}

			return waypoints;
		}
	} // namespace NSPlanning
} // namespace NSDrones