// include/algorithm/evaluator/itrajectory_evaluator.h
#pragma once

#include "core/types.h"        
#include "planning/planning_types.h" 
#include "uav/uav_fwd.h"           
#include "uav/uav_types.h"         
#include <vector>
#include <map>
#include <memory>               
#include "mission/mission_fwd.h" 
#include <string>
#include <optional>
#include "nlohmann/json.hpp" 

namespace NSDrones { 
	namespace NSUav { class Uav; class UavState; class IEnergyModel; }
	namespace NSParams { class ParamValues; }
	namespace NSMission { class Mission; }
	namespace NSPlanning{ using RouteID = std::string; }
}

namespace NSDrones {
	namespace NSAlgorithm {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSPlanning;

		/**
		 * @class ITrajectoryEvaluator
		 * @brief 重构后的轨迹评估器接口
		 *
		 * 使用统一数据结构，支持完整的轨迹评估功能
		 */
		class ITrajectoryEvaluator {
		public:
			ITrajectoryEvaluator() = default;
			virtual ~ITrajectoryEvaluator() = default;

			ITrajectoryEvaluator(const ITrajectoryEvaluator&) = default;
			ITrajectoryEvaluator& operator=(const ITrajectoryEvaluator&) = default;
			ITrajectoryEvaluator(ITrajectoryEvaluator&&) = default;
			ITrajectoryEvaluator& operator=(ITrajectoryEvaluator&&) = default;

		public:
			/**
			 * @brief 评估轨迹（使用统一数据结构）
			 * @param request 轨迹评估请求
			 * @return 轨迹评估指标
			 */
			virtual TrajectoryEvaluationMetrics evaluate(const TrajectoryEvaluationRequest& request) const = 0;

			/**
			 * @brief 批量评估轨迹
			 * @param requests 多个评估请求
			 * @return 评估结果映射
			 */
			virtual std::map<ObjectID, TrajectoryEvaluationMetrics> batchEvaluate(
				const std::vector<TrajectoryEvaluationRequest>& requests) const = 0;

		};

		using ITrajectoryEvaluatorPtr = std::shared_ptr<ITrajectoryEvaluator>;
		using ConstITrajectoryEvaluatorPtr = std::shared_ptr<const ITrajectoryEvaluator>;

	} // namespace NSAlgorithm
} // namespace NSDrones