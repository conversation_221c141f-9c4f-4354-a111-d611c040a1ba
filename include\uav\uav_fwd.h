// include/uav/uav_fwd.h
#pragma once

#include <memory> 

namespace NSDrones {
	namespace NSUav {

		enum class FlightMode; 

		class Uav; 
		using UavPtr = std::shared_ptr<Uav>;
		using ConstUavPtr = std::shared_ptr<const Uav>;

		struct UavState; 

		class IDynamicModel; 
		using IDynamicModelPtr = std::shared_ptr<IDynamicModel>;
		using ConstIDynamicModelPtr = std::shared_ptr<const IDynamicModel>;

		class IEnergyModel; 
		using IEnergyModelPtr = std::shared_ptr<IEnergyModel>;
		using ConstIEnergyModelPtr = std::shared_ptr<const IEnergyModel>;

	} // namespace NSUav
} // namespace NSDrones