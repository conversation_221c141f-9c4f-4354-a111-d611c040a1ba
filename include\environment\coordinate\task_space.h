// include/environment/coordinate/task_space.h
#pragma once

#include "core/base_object.h"
#include "core/types.h"
#include <GeographicLib/LocalCartesian.hpp>
#include <nlohmann/json.hpp>
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <mutex>
#include <atomic>
#include <chrono>

namespace NSDrones {
	namespace NSParams {
		class ParamValues;
	}
}

namespace NSDrones {
	namespace NSEnvironment {
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSUtils;

		//=== 基准点策略接口和实现 ===//
		/**
		 * @interface IOriginStrategy
		 * @brief 基准点获取策略接口
		 */
		class IOriginStrategy {
		public:
			virtual ~IOriginStrategy() = default;

			/**
			 * @brief 获取基准点
			 * @return 基准点位置
			 * @throws std::runtime_error 如果基准点未初始化
			 */
			virtual WGS84Point getOrigin() const = 0;

			/**
			 * @brief 更新位置（仅对动态策略有效）
			 * @param position 新位置
			 * @note 对于静态策略，此方法为空操作
			 */
			virtual void setOrigin(const WGS84Point& position) = 0;

			/**
			 * @brief 获取策略类型名称
			 * @return 策略类型字符串
			 */
			virtual std::string getType() const = 0;

			/**
			 * @brief 检查是否应该为指定实体更新位置
			 * @param entity_id 实体ID
			 * @return 是否应该更新，静态策略总是返回false
			 */
			virtual bool canSetOrigin(ObjectID entity_id) const = 0;
		};

		/**
		 * @class StaticOriginStrategy
		 * @brief 静态基准点策略
		 */
		class StaticOriginStrategy : public IOriginStrategy {
		private:
			WGS84Point static_origin_;

		public:
			explicit StaticOriginStrategy(const WGS84Point& point);
			WGS84Point getOrigin() const override;
			void setOrigin(const WGS84Point& position) override; // 空操作
			std::string getType() const override;
			bool canSetOrigin(ObjectID entity_id) const override; // 总是返回false
		};

		/**
		 * @class DynamicOriginStrategy
		 * @brief 动态实体跟随策略
		 */
		class DynamicOriginStrategy : public IOriginStrategy {
		private:
			ObjectID reference_entity_id_;
			WGS84Point current_position_;
			bool initialized_ = false;

		public:
			explicit DynamicOriginStrategy(ObjectID entity_id);
			WGS84Point getOrigin() const override; // 如果未初始化则抛出异常
			void setOrigin(const WGS84Point& position) override;
			std::string getType() const override;
			bool canSetOrigin(ObjectID entity_id) const override;

			bool isInitialized() const { return initialized_; }
		};

		//=== 任务空间类 ===//
		/**
		 * @class TaskSpace
		 * @brief 任务空间 - 独立的坐标系定义，继承BaseObject获得参数管理能力
		 */
		class TaskSpace : public BaseObject {
		private:
			std::unique_ptr<IOriginStrategy> origin_strategy_;

			// 坐标转换缓存（性能优化）
			mutable std::shared_ptr<GeographicLib::LocalCartesian> local_cartesian_;
			mutable WGS84Point origin_point_;

			// 对象管理
			std::unordered_set<ObjectID> registered_objects_;  ///< 注册到此任务空间的对象ID列表
			mutable std::mutex objects_mutex_;                 ///< 对象列表访问锁

		public:
			/**
			 * @brief TaskSpace构造函数
			 * @param id TaskSpace的唯一ID
			 * @param object_type_key 对象类型键，用于参数系统
			 * @param name TaskSpace名称
			 * @param strategy 基准点策略
			 */
			TaskSpace(ObjectID id, const std::string& object_type_key,
				const std::string& name, std::unique_ptr<IOriginStrategy> strategy);
			~TaskSpace();

			/**
			 * @brief 初始化TaskSpace
			 * @param final_params 最终参数
			 * @param raw_instance_json_config 原始JSON配置
			 * @return 是否初始化成功
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> final_params,
				const nlohmann::json& raw_instance_json_config) override;

			/**
			 * @brief 获取类名（BaseObject抽象方法实现）
			 * @return 类名字符串
			 */
			std::string getClassName() const override {return "TaskSpace";}

			// 基本信息
			WGS84Point getOrigin() const;

			/**
			 * @brief 检查是否需要为指定实体更新基准点（仅对动态策略有效）
			 * @param entity_id 实体ID
			 * @return 是否需要更新，如果是静态策略则总是返回false
			 */
			bool canSetOrigin(ObjectID entity_id) const;

			/**
			 * @brief 更新基准点并通知所有注册的对象更新其局部坐标
			 * @param new_origin 新的基准点
			 */
			void updateOriginAndNotifyObjects(const WGS84Point& new_origin);

			// 坐标转换（线程安全）
			NedPoint wgs84ToNED(const WGS84Point& wgs84_pos) const;
			WGS84Point nedToWGS84(const NedPoint& ned_pos) const;
			std::vector<NedPoint> batchWgs84ToNED(const std::vector<WGS84Point>& wgs84_points) const;
			std::vector<WGS84Point> batchnedToWGS84(const std::vector<NedPoint>& ned_points) const;

			// 对象管理方法
			/**
			 * @brief 注册对象到此任务空间
			 * @param object_id 对象ID
			 */
			void registerObject(ObjectID object_id);

			/**
			 * @brief 从此任务空间注销对象
			 * @param object_id 对象ID
			 */
			void unregisterObject(ObjectID object_id);

			/**
			 * @brief 获取注册到此任务空间的所有对象ID
			 * @return 对象ID集合
			 */
			std::unordered_set<ObjectID> getRegisteredObjects() const;

		private:
			/**
			 * @brief 执行WGS84到NED的坐标转换（使用GeographicLib，带缓存优化）
			 * @param wgs84_pos WGS84位置
			 * @param reference_point 基准点
			 * @return NED坐标
			 */
			NedPoint performWGS84ToNED(const WGS84Point& wgs84_pos, const WGS84Point& reference_point) const;

			/**
			 * @brief 执行NED到WGS84的坐标转换（使用GeographicLib，带缓存优化）
			 * @param ned_pos NED坐标
			 * @param reference_point 基准点
			 * @return WGS84坐标
			 */
			WGS84Point performnedToWGS84(const NedPoint& ned_pos, const WGS84Point& reference_point) const;

			/**
			 * @brief 获取或创建LocalCartesian对象（带缓存）
			 * @param reference_point 基准点
			 * @return LocalCartesian对象的共享指针
			 */
			std::shared_ptr<GeographicLib::LocalCartesian> getOrCreateLocalCartesian(const WGS84Point& reference_point) const;

			/**
			 * @brief 线程安全地获取基准点（内部使用，不加锁）
			 * @return 基准点位置
			 * @throws std::runtime_error 如果基准点未初始化
			 */
			WGS84Point getSafeOrigin() const;
		};

	} // namespace NSEnvironment
} // namespace NSDrones
