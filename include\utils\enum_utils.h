#pragma once

#include <string>
#include <type_traits>
#include <sstream>
#include <optional>
#include <stdexcept>
#include <algorithm>
#include "utils/logging.h"
#include "magic_enum.hpp"
#include <any>
#include <functional>
#include <map>
#include <typeindex>

namespace NSDrones {
	namespace NSUtils {

		// Generic enumToString using magic_enum
		template <typename EnumType>
		inline std::string enumToString(EnumType e) {
			static_assert(std::is_enum_v<EnumType>, "EnumType must be an enumeration type.");
			auto name_sv = magic_enum::enum_name(e);
			if (name_sv.empty()) {
				LOG_ERROR("enumToString<{}>: Invalid or out-of-range enum value: {}", std::string(magic_enum::enum_type_name<EnumType>()), static_cast<std::underlying_type_t<EnumType>>(e));
				return "Invalid<" + std::string(magic_enum::enum_type_name<EnumType>()) + ">(" + std::to_string(static_cast<std::underlying_type_t<EnumType>>(e)) + ")";
			}
			return std::string(name_sv);
		}

		// Generic stringToEnum using magic_enum
		template <typename EnumType>
		inline EnumType stringToEnum(const std::string& s, EnumType defaultValue) {
			static_assert(std::is_enum_v<EnumType>, "EnumType must be an enumeration type.");
			// Use case-insensitive casting provided by magic_enum
			auto enum_val = magic_enum::enum_cast<EnumType>(s, magic_enum::case_insensitive);
			if (enum_val.has_value()) {
				return enum_val.value();
			} else {
				// Attempt to match common alternative casings or patterns if direct case-insensitive cast fails
				// For example, "VtolFixedWing" vs "VTOL_FIXED_WING"
				// This can become complex. For now, rely on magic_enum::case_insensitive and good logging.
				// If specific aliases are needed, they could be handled with a pre-processing step on 's'
				// or by customizing magic_enum further if possible.

				// If still not found, log and return default
				LOG_WARN("无法将string '{}'转换为enum'{}'. 转换值为 [{}]. 返回缺省值为'{}'.",
				         s,
				         std::string(magic_enum::enum_type_name<EnumType>()),
				         magic_enum::enum_names<EnumType>().size() > 0 ?
				            ([]{
						           std::string names;
						           for (auto n : magic_enum::enum_names<EnumType>()) {
							            names += std::string(n) + ", ";
						           }
						           names.pop_back();
						           names.pop_back();
						           return names;
							}()) :
				            "none", // Handle empty enum case, though unlikely for user enums
				         NSUtils::enumToString(defaultValue)); // Use our enumToString for the default value
				return defaultValue;
			}
		}

		/**
		 * @brief 通用枚举转换函数的类型别名。
		 * 接收一个字符串值和原始的默认值(std::any)，返回转换后的枚举值 (std::any) 或在失败时返回原始默认值。
		 */
		using EnumConverterFunc = std::function<std::any(const std::string& /*stringValue*/, std::any /*defaultValueAny*/)>;

		/**
		 * @brief 全局枚举转换器注册表。
		 * 存储从枚举类型字符串名称到其对应转换函数的映射。
		 */
		extern std::map<std::string, EnumConverterFunc> G_ENUM_CONVERTERS;

		/**
		 * @brief 用于将 std::any 包装的枚举转换回字符串的函数类型。
		 */
		using EnumToStringAnyFunc = std::function<std::string(const std::any& /*enumValueAny*/)>;

		/**
		 * @brief 全局枚举到字符串转换器注册表 (用于 std::any)。
		 * 存储从枚举类型字符串名称到其对应 std::any 到字符串转换函数的映射。
		 */
		extern std::map<std::string, EnumToStringAnyFunc> G_ENUM_TO_STRING_ANY_CONVERTERS;

		/**
		 * @brief 全局类型索引到枚举字符串名称的映射表。
		 * 用于从 C++ type_index 反向查找注册时使用的枚举类型字符串名称。
		 */
		extern std::map<std::type_index, std::string> G_TYPE_INDEX_TO_ENUM_NAME_MAP;

		/**
		 * @brief 注册一个枚举类型及其转换逻辑。
		 *
		 * @tparam EnumType 要注册的枚举类型。
		 * @param typeName 枚举类型的字符串名称 ，必须与JSON配置中的 "enum_type" 字段匹配。
		 * @param defaultValue 此枚举类型的默认成员，用于转换失败时的回退。
		 */
		template <typename EnumType>
		void registerEnum(const std::string& typeName, EnumType defaultValue) {
			// 注册 string -> std::any<EnumType> 转换器
			G_ENUM_CONVERTERS[typeName] = [defaultValue, typeName](const std::string& stringValue, std::any defaultValueAny) -> std::any {
				std::optional<EnumType> enumValueOpt = magic_enum::enum_cast<EnumType>(stringValue, magic_enum::case_insensitive);
				if (enumValueOpt.has_value()) {
					return enumValueOpt.value();
				}
				// 如果字符串值转换失败, 尝试使用传入的 defaultValueAny
				try {
					if (defaultValueAny.has_value() && defaultValueAny.type() == typeid(EnumType)) {
						return std::any_cast<EnumType>(defaultValueAny);
					} else if (defaultValueAny.has_value()) {
                        LOG_WARN("类型 '{}' 提供的 defaultValueAny 类型不匹配 (期望 {}, 实际 {}), 将为输入字符串 '{}' 使用编译期默认值.",
                                  typeName, magic_enum::enum_type_name<EnumType>(), defaultValueAny.type().name(), stringValue); // 需要日志系统
                    }
				} catch (const std::bad_any_cast& e) {
					LOG_WARN("类型 '{}' 无法将 defaultValueAny 转换为期望的枚举类型 {} (输入字符串 '{}')。错误: {}。将使用编译期默认值.",
						typeName, magic_enum::enum_type_name<EnumType>(), stringValue, e.what());
				}
				LOG_DEBUG("类型 '{}' 无法转换字符串 '{}' 且 defaultValueAny 处理未成功或不适用，返回编译期默认值 '{}'",
					typeName, stringValue, NSUtils::enumToString(defaultValue));
				return defaultValue; // 回退到编译期指定的默认值
			};

			// 注册 std::any<EnumType> -> string 转换器
			G_ENUM_TO_STRING_ANY_CONVERTERS[typeName] = [typeName](const std::any& enumValueAny) -> std::string {
				try {
					EnumType val = std::any_cast<EnumType>(enumValueAny);
					return NSUtils::enumToString(val); // 使用已有的 enumToString (基于 magic_enum)
				} catch (const std::bad_any_cast& e) {
					LOG_ERROR("无法将 std::any 转换为枚举类型 {}。错误: {}", typeName, std::string(magic_enum::enum_type_name<EnumType>()), e.what());
					return "ERROR_CASTING_ANY_TO_ENUM<" + typeName + ">";
				}
			};

			// 注册类型索引到枚举名称的映射
			G_TYPE_INDEX_TO_ENUM_NAME_MAP[std::type_index(typeid(EnumType))] = typeName;
			LOG_TRACE("已为类型索引 {} (来自类型 '{}') 映射到枚举名称 '{}'", typeid(EnumType).name(), std::string(magic_enum::enum_type_name<EnumType>()), typeName);
		}

		/**
		 * @brief 根据枚举类型的字符串名称和字符串值，将其转换为对应的枚举类型 (std::any 包装)。
		 *
		 * @param enumTypeName 枚举类型的字符串名称 (来自JSON的 "enum_type")。
		 * @param stringValue 要转换的字符串值。
		 * @param defaultValueAny 转换失败时返回的默认值，包装在 std::any 中。
		 * @return std::any 转换后的枚举值，如果失败则为 defaultValueAny。
		 */
		std::any convertStringToEnumAny(const std::string& enumTypeName, const std::string& stringValue, std::any defaultValueAny);

		/**
		 * @brief 将存储在 std::any 中的枚举值转换为其字符串表示形式。
		 *
		 * @param enumValueAny 包含枚举值的 std::any 对象。
		 * @param enumTypeName 枚举类型的字符串名称 (例如 "NSDrones::ShapeType")。
		 * @return std::string 枚举的字符串表示，如果转换失败则为错误指示字符串。
		 */
		std::string enumToStringAny(const std::any& enumValueAny, const std::string& enumTypeName);

		/**
		 * @brief 初始化并注册所有在系统中使用的枚举类型。
		 * 应在应用程序启动时调用。
		 */
		void initializeAndRegisterAllEnums();

		/**
		 * @brief 根据 C++ 类型索引获取注册时使用的枚举类型字符串名称。
		 *
		 * @param tid 枚举类型的 std::type_index。
		 * @return std::optional<std::string> 如果找到，则为枚举的注册名称；否则为 std::nullopt。
		 */
		std::optional<std::string> typeIdToRegisteredName(const std::type_index& tid);

	} // namespace NSUtils
} // namespace NSDrones
