#include "utils/orientation_utils.h"
#include "utils/logging.h"
#include <cmath>
#include <algorithm>

namespace NSDrones {
    namespace NSUtils {
        using namespace NSCore;

        // 常量定义
        static constexpr double PI = 3.14159265358979323846;
        static constexpr double DEG_TO_RAD = PI / 180.0;
        static constexpr double RAD_TO_DEG = 180.0 / PI;

        double OrientationUtils::degToRad(double degrees) {
            return degrees * DEG_TO_RAD;
        }

        double OrientationUtils::radToDeg(double radians) {
            return radians * RAD_TO_DEG;
        }

        double OrientationUtils::normalizeAngle(double angle_deg) {
            while (angle_deg > 180.0) angle_deg -= 360.0;
            while (angle_deg <= -180.0) angle_deg += 360.0;
            return angle_deg;
        }

        Orientation OrientationUtils::yprToQuaternion(double yaw_deg, double pitch_deg, double roll_deg) {
            LOG_TRACE("姿态转换: YPR({:.2f}, {:.2f}, {:.2f})度 -> 四元数", yaw_deg, pitch_deg, roll_deg);

            // 转换为弧度
            double yaw_rad = degToRad(yaw_deg);
            double pitch_rad = degToRad(pitch_deg);
            double roll_rad = degToRad(roll_deg);

            // 计算半角的三角函数值
            double cy = std::cos(yaw_rad * 0.5);
            double sy = std::sin(yaw_rad * 0.5);
            double cp = std::cos(pitch_rad * 0.5);
            double sp = std::sin(pitch_rad * 0.5);
            double cr = std::cos(roll_rad * 0.5);
            double sr = std::sin(roll_rad * 0.5);

            // 计算四元数分量 (ZYX 旋转顺序: Yaw -> Pitch -> Roll)
            double w = cr * cp * cy + sr * sp * sy;
            double x = sr * cp * cy - cr * sp * sy;
            double y = cr * sp * cy + sr * cp * sy;
            double z = cr * cp * sy - sr * sp * cy;

            Orientation result(w, x, y, z);
            result.normalize();

            LOG_TRACE("  -> 四元数: w={:.4f}, x={:.4f}, y={:.4f}, z={:.4f}", 
                     result.w(), result.x(), result.y(), result.z());

            return result;
        }

        Vector3D OrientationUtils::quaternionToYPR(const Orientation& quaternion) {
            // 确保四元数已归一化
            Orientation q = quaternion.normalized();

            // 提取四元数分量
            double w = q.w();
            double x = q.x();
            double y = q.y();
            double z = q.z();

            // 计算 YPR 角度
            // Roll (x-axis rotation)
            double sinr_cosp = 2 * (w * x + y * z);
            double cosr_cosp = 1 - 2 * (x * x + y * y);
            double roll_rad = std::atan2(sinr_cosp, cosr_cosp);

            // Pitch (y-axis rotation)
            double sinp = 2 * (w * y - z * x);
            double pitch_rad;
            if (std::abs(sinp) >= 1) {
                pitch_rad = std::copysign(PI / 2, sinp); // 使用 90 度，如果超出范围
            } else {
                pitch_rad = std::asin(sinp);
            }

            // Yaw (z-axis rotation)
            double siny_cosp = 2 * (w * z + x * y);
            double cosy_cosp = 1 - 2 * (y * y + z * z);
            double yaw_rad = std::atan2(siny_cosp, cosy_cosp);

            // 转换为度并规范化
            double yaw_deg = normalizeAngle(radToDeg(yaw_rad));
            double pitch_deg = normalizeAngle(radToDeg(pitch_rad));
            double roll_deg = normalizeAngle(radToDeg(roll_rad));

            return Vector3D(yaw_deg, pitch_deg, roll_deg);
        }

        Orientation OrientationUtils::yprVectorToQuaternion(const Vector3D& ypr_deg) {
            return yprToQuaternion(ypr_deg.x(), ypr_deg.y(), ypr_deg.z());
        }

        bool OrientationUtils::isValidQuaternion(const Orientation& quaternion, double tolerance) {
            double norm = quaternion.norm();
            return std::abs(norm - 1.0) <= tolerance;
        }

        Orientation OrientationUtils::normalizeQuaternion(const Orientation& quaternion) {
            return quaternion.normalized();
        }

        double OrientationUtils::quaternionAngularDistance(const Orientation& q1, const Orientation& q2) {
            // 确保四元数已归一化
            Orientation nq1 = q1.normalized();
            Orientation nq2 = q2.normalized();

            // 计算点积
            double dot = nq1.dot(nq2);

            // 处理四元数的双重覆盖性质
            dot = std::abs(dot);

            // 限制在有效范围内
            dot = std::min(1.0, dot);

            // 计算角度距离
            return 2.0 * std::acos(dot);
        }

        Orientation OrientationUtils::quaternionSlerp(const Orientation& q1, const Orientation& q2, double t) {
            // 确保 t 在 [0, 1] 范围内
            t = std::max(0.0, std::min(1.0, t));

            // 确保四元数已归一化
            Orientation nq1 = q1.normalized();
            Orientation nq2 = q2.normalized();

            // 计算点积
            double dot = nq1.dot(nq2);

            // 如果点积为负，取反其中一个四元数以选择较短的路径
            if (dot < 0.0) {
                nq2 = Orientation(-nq2.w(), -nq2.x(), -nq2.y(), -nq2.z());
                dot = -dot;
            }

            // 如果四元数非常接近，使用线性插值
            if (dot > 0.9995) {
                // 线性插值：lerp(q1, q2, t) = (1-t)*q1 + t*q2
                Orientation result(
                    (1.0 - t) * nq1.w() + t * nq2.w(),
                    (1.0 - t) * nq1.x() + t * nq2.x(),
                    (1.0 - t) * nq1.y() + t * nq2.y(),
                    (1.0 - t) * nq1.z() + t * nq2.z()
                );
                return result.normalized();
            }

            // 球面线性插值
            double theta_0 = std::acos(dot);
            double sin_theta_0 = std::sin(theta_0);
            double theta = theta_0 * t;
            double sin_theta = std::sin(theta);

            double s0 = std::cos(theta) - dot * sin_theta / sin_theta_0;
            double s1 = sin_theta / sin_theta_0;

            // 手动计算线性组合
            Orientation result(
                s0 * nq1.w() + s1 * nq2.w(),
                s0 * nq1.x() + s1 * nq2.x(),
                s0 * nq1.y() + s1 * nq2.y(),
                s0 * nq1.z() + s1 * nq2.z()
            );

            return result.normalized();
        }

    } // namespace NSUtils
} // namespace NSDrones
