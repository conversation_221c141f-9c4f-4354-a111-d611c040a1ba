// include/mission/task_strategies.h
#pragma once

#include "core/types.h"
#include "params/parameters.h"
#include <string>
#include <memory>             
#include <map>
#include <optional>
#include <algorithm> 

namespace NSDrones {
	namespace NSMission {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSParams;

		// --- 策略基类 ---
		/**
		 * @class ITaskStrategy
		 * @brief 任务执行策略的抽象基类
		 *
		 * 定义了所有任务策略必须实现的基本接口。策略用于指导任务执行过程中的
		 * 具体行为，如高度控制、速度控制、路径约束等。每个策略都有唯一的名称
		 * 标识符，用于在策略映射中进行查找和管理。
		 *
		 * @note 所有具体策略类都应继承此接口并实现相应方法
		 */
		class ITaskStrategy {
		public:
			virtual ~ITaskStrategy() = default;

			/**
			 * @brief 获取策略的唯一标识名称
			 * @return 策略名称字符串，用于在策略映射中查找和识别
			 */
			virtual std::string getName() const = 0;

			/**
			 * @brief 获取策略的详细描述信息
			 * @return 包含策略用途和参数说明的描述字符串
			 */
			virtual std::string getDescription() const = 0;

			/**
			 * @brief 验证当前策略参数的有效性
			 * @return true表示所有参数都在有效范围内，false表示存在无效参数
			 */
			virtual bool isValid() const = 0;
		};
		using ITaskStrategyPtr = std::shared_ptr<ITaskStrategy>;
		using ConstITaskStrategyPtr = std::shared_ptr<const ITaskStrategy>;

		// --- 具体策略定义 ---

		/**
		 * @class AltitudeStrategy
		 * @brief 高度控制策略
		 *
		 * 定义任务执行时的高度控制参数，包括高度类型（相对地面或海平面）
		 * 和具体的高度数值。用于指导无人机在执行任务时保持合适的飞行高度。
		 */
		class AltitudeStrategy : public ITaskStrategy {
		public:
			// --- 公共成员变量 ---
			AltitudeType height_type;  ///< 高度参考类型（相对地面AGL或海平面MSL）
			double value;              ///< 高度数值（米）

			// --- 构造函数 ---
			/**
			 * @brief 构造高度策略
			 * @param ht 高度类型
			 * @param val 高度值（米），必须为非负数
			 */
			explicit AltitudeStrategy(AltitudeType ht = AltitudeType::ABOVE_GROUND_LEVEL,
									 double val = 100.0)
				: height_type(ht), value(std::max(0.0, val)) {}

			// --- 接口实现 ---
			std::string getName() const override { return "Altitude"; }

			std::string getDescription() const override {
				return "高度控制策略 - 类型: " + NSUtils::enumToString(height_type) +
					   ", 高度: " + std::to_string(value) + "米";
			}

			bool isValid() const override {
				return value >= 0.0 && value <= 10000.0; // 合理的飞行高度范围
			}
		};

		/**
		 * @class SpeedStrategy
		 * @brief 速度控制策略
		 *
		 * 定义任务执行时的速度控制参数，包括期望速度、最大加速度等。
		 * 用于指导无人机在执行任务时保持合适的飞行速度。
		 */
		class SpeedStrategy : public ITaskStrategy {
		public:
			// --- 公共成员变量 ---
			double desired_speed;      ///< 期望飞行速度（米/秒）
			double max_acceleration;   ///< 最大加速度（米/秒²）
			double max_deceleration;   ///< 最大减速度（米/秒²）

			// --- 构造函数 ---
			/**
			 * @brief 构造速度策略
			 * @param speed 期望速度（米/秒），必须为正数
			 * @param max_accel 最大加速度（米/秒²）
			 * @param max_decel 最大减速度（米/秒²）
			 */
			explicit SpeedStrategy(double speed = 10.0,
								  double max_accel = 2.0,
								  double max_decel = 3.0)
				: desired_speed(std::max(0.1, speed))
				, max_acceleration(std::max(0.1, max_accel))
				, max_deceleration(std::max(0.1, max_decel)) {}

			// --- 接口实现 ---
			std::string getName() const override { return "Speed"; }

			std::string getDescription() const override {
				return "速度控制策略 - 期望速度: " + std::to_string(desired_speed) + "m/s" +
					   ", 最大加速度: " + std::to_string(max_acceleration) + "m/s²";
			}

			bool isValid() const override {
				return desired_speed > 0.0 && desired_speed <= 50.0 &&  // 合理的飞行速度范围
					   max_acceleration > 0.0 && max_acceleration <= 10.0 &&
					   max_deceleration > 0.0 && max_deceleration <= 10.0;
			}
		};

		/**
		 * @class PathConstraintStrategy
		 * @brief 路径约束策略
		 *
		 * 定义任务执行时的路径约束参数，包括最大曲率、坡度限制、高度范围等。
		 * 用于确保生成的飞行路径符合无人机的飞行性能和安全要求。
		 */
		class PathConstraintStrategy : public ITaskStrategy {
		public:
			// --- 公共成员变量 ---
			double max_curvature;      ///< 最大曲率（1/米），限制转弯半径
			double max_slope_deg;      ///< 最大坡度（度），限制爬升/下降角度
			double min_altitude_agl;   ///< 最小离地高度（米）
			double max_altitude_agl;   ///< 最大离地高度（米）
			double max_altitude_msl;   ///< 最大海拔高度（米）
			double required_width;     ///< 航线要求的宽度（米），用于碰撞检测

			// --- 构造函数 ---
			/**
			 * @brief 构造路径约束策略
			 * @param max_curv 最大曲率（1/米）
			 * @param max_slope 最大坡度（度）
			 * @param min_alt_agl 最小离地高度（米）
			 * @param max_alt_agl 最大离地高度（米）
			 * @param max_alt_msl 最大海拔高度（米）
			 * @param width 航线宽度（米）
			 */
			explicit PathConstraintStrategy(double max_curv = 0.1,
										   double max_slope = 30.0,
										   double min_alt_agl = 10.0,
										   double max_alt_agl = 500.0,
										   double max_alt_msl = 1000.0,
										   double width = 0.0)
				: max_curvature(std::max(0.001, max_curv))
				, max_slope_deg(std::clamp(max_slope, 0.0, 90.0))
				, min_altitude_agl(std::max(0.0, min_alt_agl))
				, max_altitude_agl(std::max(min_altitude_agl, max_alt_agl))
				, max_altitude_msl(std::max(0.0, max_alt_msl))
				, required_width(std::max(0.0, width)) {}

			// --- 接口实现 ---
			std::string getName() const override { return "PathConstraint"; }

			std::string getDescription() const override {
				return "路径约束策略 - 最大曲率: " + std::to_string(max_curvature) + "/m" +
					   ", 最大坡度: " + std::to_string(max_slope_deg) + "°" +
					   ", 高度范围: " + std::to_string(min_altitude_agl) + "-" +
					   std::to_string(max_altitude_agl) + "m AGL";
			}

			bool isValid() const override {
				return max_curvature > 0.0 && max_curvature <= 1.0 &&
					   max_slope_deg >= 0.0 && max_slope_deg <= 90.0 &&
					   min_altitude_agl >= 0.0 && max_altitude_agl >= min_altitude_agl &&
					   max_altitude_msl >= 0.0 && required_width >= 0.0;
			}
		};

		/**
		 * @class PayloadControlStrategy
		 * @brief 载荷控制策略
		 *
		 * 定义任务执行时的载荷控制参数，包括目标载荷ID、控制命令和相关参数。
		 * 用于在任务执行过程中控制相机、传感器等载荷设备的操作。
		 */
		class PayloadControlStrategy : public ITaskStrategy {
		public:
			// --- 公共成员变量 ---
			std::string payload_id;                        ///< 目标载荷设备的唯一标识符
			std::string command;                           ///< 控制命令（如"TakePhoto"、"SetZoom"等）
			std::map<std::string, ParamValue> parameters;  ///< 命令执行所需的参数映射

			// --- 构造函数 ---
			/**
			 * @brief 构造载荷控制策略
			 * @param id 载荷设备ID
			 * @param cmd 控制命令
			 * @param params 命令参数
			 */
			explicit PayloadControlStrategy(const std::string& id = "",
										   const std::string& cmd = "",
										   const std::map<std::string, ParamValue>& params = {})
				: payload_id(id), command(cmd), parameters(params) {}

			// --- 接口实现 ---
			std::string getName() const override { return "PayloadControl"; }

			std::string getDescription() const override {
				return "载荷控制策略 - 载荷ID: " + payload_id +
					   ", 命令: " + command +
					   ", 参数数量: " + std::to_string(parameters.size());
			}

			bool isValid() const override {
				return !payload_id.empty() && !command.empty();
			}
		};

		// --- 策略集合类型定义 ---
		/**
		 * @brief 任务策略映射表
		 *
		 * 使用策略名称作为键，策略对象指针作为值的映射表。
		 * 允许一个任务同时应用多种不同类型的策略。
		 *
		 * ## 策略继承机制
		 *
		 * 策略系统采用两级继承结构：
		 *
		 * 1. **Mission级别策略**：作为所有Task的默认策略模板
		 *    - 定义整个任务计划的通用执行策略
		 *    - 例如：统一的高度策略、速度限制、安全约束等
		 *
		 * 2. **Task级别策略**：可以覆盖或补充Mission的默认策略
		 *    - 任务特定的执行策略
		 *    - 同名策略会覆盖Mission的默认策略
		 *    - 新策略会补充到策略集合中
		 *
		 * ## 使用建议
		 *
		 * - 使用 `Mission::createTask()` 方法创建任务，自动处理策略继承
		 * - 直接使用 `Task` 构造函数时，需要手动合并策略
		 * - 策略名称应该保持一致，以确保正确的覆盖行为
		 *
		 * @example
		 * ```cpp
		 * // Mission级别设置默认高度策略
		 * mission.setStrategies({{"Altitude", std::make_shared<AltitudeStrategy>(100.0)}});
		 *
		 * // 创建任务时，特定任务可以覆盖高度策略
		 * auto task = mission.createTask(id, type, target, requirements, params,
		 *     {{"Altitude", std::make_shared<AltitudeStrategy>(200.0)}});
		 * ```
		 */
		using ITaskStrategyMap = std::map<std::string, ITaskStrategyPtr>;
	} // namespace NSMission
} // namespace NSDrones