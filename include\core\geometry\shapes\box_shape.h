#pragma once

#include "core/geometry/ishape.h"
#include <fcl/geometry/shape/box.h>

namespace NSDrones {
namespace NSCore {

    /**
     * @brief 长方体形状
     * 
     * 表示一个轴对齐的长方体，具有长、宽、高三个维度。
     * 几何中心位于局部坐标系原点。
     */
    class BoxShape : public IShape {
    private:
        double length_;  // X轴方向长度
        double width_;   // Y轴方向宽度  
        double height_;  // Z轴方向高度
        
        mutable std::shared_ptr<fcl::Boxd> fcl_box_;  // FCL几何对象（延迟创建）

    public:
        /**
         * @brief 构造函数
         * @param length X轴方向长度
         * @param width Y轴方向宽度
         * @param height Z轴方向高度
         */
        BoxShape(double length, double width, double height);

        /**
         * @brief 默认构造函数（单位立方体）
         */
        BoxShape();

        /**
         * @brief 拷贝构造函数
         */
        BoxShape(const BoxShape& other);

        /**
         * @brief 赋值操作符
         */
        BoxShape& operator=(const BoxShape& other);

        // IShape接口实现
        ShapeType getType() const override { return ShapeType::BOX; }
        int getDimension() const override { return 3; }  // 3D实体
        std::shared_ptr<fcl::CollisionGeometryd> getFCLGeometry() const override;
        fcl::AABBd getAABB(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const override;
        double getVolume() const override;
        double getSurfaceArea() const override;
        fcl::Vector3d getCentroid() const override;
        fcl::Matrix3d getInertiaMatrix(double mass) const override;
        std::unique_ptr<IShape> clone() const override;
        nlohmann::json serialize() const override;
        bool deserialize(const nlohmann::json& json) override;
        std::string toString() const override;
        bool containsPoint(const fcl::Vector3d& point) const override;
        double distanceToPoint(const fcl::Vector3d& point) const override;
        double getCharacteristicSize() const override;

        // BoxShape特有方法
        /**
         * @brief 获取长度（X轴）
         */
        double getLength() const { return length_; }

        /**
         * @brief 获取宽度（Y轴）
         */
        double getWidth() const { return width_; }

        /**
         * @brief 获取高度（Z轴）
         */
        double getHeight() const { return height_; }

        /**
         * @brief 获取尺寸向量
         */
        fcl::Vector3d getSize() const { return fcl::Vector3d(length_, width_, height_); }

        /**
         * @brief 设置尺寸
         * @param length 长度
         * @param width 宽度
         * @param height 高度
         */
        void setSize(double length, double width, double height);

        /**
         * @brief 设置尺寸
         * @param size 尺寸向量
         */
        void setSize(const fcl::Vector3d& size);

        /**
         * @brief 获取顶点列表（8个顶点）
         * @param transform 变换矩阵
         * @return 顶点列表
         */
        std::vector<fcl::Vector3d> getVertices(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 获取面的法向量列表（6个面）
         * @param transform 变换矩阵
         * @return 法向量列表
         */
        std::vector<fcl::Vector3d> getFaceNormals(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 检查是否为立方体
         * @param tolerance 容差
         * @return 是否为立方体
         */
        bool isCube(double tolerance = 1e-6) const;

        /**
         * @brief 创建单位立方体
         * @return BoxShape实例
         */
        static std::unique_ptr<BoxShape> createUnitCube();

        /**
         * @brief 创建立方体
         * @param size 边长
         * @return BoxShape实例
         */
        static std::unique_ptr<BoxShape> createCube(double size);

    private:
        /**
         * @brief 确保FCL对象已创建
         */
        void ensureFCLObject() const;

        /**
         * @brief 验证尺寸参数
         */
        void validateDimensions() const;
    };

} // namespace NSCore
} // namespace NSDrones