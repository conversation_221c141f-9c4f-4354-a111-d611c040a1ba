// include/algorithm/path_planner/rrtstar_planner.h
#pragma once

#include "algorithm/path_planner/ipath_planner.h" 
#include "algorithm/algorithm_object.h"
#include <memory>                   
#include <string>
#include <vector>
#include <map>
#include <optional>
#include <utility>                  

namespace ompl {
	namespace base {
		class State;
		class StateSpace; using StateSpacePtr = std::shared_ptr<StateSpace>;
		class SpaceInformation; using SpaceInformationPtr = std::shared_ptr<SpaceInformation>;
		template <typename T> class ScopedState; 
		class RealVectorStateSpace; 
	}
	namespace geometric {
		class RRTstar; 
	}
} 

namespace NSDrones {
	namespace NSMission { struct PathConstraintStrategy; } 
}

namespace NSDrones {
	namespace NSAlgorithm {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class RRTStarPlanner
		 * @brief 使用 OMPL 库中的 RRT* 算法实现路径规划。
		 *        继承自 AlgorithmObject 和 IPathPlanner。
		 */
		class RRTStarPlanner : public IPathPlanner, public AlgorithmObject {
		public:
			/**
			 * @struct Options
			 * @brief OMPL RRT* 规划器的配置选项。
			 */
			struct Options {
				double planning_time_limit = 1.0;     // 规划时间限制 (秒)
				double rrtstar_range = 0.0;           // RRT* 步长 (0.0 表示默认)
				double goal_bias = 0.05;              // 目标偏置概率 (0.0 到 1.0)
				double goal_threshold = 1.0;          // 到达目标的距离阈值 (米)
				double safety_margin = 0.5;           // 碰撞检测安全距离 (米)
				bool simplify_path = true;            // 是否简化生成的路径
				double simplification_time_limit = 0.1; // 路径简化时间限制 (秒)
				bool interpolate_path = true;         // 是否对路径进行插值
				double interpolation_distance = 0.5;  // 插值目标距离 (米)
				std::optional<std::pair<EcefPoint, EcefPoint>> bounds = std::nullopt; // (可选) 自定义状态空间边界
				double default_speed = 5.0; // 用于轨迹优化的默认速度 (如果动力学模型未提供)
				// Options() = default; // 确保有默认构造函数，所有成员都有默认值
			};

			/**
			 * @brief 构造函数。
			 * @param id 对象ID。
			 * @param type_tag 类型标签。
			 * @param env_ref 环境对象的引用。
			 * @param name (可选) 规划器实例名称。
			 * @param version (可选) 规划器版本。
			 */
			explicit RRTStarPlanner(ObjectID id,
			                        const std::string& type_tag,
			                        const std::string& name = "AlgorithmObect.RRTStarPlanner",
			                        const std::string& version = "1.0.0.0");

			/** @brief 虚析构函数 */
			~RRTStarPlanner() override = default;

			/**
			 * @brief 查找路径（重构后的统一接口）
			 * @param request 路径规划请求（包含WGS84坐标）
			 * @return 路径规划结果（包含WGS84坐标）
			 */
			PathPlanningResult findPath(const PathPlanningRequest& request) override;

			/**
			 * @brief 检查路径可行性（重构后的统一接口）
			 * @param waypoints WGS84路径点
			 * @return 可行性检查结果
			 */
			bool isPathFeasible(const std::vector<WGS84Point>& waypoints) const override;

		protected:
			/**
			 * @brief 内部ECEF坐标路径查找实现
			 * @param start 起始点（ECEF）
			 * @param goal 目标点（ECEF）
			 * @return ECEF路径点序列
			 */
			std::vector<EcefPoint> findPathECEF(const EcefPoint& start, const EcefPoint& goal) override;

		public:

			/**
			 * @brief 设置规划器参数。
			 * @param parameters 参数键值对。
			 */
			void setParameters(const std::map<std::string, double>& parameters);

			/**
			 * @brief 初始化 RRT* 规划器。
			 * @param params 包含配置参数的 ParamValues 对象。
			 * @param raw_config 实例的原始JSON配置。
			 * @return 如果初始化成功则返回 true，否则返回 false。
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) override;

		private:
			Options options_; // 存储配置选项
			ompl::base::StateSpacePtr state_space_; // OMPL 状态空间 (在构造函数中初始化)

			// --- 私有辅助函数 ---
			/** @brief 将 EcefPoint 转换为 OMPL 状态。 */
			void ecefToOmplState(const EcefPoint& point, ompl::base::ScopedState<ompl::base::RealVectorStateSpace>& state) const;
			/** @brief 将 OMPL 状态转换为 EcefPoint。 */
			EcefPoint omplStateToEcef(const ompl::base::State* state) const;
		};

	} // namespace NSAlgorithm
} // namespace NSDrones