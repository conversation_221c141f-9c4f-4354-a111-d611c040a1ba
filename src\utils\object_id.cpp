#include "utils/object_id.h"
#include "utils/logging.h"
#include <uuid.h>        // For UUID generation
#include <random>      // For random device and generator
#include <system_error> // For system_error exception

namespace NSDrones {
namespace NSUtils {

	// generateObjectID 函数的定义
	ObjectID generateObjectID() {
		try {
			// --- 推荐方式：使用线程局部存储的生成器 ---
			// 确保每个线程有自己的随机数引擎和 UUID 生成器实例，避免数据竞争
			thread_local static std::mt19937 generator([] {
				// 使用 lambda 初始化，确保 random_device 只调用一次
				try {
					return std::random_device{}();
				}
				catch (const std::exception& e) {
					LOG_CRITICAL("无法初始化随机设备用于 UUID 生成: {}。将使用固定种子。", e.what());
					return std::mt19937::default_seed; // 使用默认种子作为后备
				}
				}());
			thread_local static uuids::uuid_random_generator gen(generator);

			uuids::uuid const id = gen(); // 使用线程局部生成器生成 UUID v4

			// 检查生成的 UUID 是否为 nil (不太可能发生，但作为健壮性检查)
			if (id.is_nil()) {
				LOG_ERROR("UUID 生成器返回了 Nil UUID。");
				return INVALID_OBJECT_ID; // 返回无效 ID
			}
			// 将 UUID 转换为字符串并返回
			return uuids::to_string(id);

		}
		catch (const std::system_error& e) { // 捕获 random_device 可能抛出的系统错误
			LOG_CRITICAL("生成 UUID 时发生系统错误 (可能 random_device 不可用): {}。返回无效 ID。", e.what());
			return INVALID_OBJECT_ID;
		}
		catch (const std::exception& e) { // 捕获其他可能的异常 (例如内存分配)
			LOG_ERROR("生成 UUID 时发生异常: {}。返回无效 ID。", e.what());
			return INVALID_OBJECT_ID;
		}
		catch (...) { // 捕获所有其他未知错误
			LOG_ERROR("生成 UUID 时发生未知错误。返回无效 ID。");
			return INVALID_OBJECT_ID;
		}
	}

} // namespace NSUtils
} // namespace NSDrones 