#pragma once

#include "core/geometry/ishape.h"
#include <fcl/geometry/shape/sphere.h>

namespace NSDrones {
namespace NSCore {

    /**
     * @brief 球体形状
     * 
     * 表示一个以局部坐标系原点为中心的球体。
     */
    class SphereShape : public IShape {
    private:
        double radius_;  // 球体半径
        
        mutable std::shared_ptr<fcl::Sphered> fcl_sphere_;  // FCL几何对象（延迟创建）

    public:
        /**
         * @brief 构造函数
         * @param radius 球体半径
         */
        explicit SphereShape(double radius);

        /**
         * @brief 默认构造函数（单位球）
         */
        SphereShape();

        /**
         * @brief 拷贝构造函数
         */
        SphereShape(const SphereShape& other);

        /**
         * @brief 赋值操作符
         */
        SphereShape& operator=(const SphereShape& other);

        // IShape接口实现
        ShapeType getType() const override { return ShapeType::SPHERE; }
        int getDimension() const override { return 3; }  // 3D实体
        std::shared_ptr<fcl::CollisionGeometryd> getFCLGeometry() const override;
        fcl::AABBd getAABB(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const override;
        double getVolume() const override;
        double getSurfaceArea() const override;
        fcl::Vector3d getCentroid() const override;
        fcl::Matrix3d getInertiaMatrix(double mass) const override;
        std::unique_ptr<IShape> clone() const override;
        nlohmann::json serialize() const override;
        bool deserialize(const nlohmann::json& json) override;
        std::string toString() const override;
        bool containsPoint(const fcl::Vector3d& point) const override;
        double distanceToPoint(const fcl::Vector3d& point) const override;
        double getCharacteristicSize() const override;

        // SphereShape特有方法
        /**
         * @brief 获取半径
         */
        double getRadius() const { return radius_; }

        /**
         * @brief 设置半径
         * @param radius 新半径
         */
        void setRadius(double radius);

        /**
         * @brief 获取直径
         */
        double getDiameter() const { return 2.0 * radius_; }

        /**
         * @brief 计算球面上的点
         * @param theta 极角（0到π）
         * @param phi 方位角（0到2π）
         * @param transform 变换矩阵
         * @return 球面上的点
         */
        fcl::Vector3d getPointOnSphere(double theta, double phi, 
                                      const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 生成球面上的均匀采样点
         * @param num_points 点的数量
         * @param transform 变换矩阵
         * @return 点列表
         */
        std::vector<fcl::Vector3d> generateUniformPoints(size_t num_points,
                                                         const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 计算球面法向量（在给定点处）
         * @param point 球面上的点
         * @param transform 变换矩阵
         * @return 法向量
         */
        fcl::Vector3d getSurfaceNormal(const fcl::Vector3d& point,
                                      const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const;

        /**
         * @brief 创建单位球
         * @return SphereShape实例
         */
        static std::unique_ptr<SphereShape> createUnitSphere();

        /**
         * @brief 从直径创建球体
         * @param diameter 直径
         * @return SphereShape实例
         */
        static std::unique_ptr<SphereShape> createFromDiameter(double diameter);

    private:
        /**
         * @brief 确保FCL对象已创建
         */
        void ensureFCLObject() const;

        /**
         * @brief 验证半径参数
         */
        void validateRadius() const;
    };

} // namespace NSCore
} // namespace NSDrones