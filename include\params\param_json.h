// include/params/param_json.h
#pragma once

#include "params/parameters.h" 
#include "core/types.h"
#include <nlohmann/json.hpp>
#include <vector>
#include <string>
#include <optional>
#include <typeindex>
#include <system_error>
#include <filesystem> 

// 前向声明，为了避免循环依赖
namespace NSDrones { namespace NSParams { class ParamRegistry; } }

namespace NSDrones {
	namespace NSParams {
		using namespace NSDrones::NSCore;

		using json = nlohmann::json;
		namespace fs = std::filesystem; // C++17 文件系统别名简化

		// --- 错误处理 ---
		enum class ParamJsonError {
			Success = 0,
			FileReadError,            // 文件读取失败
			FileWriteError,           // 文件写入失败
			ParseError,               // JSON 解析错误
			ValidationError,          // 验证失败
			InvalidType,              // 无效类型
			InvalidValueFormat,       // 无效值格式
			MissingRequiredField,     // 缺少必填字段
			KeyNotFound,              // 未找到键
			ValueConversionFailed,    // 值转换失败
			TypeConversionFailed,     // 类型转换失败
			OutputFormattingError,    // 输出格式错误
			UnknownError              // 未知错误
		};

		// 为 ParamJsonError 创建 std::error_category
		class ParamJsonCategory : public std::error_category {
		public:
			const char* name() const noexcept override;
			std::string message(int ev) const override;
			static const ParamJsonCategory& get() { // 单例
				static ParamJsonCategory instance;
				return instance;
			}
		};

		// 使 ParamJsonError 可用于 std::error_code
		std::error_code make_error_code(ParamJsonError e);

		// --- 类型转换辅助函数 ---
		std::string typeIndexToString(const std::type_index& type);
		// 将类型字符串转换为 type_index
		std::optional<std::type_index> stringToTypeIndex(const std::string& type_str);

		/**
		 * @brief 从 JSON 对象中获取参数值
		 * @tparam T 参数值类型
		 * @param json JSON 对象
		 * @param key 参数键名
		 * @param defaultValue 默认值，当参数不存在时返回
		 * @return 参数值或默认值
		 */
		template<typename T>
		T getParamValueFromJson(const nlohmann::json& json, const std::string& key, const T& defaultValue) {
			if (json.contains(key) && !json[key].is_null()) {
				try {
					return json[key].get<T>();
				} catch (const std::exception& e) {
					LOG_WARN("从JSON获取参数 {} 失败: {}，使用默认值: {}", key, e.what(), defaultValue);
					return defaultValue;
				}
			}
			return defaultValue;
		}

		/**
		 * @brief 将 JSON 数组转换为 Eigen 向量
		 * @tparam EigenVectorType Eigen 向量类型
		 * @param j JSON 数组
		 * @return 转换后的 Eigen 向量，如果转换失败则返回 std::nullopt
		 */
		template<typename EigenVectorType>
		std::optional<EigenVectorType> jsonToEigenVector(const nlohmann::json& j) {
			if (!j.is_array()) {
				LOG_WARN("JSON转Eigen：输入不是数组。JSON: {}", j.dump(2));
				return std::nullopt;
			}

			// 检查 EigenVectorType 是否派生自 Eigen::QuaternionBase
			constexpr bool is_quaternion = std::is_base_of_v<Eigen::QuaternionBase<EigenVectorType>, EigenVectorType>;
			
			// 使用 if constexpr 来安全地确定维度
			int determined_dim;
			if constexpr (is_quaternion) {
				determined_dim = 4;
			} else {
				determined_dim = EigenVectorType::RowsAtCompileTime; 
			}
			const int dim = determined_dim;

			if (static_cast<int>(j.size()) != dim) {
				LOG_WARN("JSON转Eigen：数组大小不匹配。期望 {}, 实际 {}. JSON: {}", dim, j.size(), j.dump(2));
				return std::nullopt;
			}
			
			EigenVectorType vec;
			try {
				if constexpr (is_quaternion) {
					// 确保所有元素都是数字
					if (!(j[0].is_number() && j[1].is_number() && j[2].is_number() && j[3].is_number())) {
						LOG_WARN("JSON转EigenQuaternion：数组中包含非数字值。JSON: {}", j.dump(2));
						return std::nullopt;
					}
					// JSON 中的顺序是 w, x, y, z
					vec.w() = j[0].get<typename EigenVectorType::Scalar>();
					vec.x() = j[1].get<typename EigenVectorType::Scalar>();
					vec.y() = j[2].get<typename EigenVectorType::Scalar>();
					vec.z() = j[3].get<typename EigenVectorType::Scalar>();
				} else { // 是 Vector (Matrix 类型)
					for (int i = 0; i < dim; ++i) {
						if (!j[i].is_number()) {
							LOG_WARN("JSON转EigenVector：数组中索引 {} 处包含非数字值。JSON: {}", i, j.dump(2));
							return std::nullopt;
						}
						vec(i) = j[i].get<typename EigenVectorType::Scalar>();
					}
				}
			}
			catch (const json::exception& e) { 
				LOG_ERROR("JSON转Eigen：转换过程中发生 nlohmann::json 异常: {}. JSON: {}", e.what(), j.dump(2));
				return std::nullopt; 
			}
			catch (const std::exception& e) { 
				LOG_ERROR("JSON转Eigen：转换过程中发生标准异常: {}. JSON: {}", e.what(), j.dump(2));
				return std::nullopt; 
			}
			return vec;
		}

		// --- Eigen <-> JSON  ---
		/**
		 * @brief 将 Eigen 向量转换为 JSON 数组
		 * @tparam Derived Eigen 向量或矩阵类型
		 * @param vec Eigen 向量
		 * @return 转换后的 JSON 数组
		 */
		template<typename Derived>
		json eigenVectorToJson(const Eigen::MatrixBase<Derived>& vec) {
			json j_arr = json::array();
			for (Eigen::Index i = 0; i < vec.size(); ++i) { j_arr.push_back(vec(i)); }
			return j_arr;
		}

		// --- 新增: JSON 值到特定几何类型的转换辅助函数 ---
		/**
		 * @brief 从 JSON 对象直接转换为 std::optional<WGS84Point>。
		 * JSON 值应为一个包含2或3个数字的数组 (lon, lat, [alt])。
		 * @param j 输入的 nlohmann::json 对象。
		 * @return 包含转换后 WGS84Point 的可选对象，或在失败时为 std::nullopt。
		 */
		std::optional<WGS84Point> jsonToWGS84Point(const nlohmann::json& j);

		// --- ParamValue 和 JSON 之间的转换 ---
		json paramValueToJson(const ParamValue& value);
		// 将 JSON 转换为 ParamValue，需要提供预期的 C++ 类型
		std::pair<ParamValue, ParamJsonError> jsonToParamValue(const json& j, const std::type_index& expected_type, 
			const std::optional<std::string>& enum_type_name = std::nullopt);

		// --- ParamConstraint 和 JSON 之间的转换 ---
		json paramConstraintToJson(const ParamConstraint& constraint);
		std::pair<ParamConstraint, ParamJsonError> jsonToParamConstraint(const json& j); // 从 JSON 对象的 "type" 字段推断约束类型

		// --- ParamCondition 和 JSON 之间的转换 ---
		json paramConditionToJson(const ParamCondition& pc);
		std::pair<ParamCondition, ParamJsonError> jsonToParamCondition(const json& j);

		// --- ParamDefine 和 JSON 对象之间的转换 ---
		json ParamDefineToJson(const ParamDefine& def);
		std::pair<ParamDefine, ParamJsonError> jsonToParamDefine(const json& j);

		// --- vector<ParamDefine> 和 JSON 数组 (Standard 1) 之间的转换 ---
		json ParamDefineVecToJson(const std::vector<ParamDefine>& defs);
		// 从 JSON 数组解析 ParamDefine 列表, 也就是将标准 1 格式的 JSON 转换为 vector<ParamDefine>
		std::pair<std::vector<ParamDefine>, ParamJsonError> jsonToParamDefineVec(const json& j);
		// 从文件加载 ParamDefine 列表, 支持标准 1 格式的 JSON
		std::pair<std::vector<ParamDefine>, ParamJsonError> loadParamDefinesFromFile(const fs::path& filepath);
		// 将 ParamDefine 列表保存到文件，保存为标准 1 格式的 JSON
		ParamJsonError saveParamDefinesToFile(const std::vector<ParamDefine>& defs, const fs::path& filepath);

		// --- ParamSet 数据 和 JSON 对象 (Standard 2) 之间的转换 ---
		// 将 JSON 对象 (Standard 2) 解析为 key-value 映射，需要 ParamRegistry 注册表来确定每个键的类型
		using ParamValueMap = std::unordered_map<std::string, ParamValue>;
		std::pair<ParamValueMap, ParamJsonError> jsonToParamValueMap(const json& j, const ParamRegistry& registry, const std::string& type_tag);

	} // namespace NSParams
} // namespace NSDrones

// 特化 std::is_error_code_enum 以便 ParamJsonError 可以隐式转换为 std::error_code
namespace std {
	template <>
	struct is_error_code_enum<NSDrones::NSParams::ParamJsonError> : true_type {};
}