// src/planning/task_planners/task_planner_surveycylinder.cpp
#include "planning/task_planners/surveycylinder_taskplanner.h"
#include "planning/planning_types.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/task_strategies.h"
#include "mission/control_point.h"
#include "uav/uav.h"
#include "environment/environment.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"
#include "utils/geometry_manager.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include <vector>
#include <cmath>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <utility>

namespace NSDrones {
	namespace NSPlanning {

		SurveyCylinderTaskPlanner::SurveyCylinderTaskPlanner()
			: ITaskPlanner() {}

		// 初始化方法实现
		bool SurveyCylinderTaskPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) {
			LOG_DEBUG("[SurveyCylinderTaskPlanner] 开始初始化...");

			try {
				// === 加载圆柱勘察特定配置参数 ===
				if (params) {
					// 默认每圈点数
					auto points_per_circle_opt = params->getValue<int>("planning.cylinder.points_per_circle");
					if (points_per_circle_opt.has_value()) {
						default_points_per_circle_ = points_per_circle_opt.value();
						LOG_DEBUG("[SurveyCylinderTaskPlanner] 设置默认每圈点数: {}", default_points_per_circle_);
					}

					// 默认飞行速度
					auto flight_speed_opt = params->getValue<double>("planning.cylinder.default_speed");
					if (flight_speed_opt.has_value()) {
						default_flight_speed_ = flight_speed_opt.value();
						LOG_DEBUG("[SurveyCylinderTaskPlanner] 设置默认飞行速度: {:.2f} m/s", default_flight_speed_);
					}

					// 最小半径限制
					auto min_radius_opt = params->getValue<double>("planning.cylinder.min_radius");
					if (min_radius_opt.has_value()) {
						min_radius_ = min_radius_opt.value();
						LOG_DEBUG("[SurveyCylinderTaskPlanner] 设置最小半径限制: {:.2f} m", min_radius_);
					}

					// 最大高度限制
					auto max_height_opt = params->getValue<double>("planning.cylinder.max_height");
					if (max_height_opt.has_value()) {
						max_height_ = max_height_opt.value();
						LOG_DEBUG("[SurveyCylinderTaskPlanner] 设置最大高度限制: {:.2f} m", max_height_);
					}

					// 是否启用优化
					auto enable_optimization_opt = params->getValue<bool>("planning.cylinder.enable_optimization");
					if (enable_optimization_opt.has_value()) {
						enable_optimization_ = enable_optimization_opt.value();
						LOG_DEBUG("[SurveyCylinderTaskPlanner] 设置优化开关: {}", enable_optimization_ ? "启用" : "禁用");
					}
				}

				// === 处理原始JSON配置 ===
				if (!raw_config.empty() && raw_config.is_object()) {
					LOG_DEBUG("[SurveyCylinderTaskPlanner] 处理原始JSON配置...");

					// 可以在这里处理一些特殊的JSON配置项
					if (raw_config.contains("cylinder_specific")) {
						const auto& cylinder_config = raw_config["cylinder_specific"];

						if (cylinder_config.contains("scan_pattern") && cylinder_config["scan_pattern"].is_string()) {
							scan_pattern_ = cylinder_config["scan_pattern"].get<std::string>();
							LOG_DEBUG("[SurveyCylinderTaskPlanner] 设置扫描模式: {}", scan_pattern_);
						}
					}
				}

				// === 验证配置参数 ===
				if (default_points_per_circle_ < 8 || default_points_per_circle_ > 360) {
					LOG_WARN("[SurveyCylinderTaskPlanner] 每圈点数({})超出合理范围[8,360]，重置为36", default_points_per_circle_);
					default_points_per_circle_ = 36;
				}

				if (default_flight_speed_ <= 0.0 || default_flight_speed_ > 50.0) {
					LOG_WARN("[SurveyCylinderTaskPlanner] 默认飞行速度({:.2f})超出合理范围(0,50]，重置为8.0", default_flight_speed_);
					default_flight_speed_ = 8.0;
				}

				if (min_radius_ <= 0.0) {
					LOG_WARN("[SurveyCylinderTaskPlanner] 最小半径({:.2f})无效，重置为1.0", min_radius_);
					min_radius_ = 1.0;
				}

				LOG_INFO("[SurveyCylinderTaskPlanner] 初始化完成 - 每圈点数:{}, 默认速度:{:.1f}m/s, 最小半径:{:.1f}m, 最大高度:{:.1f}m, 优化:{}",
					default_points_per_circle_, default_flight_speed_, min_radius_, max_height_,
					enable_optimization_ ? "启用" : "禁用");

				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("[SurveyCylinderTaskPlanner] 初始化异常: {}", e.what());
				return false;
			}
		}

		/**
		 * @brief 规划单机圆柱勘察任务（重构后的接口）
		 */
		SingleTaskPlanningResult SurveyCylinderTaskPlanner::planSingleTask(
			const SingleTaskPlanningRequest& request) {

			LOG_INFO("[SurveyCylinderTaskPlanner] 开始规划子任务 [{}]，UAV [{}]",
				request.assignment.sub_task_id, request.assignment.assigned_uav_id);

			SingleTaskPlanningResult result;
			result.success = false;
			result.sub_task_id = request.assignment.sub_task_id;
			result.uav_id = request.assignment.assigned_uav_id;

			try {
				// === 第1步：验证输入 ===
				if (!request.uav) {
					result.message = "无人机指针无效";
					LOG_ERROR("[SurveyCylinderTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				if (!request.original_task) {
					result.message = "原始任务指针无效";
					LOG_ERROR("[SurveyCylinderTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// 验证任务类型
				if (request.original_task->getType() != NSCore::TaskType::SURVEY_CYLINDER) {
					result.message = "任务类型不匹配，期望 SURVEY_CYLINDER，实际 " +
						NSUtils::enumToString(request.original_task->getType());
					LOG_ERROR("[SurveyCylinderTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第2步：获取任务参数 ===
				auto params_ptr = request.original_task->getTaskParameters<NSMission::SurveyCylinderTaskParams>();
				if (!params_ptr) {
					result.message = "无法获取 SurveyCylinder 任务参数";
					LOG_ERROR("[SurveyCylinderTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第3步：获取起始状态 ===
				NSUav::UavState start_state;
				if (!getUavStartState(request.uav, start_state)) {
					result.message = "无法获取无人机起始状态";
					LOG_ERROR("[SurveyCylinderTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第4步：执行圆柱勘察规划 ===
				result = planCylinderTrajectory(request, *params_ptr, start_state);

			} catch (const std::exception& e) {
				result.success = false;
				result.message = "规划异常: " + std::string(e.what());
				LOG_ERROR("[SurveyCylinderTaskPlanner] 子任务 [{}] 规划异常: {}", result.sub_task_id, e.what());
			}

			LOG_INFO("[SurveyCylinderTaskPlanner] 子任务 [{}] 规划完成，成功: {}",
				result.sub_task_id, result.success);
			return result;
		}

		/**
		 * @brief 规划圆柱勘察轨迹的核心实现
		 */
		SingleTaskPlanningResult SurveyCylinderTaskPlanner::planCylinderTrajectory(
			const SingleTaskPlanningRequest& request,
			const NSMission::SurveyCylinderTaskParams& params,
			const NSUav::UavState& start_state) {

			SingleTaskPlanningResult result;
			result.success = false;
			result.sub_task_id = request.assignment.sub_task_id;
			result.uav_id = request.assignment.assigned_uav_id;

			try {
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 开始规划圆柱勘察轨迹，半径: {:.1f}m，高度: {:.1f}m",
					params.radius, params.height);

				// === 第1步：验证几何参数 ===
				if (params.radius <= NSCore::Constants::GEOMETRY_EPSILON ||
					params.height <= NSCore::Constants::GEOMETRY_EPSILON ||
					params.line_spacing <= NSCore::Constants::GEOMETRY_EPSILON) {
					std::ostringstream oss_msg;
					oss_msg << "无效的圆柱勘察参数：半径(" << std::fixed << std::setprecision(2) << params.radius
						<< ")/高度(" << std::fixed << std::setprecision(2) << params.height
						<< ")/行间距(" << std::fixed << std::setprecision(2) << params.line_spacing
						<< ") 必须为正。";
					result.message = oss_msg.str();
					LOG_ERROR("[SurveyCylinderTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				LOG_DEBUG("[SurveyCylinderTaskPlanner] 参数验证通过 - 底面中心: {}, 半径: {:.2f}m, 高度: {:.2f}m, 行间距: {:.2f}m",
					params.center_bottom.position.toString(), params.radius, params.height, params.line_spacing);

				// === 第2步：生成圆柱扫描几何路径 ===
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 生成圆柱扫描几何路径");

				// 使用配置的每圈点数
				int points_per_circle = default_points_per_circle_;
				LOG_TRACE("[SurveyCylinderTaskPlanner] 使用配置的每圈点数: {}", points_per_circle);

				LOG_DEBUG("[SurveyCylinderTaskPlanner] 调用 generateCylinderScanPath: 中心=({}), 半径={:.2f}, 高度={:.2f}, 垂直步长={:.2f}, 每圈点数={}",
					params.center_bottom.position.toString(), params.radius, params.height, params.line_spacing, points_per_circle);

				std::vector<NSCore::WGS84Point> cylinder_scan_path_wgs84 = GeometryManager::generateCylinderScanPath(
					params.center_bottom.position,  // WGS84 中心点
					params.radius,
					params.height,
					params.line_spacing,
					points_per_circle,
					params.clockwise
				);

				if (cylinder_scan_path_wgs84.empty()) {
					result.message = "未能生成有效的圆柱扫描几何路径";
					LOG_ERROR("[SurveyCylinderTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 生成圆柱扫描几何路径 {} 个点。", cylinder_scan_path_wgs84.size());

				// === 第3步：构建完整几何路径 ===
				std::vector<NSCore::WGS84Point> geometric_path;
				geometric_path.reserve(cylinder_scan_path_wgs84.size() + 1);

				// 添加起始点
				geometric_path.push_back(start_state.position);

				// 添加圆柱扫描路径点
				geometric_path.insert(geometric_path.end(), cylinder_scan_path_wgs84.begin(), cylinder_scan_path_wgs84.end());

				LOG_DEBUG("[SurveyCylinderTaskPlanner] 构建几何路径完成，总点数: {}", geometric_path.size());

				// === 第4步：确定飞行速度 ===
				double flight_speed = default_flight_speed_; // 使用配置的默认速度

				// 检查UAV的速度限制
				auto dynamics = request.uav->getDynamicsModel();
				if (dynamics) {
					double max_speed = dynamics->getMaxSpeed();
					if (flight_speed > max_speed) {
						double original_speed = flight_speed;
						flight_speed = max_speed;
						LOG_WARN("[SurveyCylinderTaskPlanner] 期望速度 {:.2f} 超过UAV最大速度 {:.2f}，调整为最大速度",
							original_speed, max_speed);
					}
				}

				LOG_DEBUG("[SurveyCylinderTaskPlanner] 使用飞行速度: {:.2f} m/s", flight_speed);

				// === 第5步：生成轨迹 ===
				Trajectory trajectory;
				if (!smoothAndTimeParameterize(geometric_path, request.uav, start_state,
					flight_speed, trajectory, &result)) {
					result.message = "轨迹生成失败";
					LOG_ERROR("[SurveyCylinderTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第6步：应用避障约束（如果有） ===
				if (!request.avoidance_constraints.empty()) {
					LOG_DEBUG("[SurveyCylinderTaskPlanner] 应用 {} 个避障约束", request.avoidance_constraints.size());

					if (!applyAvoidanceConstraints(trajectory, request.avoidance_constraints, result)) {
						result.message = "应用避障约束失败";
						LOG_ERROR("[SurveyCylinderTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
						return result;
					}
				}

				// === 第7步：验证轨迹 ===
				if (trajectory.empty()) {
					result.message = "生成的轨迹为空";
					LOG_ERROR("[SurveyCylinderTaskPlanner] 子任务 [{}]: {}", result.sub_task_id, result.message);
					return result;
				}

				// === 第8步：设置结果 ===
				result.trajectory = std::move(trajectory);
				result.success = true;
				result.message = "圆柱勘察规划成功";

				LOG_INFO("[SurveyCylinderTaskPlanner] 子任务 [{}] 规划成功，轨迹段数: {}",
					result.sub_task_id, result.trajectory.size());

			} catch (const std::exception& e) {
				result.success = false;
				result.message = "规划异常: " + std::string(e.what());
				LOG_ERROR("[SurveyCylinderTaskPlanner] 子任务 [{}] 规划异常: {}", result.sub_task_id, e.what());
			}

			return result;
		}

		/**
		 * @brief 应用避障约束到轨迹
		 */
		bool SurveyCylinderTaskPlanner::applyAvoidanceConstraints(
			Trajectory& trajectory,
			const std::vector<AvoidanceConstraint>& constraints,
			SingleTaskPlanningResult& result) {

			if (constraints.empty()) {
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 无避障约束需要应用");
				return true;
			}

			LOG_DEBUG("[SurveyCylinderTaskPlanner] 开始应用 {} 个避障约束", constraints.size());

			try {
				// 简化实现：检查轨迹是否与约束冲突
				for (const auto& constraint : constraints) {
					LOG_DEBUG("[SurveyCylinderTaskPlanner] 检查约束: 类型={}, 来源UAV={}",
						static_cast<int>(constraint.type), constraint.source_uav_id);

					// 这里可以实现具体的约束检查逻辑
					// 当前为简化实现，仅记录约束信息
					if (constraint.type == AvoidanceConstraint::AVOID_TRAJECTORY) {
						LOG_DEBUG("[SurveyCylinderTaskPlanner] 应用轨迹避障约束: 来源UAV={}, 安全边距={:.1f}m",
							constraint.source_uav_id, constraint.safety_margin);
					}
					else if (constraint.type == AvoidanceConstraint::AVOID_AREA) {
						LOG_DEBUG("[SurveyCylinderTaskPlanner] 应用区域避障约束: 避障区域点数={}, 安全边距={:.1f}m",
							constraint.area_to_avoid.size(), constraint.safety_margin);
					}
					else if (constraint.type == AvoidanceConstraint::AVOID_TIME_WINDOW) {
						LOG_DEBUG("[SurveyCylinderTaskPlanner] 应用时间窗口避障约束: 时间范围=[{:.1f}, {:.1f}]",
							constraint.start_time, constraint.end_time);
					}
				}

				LOG_DEBUG("[SurveyCylinderTaskPlanner] 避障约束应用完成");
				return true;

			} catch (const std::exception& e) {
				result.warnings.push_back(WarningEvent(WarningType::PLANNING_ERROR,
					"应用避障约束异常: " + std::string(e.what()),
					0.0, {}, result.uav_id));
				LOG_ERROR("[SurveyCylinderTaskPlanner] 应用避障约束异常: {}", e.what());
				return false;
			}
		}

	} // namespace NSPlanning
} // namespace NSDrones