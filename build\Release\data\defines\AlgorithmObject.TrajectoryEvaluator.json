{"description": "Parameters for Trajectory Evaluator algorithms. This defines common parameters shared by all trajectory evaluator implementations.", "parameters": [{"key": "evaluation_resolution", "name": "评估分辨率", "description": "轨迹评估的时间分辨率 (秒)。", "type": "double", "default": 0.1, "constraints": {"type": "numeric", "min": 0.01, "max": 1.0}, "required": false}, {"key": "weight_energy", "name": "能量权重", "description": "能量消耗在总评估分数中的权重。", "type": "double", "default": 0.6, "constraints": {"type": "numeric", "min": 0.0, "max": 1.0}, "required": false}, {"key": "weight_time", "name": "时间权重", "description": "飞行时间在总评估分数中的权重。", "type": "double", "default": 0.3, "constraints": {"type": "numeric", "min": 0.0, "max": 1.0}, "required": false}, {"key": "weight_safety", "name": "安全权重", "description": "安全性在总评估分数中的权重。", "type": "double", "default": 0.1, "constraints": {"type": "numeric", "min": 0.0, "max": 1.0}, "required": false}]}