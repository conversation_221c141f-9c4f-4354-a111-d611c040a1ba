// src/utils/logging.cpp
#include "utils/logging.h"
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <stdexcept>
#include <iostream>
#include <vector>

namespace NSDrones {

	// 初始化静态日志记录器实例指针为空
	std::shared_ptr<spdlog::logger> Logger::logger_instance_;
	std::shared_ptr<spdlog::sinks::stdout_color_sink_mt> Logger::console_sink_;
	std::shared_ptr<spdlog::sinks::basic_file_sink_mt> Logger::file_sink_;

	// 初始化日志行号计数器
	std::atomic<int> Logger::log_line_counter_(1);

	/**
	 * @brief 初始化日志系统
	 * @param log_file 日志文件名
	 * @param truncate 是否覆盖模式
	 * @param console_level 控制台日志级别
	 * @param file_level 文件日志级别
	 */
	void Logger::init(const std::string& log_file, bool truncate,
	                 const std::string& console_level, const std::string& file_level) {
		// 防止重复初始化
		if (logger_instance_) {
			// 如果已经初始化，记录警告并返回
			logger_instance_->warn("日志系统已初始化，忽略重复调用 init()。");
			return;
		}

		try {
			// 配置多个 sink (日志输出目标)
			std::vector<spdlog::sink_ptr> sinks;

			// 1. 创建控制台输出 sink (多线程安全，彩色)
			console_sink_ = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();

			// 设置控制台日志级别
			auto console_spdlog_level = spdlog::level::from_str(console_level);
			console_sink_->set_level(console_spdlog_level);

			// 设置控制台日志格式: [彩色级别]:[文件信息] 日志消息
			console_sink_->set_pattern("[%^%l%$]:[文件：%s，第%#行，函数：%!]: \n%v");
			sinks.push_back(console_sink_); // 添加到 sink 列表

			// 2. 创建文件输出 sink (尝试)
			try {
				// 使用 basic_file_sink_mt 表示多线程安全的基础文件 sink
				file_sink_ = std::make_shared<spdlog::sinks::basic_file_sink_mt>(log_file, truncate);

				// 设置文件日志级别
				auto file_spdlog_level = spdlog::level::from_str(file_level);
				file_sink_->set_level(file_spdlog_level);

				// 设置文件日志格式: [级别]:[文件信息] 日志消息 (文件中通常不使用颜色)
				file_sink_->set_pattern("[%l]:[文件：%s，第%#行，函数：%!]: \n%v");
				sinks.push_back(file_sink_); // 添加到 sink 列表
			}
			catch (const spdlog::spdlog_ex& ex) {
				// 如果文件创建失败，输出错误到标准错误流 (此时 logger_instance_ 还未创建)
				std::cerr << "[日志初始化错误] 无法创建日志文件 sink '" << log_file << "': " << ex.what() << ". 将仅使用控制台输出。" << std::endl;
				// 可以选择继续只使用控制台 sink，或者抛出异常使程序停止
				// 这里选择继续只使用控制台
			}

			// 3. 创建并注册组合 logger
			// 使用 "DronesFrameworkLogger" 作为 logger 的名称
			logger_instance_ = std::make_shared<spdlog::logger>("DronesFrameworkLogger", begin(sinks), end(sinks));

			// 设置 logger 的总级别 (通常设为最低级别，由 sink 控制实际输出哪个级别)
			// 这里设为 trace，表示所有级别的日志都会传递给 sinks
			logger_instance_->set_level(spdlog::level::trace);

			// 设置当日志队列满时的行为 (可选, 默认为阻塞)
			// logger_instance_->flush_on(spdlog::level::warn); // 例如：当记录警告或更高级别时立即刷新缓冲区到文件

			// 注册 logger，以便可以通过 spdlog::get() 按名称获取 (可选)
			spdlog::register_logger(logger_instance_);

			// 将此 logger 设置为默认 logger，允许直接使用 spdlog::info() 等不带 logger 名称的宏 (推荐)
			spdlog::set_default_logger(logger_instance_);

			// 使用新创建的 logger 记录初始化完成信息
			LOG_INFO("日志系统初始化完成。日志文件: '{}', 覆盖模式: {}, 控制台级别: {}, 文件级别: {}",
			         log_file, truncate, console_level, file_level);

		}
		catch (const std::exception& e) { // 捕获初始化过程中的其他标准异常
			std::cerr << "[日志初始化关键错误] Logger 设置期间发生异常: " << e.what() << std::endl;
			// 确保 logger_instance_ 为空，以便 get_logger() 知道初始化失败
			logger_instance_.reset();
			// 抛出运行时错误，通常表示程序无法继续
			throw std::runtime_error("日志系统初始化失败");
		}
		catch (...) { // 捕获所有未知异常
			std::cerr << "[日志初始化关键错误] Logger 设置期间发生未知异常。" << std::endl;
			logger_instance_.reset();
			throw std::runtime_error("日志系统初始化时发生未知错误");
		}
	}

	/**
	 * @brief 获取全局日志记录器实例。
	 * @return spdlog::logger 的共享指针引用。
	 * @throws std::runtime_error 如果在 init() 调用之前调用。
	 */
	std::shared_ptr<spdlog::logger>& Logger::get_logger() {
		if (!logger_instance_) {
			// 如果日志实例尚未创建 (init 未调用或失败)，抛出异常
			// 这是严格模式，确保日志系统已正确初始化
			throw std::runtime_error("日志系统尚未初始化 (必须先调用 Logger::init())。");
		}
		// 返回已初始化的日志记录器实例
		return logger_instance_;
	}

	/**
	 * @brief 关闭日志系统，确保所有挂起的日志都被刷新。
	 */
	void Logger::shutdown() {
		if (logger_instance_) {
			// 使用 LOG_INFO 可能会在 shutdown 过程中尝试重新获取 logger，这可能导致问题
			// 因此，在 shutdown 期间，如果需要日志，直接使用 cerr 或 cout，或者确保 LOG_INFO 不会触发 get_logger() 的异常
			// 或者，在调用 spdlog::shutdown() 之前记录。
			logger_instance_->info("Logger::shutdown() called. Shutting down spdlog."); // 在 shutdown 前记录

			// 在调用 spdlog::shutdown() 之前，显式刷新所有 sinks
			// 这对于确保异步日志被写入尤为重要
			logger_instance_->flush();

			spdlog::shutdown(); // 正确关闭 spdlog，会刷新所有 sinks 并释放资源

			// spdlog::shutdown() 之后，logger_instance_ 所引用的 logger 对象已失效
			// LOG_XXX 宏也不应再使用，因为它们依赖于 spdlog 的全局状态或默认 logger
			logger_instance_.reset(); // 清理我们自己的共享指针实例

			// Shutdown 后，如果需要记录任何信息，应该使用标准输出/错误流
			std::cout << "[Logger Shutdown] spdlog has been shut down." << std::endl;
		} else {
			std::cerr << "[Logger Shutdown] Logger was not initialized or already shut down." << std::endl;
		}
	}

	/**
	 * @brief 获取下一个日志行号（四位整数格式）
	 * @return 格式化的四位整数行号字符串
	 */
	int Logger::get_next_log_line_number() {
		int current_line = log_line_counter_.fetch_add(1);
		// 如果行号超过9999，重置为1（保持四位数格式）
		if (current_line > 9999) {
			log_line_counter_.store(1);
			return 1;
		}
		return current_line;
	}

	/**
	 * @brief 设置控制台日志级别
	 */
	bool Logger::set_console_level(const std::string& level_str) {
		if (!logger_instance_ || !console_sink_) {
			std::cerr << "[控制台级别设置错误] 日志系统或控制台输出未初始化。" << std::endl;
			return false;
		}

		try {
			auto new_level = spdlog::level::from_str(level_str);
			console_sink_->set_level(new_level);

			// 更新主记录器级别
			auto file_level = file_sink_ ? file_sink_->level() : spdlog::level::off;
			auto overall_level = (new_level < file_level) ? new_level : file_level;
			if (!file_sink_) overall_level = new_level;

			logger_instance_->set_level(overall_level);
			logger_instance_->flush();

			std::cout << "[控制台级别设置] 控制台日志级别已设置为: " << level_str << std::endl;
			return true;
		}
		catch (const std::exception& e) {
			std::cerr << "[控制台级别设置错误] 设置控制台日志级别时发生异常: " << e.what() << std::endl;
			return false;
		}
	}

	/**
	 * @brief 设置文件日志级别
	 */
	bool Logger::set_file_level(const std::string& level_str) {
		if (!logger_instance_ || !file_sink_) {
			std::cerr << "[文件级别设置错误] 日志系统或文件输出未初始化。" << std::endl;
			return false;
		}

		try {
			auto new_level = spdlog::level::from_str(level_str);
			file_sink_->set_level(new_level);

			// 更新主记录器级别
			auto console_level = console_sink_ ? console_sink_->level() : spdlog::level::off;
			auto overall_level = (console_level < new_level) ? console_level : new_level;
			if (!console_sink_) overall_level = new_level;

			logger_instance_->set_level(overall_level);
			logger_instance_->flush();

			std::cout << "[文件级别设置] 文件日志级别已设置为: " << level_str << std::endl;
			return true;
		}
		catch (const std::exception& e) {
			std::cerr << "[文件级别设置错误] 设置文件日志级别时发生异常: " << e.what() << std::endl;
			return false;
		}
	}

	/**
	 * @brief 获取当前控制台日志级别
	 */
	std::string Logger::get_console_level() {
		if (!console_sink_) {
			return "未初始化";
		}
		return std::string(spdlog::level::to_short_c_str(console_sink_->level()));
	}

	/**
	 * @brief 获取当前文件日志级别
	 */
	std::string Logger::get_file_level() {
		if (!file_sink_) {
			return "未初始化";
		}
		return std::string(spdlog::level::to_short_c_str(file_sink_->level()));
	}

} // namespace NSDrones