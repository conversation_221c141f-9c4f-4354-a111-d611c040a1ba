// include/environment/collision/collision_engine.h
#pragma once

#include "environment/collision/collision_types.h"
#include "environment/collision/detector_registry.h"
#include "core/entity_object.h"
#include "core/geometry/ishape.h" 
#include "environment/indices/ispatial_index.h"
#include "environment/storage/object_storage.h"
#include "environment/coordinate/coordinate_manager.h"
#include "environment/coordinate/task_space.h"
#include "utils/coordinate_converter.h"
#include "core/types.h"
#include "utils/geometry_manager.h"
#include "utils/logging.h"
#include <vector>
#include <memory>
#include <unordered_map>
#include <unordered_set>

// 前向声明 ObjectStorage
template<typename T = std::unordered_map<ObjectID, std::shared_ptr<EntityObject>>>
class ObjectStorage;

namespace NSDrones {
	namespace NSEnvironment {

		/**
		 * @class CollisionEngine
		 * @brief 碰撞检测引擎
		 *
		 * 高性能的碰撞检测引擎，提供多种类型的碰撞检测服务：
		 * - 对象间碰撞检测
		 * - 对象与环境碰撞检测
		 * - 几何体与环境碰撞检测
		 * - 点和线段碰撞检测
		 *
		 * 特性：
		 * - 基于FCL库的高精度碰撞检测
		 * - 支持WGS84和ECEF坐标系
		 * - 集成空间索引优化性能
		 * - 可配置的检测选项
		 * - 详细的碰撞信息提取
		 *
		 * @tparam ObjectContainer 对象容器类型，默认为unordered_map
		 */
		template<typename ObjectContainer = std::unordered_map<ObjectID, std::shared_ptr<EntityObject>>>
		class CollisionEngine {
		public:
			// === 构造函数和析构函数 ===

			/**
			 * @brief 构造函数
			 * @param object_storage 对象存储引用
			 * @param spatial_index 空间索引引用
			 * @param coordinate_manager 坐标系统管理器（可选）
			 */
			CollisionEngine(
				ObjectStorage<ObjectContainer>& object_storage,
				ISpatialIndex& spatial_index,
				std::shared_ptr<CoordinateManager> coordinate_manager = nullptr
			) : object_storage_(object_storage),
				spatial_index_(spatial_index),
				coordinate_manager_(coordinate_manager) {
				LOG_INFO("碰撞检测引擎: 初始化完成，集成对象存储、空间索引和坐标系统管理器");
			}

			/**
			 * @brief 析构函数
			 */
			~CollisionEngine() = default;

			// === 初始化和配置 ===

			/**
			 * @brief 初始化碰撞引擎
			 * @param global_params 全局参数配置
			 * @return 初始化成功返回true
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> global_params);

			/**
			 * @brief 设置坐标系统管理器
			 * @param coordinate_manager 坐标系统管理器
			 */
			void setCoordinateManager(std::shared_ptr<CoordinateManager> coordinate_manager) {
				coordinate_manager_ = coordinate_manager;
				LOG_INFO("碰撞检测引擎: 已更新坐标系统管理器");
			}

			// === 对象碰撞检测接口 ===

			/**
			 * @brief 检测单个对象与环境的碰撞
			 * @param objectId 对象ID
			 * @param options 碰撞检测选项
			 * @return 碰撞结果列表
			 */
			std::vector<CollisionResult> checkObjectCollisions(
				ObjectID objectId,
				const CollisionOptions& options = CollisionOptions{}
			);

			/**
			 * @brief 检测场景中所有对象间的碰撞
			 * @param options 碰撞检测选项
			 * @return 碰撞结果列表
			 */
			std::vector<CollisionResult> checkAllCollisions(
				const CollisionOptions& options = CollisionOptions{}
			);

			/**
			 * @brief 检测两个指定对象间的碰撞
			 * @param obj1 第一个对象
			 * @param obj2 第二个对象
			 * @param options 碰撞检测选项
			 * @return 碰撞结果
			 */
			CollisionResult checkObjectCollision(
				const EntityObject& obj1,
				const EntityObject& obj2,
				const CollisionOptions& options = CollisionOptions{}
			);

			// === 几何体碰撞检测接口 ===

			/**
			 * @brief 检测临时几何体与环境的碰撞
			 * @param temp_shape 临时几何体
			 * @param ecef_position ECEF坐标位置
			 * @param orientation 几何体朝向
			 * @param options 碰撞检测选项
			 * @return 碰撞结果列表
			 */
			std::vector<CollisionResult> checkGeometryAgainstEnvironment(
				const IShape& temp_shape,
				const EcefPoint& ecef_position,
				const Orientation& orientation,
				const CollisionOptions& options = CollisionOptions{}
			);

			// === 基础几何碰撞检测接口 ===

			/**
			 * @brief 检测点与环境的碰撞
			 * @param ecef_point 查询点的ECEF坐标
			 * @param safety_radius 安全半径（米）
			 * @param options 碰撞检测选项
			 * @return 碰撞结果列表
			 */
			std::vector<CollisionResult> checkPointAgainstEnvironment(
				const EcefPoint& ecef_point,
				double safety_radius,
				const CollisionOptions& options = CollisionOptions{}
			);

			/**
			 * @brief 检测线段与环境的碰撞
			 * @param ecef_start 线段起点ECEF坐标
			 * @param ecef_end 线段终点ECEF坐标
			 * @param safety_radius 安全半径（米）
			 * @param options 碰撞检测选项
			 * @return 碰撞结果列表
			 */
			std::vector<CollisionResult> checkSegmentAgainstEnvironment(
				const EcefPoint& ecef_start,
				const EcefPoint& ecef_end,
				double safety_radius,
				const CollisionOptions& options = CollisionOptions{}
			);

			// === 访问器方法 ===

			/**
			 * @brief 获取对象存储引用
			 * @return 对象存储的常量引用
			 */
			const ObjectStorage<ObjectContainer>& getObjectStorage() const {
				return object_storage_;
			}

			/**
			 * @brief 获取坐标系统管理器
			 * @return 坐标系统管理器的共享指针
			 */
			std::shared_ptr<CoordinateManager> getCoordinateManager() const {
				return coordinate_manager_;
			}

			/**
			 * @brief 根据对象ID获取对象
			 * @param id 对象ID
			 * @return 对象的共享指针，未找到返回nullptr
			 */
			std::shared_ptr<EntityObject> getObject(ObjectID id) const;

			// === 对象间碰撞检测接口（供Environment等外部调用） ===

			/**
			 * @brief 检测对象与环境的碰撞
			 * @param obj 要检测的对象
			 * @param options 碰撞检测选项
			 * @return 碰撞结果列表
			 */
			std::vector<CollisionResult> checkObjectAgainstEnvironment(
				std::shared_ptr<EntityObject> obj,
				const CollisionOptions& options = CollisionOptions{}
			);

			/**
			 * @brief 检测两个对象间的碰撞
			 * @param obj1 第一个对象
			 * @param obj2 第二个对象
			 * @param options 碰撞检测选项
			 * @return 碰撞结果列表
			 */
			std::vector<CollisionResult> checkObjectAgainstObject(
				std::shared_ptr<EntityObject> obj1,
				std::shared_ptr<EntityObject> obj2,
				const CollisionOptions& options = CollisionOptions{}
			);

		private:
			// === 成员变量 ===
			DetectorRegistry detector_registry_;                           ///< 碰撞检测器注册表
			ObjectStorage<ObjectContainer>& object_storage_;               ///< 对象存储引用
			ISpatialIndex& spatial_index_;                                 ///< 空间索引引用
			std::shared_ptr<CoordinateManager> coordinate_manager_;        ///< 坐标系统管理器（可选）

			// === FCL对象创建方法 ===

			/**
			 * @brief 创建FCL碰撞对象（ECEF坐标系）
			 * @param shape 几何形状指针
			 * @param ecef_position ECEF坐标位置
			 * @param orientation 几何体朝向（四元数）
			 * @return FCL碰撞对象，创建失败返回nullptr
			 */
			std::shared_ptr<fcl::CollisionObjectd> createFCLObject(
				const IShape* shape,
				const EcefPoint& ecef_position,
				const Orientation& orientation
			) const {
				if (!shape) {
					LOG_WARN("碰撞检测引擎: 尝试为空形状创建FCL对象");
					return nullptr;
				}

				auto geometry = shape->getFCLGeometry();
				if (!geometry) {
					LOG_WARN("碰撞检测引擎: 无法获取形状 {} 的FCL几何体", shape->toString());
					return nullptr;
				}

				// 创建变换矩阵
				fcl::Transform3d transform = NSUtils::GeometryManager::createFCLTransform(ecef_position, orientation);
				auto fcl_object = std::make_shared<fcl::CollisionObjectd>(geometry, transform);

				LOG_TRACE("碰撞检测引擎: 成功创建FCL对象，形状: {}，ECEF位置: [{:.2f},{:.2f},{:.2f}]",
					shape->toString(), ecef_position.x(), ecef_position.y(), ecef_position.z());

				return fcl_object;
			}

			/**
			 * @brief 创建FCL碰撞对象（从WGS84坐标转换）
			 * @param shape 几何形状指针
			 * @param wgs84_position WGS84坐标位置
			 * @param orientation 几何体朝向（四元数）
			 * @return FCL碰撞对象，创建失败返回nullptr
			 */
			std::shared_ptr<fcl::CollisionObjectd> createFCLObjectFromWGS84(
				const IShape* shape,
				const WGS84Point& wgs84_position,
				const Orientation& orientation
			) const {
				if (!shape) {
					LOG_WARN("碰撞检测引擎: 尝试为空形状创建FCL对象");
					return nullptr;
				}

				// 使用CoordinateConverter进行坐标转换
				EcefPoint ecef_position = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_position);

				LOG_TRACE("碰撞检测引擎: WGS84坐标 {} 转换为ECEF坐标进行FCL对象创建",
					wgs84_position.toString());

				return createFCLObject(shape, ecef_position, orientation);
			}

			// === 碰撞检测辅助方法 ===
			/**
			 * @brief 查找潜在碰撞对象
			 * @param query_bounds 查询边界框
			 * @param options 碰撞检测选项
			 * @return 潜在碰撞对象ID列表
			 */
			std::vector<ObjectID> findPotentialColliders(
				const WGS84BoundingBox& query_bounds,
				const CollisionOptions& options
			) const;

			/**
			 * @brief 执行详细碰撞检测
			 * @param query_fcl_object 查询对象的FCL对象
			 * @param query_shape_type 查询对象的形状类型
			 * @param query_object_id 查询对象ID
			 * @param potential_colliders 潜在碰撞对象列表
			 * @param options 碰撞检测选项
			 * @return 碰撞结果列表
			 */
			std::vector<CollisionResult> performDetailedCollisionCheck(
				const fcl::CollisionObjectd* query_fcl_object,
				ShapeType query_shape_type,
				ObjectID query_object_id,
				const std::vector<ObjectID>& potential_colliders,
				const CollisionOptions& options
			) const;

			/**
			 * @brief 检查是否应该检测指定对象
			 * @param object_id 对象ID
			 * @param options 碰撞检测选项
			 * @return 如果应该检测返回true
			 */
			bool shouldCheckObject(
				ObjectID object_id,
				const CollisionOptions& options
			) const;

			/**
			 * @brief 检查是否应该检测对象对
			 * @param obj1_id 第一个对象ID
			 * @param obj2_id 第二个对象ID
			 * @param options 碰撞检测选项
			 * @return 如果应该检测返回true
			 */
			bool shouldCheckObjectPair(
				ObjectID obj1_id,
				ObjectID obj2_id,
				const CollisionOptions& options
			) const;

			/**
			 * @brief 检查是否应该保存碰撞结果
			 * @param result 碰撞结果
			 * @param options 碰撞检测选项
			 * @return 如果应该保存返回true
			 */
			bool shouldSaveResult(
				const CollisionResult& result,
				const CollisionOptions& options
			) const;



			/**
			 * @brief 合并唯一的碰撞结果
			 * @param target 目标结果列表
			 * @param source 源结果列表
			 */
			void mergeUniqueResults(
				std::vector<CollisionResult>& target,
				const std::vector<CollisionResult>& source
			) const;

		};

	} // namespace NSEnvironment
} // namespace NSDrones
