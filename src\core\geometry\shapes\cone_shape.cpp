#include "core/geometry/shapes/cone_shape.h"
#include "utils/enum_utils.h"
#include <stdexcept>
#include <sstream>
#include <cmath>
#include <algorithm>

namespace NSDrones {
namespace NSCore {

    ConeShape::ConeShape(double radius, double height) 
        : radius_(radius), height_(height), fcl_cone_(nullptr) {
        validateDimensions();
    }

    ConeShape::ConeShape() : ConeShape(1.0, 2.0) {
    }

    ConeShape::ConeShape(const ConeShape& other)
        : radius_(other.radius_), height_(other.height_), fcl_cone_(nullptr) {
    }

    ConeShape& ConeShape::operator=(const ConeShape& other) {
        if (this != &other) {
            radius_ = other.radius_;
            height_ = other.height_;
            fcl_cone_.reset();  // 重置FCL对象，延迟重新创建
        }
        return *this;
    }

    std::shared_ptr<fcl::CollisionGeometryd> ConeShape::getFCLGeometry() const {
        ensureFCLObject();
        return fcl_cone_;
    }

    fcl::AABBd ConeShape::getAABB(const fcl::Transform3d& transform) const {
        ensureFCLObject();
        fcl::AABBd aabb;
        fcl::computeBV(*fcl_cone_, transform, aabb);
        return aabb;
    }

    double ConeShape::getVolume() const {
        return (1.0 / 3.0) * M_PI * radius_ * radius_ * height_;
    }

    double ConeShape::getSurfaceArea() const {
        double slant_height = getSlantHeight();
        return M_PI * radius_ * (radius_ + slant_height);
    }

    fcl::Vector3d ConeShape::getCentroid() const {
        return fcl::Vector3d(0.0, 0.0, 0.0);  // 几何中心在原点
    }

    fcl::Matrix3d ConeShape::getInertiaMatrix(double mass) const {
        // 圆锥体的惯性张量（相对于质心，Z轴为圆锥轴）
        double r2 = radius_ * radius_;
        double h2 = height_ * height_;
        
        fcl::Matrix3d inertia = fcl::Matrix3d::Zero();
        inertia(0, 0) = mass / 80.0 * (12.0 * r2 + 3.0 * h2);  // Ixx
        inertia(1, 1) = mass / 80.0 * (12.0 * r2 + 3.0 * h2);  // Iyy
        inertia(2, 2) = mass / 10.0 * (3.0 * r2);              // Izz
        
        return inertia;
    }

    std::unique_ptr<IShape> ConeShape::clone() const {
        return std::make_unique<ConeShape>(*this);
    }

    nlohmann::json ConeShape::serialize() const {
        nlohmann::json j;
        j["type"] = NSUtils::enumToString(getType());
        j["radius"] = radius_;
        j["height"] = height_;
        return j;
    }

    bool ConeShape::deserialize(const nlohmann::json& json) {
        try {
            std::string type_str = json.at("type").get<std::string>();
            auto type_opt = NSUtils::stringToEnum<ShapeType>(type_str, ShapeType::UNKNOWN);
            if (type_opt != ShapeType::CONE) {
                return false;
            }
            
            radius_ = json.at("radius").get<double>();
            height_ = json.at("height").get<double>();
            
            validateDimensions();
            fcl_cone_.reset();  // 重置FCL对象
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    std::string ConeShape::toString() const {
        std::ostringstream oss;
        oss << "ConeShape(radius=" << radius_ << ", height=" << height_ << ")";
        return oss.str();
    }

    bool ConeShape::containsPoint(const fcl::Vector3d& point) const {
        double half_height = height_ * 0.5;
        
        // 检查Z轴范围
        if (point.z() < -half_height || point.z() > half_height) {
            return false;
        }
        
        // 计算在当前高度处的圆锥半径
        // 圆锥顶点在+Z方向，底面在-Z方向
        double z_normalized = (point.z() + half_height) / height_;  // 0到1
        double cone_radius_at_z = radius_ * (1.0 - z_normalized);
        
        // 检查径向距离
        double radial_distance = std::sqrt(point.x() * point.x() + point.y() * point.y());
        return radial_distance <= cone_radius_at_z;
    }

    double ConeShape::distanceToPoint(const fcl::Vector3d& point) const {
        double half_height = height_ * 0.5;
        
        if (containsPoint(point)) {
            // 点在内部，计算到最近表面的距离
            double z_normalized = (point.z() + half_height) / height_;
            double cone_radius_at_z = radius_ * (1.0 - z_normalized);
            double radial_distance = std::sqrt(point.x() * point.x() + point.y() * point.y());
            
            // 到侧面的距离
            double lateral_dist = cone_radius_at_z - radial_distance;
            
            // 到底面的距离
            double base_dist = point.z() + half_height;
            
            return -std::min(lateral_dist, base_dist);
        }
        
        // 点在外部，计算最小距离
        // 这是一个复杂的计算，这里使用简化版本
        double radial_distance = std::sqrt(point.x() * point.x() + point.y() * point.y());
        
        if (point.z() < -half_height) {
            // 在底面下方
            if (radial_distance <= radius_) {
                return -half_height - point.z();
            } else {
                double dx = radial_distance - radius_;
                double dz = -half_height - point.z();
                return std::sqrt(dx * dx + dz * dz);
            }
        } else if (point.z() > half_height) {
            // 在顶点上方
            double dx = radial_distance;
            double dz = point.z() - half_height;
            return std::sqrt(dx * dx + dz * dz);
        } else {
            // 在圆锥高度范围内
            double z_normalized = (point.z() + half_height) / height_;
            double cone_radius_at_z = radius_ * (1.0 - z_normalized);
            return radial_distance - cone_radius_at_z;
        }
    }

    double ConeShape::getCharacteristicSize() const {
        // 使用对角线长度作为特征尺寸
        return std::sqrt(4.0 * radius_ * radius_ + height_ * height_);
    }

    void ConeShape::setSize(double radius, double height) {
        radius_ = radius;
        height_ = height;
        validateDimensions();
        fcl_cone_.reset();  // 重置FCL对象
    }

    double ConeShape::getBaseArea() const {
        return M_PI * radius_ * radius_;
    }

    double ConeShape::getLateralArea() const {
        double slant_height = getSlantHeight();
        return M_PI * radius_ * slant_height;
    }

    double ConeShape::getSlantHeight() const {
        return std::sqrt(radius_ * radius_ + height_ * height_);
    }

    fcl::Vector3d ConeShape::getPointOnCone(double theta, double z,
                                           const fcl::Transform3d& transform) const {
        // 确保z在有效范围内
        double half_height = height_ * 0.5;
        z = std::clamp(z, -half_height, half_height);
        
        // 计算在当前高度处的圆锥半径
        double z_normalized = (z + half_height) / height_;
        double cone_radius_at_z = radius_ * (1.0 - z_normalized);
        
        // 圆锥坐标转换为笛卡尔坐标
        double x = cone_radius_at_z * std::cos(theta);
        double y = cone_radius_at_z * std::sin(theta);
        
        fcl::Vector3d point(x, y, z);
        return transform * point;
    }

    std::vector<fcl::Vector3d> ConeShape::generateUniformPoints(size_t num_circumference, 
                                                               size_t num_height, 
                                                               bool include_base,
                                                               const fcl::Transform3d& transform) const {
        std::vector<fcl::Vector3d> points;
        
        double half_height = height_ * 0.5;
        
        // 生成侧面点
        for (size_t i = 0; i < num_circumference; ++i) {
            double theta = 2.0 * M_PI * i / num_circumference;
            for (size_t j = 0; j < num_height; ++j) {
                double z = -half_height + height_ * j / (num_height - 1);
                points.push_back(getPointOnCone(theta, z, transform));
            }
        }
        
        // 生成底面点
        if (include_base) {
            for (size_t i = 0; i < num_circumference; ++i) {
                double theta = 2.0 * M_PI * i / num_circumference;
                
                // 底面
                fcl::Vector3d base_point(radius_ * std::cos(theta), radius_ * std::sin(theta), -half_height);
                points.push_back(transform * base_point);
            }
            
            // 添加顶点
            fcl::Vector3d apex(0.0, 0.0, half_height);
            points.push_back(transform * apex);
        }
        
        return points;
    }

    fcl::Vector3d ConeShape::getSurfaceNormal(const fcl::Vector3d& point,
                                             const fcl::Transform3d& transform) const {
        // 将点转换到局部坐标系
        fcl::Vector3d local_point = transform.inverse() * point;
        
        double half_height = height_ * 0.5;
        fcl::Vector3d normal;
        
        // 判断点在哪个表面上
        if (std::abs(local_point.z() + half_height) < 1e-6) {
            // 底面
            normal = fcl::Vector3d(0.0, 0.0, -1.0);
        } else {
            // 侧面
            double radial_distance = std::sqrt(local_point.x() * local_point.x() + local_point.y() * local_point.y());
            if (radial_distance > 1e-10) {
                // 圆锥侧面的法向量
                double slant_height = getSlantHeight();
                double nx = local_point.x() / radial_distance * height_ / slant_height;
                double ny = local_point.y() / radial_distance * height_ / slant_height;
                double nz = radius_ / slant_height;
                normal = fcl::Vector3d(nx, ny, nz).normalized();
            } else {
                // 在轴上，使用默认法向量
                normal = fcl::Vector3d(1.0, 0.0, 0.0);
            }
        }
        
        // 转换回全局坐标系（只应用旋转）
        return transform.rotation() * normal;
    }

    std::unique_ptr<ConeShape> ConeShape::createUnitCone() {
        return std::make_unique<ConeShape>(1.0, 2.0);
    }

    std::unique_ptr<ConeShape> ConeShape::createFromDiameter(double diameter, double height) {
        return std::make_unique<ConeShape>(diameter * 0.5, height);
    }

    void ConeShape::ensureFCLObject() const {
        if (!fcl_cone_) {
            fcl_cone_ = std::make_shared<fcl::Coned>(radius_, height_);
        }
    }

    void ConeShape::validateDimensions() const {
        if (radius_ <= 0.0 || height_ <= 0.0) {
            throw std::invalid_argument("ConeShape: 半径和高度必须为正数");
        }
        if (!std::isfinite(radius_) || !std::isfinite(height_)) {
            throw std::invalid_argument("ConeShape: 尺寸必须为有限数值");
        }
    }

} // namespace NSCore
} // namespace NSDrones
