// include/uav/uav_config.h
#pragma once

#include "drones.h"            
#include "params/param_defs.h" 
#include <string>
#include <memory>
#include <optional> 
#include <nlohmann/json.hpp>

namespace NSDrones {
	namespace NSParams { class ParamValues; } 
	namespace NSConfig { class ParameterManager; } 
}
using json = nlohmann::json;

namespace NSDrones {
	namespace NSUav {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class UavConfig
		 * @brief 代表一个无人机类型的静态配置模板。
		 *
		 * 包含无人机的 ID、类型、名称、物理属性，以及指向具体动力学和能量模型参数的**不可变**共享指针。
		 * 这个类本身不包含模型实现，只包含配置数据。
		 * 配置通常在启动时加载，并在模拟过程中保持不变。
		 */
		class UavConfig {
		public:
			/**
			 * @brief 构造函数。
			 * @param id 配置的唯一标识符。
			 * @param uav_type 无人机类型。
			 * @param physicalProps 物理属性参数。
			 * @param dyn_params 指向动力学参数的 const 共享指针。
			 * @param energy_params 指向能量参数的 const 共享指针。
			 * @throws DroneException 如果输入参数无效或类型不匹配。
			 */
			UavConfig(ObjectID id,
				UavType uav_type,
				std::shared_ptr<const NSParams::UAVPhysicalProperties> physicalProps,
				std::shared_ptr<const NSParams::IDynamicParams> dyn_params,
				std::shared_ptr<const NSParams::IEnergyParams> energy_params);

			// --- 禁止拷贝和移动 (配置应该是不可变的，或通过加载器创建) ---
			UavConfig(const UavConfig&) = delete;
			UavConfig& operator=(const UavConfig&) = delete;
			UavConfig(UavConfig&&) = delete;
			UavConfig& operator=(UavConfig&&) = delete;

			// --- Getters ---
			const ObjectID& getId() const { return id_; } // 获取配置 ID
			UavType getType() const { return type_; }    // 获取无人机类型
			const std::string& getName() const { return name_; } // 获取配置名称
			void setName(const std::string& name) { name_ = name; } // 名称可修改 (例如加载后设置)

			/** @brief 获取物理属性参数 (const 指针)。 */
			std::shared_ptr<const NSParams::UAVPhysicalProperties> getPhysicalProperties() const { return physical_properties_; }
			
			/** @brief 获取动力学参数基类指针 (const 指针)。 */
			std::shared_ptr<const NSParams::IDynamicParams> getDynamicsParams() const { return dynamics_params_; }
			/**
			 * @brief 获取特定类型的动力学参数 (需要 dynamic_pointer_cast)。
			 * @tparam T 期望的动力学参数类型 (例如 MultirotorDynamicsParams)。
			 * @return 指向特定类型参数的 const 共享指针，如果类型不匹配则为空。
			 */
			template<typename T>
			std::shared_ptr<const T> getDynamicsParams() const {
				return std::dynamic_pointer_cast<const T>(dynamics_params_);
			}

			/** @brief 获取能量参数基类指针 (const 指针)。 */
			std::shared_ptr<const NSParams::IEnergyParams> getEnergyParams() const { return energy_params_; }
			/**
			 * @brief 获取特定类型的能量参数 (需要 dynamic_pointer_cast)。
			 * @tparam T 期望的能量参数类型 (例如 MultirotorEnergyParams)。
			 * @return 指向特定类型参数的 const 共享指针，如果类型不匹配则为空。
			 */
			template<typename T>
			std::shared_ptr<const T> getEnergyParams() const {
				return std::dynamic_pointer_cast<const T>(energy_params_);
			}

		private:
			const ObjectID id_;           // 配置 ID (不可变)
			const UavType type_;          // 无人机类型 (不可变)
			std::string name_;      // 配置名称 (可变)

			// 指向配置参数的共享指针
			std::shared_ptr<const NSParams::UAVPhysicalProperties> physical_properties_;
			std::shared_ptr<const NSParams::IDynamicParams> dynamics_params_;
			std::shared_ptr<const NSParams::IEnergyParams> energy_params_;

		};

		// UavConfig 智能指针类型别名
		using UAVConfigPtr = std::shared_ptr<UavConfig>;
		using ConstUAVConfigPtr = std::shared_ptr<const UavConfig>;

	} // namespace NSUav
} // namespace NSDrones