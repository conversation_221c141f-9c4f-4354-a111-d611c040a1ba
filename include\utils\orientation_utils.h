#pragma once

#include "core/types.h"
#include <vector>

namespace NSDrones {
    namespace NSUtils {
        using namespace NSCore;

        /**
         * @class OrientationUtils
         * @brief 姿态转换工具类
         * 
         * 提供各种姿态表示之间的转换功能：
         * - YPR (Yaw-Pitch-Roll) 欧拉角
         * - 四元数 (Quaternion)
         * - 旋转矩阵 (Rotation Matrix)
         * 
         * 坐标系约定：
         * - NED 坐标系：x=North, y=East, z=Down
         * - 机体坐标系：x=前向, y=右向, z=下向
         * - YPR 角度：Yaw(偏航), Pitch(俯仰), Roll(横滚)
         */
        class OrientationUtils {
        public:
            /**
             * @brief YPR 欧拉角转换为四元数
             * @param yaw_deg 偏航角 (度)，绕 z 轴旋转，正值为顺时针
             * @param pitch_deg 俯仰角 (度)，绕 y 轴旋转，正值为机头向下
             * @param roll_deg 横滚角 (度)，绕 x 轴旋转，正值为右翼向下
             * @return 对应的四元数，表示从 NED 坐标系到机体坐标系的旋转
             */
            static Orientation yprToQuaternion(double yaw_deg, double pitch_deg, double roll_deg);

            /**
             * @brief 四元数转换为 YPR 欧拉角
             * @param quaternion 四元数
             * @return YPR 角度向量 [yaw, pitch, roll] (度)
             */
            static Vector3D quaternionToYPR(const Orientation& quaternion);

            /**
             * @brief 从 YPR 角度向量创建四元数
             * @param ypr_deg YPR 角度向量 [yaw, pitch, roll] (度)
             * @return 对应的四元数
             */
            static Orientation yprVectorToQuaternion(const Vector3D& ypr_deg);

            /**
             * @brief 验证四元数是否有效（归一化）
             * @param quaternion 待验证的四元数
             * @param tolerance 容差，默认为 1e-6
             * @return 如果四元数有效返回 true
             */
            static bool isValidQuaternion(const Orientation& quaternion, double tolerance = 1e-6);

            /**
             * @brief 归一化四元数
             * @param quaternion 待归一化的四元数
             * @return 归一化后的四元数
             */
            static Orientation normalizeQuaternion(const Orientation& quaternion);

            /**
             * @brief 计算两个四元数之间的角度差
             * @param q1 第一个四元数
             * @param q2 第二个四元数
             * @return 角度差 (弧度)
             */
            static double quaternionAngularDistance(const Orientation& q1, const Orientation& q2);

            /**
             * @brief 四元数球面线性插值 (SLERP)
             * @param q1 起始四元数
             * @param q2 结束四元数
             * @param t 插值参数 [0, 1]
             * @return 插值结果四元数
             */
            static Orientation quaternionSlerp(const Orientation& q1, const Orientation& q2, double t);

        private:
            /**
             * @brief 角度转弧度
             * @param degrees 角度值
             * @return 弧度值
             */
            static double degToRad(double degrees);

            /**
             * @brief 弧度转角度
             * @param radians 弧度值
             * @return 角度值
             */
            static double radToDeg(double radians);

            /**
             * @brief 将角度规范化到 [-180, 180] 度范围
             * @param angle_deg 角度值
             * @return 规范化后的角度值
             */
            static double normalizeAngle(double angle_deg);
        };

    } // namespace NSUtils
} // namespace NSDrones
