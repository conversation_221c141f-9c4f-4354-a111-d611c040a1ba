// src/utils/enum_utils.cpp

#include "core/types.h"
#include "core/geometry/ishape.h"
#include "planning/planning_types.h"
#include "uav/uav_types.h"
#include "utils/enum_utils.h"
#include "utils/logging.h"


namespace NSDrones {
	namespace NSUtils {

		// 定义全局枚举转换器注册表
		std::map<std::string, EnumConverterFunc> G_ENUM_CONVERTERS;

		// 定义全局枚举到字符串转换器 (std::any 版本)
		std::map<std::string, EnumToStringAnyFunc> G_ENUM_TO_STRING_ANY_CONVERTERS;

		// 新增：定义全局类型索引到枚举字符串名称的映射表
		std::map<std::type_index, std::string> G_TYPE_INDEX_TO_ENUM_NAME_MAP;

		std::any convertStringToEnumAny(const std::string& enumTypeName, const std::string& stringValue, std::any defaultValueAny) {
			auto it = G_ENUM_CONVERTERS.find(enumTypeName);
			if (it != G_ENUM_CONVERTERS.end()) {
				// 找到转换器，调用它
				LOG_DEBUG("使用类型 '{}' 的转换器转换值 '{}'", enumTypeName, stringValue); // 日志：尝试转换
				std::any result = it->second(stringValue, defaultValueAny);
				// 检查转换后的结果是否与输入字符串通过 magic_enum 反向转换后一致（如果适用），或者是否有更通用的验证方式
				// 这是一个可选的增强步骤，取决于具体需求
				return result;
			}
			else {
				LOG_WARN("未找到类型 '{}' 的枚举转换器。将为值 '{}' 返回提供的默认值。", enumTypeName, stringValue); // 日志：未找到转换器
				return defaultValueAny; // 未找到转换器，返回原始默认值
			}
		}

		std::string enumToStringAny(const std::any& enumValueAny, const std::string& enumTypeName) {
			auto it = G_ENUM_TO_STRING_ANY_CONVERTERS.find(enumTypeName);
			if (it != G_ENUM_TO_STRING_ANY_CONVERTERS.end()) {
				//LOG_DEBUG("使用类型 '{}' 的 std::any 到字符串转换器转换值。", enumTypeName);
				return it->second(enumValueAny);
			} else {
				LOG_WARN("未找到类型 '{}' 的 std::any 到字符串的枚举转换器。", enumTypeName);
				if (enumValueAny.has_value()) {
				    return "UNKNOWN_ENUM_TYPE<" + enumTypeName + ">(" + enumValueAny.type().name() + ")";
                } else {
                    return "UNKNOWN_ENUM_TYPE<" + enumTypeName + ">(empty_any)";
                }
			}
		}

		// 新增：实现 typeIdToRegisteredName
		std::optional<std::string> typeIdToRegisteredName(const std::type_index& tid) {
			auto it = G_TYPE_INDEX_TO_ENUM_NAME_MAP.find(tid);
			if (it != G_TYPE_INDEX_TO_ENUM_NAME_MAP.end()) {
				return it->second;
			}
			LOG_WARN("typeIdToRegisteredName: 未找到类型索引 '{}' 对应的已注册枚举名称。", tid.name());
			return std::nullopt;
		}

		void initializeAndRegisterAllEnums() {
			LOG_INFO("开始注册所有枚举类型..."); // 日志：开始注册

			// 从 core/types.h 注册枚举
			registerEnum<NSDrones::NSCore::Status>("NSDrones::NSCore::Status", NSDrones::NSCore::Status::UNKNOWN);
			registerEnum<NSDrones::NSCore::ErrorCode>("NSDrones::NSCore::ErrorCode", NSDrones::NSCore::ErrorCode::Success);
			registerEnum<NSDrones::NSCore::ShapeType>("NSDrones::NSCore::ShapeType", NSDrones::NSCore::ShapeType::UNKNOWN);
			registerEnum<NSDrones::NSCore::ZoneType>("NSDrones::NSCore::ZoneType", NSDrones::NSCore::ZoneType::UNKNOWN);
			registerEnum<NSDrones::NSCore::BuildingMaterialType>("NSDrones::NSCore::BuildingMaterialType", NSDrones::NSCore::BuildingMaterialType::UNKNOWN);
			registerEnum<NSDrones::NSCore::EnergyModelType>("NSDrones::NSCore::EnergyModelType", NSDrones::NSCore::EnergyModelType::UNKNOWN);
			registerEnum<NSDrones::NSCore::FlightControllerType>("NSDrones::NSCore::FlightControllerType", NSDrones::NSCore::FlightControllerType::UNKNOWN);
			registerEnum<NSDrones::NSCore::PropulsionType>("NSDrones::NSCore::PropulsionType", NSDrones::NSCore::PropulsionType::UNKNOWN);
			registerEnum<NSDrones::NSCore::FeatureType>("NSDrones::NSCore::FeatureType", NSDrones::NSCore::FeatureType::UNKNOWN);
			registerEnum<NSDrones::NSCore::AltitudeType>("NSDrones::NSCore::AltitudeType", NSDrones::NSCore::AltitudeType::ABSOLUTE_ALTITUDE);
			registerEnum<NSDrones::NSCore::ControlPointType>("NSDrones::NSCore::ControlPointType", NSDrones::NSCore::ControlPointType::WAYPOINT_MUST_PASS);
			registerEnum<NSDrones::NSCore::TaskType>("NSDrones::NSCore::TaskType", NSDrones::NSCore::TaskType::UNKNOWN);
			registerEnum<NSDrones::NSCore::UavType>("NSDrones::NSCore::UavType", NSDrones::NSCore::UavType::UNKNOWN);
			registerEnum<NSDrones::NSCore::LogLevel>("NSDrones::NSCore::LogLevel", NSDrones::NSCore::LogLevel::trace);
			registerEnum<NSDrones::NSCore::SeverityType>("NSDrones::NSCore::SeverityType", NSDrones::NSCore::SeverityType::UNKNOWN);
			registerEnum<NSDrones::NSCore::MovementStrategyType>("NSDrones::NSCore::MovementStrategyType", NSDrones::NSCore::MovementStrategyType::UNKNOWN);

			// 从 planning/planning_types.h 注册枚举
			registerEnum<NSDrones::NSPlanning::WarningType>("NSDrones::NSPlanning::WarningType", NSDrones::NSPlanning::WarningType::UNKNOWN);
			registerEnum<NSDrones::NSPlanning::PlanningStrategyType>("NSDrones::NSPlanning::PlanningStrategyType", NSPlanning::PlanningStrategyType::DEFAULT);
			registerEnum<NSDrones::NSPlanning::PlannerType>("NSDrones::NSPlanning::PlannerType", NSDrones::NSPlanning::PlannerType::UNKNOWN);
			registerEnum<NSDrones::NSPlanning::CalculationFidelityType>("NSDrones::NSPlanning::CalculationFidelityType", NSPlanning::CalculationFidelityType::UNKNOWN);
			registerEnum<NSDrones::NSPlanning::TaskAllocationStrategyType>("NSDrones::NSPlanning::TaskAllocationStrategyType", NSPlanning::TaskAllocationStrategyType::UNKNOWN);
			registerEnum<NSDrones::NSPlanning::ScanPatternType>("NSDrones::NSPlanning::ScanPatternType", NSPlanning::ScanPatternType::UNKNOWN);

			// 从 uav/uav_types.h 注册枚举
			registerEnum<NSDrones::NSUav::FlightMode>("NSDrones::NSUav::FlightMode", NSDrones::NSUav::FlightMode::UNKNOWN);
			registerEnum<NSDrones::NSUav::MissionStatus>("NSDrones::NSUav::MissionStatus", NSDrones::NSUav::MissionStatus::PENDING);
			// 注意: UavState::status 是 std::string，不是这里的 MissionStatus 枚举

			LOG_INFO("所有枚举类型注册完毕。"); // 日志：注册完成
		}

	} // namespace NSUtils
} // namespace NSDrones