// include/uav/dynamics/fixedwing_dynamics.h
#pragma once

#include "uav/idynamic_model.h" 
#include "uav/uav.h"
#include <memory>                 
#include <limits>                 
#include <cmath>                 

// 前向声明 EntityObject
namespace NSDrones { namespace NSCore { class EntityObject; } }

namespace NSDrones {
	namespace NSUav {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class FixedWingDynamics
		 * @brief 固定翼无人机动力学模型实现。
		 *        特点是需要保持一定的速度飞行，转弯受最小半径和最大倾角限制。
		 *        依赖于 owner_ 引用来访问参数。
		 */
		class FixedWingDynamics : public IDynamicModel {
		public:
			/**
			 * @brief 构造函数。
			 * @param owner 指向拥有此模型的无人机对象 (const 引用)。
			 */
			explicit FixedWingDynamics(const Uav& owner); // 接收 owner 引用

			// --- 禁止拷贝和移动 ---
			FixedWingDynamics(const FixedWingDynamics&) = delete;
			FixedWingDynamics& operator=(const FixedWingDynamics&) = delete;
			FixedWingDynamics(FixedWingDynamics&&) = delete;
			FixedWingDynamics& operator=(FixedWingDynamics&&) = delete;

			/** @brief 虚析构函数 */
			~FixedWingDynamics() override = default;

			// --- 覆写 IDynamicModel 虚函数 ---
			/** @brief 返回无人机类型。*/
			UavType getType() const override { return UavType::FIXED_WING; }

			// --- 固定翼特定行为实现 ---
			// 参数查找基于 "dynamics.fw.*"
			double getMaxTurnRate(const UavState& state) const override;
			double getMinTurnRadius(const UavState& state) const override;
			double getMaxBankAngle(const UavState& state) const override;
			/** @brief 获取最小运行速度 (即失速速度, m/s)。参数键 "dynamics.fw.stallSpeed"。*/
			double getMinOperationalSpeed(const UavState& state) const override;
			/** @brief 获取最大水平减速度 (m/s^2)。参数键 "dynamics.fw.maxDecel"。 */
			double getMaxHorizontalDeceleration(const UavState& state) const override;
			// 继承基类的其他速度/加速度限制实现，它们会查找 "dynamics.base.*" 或 "dynamics.fw.*"

			/** @brief 检查状态转换是否可行。*/
			bool isStateTransitionFeasible(const UavState& current, const UavState& next, Time dt) const override;

			// --- 力学计算实现 (简化占位符) ---
			/** @brief 计算升力向量 (简化)。*/
			Vector3D computeLiftForce(const UavState& state, double air_density) const override;
			/** @brief 计算阻力向量 (简化)。参数键 "dynamics.fw.drag_coeff_k"。*/
			Vector3D computeDragForce(const UavState& state, double air_density) const override;
			/** @brief 计算所需推力向量 (简化)。*/
			Vector3D computeThrustForce(const UavState& state) const override;
		};

	} // namespace NSUav
} // namespace NSDrones