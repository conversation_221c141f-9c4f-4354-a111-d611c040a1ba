#pragma once

#include "core/geometry/ishape.h"
#include <fcl/geometry/shape/capsule.h>

namespace NSDrones {
namespace NSCore {

    /**
     * @brief 线段形状
     * 
     * 表示一个几何线段，在FCL中用极细的胶囊体实现。
     * 主要用于路径表示、激光束、连接线等。
     */
    class LineShape : public IShape {
    private:
        fcl::Vector3d start_;  // 起点
        fcl::Vector3d end_;    // 终点
        static constexpr double LINE_RADIUS = 1e-6;  // 极小半径，用于FCL表示
        
        mutable std::shared_ptr<fcl::Capsuled> fcl_capsule_;  // FCL胶囊体对象（延迟创建）

    public:
        /**
         * @brief 构造函数
         * @param start 起点
         * @param end 终点
         */
        LineShape(const fcl::Vector3d& start, const fcl::Vector3d& end);

        /**
         * @brief 默认构造函数（单位线段）
         */
        LineShape();

        /**
         * @brief 拷贝构造函数
         */
        LineShape(const LineShape& other);

        /**
         * @brief 赋值操作符
         */
        LineShape& operator=(const LineShape& other);

        // IShape接口实现
        ShapeType getType() const override { return ShapeType::LINE; }
        std::shared_ptr<fcl::CollisionGeometryd> getFCLGeometry() const override;
        fcl::AABBd getAABB(const fcl::Transform3d& transform = fcl::Transform3d::Identity()) const override;
        double getVolume() const override { return 0.0; }  // 线的体积为0
        double getSurfaceArea() const override { return 0.0; }  // 线的表面积为0
        fcl::Vector3d getCentroid() const override { return (start_ + end_) * 0.5; }
        fcl::Matrix3d getInertiaMatrix(double mass) const override;
        std::unique_ptr<IShape> clone() const override;
        nlohmann::json serialize() const override;
        bool deserialize(const nlohmann::json& json) override;
        std::string toString() const override;
        bool containsPoint(const fcl::Vector3d& point) const override;
        double distanceToPoint(const fcl::Vector3d& point) const override;
        double getCharacteristicSize() const override { return getLength(); }
        int getDimension() const override { return 1; }  // 线是1维

        // 线段特有方法
        /**
         * @brief 获取起点
         * @return 起点位置
         */
        const fcl::Vector3d& getStart() const { return start_; }

        /**
         * @brief 获取终点
         * @return 终点位置
         */
        const fcl::Vector3d& getEnd() const { return end_; }

        /**
         * @brief 设置起点和终点
         * @param start 新的起点
         * @param end 新的终点
         */
        void setEndpoints(const fcl::Vector3d& start, const fcl::Vector3d& end);

        /**
         * @brief 获取线段长度
         * @return 线段长度
         */
        double getLength() const;

        /**
         * @brief 获取线段方向向量（归一化）
         * @return 方向向量
         */
        fcl::Vector3d getDirection() const;

        /**
         * @brief 获取线段上的点
         * @param t 参数（0=起点，1=终点）
         * @return 线段上的点
         */
        fcl::Vector3d getPointAt(double t) const;



        /**
         * @brief 创建单位线段（从原点到(1,0,0)）
         * @return 线段形状实例
         */
        static std::unique_ptr<LineShape> createUnitLine();

        /**
         * @brief 从两点创建线段
         * @param start 起点
         * @param end 终点
         * @return 线段形状实例
         */
        static std::unique_ptr<LineShape> createFromPoints(const fcl::Vector3d& start, const fcl::Vector3d& end);

    private:
        void ensureFCLObject() const;
        void validateEndpoints() const;
    };

} // namespace NSCore
} // namespace NSDrones
