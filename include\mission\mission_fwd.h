// include/mission/mission_fwd.h
#pragma once

#include <memory> 

namespace NSDrones {
	namespace NSMission {
		class CapabilityRequirement;

		class ITaskStrategy;
		using ITaskStrategyPtr = std::shared_ptr<ITaskStrategy>;
		using ConstITaskStrategyPtr = std::shared_ptr<const ITaskStrategy>;
		using ITaskStrategyMap = std::map<std::string, ITaskStrategyPtr>;

		class Task; 
		using TaskPtr = std::shared_ptr<Task>;
		using ConstTaskPtr = std::shared_ptr<const Task>;

		struct ITaskParams;
		using ITaskParamsPtr = std::shared_ptr<ITaskParams>;
		using ConstITaskParamsPtr = std::shared_ptr<const ITaskParams>;

	} // namespace NSMission
} // namespace NSDrones